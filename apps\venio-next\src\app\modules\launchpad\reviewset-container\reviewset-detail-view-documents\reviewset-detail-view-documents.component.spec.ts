import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewDocumentsComponent } from './reviewset-detail-view-documents.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'

describe('ReviewsetDocumentViewComponent', () => {
  let component: ReviewsetDetailViewDocumentsComponent
  let fixture: ComponentFixture<ReviewsetDetailViewDocumentsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewDocumentsComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectIsReviewSetDocumentViewLoading$: of(undefined),
            selectReviewSetDocumentViewSuccess$: of({} as ResponseModel),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewDocumentsComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
