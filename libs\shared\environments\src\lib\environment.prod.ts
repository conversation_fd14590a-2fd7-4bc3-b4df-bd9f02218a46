export const environment = {
  production: true,
  baseUrl: '' as string,
  apiUrl: '' as string,
  applicationUrl: '' as string,
  // Derives from libs/shared/assets/src/files/json/app-settings.json
  aiSearchEnabled: false,
  /**
   * If there are any other URL configured, please update here as well.
   */
  deployUrl: '/VenioWeb/OnDemand/venio-next/' as string,
  // Derives from libs/shared/assets/src/files/json/app-settings.json
  chatApiUrl: '' as string,

  pspdfkitLicense: '',

  allowedOrigin: typeof window !== 'undefined' ? window.location.origin : '',

  version: '' as string,

  // Elastic Fleet API token see app-settings.json
  elasticFleetApiToken: '',
  // Elastic Fleet API URL see app-settings.json
  elasticFleetApiUrl: '',
}
