import {
  ChangeDetectionStrategy,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { TextBoxComponent } from '@progress/kendo-angular-inputs'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms'
import { DebounceTimer } from '@venio/util/utilities'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { debounceTime, filter, Subject, takeUntil } from 'rxjs'
import { map } from 'rxjs/operators'
import { LoaderComponent } from '@progress/kendo-angular-indicators'

/**
 * The local content states model.
 */
interface LocalContentState {
  isRequestNewCode: boolean
  isLoinInvoked: boolean
  securityCodeControlValue: string
  isTwoFactorAuthCodeSystem: boolean
}

/**
 * The local message states model.
 */
interface LocalMessageState {
  type: 'error' | 'success'
  message: string
}
@Component({
  selector: 'venio-external-user-login-container',
  standalone: true,
  imports: [
    CommonModule,
    TextBoxComponent,
    ButtonComponent,
    NgOptimizedImage,
    ReactiveFormsModule,
    LoaderComponent,
  ],
  templateUrl: './external-user-login-container.component.html',
  styleUrl: './external-user-login-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExternalUserLoginContainerComponent implements OnDestroy, OnInit {
  private readonly toDestroy$ = new Subject<void>()

  /** The security code control to enter the security code. */
  public readonly securityCodeControl = new FormControl('', Validators.required)

  /** The iframe messenger service to send and receive messages from the parent window. */
  private readonly iframeMessengerService = inject(IframeMessengerService)

  /** The state to show the loader when the login is in progress. */
  public readonly isLoginInProgress = signal<boolean>(false)

  /** The message state to show the success or error message. */
  public readonly messageState = signal<LocalMessageState>(undefined)

  /** Returns true if the security code control is invalid. */
  public get isInvalid(): boolean {
    return (
      this.securityCodeControl.invalid &&
      this.securityCodeControl.touched &&
      this.securityCodeControl.dirty
    )
  }

  public ngOnInit(): void {
    this.#selectParentNotifiedMessage()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Login the user. Notify the parent window that the user is trying to log in.
   * @returns {void}
   */
  public login(): void {
    this.securityCodeControl.markAsDirty()
    this.securityCodeControl.markAsTouched()

    if (this.isLoginInProgress() || !this.securityCodeControl.value.trim())
      return

    // Update the local state to show the loader.
    this.isLoginInProgress.set(true)

    // When the user clicks the login button, we notify the parent window that the login is invoked.
    // The coed is sent to the parent window which will be used to authenticate the user
    // from the parent window.
    this.#notifyParentWindowForStateChange({
      isTwoFactorAuthCodeSystem: true,
      isRequestNewCode: false,
      isLoinInvoked: true,
      securityCodeControlValue: this.securityCodeControl.value,
    })
  }

  /**
   * Regenerate the security code. Notify the parent window that the user requested a new code.
   * @returns {void}
   */
  @DebounceTimer(100)
  public regenerateCode(): void {
    this.#notifyParentWindowForStateChange({
      isTwoFactorAuthCodeSystem: true,
      isRequestNewCode: true,
      isLoinInvoked: false,
      securityCodeControlValue: this.securityCodeControl.value,
    })
  }

  /**
   * Notify the parent window that the changing new password was successful. In the future, once the new application is
   * built, this method will be removed.
   * @param {LocalContentState} state - The state of the local content.
   * @returns {void}
   */
  #notifyParentWindowForStateChange(state: LocalContentState): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.AUTH_UPDATE,
        content: {
          ...state,
        },
      } as MessageContent,
    })
  }

  /**
   * Select the parent notified message from the iframe messenger service.
   * This message is sent from the parent window to the child window.
   * The message contains the success or error message from the parent window.
   * @returns {void}
   */
  #selectParentNotifiedMessage(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'FRAME_WINDOW' &&
            (m.payload as MessageContent).content['securityCodeChildMessage'] &&
            (m.payload as MessageContent).type === MessageType.AUTH_UPDATE
        ),
        map((mc) => (mc.payload as MessageContent)?.content),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((mc) => {
        const { errorMessage, successMessage, isSecurityCodeLoginProgress } = mc
        this.messageState.update((prev) => ({
          ...prev,
          type: errorMessage ? 'error' : 'success',
          message: errorMessage || successMessage,
        }))
        // We are showing the loader when the security code login is in progress.
        this.isLoginInProgress.set(isSecurityCodeLoginProgress)
      })
  }
}
