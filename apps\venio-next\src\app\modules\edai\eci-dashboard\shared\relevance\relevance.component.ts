import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';
import { CenterTextComponent } from '../center-text/center-text.component';
import { NgFor } from '@angular/common';

const mockData = [40951, 28816, 12345];

@Component({
  selector: 'app-relevance',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent, CenterTextComponent, NgFor],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss'
})
export class RelevanceComponent {
  public sunburstChartColors = [
    'transparent',
    '#6305FF',
    '#0084FF',
    '#FF00FF',
    '#1100FF',
    '#0FE5B7',
    '#00D0FF'
  ];
  getColor(i: number): string {
    return this.sunburstChartColors[i + 1];
  }
  public labels = [
    'Relevant',
    'Not Relevant',
    'Potentially Relevant',
    'Likely Relevant',
    'No Content',
    'Tech Issue'
  ];
  public graph = {
    data: [{ values: mockData, type: 'pie', hole: 0.5, marker: { color: 'red' } }],
    layout: {
      width: 350,
      height: 350,
      autosize: false,
      automargin: false,
      margin: { t: 0, r: 0, b: 0, l: 0 },
      showlegend: false
    }
  };
  public config = {
    responsive: false,
    displayModeBar: false,
    displaylogo: false,
    staticPlot: false,
    doubleClick: false,
    scrollZoom: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d'
    ]
  };
}
