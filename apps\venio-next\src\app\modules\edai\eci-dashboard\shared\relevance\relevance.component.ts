import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { CommonModule } from '@angular/common';
import { EciDataService } from '../data.service';

const mockData = [40951, 28816, 12345];

@Component({
  selector: 'venio-eci-relevance',
  standalone: true,
  imports: [PlotlyModule, CommonModule],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss'
})
export class EciRelevanceComponent {
  constructor(private dataService: EciDataService) {}

  public sunburstChartColors = [
    '#6305FF',
    '#0084FF', 
    '#FF00FF',
    '#1100FF',
    '#0FE5B7',
    '#00D0FF'
  ];

  getColor(i: number): string {
    return this.sunburstChartColors[i];
  }

  public labels = [
    'Relevant',
    'Not Relevant', 
    'Potentially Relevant',
    'Likely Relevant',
    'No Content',
    'Tech Issue'
  ];

  public graph = {
    data: [{
      values: mockData,
      type: 'pie',
      hole: 0.5,
      marker: { 
        colors: this.sunburstChartColors.slice(0, mockData.length)
      },
      textinfo: 'none',
      hovertemplate: '<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
    }],
    layout: {
      autosize: true,
      automargin: true,
      margin: { t: 0, r: 0, b: 0, l: 0 },
      showlegend: false,
      paper_bgcolor: 'transparent',
      plot_bgcolor: 'transparent'
    }
  };

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false
  };

  onChartClick(event: any) {
    console.log('Relevance chart clicked:', event);
    // Handle chart click interactions
    this.dataService.setIsFocusedSectionOpened(true);
  }
}
