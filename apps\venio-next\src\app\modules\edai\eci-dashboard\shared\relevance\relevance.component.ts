import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';
import { CenterTextComponent } from '../center-text/center-text.component';
import { NgFor } from '@angular/common';

const mockData = [40951, 28816, 12345, 8500, 3200, 1800];

@Component({
  selector: 'venio-relevance',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent, CenterTextComponent, NgFor],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss'
})
export class RelevanceComponent {
  public sunburstChartColors = [
    'transparent',
    '#6305FF',
    '#0084FF',
    '#FF00FF',
    '#1100FF',
    '#0FE5B7',
    '#00D0FF'
  ];
  getColor(i: number): string {
    return this.sunburstChartColors[i + 1];
  }
  public labels = [
    'Relevant',
    'Not Relevant',
    'Potentially Relevant',
    'Likely Relevant',
    'No Content',
    'Tech Issue'
  ];
  public graph = {
    data: [{
      values: mockData,
      type: 'pie',
      hole: 0.5,
      marker: {
        colors: ['#ff00ff', '#6305ff', '#0084ff', '#1100ff', '#0fe5b7', '#00d0ff']
      },
      textinfo: 'none'
    }],
    layout: {
      autosize: true,
      title: '',
      automargin: true,
      margin: { t: 0, r: 0, b: 20, l: 20 },
      showlegend: false,
      plot_bgcolor: 'rgba(0,0,0,0)',
      paper_bgcolor: 'rgba(0,0,0,0)'
    }
  };
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d'
    ]
  };
}
