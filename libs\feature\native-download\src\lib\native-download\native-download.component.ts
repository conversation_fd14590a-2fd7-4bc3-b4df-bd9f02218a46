import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core'
import { CommonModule, DatePipe } from '@angular/common'
import { Subject, debounceTime, filter, takeUntil, timer } from 'rxjs'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  InputsModule,
  NumericTextBoxModule,
  TextBoxModule,
} from '@progress/kendo-angular-inputs'
import {
  NativeDownloadFacade,
  NativeDownloadOptions,
  PrintImageActionTemplateService,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'lib-native-download',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    LabelModule,
    TextBoxModule,
    NumericTextBoxModule,
    InputsModule,
  ],
  templateUrl: './native-download.component.html',
  styleUrl: './native-download.component.scss',
  providers: [DatePipe],
})
export class NativeDownloadComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private readonly toDestroy$ = new Subject<void>()

  private dateStamp: Date = new Date()

  public downloadForm: FormGroup

  public disableSaveButton = false

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  @ViewChild('nativeDownloadActionTemplate')
  public nativeDownloadActionTemplate!: TemplateRef<any>

  constructor(
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private activatedRoute: ActivatedRoute,
    private nativeDownloadFacade: NativeDownloadFacade,
    private cdr: ChangeDetectorRef,
    private templateService: PrintImageActionTemplateService
  ) {}

  public ngAfterViewInit(): void {
    this.templateService.nativeDownloadActionTemplate =
      this.nativeDownloadActionTemplate
    timer(50).subscribe(() => {
      this.cdr.markForCheck()
    })
  }

  public ngOnInit(): void {
    this.initialize()
  }

  public initialize(): void {
    this.disableSaveButton = false
    this.initializeForm()
    this.enableDisableCustomNameOption()
  }

  public initializeForm(): void {
    this.dateStamp = new Date()
    this.downloadForm = this.fb.group({
      nativeDownloadName: [
        'NativeDownload_' +
          this.datePipe.transform(this.dateStamp, 'yyyyMMddHHmmss'),
        [Validators.required, NativeDownloadComponent.fileNameValidator],
      ],
      downloadOriginal: 'DOWNLOAD_ORIGINAL',
      downloadWithRedaction: true,
      downloadFileName: 'CUSTOM_FILE_NAME',
      customFileNameSettings: this.fb.group({
        prefix: [
          {
            value:
              'NativeDownload_' +
              this.datePipe.transform(this.dateStamp, 'yyyyMMddHHmmss') +
              '_',
            disabled: false,
          },
          [Validators.required, NativeDownloadComponent.fileNameValidator],
        ],
        startingNumber: [{ value: 0, disabled: false }, [Validators.required]],
        padding: [{ value: 8, disabled: false }, [Validators.required]],
      }),
    })
  }

  public isCustomFileNameSelected(): boolean {
    return (
      this.downloadForm?.get('downloadFileName').value === 'CUSTOM_FILE_NAME'
    )
  }

  public getMaxValue(): number {
    return Number('9'.repeat(this.getMaxLength()))
  }

  public getMaxLength(): number {
    return Number(
      this.downloadForm?.get('customFileNameSettings')?.get('padding').value ??
        1
    )
  }

  public onPaddingChanged($event: string): void {
    const maxlength = Number($event)
    const startingNumber =
      this.downloadForm?.get('customFileNameSettings')?.get('startingNumber')
        .value ?? 0
    if (Number(startingNumber) > Number('9'.repeat(maxlength))) {
      this.downloadForm
        ?.get('customFileNameSettings')
        ?.get('startingNumber')
        .setValue(Number('9'.repeat(maxlength)))
    }
  }

  public enableDisableCustomNameOption(): void {
    this.downloadForm
      .get('downloadFileName')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe((selectedOption) => {
        const customFileNameSettings = this.downloadForm?.get(
          'customFileNameSettings'
        )
        if (selectedOption === 'CUSTOM_FILE_NAME') {
          customFileNameSettings?.enable()
        } else {
          customFileNameSettings?.disable()
        }
      })
  }

  public hasNativeDownloadName(): boolean {
    return this.downloadForm?.get('nativeDownloadName').valid
  }

  public onDeleteClicked(): void {
    this.disableSaveButton = true
    const nativeDownloadOptions = Object.assign(
      {},
      <NativeDownloadOptions>this.downloadForm.value
    )

    this.nativeDownloadFacade.queueNativeDownload(
      this.projectId,
      nativeDownloadOptions
    )

    const queueResponseObs =
      this.nativeDownloadFacade.getNativeDownloadQueueResponse$
        .pipe(
          filter((data) => !!data),
          takeUntil(this.toDestroy$)
        )
        .subscribe((response) => {
          this.cdr.markForCheck()
          this.initialize()
          this.nativeDownloadFacade.clearNativeDownloadQueueResponse()
          queueResponseObs.unsubscribe()
        })
  }

  public static fileNameValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    const forbiddenChars = /[\\\/:\*\?"<>\|]/g
    if (control.value && forbiddenChars.test(control.value)) {
      return { invalidFileName: true }
    }
    return null
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
