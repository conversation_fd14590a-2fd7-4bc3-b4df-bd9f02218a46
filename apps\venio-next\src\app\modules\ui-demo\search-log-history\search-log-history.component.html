<div class="t-flex t-mt-4 t-flex-col t-w-full">
  <kendo-grid
    [kendoGridBinding]="gridView"
    kendoGridSelectBy="id"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    [skip]="skip">
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total">
      <kendo-grid-spacer></kendo-grid-spacer>
      <kendo-label class="k-form">
        <kendo-numerictextbox
          class="!t-w-[3rem]"
          format="number"
          [step]="1"
          [value]="currentPage"
          [min]="1"
          [max]="totalPages"
          [spinners]="false"
          [selectOnFocus]="true"></kendo-numerictextbox>
      </kendo-label>
      <span> - {{ pageSize }} Of {{ gridView.length }} </span>
      <kendo-dropdownlist
        class="!t-w-[5rem] !t-border-[#707070]"
        [data]="sizes"
        [value]="pageSize"></kendo-dropdownlist>
      per page
      <div class="t-flex t-gap-2">
        <button
          kendoButton
          #parentEl
          *ngFor="let icon of svgIconForPageControls"
          class="!t-p-[0.3rem]"
          (click)="browseActionClicked(icon.actionType)"
          fillMode="outline"
          size="none">
          <span
            [parentElement]="parentEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            [svgUrl]="icon.iconPath"
            height="0.8rem"
            width="1rem"></span>
        </button>
      </div>
    </ng-template>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="full_name"
      title="Search Expression"
      [width]="220">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          class="customer-photo"
          [ngStyle]="{ 'background-image': '' }"></div>
        <div class="customer-name">{{ dataItem.full_name }}</div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="job_title"
      title="Dynamic Folder"
      [width]="220">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="is_online"
      title="Search By"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false"
      filter="boolean">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span *ngIf="dataItem.is_online === true" class="badge badge-success"
          >Online</span
        >
        <span *ngIf="dataItem.is_online === false" class="badge badge-danger"
          >Offline</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="phone"
      title="Search Date"
      [width]="130">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="target"
      title="Is included Family"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="gender"
      title="Duplicate Option"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="budget"
      title="Hit Count"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="address"
      title="Is Query Mode"
      [width]="200">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      title="Action"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex">
          <kendo-buttongroup>
            <button
              kendoButton
              #actionGrid
              *ngFor="let icon of svgIconForGridControls"
              class="!t-p-[0.3rem] t-w-1/2"
              (click)="browseActionClicked(icon.actionType)"
              fillMode="clear"
              size="none">
              <span
                [parentElement]="actionGrid.element"
                venioSvgLoader
                hoverColor="#FFBB12"
                color="#979797"
                [svgUrl]="icon.iconPath"
                height="0.9rem"
                width="1rem"></span>
            </button>
          </kendo-buttongroup>
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
