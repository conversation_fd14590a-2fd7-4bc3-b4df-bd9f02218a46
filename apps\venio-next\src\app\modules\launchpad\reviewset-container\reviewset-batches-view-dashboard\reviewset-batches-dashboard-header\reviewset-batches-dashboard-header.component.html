<span class="k-window-title t-items-center t-text-primary t-text-lg">
  <button
    class="t-inline-block t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-items-center"
    fillMode="clear"
    kendoButton
    imageUrl="assets/svg/icon-set-one-rating-multiple.svg"></button>
  {{ selectedReviewSetEntry()?.reviewSetName }}

  <span
    class="t-inline-block t-rounded t-px-3 t-py-1 t-text-uppercase t-text-xs t-bg-[#9BD2A7] t-ml-2">
    <span class="t-text-[#0F4B1B] t-tracking-widest"> CASE NAME </span>

    <span class="t-text-[#FFFFFF] t-tracking-wide">
      {{ selectedReviewSetEntry()?.projectName }}</span
    >
  </span>

  <span class="t-text-[#263238] t-text-base t-ml-2">
    Number of documents {{ selectedReviewSetEntry()?.totalDocCount | number }}
  </span>
</span>

<div class="t-flex t-gap-4 t-justify-between t-items-center">
  <div class="t-flex t-gap-4 t-mr-[5rem]">
    <venio-report-date-picker class="t-min-w-[150px] t-w-56 t-self-center" />

    <kendo-multiselect
      placeholder="Select Reviewer"
      [checkboxes]="true"
      [data]="loadedReviewSetReviewers()"
      [value]="selectedReviewer()"
      (valueChange)="onValueChange($event)"
      [tagMapper]="tagMapper"
      [valuePrimitive]="true"
      [autoClose]="false"
      textField="fullName"
      valueField="userId"
      class="!t-w-52">
      <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
        <div
          class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50">
          <input
            [checked]="isAllReviewerSelected()"
            (click)="onSelectAllChanged()"
            type="checkbox"
            #allReviewer
            id="all-reviewer"
            kendoCheckBox />
          <kendo-label
            (click)="onSelectAllChanged()"
            class="k-checkbox-label t-w-full t-h-full"
            for="all-reviewer"
            text="All"></kendo-label>
        </div>
      </ng-template>
      <ng-template kendoSuffixTemplate>
        <button
          kendoButton
          [svgIcon]="icons.chevronDownIcon"
          fillMode="link"
          class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
      </ng-template>
    </kendo-multiselect>
  </div>
  <button
    kendoWindowCloseAction
    (click)="dialogCloseAction.emit()"
    class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
</div>
