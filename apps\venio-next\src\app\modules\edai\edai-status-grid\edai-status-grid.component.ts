import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  ColumnComponent,
  DataBindingDirective,
  FilterMenuTemplateDirective,
  GridComponent,
  GridSpacerComponent,
  HeaderTemplateDirective,
  NoRecordsTemplateDirective,
  PagerTemplateDirective,
  SelectionDirective,
} from '@progress/kendo-angular-grid'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { eyeIcon } from '@progress/kendo-svg-icons'
import {
  PopoverAnchorDirective,
  PopoverBodyTemplateDirective,
  PopoverComponent,
  TooltipDirective,
} from '@progress/kendo-angular-tooltip'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { filter, Subject, switchMap, take, takeUntil } from 'rxjs'
import {
  AiFacade,
  AIJobType,
  EDaiStatusModel,
  EdaiStatusResponseModel,
} from '@venio/data-access/ai'
import dayjs from 'dayjs'
import { ActivatedRoute } from '@angular/router'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { EdaiRelevanceStatusDetailComponent } from '../edai-relevance-status-detail/edai-relevance-status-detail.component'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { EdaiPrivilegeStatusDetailComponent } from '../edai-privilege-status-detail/edai-privilege-status-detail.component'
import { SearchFacade, SearchResultFacade } from '@venio/data-access/review'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { UuidGenerator } from '@venio/util/uuid'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { CommandsFacade } from '@venio/data-access/common'
import { GridCustomFilterChecklistComponent } from '@venio/ui/grid-custom-filter-checklist'
import { EdaiPiiStatusDetailComponent } from '../edai-pii-status-detail/edai-pii-status-detail.component'

@Component({
  selector: 'venio-edai-status-grid',
  standalone: true,
  imports: [
    CommonModule,
    CellTemplateDirective,
    ColumnComponent,
    DynamicHeightDirective,
    PopoverAnchorDirective,
    SVGIconComponent,
    HeaderTemplateDirective,
    TooltipDirective,
    DataBindingDirective,
    GridComponent,
    SelectionDirective,
    GridSpacerComponent,
    UiPaginationModule,
    PagerTemplateDirective,
    EdaiRelevanceStatusDetailComponent,
    PopoverBodyTemplateDirective,
    PopoverComponent,
    SkeletonComponent,
    EdaiPrivilegeStatusDetailComponent,
    NoRecordsTemplateDirective,
    GridCustomFilterChecklistComponent,
    FilterMenuTemplateDirective,
    EdaiPiiStatusDetailComponent,
  ],
  templateUrl: './edai-status-grid.component.html',
  styleUrl: './edai-status-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiStatusGridComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public readonly eyeIcon = eyeIcon

  public readonly jobType = AIJobType

  private readonly dialogRef = inject(DialogRef)

  private readonly commandFacade = inject(CommandsFacade)

  private readonly breadcrumbFacade = inject(BreadcrumbFacade)

  private readonly searchResultFacade = inject(SearchResultFacade)

  private readonly aiFacade = inject(AiFacade)

  private readonly searchFacade = inject(SearchFacade)

  private readonly injector = inject(Injector)

  private readonly activatedRoute = inject(ActivatedRoute)

  public breadcrumbService = inject(BreadcrumbService)

  public readonly pagingChange = output<PageArgs>()

  private readonly pageInfo = signal<PageArgs>({ pageSize: 100, pageNumber: 1 })

  private readonly currentFormData = toSignal(
    this.searchFacade.selectSearchFormValues$
  )

  public readonly selectedJobStatus = signal<EDaiStatusModel>(undefined)

  public readonly isEdaiStatusLoading = toSignal(
    this.aiFacade.isEdaiStatusLoading$,
    { initialValue: true }
  )

  private readonly allJobStatusResponse = toSignal(
    this.aiFacade.selectEdaiStatusResults$.pipe(
      map((res) => (res?.data || {}) as EdaiStatusResponseModel)
    )
  )

  public readonly allJobStatuses = computed(() => {
    const result = this.allJobStatusResponse()
    const startIndex =
      (this.pageInfo().pageNumber - 1) * this.pageInfo().pageSize
    return (result?.statusEntries || []).map(
      (result: EDaiStatusModel, index) =>
        ({
          ...result,
          createdDate: dayjs(result.createdDate).format('MMMM DD, YYYY'),
          sn: startIndex + index + 1,
        } as EDaiStatusModel)
    )
  })

  public readonly totalRecords = computed(() => {
    const result = this.allJobStatusResponse()
    return result?.totalHitCount || 0
  })

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#fetchJobDetails()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public getStatusCssClass(status: string): Record<string, boolean> {
    status = status.toLowerCase()
    return {
      't-text-success': status === 'completed',
      't-text-error': status === 'failed',
      't-text-[#FFBB12]': status === 'in progress' || status === 'not started',
    }
  }

  public setSelectedJobStatus(jobStatus: EDaiStatusModel): void {
    this.selectedJobStatus.set(jobStatus)
  }

  public getJobType(data: EDaiStatusModel): AIJobType {
    return data.jobType
  }

  public pageChanged(args: PageArgs): void {
    this.pageInfo.update((prev) => ({
      ...prev,
      pageSize: args.pageSize,
      pageNumber: args.pageNumber,
    }))
    this.pagingChange.emit(args)
  }

  public pageSizeChanged(args: PageArgs): void {
    this.pageInfo.update((prev) => ({
      ...prev,
      pageSize: args.pageSize,
      pageNumber: 1,
    }))
    this.pagingChange.emit(args)
  }

  public calculateCompletionPercentage(
    totalItems: number,
    completedItems: number
  ): number {
    if (
      isNaN(totalItems) ||
      totalItems <= 0 ||
      isNaN(completedItems) ||
      completedItems < 0 ||
      completedItems > totalItems
    ) {
      return 0
    }

    const percentage = (completedItems / totalItems) * 100
    return parseFloat(percentage.toFixed(2))
  }

  /**
   * Performs an AI privilege/Relevance document search based on the provided status model.
   *
   * - Validates that the job type is AI privilege before proceeding.
   * - Constructs a search expression using the provided job ID and initiates the search.
   * - Handles UI updates: closes the dialog and updates breadcrumbs for navigation.
   * - Waits for both search loading and document loading phases to complete before proceeding.
   * - Ensures that only the latest values are fetched after the document loading phase is complete.
   * - Subscribes once to the relevant loading states and cleans up subscriptions on destroy.
   * - Dispatches a command to notify the viewer panel entry after the search and stores breadcrumbs.
   *
   * @see DocumentTableComponent
   * @param {EDaiStatusModel} data - Contains job type and job ID needed for the privilege search.
   * @returns {void}
   */
  public performPrivilegeOrRelevanceDocumentSearch(
    data: EDaiStatusModel
  ): void {
    // Determine if the job is either Privilege or Relevance.
    const isPrivilegeJob = data.jobType === AIJobType.Privilege
    const isRelevanceJob = data.jobType === AIJobType.Relevance
    if (!isPrivilegeJob && !isRelevanceJob) return

    // Construct the search expression.
    const searchExpression = `JobID IN (${data.jobId})`

    // Cache the current form data to avoid multiple calls.
    const currentData = this.currentFormData()

    // Trigger the search with required parameters.
    this.searchFacade.search({
      searchExpression,
      isResetBaseGuid: true,
      searchDuplicateOption: currentData?.searchDuplicateOption,
      includePC: currentData?.includePC,
    })

    this.dialogRef.close()

    const breadcrumb = {
      id: UuidGenerator.uuid,
      groupStackType: isPrivilegeJob
        ? GroupStackType.AI_PRIVILEGE_SEARCH
        : GroupStackType.AI_RELEVANCE_SEARCH,
      checked: true,
      conditionType: ConditionType.Group,
      conditions: [{ conditionSyntax: searchExpression }] as ConditionElement[],
    } as ConditionGroup

    // Wait for the search to complete:
    // 1. Wait until getIsSearchLoading$ emits false.
    // 2. Then wait until selectAreDocumentsLoading$ emits false.
    // 3. Then wait for the search result field values to load.
    // Finally, store the new breadcrumb and update the checkbox states.
    this.searchFacade.getIsSearchLoading$
      .pipe(
        filter(
          (isSearchLoading) =>
            typeof isSearchLoading === 'boolean' && !isSearchLoading
        ),
        switchMap(() =>
          this.searchResultFacade.selectAreDocumentsLoading$.pipe(
            filter(
              (isDocumentLoading) =>
                typeof isDocumentLoading === 'boolean' && !isDocumentLoading
            )
          )
        ),
        switchMap(() => this.searchResultFacade.getSearchResultFieldValues),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.breadcrumbFacade.resetBreadcrumbCurrentStates()
        this.breadcrumbFacade.storeBreadcrumbs([breadcrumb])

        this.breadcrumbService.setConditionChecked(
          GroupStackType.VIEW_SEARCH,
          false
        )
      })
  }

  #fetchJobDetails(): void {
    effect(
      () => {
        if (!this.selectedJobStatus()) return

        const { jobId, jobType } = this.selectedJobStatus()

        this.aiFacade.fetchJobStatusDetails(this.projectId, jobId, jobType)
      },
      { allowSignalWrites: true, injector: this.injector }
    )
  }

  public distinctPrimitive(fieldName: string): string[] {
    const jobStatuses = this.allJobStatuses() // Get the dynamically computed data
    return Array.from(new Set(jobStatuses.map((item) => item[fieldName])))
  }
}
