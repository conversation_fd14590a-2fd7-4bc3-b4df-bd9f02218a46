import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  Signal,
  TrackByFunction,
  viewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  ColumnComponent as GridColumnComponent,
  FilterMenuTemplateDirective,
  GridComponent,
  HeaderTemplateDirective,
  NoRecordsTemplateDirective,
  SelectionDirective,
  CheckboxColumnComponent as GridCheckboxColumn,
  FilterService,
  GridItem,
  PageChangeEvent,
  GridDataResult,
} from '@progress/kendo-angular-grid'
import {
  PopoverAnchorDirective,
  PopoverBodyTemplateDirective,
  PopoverComponent,
  TooltipDirective,
} from '@progress/kendo-angular-tooltip'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { CaseGridActionsComponent } from '../case-grid-actions/case-grid-actions.component'
import { TextBoxComponent } from '@progress/kendo-angular-inputs'
import {
  CaseDetailModel,
  CaseType,
  LaunchpadAction,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import {
  CheckboxColumnComponent as TreelistCheckboxColumn,
  ColumnComponent as TreeListColumnComponent,
  FlatBindingDirective,
  SelectableDirective,
  SelectionItem,
  TreeListComponent,
} from '@progress/kendo-angular-treelist'
import {
  CompositeFilterDescriptor,
  SortDescriptor,
} from '@progress/kendo-data-query'
import {
  LoaderComponent,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import { DebounceTimer } from '@venio/util/utilities'
import { cloneDeep } from 'lodash'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { AuthService } from '@venio/data-access/auth'
import { take } from 'rxjs'
import { FormsModule } from '@angular/forms'
import { ControlSettingService } from '@venio/data-access/control-settings'

interface CaseTypeDetail {
  caseType: CaseType
  name: string
  iconUrl: string
}

/** Local state type for client data */
type ClientType = {
  address: string
  clientId: number
  clientName: string
  contactPerson: string
  email: string
  mobileNumber: string
  phoneNumber: string
}

@Component({
  selector: 'venio-case-grid',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    GridComponent,
    SelectionDirective,
    GridColumnComponent,
    TreelistCheckboxColumn,
    HeaderTemplateDirective,
    TooltipDirective,
    FilterMenuTemplateDirective,
    CellTemplateDirective,
    CaseGridActionsComponent,
    TextBoxComponent,
    NoRecordsTemplateDirective,
    TreeListComponent,
    FlatBindingDirective,
    SelectableDirective,
    GridCheckboxColumn,
    TreeListColumnComponent,
    SkeletonComponent,
    DynamicHeightDirective,
    LoaderComponent,
    SvgLoaderDirective,
    PopoverBodyTemplateDirective,
    PopoverComponent,
    PopoverAnchorDirective,
  ],
  templateUrl: './case-grid.component.html',
  styleUrl: './case-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseGridComponent implements OnInit, OnDestroy {
  private readonly httpClient = inject(HttpClient)

  private readonly projectFacade = inject(ProjectFacade)

  private readonly authService = inject(AuthService)

  private readonly controlSettingService = inject(ControlSettingService)

  public readonly showProjectStorageSize =
    this.controlSettingService.getControlSetting.SHOW_PROJECT_SIZE_ONDEMAND

  /** Output event for the action invoked.
   * When the actions of grid e.g., produce, upload, etc. are clicked, this event is emitted with type `LaunchpadAction`
   */
  public readonly actionInvoked = output<LaunchpadAction>()

  /** An instance for grid [data] object including data and total */
  public gridView = signal<GridDataResult>(undefined)

  public sort: SortDescriptor[] = []

  /** The selected clients which will be used for fetching the case detail based on this*/
  public selectedClients: SelectionItem[] = []

  /** This page size is only for the client side grid rendering optimization, not for serve.
   * This way, the UI is not overwhelmed with too many records at once making the grid unresponsive.
   *
   * When a user scrolls up or down, the grid will add or remove records based on the pageSize and skip
   * while the actual count of data is fetched from the server.
   */
  public readonly pageSize = 45

  /** The number of records to skip when virtualization is enabled */
  public skip = 0

  private readonly caseTypeDetail: CaseTypeDetail[] = [
    {
      caseType: CaseType.VOD_SERVICE,
      name: 'Default Service',
      iconUrl: '',
    },
    {
      caseType: CaseType.PRINT_SERVICE,
      name: 'Print Service',
      iconUrl: 'assets/svg/icon-print-service.svg',
    },
    {
      caseType: CaseType.PDF_SERVICE,
      name: 'PDF Service',
      iconUrl: 'assets/svg/icon-pdf-service.svg',
    },
    {
      caseType: CaseType.VoDR_STANDARD_CONCORDANCE_SERVICE,
      name: 'Concordance Service',
      iconUrl: 'assets/svg/icon-concordance-service.svg',
    },
    {
      caseType: CaseType.VoDR_STANDARD_SUMMANTION_SERVICE,
      name: 'Summation Service',
      iconUrl: 'assets/svg/icon-summation-service.svg',
    },
    {
      caseType: CaseType.VODR_IMPORT_TO_RELATIVITY_SERVICE,
      name: 'Relativity Service',
      iconUrl: '',
    },
  ]

  /** Container ref for calculating the height */
  public readonly containerElement: Signal<
    ElementRef<HTMLDivElement> | undefined
  > = viewChild('containerElement')

  /** Computed height of the container */
  public readonly computeContainerHeight = computed(() => {
    return this.containerElement()?.nativeElement?.clientHeight
  })

  /** Static common action types */
  public readonly commonActionTypes = CommonActionTypes

  /** Signal for the selected projectIds */
  public readonly selectedProjectIds = toSignal(
    this.projectFacade.selectSelectedCaseDetail$.pipe(
      map(
        (selected: CaseDetailModel[]) =>
          selected?.map((item) => item.projectId) || []
      )
    ),
    { initialValue: [] }
  )

  /** Signal for the case detail loading state */
  public isCaseDetailLoading = toSignal(
    this.projectFacade.selectIsCaseDetailLoading$,
    { initialValue: true }
  )

  /** Signal for the case detail list */
  private readonly caseDetail = toSignal(this.projectFacade.selectCaseDetail$)

  /** Signal for the case detail list */
  private readonly loadedCases = computed<CaseDetailModel[]>(
    () => this.caseDetail()?.caseDetailEntries || []
  )

  /** Signal for the total case count */
  private readonly totalCases = computed<number>(
    () => this.caseDetail()?.totalCaseCount || 0
  )

  constructor() {
    // Effects are handle in the constructor.
    // if we need to handle outside the constructor, we need to use
    // injector and use it in lifecycle hooks.
    effect(
      () => {
        // If the case detail is loading, exit early;
        if (this.isCaseDetailLoading()) return

        this.loadGridData()
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnDestroy(): void {
    this.#resetReviewSetStates()
  }

  /** Signal for the client list */
  public readonly clientList = toSignal<ClientType[]>(
    this.httpClient
      .get<ResponseModel>(`${environment.apiUrl}client/clients`)
      .pipe(map((response) => response.data as ClientType[]))
  )

  public clientNameSearchTerm = signal<string>('')

  public readonly filteredClientList = computed(() => {
    const clients = this.clientList()
    const search = this.clientNameSearchTerm().trim().toLowerCase()

    if (!clients) return []
    if (!search) return clients

    return clients.filter((client) =>
      client.clientName.toLowerCase().includes(search)
    )
  })

  public ngOnInit(): void {
    this.#fetchBaseSettings()
  }

  /**
   * Checks if the project database is compatible with the current project database.
   * @param {string} dbVersion - The database version of the project.
   * @returns {boolean} - True if the project database is compatible with the current project database.
   */
  public isProjectDatabaseCompatible(dbVersion: string): boolean {
    const baseSetting = this.authService.loadedBaseSettings()
    return baseSetting?.venioVersion === dbVersion
  }

  /** Handles the paging event for the virtual scroll to avoid loading all the data at once
   * stressing the UI performance. Instead, we load the data in chunks.
   *
   * @see loadGridData
   * @see pageSize
   * @see skip
   *
   * @param {PageChangeEvent} event - The paging event.
   * @returns {void}
   */
  public handlePagingForVirtualScroll(event: PageChangeEvent): void {
    this.skip = event.skip
    this.loadGridData()
  }

  /** Loads the grid data based on the skip and pageSize
   * @see skip
   * @see pageSize
   * @see loadedCases
   * @returns {void}
   */
  private loadGridData(): void {
    // this.changeDetectorRef.markForCheck()
    const allCases = cloneDeep(this.loadedCases())
    this.gridView.set({
      data: allCases.slice(this.skip, this.skip + this.pageSize),
      total: this.totalCases(),
    })
  }

  /** Stores the selected case projectIds
   * @param {number[]} event - The selected case projectIds
   * @returns {void}
   * */
  public selectCase(event: number[]): void {
    this.projectFacade.storeSelectedCaseDetail(event)
  }

  /** Handles the case filter change event.
   * @param {CompositeFilterDescriptor} value - The filter descriptor.
   * @returns {void}
   **/
  public caseFilterChange(value: CompositeFilterDescriptor): void {
    // When the user clicks on the clear button, we need to clear out the selected clients
    if (!value.filters.length) {
      this.#clearOutSelectedClients()
    }

    // Whenever the filter the data, we need to reset the clientName Search Term
    this.clientNameSearchTerm.set('')

    // Reset paging to first page when filter changes
    this.skip = 0

    // Whenever the filter changes, we need to fetch the case detail, again
    // The filter payload is store in the case request info and is used to fetch the case detail
    this.projectFacade.fetchCaseDetail()
  }

  /** Handles the client selection in the grid.
   * @param {SelectionItem[]} event - The selected clients.
   * @param {FilterService} filterService - The filter service of the grid menu column.
   *
   * @remarks
   * The case request info is updated with the selected clientIds, and the case detail is fetched based on the selected clients.
   * The `filterChange` event is triggered, and the case detail is fetched based on the selected clients.
   * - Set filter object in the filterService so the filter action is enabled in column filter popup.
   * - Based on selected clients, update the clientIds in the case request info in store.
   * - Whether the user clicks on clear or filter button which triggers `caseFilterChange`, the case detail will be fetched with case request info.
   * @see caseFilterChange
   * @returns {void}
   */
  public clientItemSelected(
    event: SelectionItem[],
    filterService: FilterService
  ): void {
    // Map the clients from the selected items
    const clientNames = this.clientList().filter((item) =>
      event.some((selected) => selected.itemKey === item.clientId)
    )
    // Get the clientIds from the selected clients
    const clientIds = clientNames.map((item) => item.clientId)

    // Filter the case detail based on the selected clients in the grid
    // Once this is set, the `filterChange` event will be triggered,
    // and the case detail will be fetched based on the selected clients
    filterService.filter({
      logic: 'or',
      filters: clientNames.map((item) => ({
        field: 'clientName',
        operator: 'eq',
        value: item.clientName,
      })),
    })

    // Update the case request info with the selected clientIds
    this.projectFacade.updateCaseDetailRequestInfo({
      pageNumber: 1,
      clientIdFilter: clientIds.join(','),
    })

    // Reset paging to first page when client filter is applied
    this.skip = 0
  }

  /** Handles the case sort order change event. It updates the sort field and sort order in the case request info.
   * @param {SortDescriptor[]} sort - The sort descriptor.
   * @returns {void}
   */
  public caseSortOrderChange(sort: SortDescriptor[]): void {
    const dir = sort[0]?.dir
    this.projectFacade.updateCaseDetailRequestInfo({
      sortField: sort[0]?.field,
      sortOrder: dir === 'asc' ? 'asc' : dir === 'desc' ? 'desc' : '',
    })

    this.sort = [
      {
        field: sort[0]?.field,
        dir: dir === 'asc' ? 'asc' : dir === 'desc' ? 'desc' : undefined,
      },
    ]

    // Whenever the sort order changes, we need to fetch the case detail, again
    this.projectFacade.fetchCaseDetail()
  }

  /**
   * When column action controls are clicked, this method is called which then
   * emits another event to the parent component with selected case details and action type.
   * @param {CommonActionTypes} actionType - The action type that is clicked.
   * @param {CaseDetailModel} content - The selected case details.
   * @returns {void}
   */
  @DebounceTimer(200)
  public forwardActionControlClick(
    actionType: CommonActionTypes,
    content: CaseDetailModel
  ): void {
    this.actionInvoked.emit({
      actionType,
      content,
    })
  }

  /**
   * The grid data is rendered as virtual scroll, so when the user scrolls up or down,
   * the data are added or removed based on the pageSize and skip.
   *
   * To reflect actual changes in the UI, we need to track the data by the projectId.
   * @param {number} _ - The index of the item.
   * @param {GridItem} item - The grid item.
   * @returns {TrackByFunction<GridItem>} - The track by function.
   */
  public caseTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['projectId'] as TrackByFunction<GridItem>

  /**
   * Clears out the selected clients and updates the state with the selected client IDs.
   * @returns {void}
   */
  #clearOutSelectedClients(): void {
    this.selectedClients = []
    // If the selected client IDs are being cleared out, we need to also clear it from the state before fetching the loadedCases
    this.projectFacade.updateCaseDetailRequestInfo({
      clientIdFilter: '',
    })
  }

  /**
   * First it checks if the base settings are loaded, if not, it fetches the base settings info.
   * The settings are then reused using the computed signal throughout the application.
   * @returns {void}
   */
  #fetchBaseSettings(): void {
    if (!this.authService.loadedBaseSettings()) {
      // Let's fetch the base settings info as soon as the service is created
      // Store the base settings info in the signal and use it throughout the application
      this.authService.fetchBaseSettingsInfo().pipe(take(1)).subscribe()
    }
  }

  public getCaseTypeDetail(caseType: CaseType): CaseTypeDetail {
    return caseType === CaseType.VOD_SERVICE
      ? null
      : this.caseTypeDetail.find((c) => c.caseType === caseType)
  }

  #resetReviewSetStates(): void {
    this.projectFacade.resetProjectState([
      'caseDetailSuccessResponse',
      'caseDetailErrorResponse',
      'isCaseDetailLoading',
      'caseDetailRequestInfo',
      'selectedCaseDetail',
    ])
  }
}
