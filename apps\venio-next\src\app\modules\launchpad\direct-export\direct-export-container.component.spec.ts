import { ComponentFixture, TestBed } from '@angular/core/testing'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject, of } from 'rxjs'
import { DirectExportContainerComponent } from './direct-export-container.component'
import { DirectExportFacade, ProjectFacade } from '@venio/data-access/common'
import { provideMockStore } from '@ngrx/store/testing'
import { InjectionToken } from '@angular/core'
import { IframeMessengerService } from '@venio/data-access/iframe-messenger'
import { DialogService } from '@progress/kendo-angular-dialog'

// Define a custom InjectionToken for `Window`
const WINDOW = new InjectionToken<Window>('Window', {
  providedIn: 'root',
  factory: (): Window => window,
})

describe('DirectExportContainerComponent', () => {
  let component: DirectExportContainerComponent
  let fixture: ComponentFixture<DirectExportContainerComponent>
  let facade: jest.Mocked<DirectExportFacade>
  let mockDialogService: Partial<DialogService>

  beforeEach(async () => {
    facade = {
      selectExportFieldTemplates$: of([]),
      fetchExportTemplates: jest.fn(),
      fetchServiceTypeList: jest.fn(),
      fetchTimeZones: jest.fn(),
      selectTimeZones$: of([]),
      selectServiceTypeList$: of([]),
      selectDefaultData$: of(null),
      createServiceTypeCase: jest.fn(),
      selectServiceTypeCaseSuccess$: of(null),
      isServiceTypeCreationLoading$: of(false),
      selectIsRelativityWorkspaceLoading$: of(false),
      selectIsRelativityWorkspaceFileshareLoading$: of(false),
      selectConnctorEnvironmentSuccess$: of([
        { id: 1, name: 'Mock Environment' },
      ]),
      selectRelativityFieldTemplatesSuccess$: of([
        { id: 1, name: 'Mock Template' },
      ]),
      selectRelativityWorkspaceSuccess$: of([
        { id: 1, name: 'Mock Workspace' },
      ]),
      selectRelativityWorkspaceFileshareSuccess$: of([
        { id: 1, name: 'Mock Fileshare' },
      ]),
      selectControlNumberConflictResponse$: of(null),
      selectTimeZonesError$: of(null),
      selectExportTemplatesError$: of(null),
      selectServiceTypeListError$: of(null),
      selectConnctorEnvironmentError$: of(null),
      selectRelativityWorkspaceError$: of(null),
      selectRelativityWorkspaceFileshareError$: of(null),
      fetchExistingCaseData: jest.fn(),
      fetchConnectorEnvironments: jest.fn(),
      fetchRelativityTemplates: jest.fn(),
      fetchRelativityWorkspaces: jest.fn(),
      fetchRelativityWorkspaceFileshares: jest.fn(),
      clearControlNumberConflictResponse: jest.fn(),
      checkIfControlNumberHasConflict: jest.fn(),
      resetDirectExportState: jest.fn(),
    } as unknown as jest.Mocked<DirectExportFacade>

    const projectFacadeMock = {
      fetchCaseDetail: jest.fn(),
      selectCaseDetail$: of({ caseDetailEntries: [] }),
      selectIsCaseDetailLoading$: of(false),
    }

    const iframeMessengerServiceMock = {
      sendMessage: jest.fn(),
    }

    const queryParamMapMock: BehaviorSubject<{
      get: (key: string) => string | null
    }> = new BehaviorSubject({
      get: (key: string) => (key === 'Session' ? 'active' : null),
    })

    mockDialogService = {
      open: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        DirectExportContainerComponent,
      ],
      providers: [
        provideMockStore(), // Mock Store
        { provide: DirectExportFacade, useValue: facade },
        { provide: ProjectFacade, useValue: projectFacadeMock },
        {
          provide: ActivatedRoute,
          useValue: { queryParamMap: queryParamMapMock.asObservable() },
        }, // Mock ActivatedRoute
        { provide: WINDOW, useValue: {} }, // Mock Window object
        {
          provide: IframeMessengerService,
          useValue: iframeMessengerServiceMock,
        }, // Mock IframeMessengerService
        { provide: DialogService, useValue: mockDialogService },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DirectExportContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
