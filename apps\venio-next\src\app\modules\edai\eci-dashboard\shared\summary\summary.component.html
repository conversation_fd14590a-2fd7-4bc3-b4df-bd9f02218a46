<div class="t-flex t-flex-col lg:t-flex-row t-gap-6 t-mb-6">
  <!-- Summary Cards Row -->
  <div class="t-flex t-flex-col sm:t-flex-row t-gap-6 t-flex-1">
    <!-- Total Documents Card -->
    <div class="t-flex t-items-center t-gap-4 t-bg-white t-p-4 t-rounded-lg t-border t-border-gray-200">
      <img src="assets/eci/totalDocuments.svg" alt="Total Documents" class="t-w-12 t-h-12" />
      <div class="t-flex t-flex-col">
        <div class="t-text-sm t-text-gray-600 t-mb-1">Total Documents</div>
        <h2 class="t-text-2xl t-font-bold t-text-gray-900 t-m-0">276,897</h2>
      </div>
    </div>

    <!-- Selected Documents Card -->
    <div class="t-flex t-items-center t-gap-4 t-bg-white t-p-4 t-rounded-lg t-border t-border-gray-200">
      <img src="assets/eci/selectedDocuments.svg" alt="Selected Documents" class="t-w-12 t-h-12" />
      <div class="t-flex t-flex-col">
        <div class="t-text-sm t-text-gray-600 t-mb-1">Selected Documents</div>
        <h2 class="t-text-2xl t-font-bold t-text-gray-900 t-m-0">0</h2>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="t-flex t-gap-3">
    <button 
      kendoButton 
      size="large" 
      (click)="onCaseButtonClick()" 
      themeColor="primary"
      class="t-px-6 t-py-2">
      Case Info
    </button>
    <button 
      kendoButton 
      size="large" 
      (click)="onNarrativeButtonClick()" 
      themeColor="secondary"
      class="t-px-6 t-py-2">
      Narrative
    </button>
  </div>
</div>
