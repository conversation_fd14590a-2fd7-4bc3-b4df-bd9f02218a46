<div class="t-bg-white t-border t-border-gray-200 t-rounded-3xl t-p-7 t-flex t-items-center t-justify-between">
  <div class="t-flex t-items-center t-gap-16">
    <div class="t-flex t-items-center t-gap-3">
      <img src="assets/eci/totalDocuments.svg" alt="Total Documents" class="t-w-12 t-h-12" />
      <div class="t-flex t-flex-col">
        <div class="t-text-sm t-text-gray-600 t-mb-1">Total Documents</div>
        <h2 class="t-text-4xl t-font-bold t-text-gray-900 t-m-0 t-leading-none">139,490</h2>
      </div>
    </div>
    <div class="t-flex t-items-center t-gap-3">
      <img src="assets/eci/selectedDocuments.svg" alt="Selected Documents" class="t-w-12 t-h-12" />
      <div class="t-flex t-flex-col">
        <div class="t-text-sm t-text-gray-600 t-mb-1">Selected Documents</div>
        <h2 class="t-text-4xl t-font-bold t-text-gray-900 t-m-0 t-leading-none">23,000</h2>
      </div>
    </div>
  </div>

  <div class="t-flex t-items-center t-gap-4">
    <button kendoButton size="large" (click)="onCaseButtonClick()" themeColor="primary">
      Case Info
    </button>
    <button kendoButton size="large" (click)="onNarrativeButtonClick()" themeColor="secondary">
      Narrative
    </button>
  </div>
</div>