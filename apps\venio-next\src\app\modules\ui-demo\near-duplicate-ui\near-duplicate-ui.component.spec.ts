import { ComponentFixture, TestBed } from '@angular/core/testing'
import { NearDuplicateUiComponent } from './near-duplicate-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('NearDuplicateUiComponent', () => {
  let component: NearDuplicateUiComponent
  let fixture: ComponentFixture<NearDuplicateUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NearDuplicateUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(NearDuplicateUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
