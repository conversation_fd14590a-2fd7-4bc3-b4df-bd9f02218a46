import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseMainUiComponent } from './review-create-case-main-ui.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseMainUiComponent', () => {
  let component: ReviewCreateCaseMainUiComponent
  let fixture: ComponentFixture<ReviewCreateCaseMainUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseMainUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseMainUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
