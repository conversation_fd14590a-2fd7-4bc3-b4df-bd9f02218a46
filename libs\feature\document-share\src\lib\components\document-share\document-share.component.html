<kendo-dialog-titlebar (close)="onCancelAction()">
  @if(!documentShareDialogData?.isDocShareEdit){
  <div class="t-flex t-flex-row t-justify-center t-items-center">
    <div class="t-flex t-flex-row t-mr-[20px]">
      <div
        class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
        <img
          src="assets/svg/share-svgrepo.svg"
          alt="Share Icon"
          style="width: 12px; height: 14px" />
      </div>
      <div
        class="t-flex t-text-[#2F3080DE] t-text-[16px] t-font-medium t-relative t-top-[10px] t-ml-2">
        Document Share
      </div>
    </div>
    <div
      class="t-flex t-flex-row t-text-[#263238] t-font-medium t-text-[14px] t-relative t-top-[2px]">
      Number of selected documents&nbsp;
      <span
        data-qa="document-share-document-count"
        class="t-text-[#1EBADC] t-font-bold"
        >{{ selectedDocuments }}</span
      >
    </div>
  </div>
  }@else{
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-block">
      <span
        class="t-w-10 t-h-10 t-p-3 t-mr-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
        <span
          #shareBtn
          venioSvgLoader
          applyEffectsTo="fill"
          color="#B8B8B8"
          svgUrl="assets/svg/icon-document-share-sharing.svg"
          title="Download"
          height="1rem"
          width="1rem"></span>
      </span>

      Shared Document {{ selectedSharedDoc?.shareName }}

      <span
        class="t-inline-block t-rounded t-px-3 t-py-1 t-text-uppercase t-text-xs t-bg-[#9BD2A7] t-ml-2">
        <span class="t-text-[#0F4B1B] t-tracking-widest">
          LINK EXPIRES ON
        </span>

        <span class="t-text-[#FFFFFF] t-tracking-wide">
          {{ selectedSharedDoc?.sharedExpiryDate | date : 'MM dd yyyy' }}</span
        >
      </span>

      <span class="t-text-[#263238] t-text-base t-ml-2">
        Number of documents {{ selectedSharedDoc?.fileCountInFolder }}
      </span>
    </div>
  </div>
  }
</kendo-dialog-titlebar>
<div class="t-flex t-relative t-w-full t-flex-col">
  <div>
    <fieldset>
      <div class="t-k-form-field">
        <ng-container
          [ngComponentOutlet]="documentShareOptionsComponent | async"
          [ngComponentOutletInputs]="{
            sharedDocData: documentShareOptionsData()
          }"></ng-container>

        <ng-container
          [ngComponentOutlet]="userOptionsComponent | async"
          [ngComponentOutletInputs]="{
            sharedDocData: documentShareOptionsData()
          }"></ng-container>

        <ng-container
          [ngComponentOutlet]="documentShareInstructionComponent | async"
          [ngComponentOutletInputs]="{
            sharedDocData: documentShareOptionsData()
          }"></ng-container>
      </div>
    </fieldset>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    @if(!documentShareDialogData?.isDocShareEdit){
    <button
      kendoButton
      (click)="onSendInvitation()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      [disabled]="!selectedDocuments || (invitationInProgress$ | async)"
      data-qa="document-share-send-invite-button">
      Share
      <kendo-loader
        *ngIf="invitationInProgress$ | async"
        size="small"
        type="pulsing"
        class="t-pl-[0.5rem]"></kendo-loader>
    </button>
    <button
      kendoButton
      (click)="onCancelAction()"
      themeColor="dark"
      fillMode="outline"
      data-qa="close-document-share-cancel-button">
      Cancel
    </button>
    }@else{

    <button
      kendoButton
      (click)="reShare()"
      [disabled]="!isReharedDisabled()"
      class="v-custom-secondary-button t-uppercase"
      themeColor="secondary"
      fillMode="outline"
      data-qa="document-share-send-invite-button">
      Reshare
      <kendo-loader
        *ngIf="invitationInProgress$ | async"
        size="small"
        type="pulsing"
        class="t-pl-[0.5rem]"></kendo-loader>
    </button>
    <button
      kendoButton
      class="k-button-outline-error !t-uppercase !t-border-[#ED7428] t-text-[#ED7428] hover:!t-text-[#FFFFFF] hover:!t-bg-[#ED7428]"
      [disabled]="!isUnshareDisabled()"
      (click)="unShare()"
      fillMode="outline"
      data-qa="close-document-share-cancel-button">
      Unshare
    </button>
    }
  </div>
</kendo-dialog-actions>
