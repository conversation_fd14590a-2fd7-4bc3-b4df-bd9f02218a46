import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentShareComponent } from './document-share.component'
import { ChangeDetectorRef } from '@angular/core'
import {
  DocumentShareFacade,
  ReviewParamService,
  DocumentsFacade,
  SearchFacade,
  FieldFacade,
} from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { DialogRef, DialogModule } from '@progress/kendo-angular-dialog'
import { CommonModule } from '@angular/common'
import { provideHttpClient } from '@angular/common/http'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { ActivatedRoute, provideRouter } from '@angular/router'
import { of } from 'rxjs'

describe('DocumentShareComponent', () => {
  let component: DocumentShareComponent
  let fixture: ComponentFixture<DocumentShareComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentShareComponent],
      imports: [
        CommonModule,
        DialogModule,
        GridModule,
        InputsModule,
        LabelModule,
        FormsModule,
        ReactiveFormsModule,
        ButtonsModule,
        DropDownListModule,
        SvgLoaderDirective,
        NoopAnimationsModule,
        LoaderModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideRouter([]),
        DocumentShareFacade,
        DocumentShareFormService,
        ReviewParamService,
        NotificationService,
        VenioNotificationService,
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        ChangeDetectorRef,
        DialogRef,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '123' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentShareComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
