import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  Signal,
  signal,
  viewChild,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { EditorModule } from '@progress/kendo-angular-editor'
import {
  FormFieldComponent,
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import {
  AuthFacade,
  AuthService,
  LoginSettingsModel,
} from '@venio/data-access/auth'
import { debounceTime, filter, Subject, take, takeUntil } from 'rxjs'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { ActivatedRoute, Router } from '@angular/router'
import { HttpErrorResponse } from '@angular/common/http'
import {
  PopoverAnchorDirective,
  PopoverBodyTemplateDirective,
  PopoverComponent,
  PopoverContainerDirective,
} from '@progress/kendo-angular-tooltip'
import { CopyPreventionDirective } from '@venio/feature/shared/directives'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
interface PasswordResetModel {
  oldPassword: FormControl<string>
  newPassword: FormControl<string>
  confirmPassword: FormControl<string>
}

@Component({
  selector: 'venio-forgot-password-container',
  standalone: true,
  imports: [
    CommonModule,
    NgOptimizedImage,
    ButtonComponent,
    EditorModule,
    FormFieldComponent,
    LoaderComponent,
    ReactiveFormsModule,
    SVGIconComponent,
    TextBoxComponent,
    TextBoxSuffixTemplateDirective,
    PopoverComponent,
    PopoverAnchorDirective,
    PopoverBodyTemplateDirective,
    PopoverContainerDirective,
    CopyPreventionDirective,
  ],
  templateUrl: './forgot-password-container.component.html',
  styleUrl: './forgot-password-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ForgotPasswordContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly formBuilder = inject(FormBuilder)

  private readonly authService = inject(AuthService)

  private readonly authFacade = inject(AuthFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly notificationService = inject(NotificationService)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly router = inject(Router)

  protected readonly iconEye = eyeIcon

  protected readonly iconSlashEye = eyeSlashIcon

  public passwordResetFormGroup: FormGroup<PasswordResetModel>

  public get formControls(): PasswordResetModel {
    return this.passwordResetFormGroup?.controls
  }

  public currentActiveFocusControl = signal('')

  public readonly isCurrentPasswordVisible = signal(false)

  public readonly isNewPasswordVisible = signal(false)

  public readonly isConfirmPasswordVisible = signal(false)

  public readonly isPasswordResetLoading = signal(false)

  public readonly errorMessage = signal<string>('')

  public readonly loginSettings = signal<LoginSettingsModel>(undefined)

  public readonly getToken = computed(() => {
    // First decode the URL
    const decodedUrl = decodeURIComponent(this.router.url)

    // Fix the multiple question marks by replacing the second '?' with '&'
    const fixedUrl = decodedUrl.replace(/\?([^?]*)?\?/, '?$1&')

    // Get the query string part (everything after the '?')
    const queryString = fixedUrl.split('?')[1]

    // Parse the parameters
    const urlParams = new URLSearchParams(queryString)
    return urlParams.get('token')
  })

  public readonly isParentAppLinkRequest = computed(() => {
    const isNavbarLink =
      this.activatedRoute.snapshot.queryParams['isFromNavbarLink']

    return isNavbarLink === 'true'
  })

  public newPasswordPopoverAnchor: Signal<PopoverAnchorDirective | undefined> =
    viewChild('newPasswordPopoverAnchor')

  public newPasswordStrengthMessage = ''

  public confirmPasswordPopoverAnchor: Signal<
    PopoverAnchorDirective | undefined
  > = viewChild('confirmPasswordPopoverAnchor')

  public confirmPasswordStrengthMessage = ''

  public ngOnInit(): void {
    this.newPasswordPopoverAnchor().showOn = 'none'
    this.confirmPasswordPopoverAnchor().showOn = 'none'

    this.#selectLoginSettings()
    this.#fetchLoginSettings()
    this.#initForm()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public resetClick(): void {
    this.errorMessage.set('')

    Object.values(this.formControls).forEach((control) => {
      control.markAsTouched()
      control.markAsDirty()
    })
    if (this.passwordResetFormGroup.invalid) {
      return
    }

    if (this.isPasswordResetLoading()) {
      return
    }

    this.#changeNewPassword()
  }

  private formatMessages(messages: string[]): string {
    if (messages.length === 1) {
      return messages[0]
    } else if (messages.length === 2) {
      return `${messages[0]} and ${messages[1]}`
    }
    return `${messages.slice(0, -1).join(', ')}, and ${
      messages[messages.length - 1]
    }`
  }

  public setCurrentActiveControl(
    control: 'newPassword' | 'confirmPassword' | ''
  ): void {
    this.currentActiveFocusControl.set(control)

    // Hide or show popovers based on the active control
    if (control === 'newPassword') {
      this.confirmPasswordPopoverAnchor()?.hide()
      if (this.formControls.newPassword.invalid) {
        this.newPasswordPopoverAnchor()?.show()
      }
    } else if (control === 'confirmPassword') {
      this.newPasswordPopoverAnchor()?.hide()
      if (this.formControls.confirmPassword.invalid) {
        this.confirmPasswordPopoverAnchor()?.show()
      }
    } else {
      // If no control is active, hide both popovers
      this.newPasswordPopoverAnchor()?.hide()
      this.confirmPasswordPopoverAnchor()?.hide()
    }
  }

  private passwordStrengthValidator(
    controlType: 'newPassword' | 'confirmPassword',
    control: AbstractControl
  ): ValidationErrors | null {
    if (
      !this.newPasswordPopoverAnchor() ||
      !this.confirmPasswordPopoverAnchor()
    ) {
      return null
    }

    const password = control.value || ''
    const isActive = this.currentActiveFocusControl() === controlType

    if (!password) {
      if (controlType === 'newPassword') {
        this.newPasswordPopoverAnchor().showOn = 'none'
        this.newPasswordPopoverAnchor().hide()
        this.newPasswordStrengthMessage = ''
      } else {
        this.confirmPasswordPopoverAnchor().showOn = 'none'
        this.confirmPasswordPopoverAnchor().hide()
        this.confirmPasswordStrengthMessage = ''
      }

      return null
    }

    const loginSettings = this.loginSettings()
    const errors: ValidationErrors = {}
    const messages: string[] = []

    // Password validation logic
    if (
      loginSettings.checkEditPwdLength &&
      password.trim().length < loginSettings.spinEditPwdLength
    ) {
      errors.minlength = {
        requiredLength: loginSettings.spinEditPwdLength,
        actualLength: password.trim().length,
      }
      messages.push(
        `Password needs to be at least ${loginSettings.spinEditPwdLength} characters`
      )
    }

    if (loginSettings.checkEditUpperCase && !/[A-Z]/.test(password)) {
      errors.uppercase = true
      messages.push('one uppercase')
    }

    if (loginSettings.checkEditLowerCase && !/[a-z]/.test(password)) {
      errors.lowercase = true
      messages.push('one lowercase')
    }

    if (loginSettings.checkEditNumeric && !/\d/.test(password)) {
      errors.numeric = true
      messages.push('one numeric')
    }

    if (
      loginSettings.checkEditSpecialCharacter &&
      !/[`~!@#$%^&*()_\-+={[}\]|\\:;"'<,>.?/]/.test(password)
    ) {
      errors.specialCharacter = true
      messages.push('one special character')
    }

    if (Object.keys(errors).length > 0) {
      const formattedMessage = this.formatMessages(messages) + '.'

      if (isActive) {
        if (controlType === 'newPassword') {
          this.newPasswordPopoverAnchor().showOn = 'click'
          this.newPasswordStrengthMessage = formattedMessage
          this.newPasswordPopoverAnchor().show()
          this.confirmPasswordPopoverAnchor().hide()
        } else {
          this.confirmPasswordPopoverAnchor().showOn = 'click'
          this.confirmPasswordStrengthMessage = formattedMessage
          this.confirmPasswordPopoverAnchor().show()
          this.newPasswordPopoverAnchor().hide()
        }
      } else {
        // Hide the popover if this control is not active
        if (controlType === 'newPassword') {
          this.newPasswordPopoverAnchor().hide()
        } else {
          this.confirmPasswordPopoverAnchor().hide()
        }
      }
      return errors
    }
    // If there are no errors, hide the popover
    if (isActive) {
      if (controlType === 'newPassword') {
        this.newPasswordPopoverAnchor().showOn = 'none'
        this.newPasswordPopoverAnchor().hide()
        this.newPasswordStrengthMessage = ''
      } else {
        this.confirmPasswordPopoverAnchor().showOn = 'none'
        this.confirmPasswordPopoverAnchor().hide()
        this.confirmPasswordStrengthMessage = ''
      }
    }
    return null
  }

  private passwordMatchValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    if (!control.parent) {
      return null
    }

    const newPasswordControl = control.parent.get('newPassword')
    const confirmPasswordControl = control

    const newPassword = newPasswordControl?.value?.trim() || ''
    const confirmPassword = confirmPasswordControl.value?.trim() || ''

    if (newPasswordControl && newPasswordControl.value !== newPassword) {
      newPasswordControl.setValue(newPassword, { emitEvent: false })
    }
    if (confirmPasswordControl.value !== confirmPassword) {
      confirmPasswordControl.setValue(confirmPassword, { emitEvent: false })
    }

    if (!newPassword || !confirmPassword) {
      return null
    }

    if (newPassword !== confirmPassword) {
      return { mismatch: true }
    }
    return null
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #fetchLoginSettings(): void {
    this.authFacade.fetchLoginSettings()
  }

  #selectLoginSettings(): void {
    this.authFacade.selectLoginSettingSuccess$
      .pipe(
        filter((settings) => Boolean(settings)),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((settings) => this.loginSettings.set(settings))
  }

  #initForm(): void {
    this.passwordResetFormGroup = this.formBuilder.group<PasswordResetModel>({
      newPassword: this.formBuilder.control('', {
        validators: [
          Validators.required,
          this.passwordStrengthValidator.bind(this, 'newPassword'),
          this.passwordMatchValidator.bind(this),
        ],
      }),
      confirmPassword: this.formBuilder.control('', {
        validators: [
          Validators.required,
          this.passwordStrengthValidator.bind(this, 'confirmPassword'),
          this.passwordMatchValidator.bind(this),
        ],
      }),
      oldPassword: this.formBuilder.control(
        '',
        this.isParentAppLinkRequest() ? Validators.required : []
      ),
    })
  }

  #changeNewPassword(): void {
    this.isPasswordResetLoading.set(true)
    const { confirmPassword, oldPassword } =
      this.passwordResetFormGroup.getRawValue()
    const token = this.getToken()

    this.authService
      .resetForgotPassword(token, {
        oldPassword,
        newPassword: confirmPassword,
      })
      .pipe(debounceTime(100), take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (response) => {
          const isSuccess =
            (response?.status || '').trim().toLowerCase() === 'success'
          if (isSuccess) {
            if (!this.isParentAppLinkRequest()) {
              this.#showMessage('Password changed successfully.', {
                style: 'success',
              })
            }
            setTimeout(() => this.#notifyParentPasswordChangeSuccess(), 1500)
          }
          this.isPasswordResetLoading.set(false)
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error
          this.isPasswordResetLoading.set(false)
          this.errorMessage.set(
            error?.message || 'An error occurred. Contact support.'
          )
        },
      })
  }

  /**
   * Notify the parent window that the changing new password was successful. In the future, once the new application is
   * built, this method will be removed.
   * @returns {void}
   */
  #notifyParentPasswordChangeSuccess(): void {
    /**
     * The Parent window will navigate to the launchpad page
     * Depending on logic, this can be changed to a different page
     * If there is any additional logic or route that user needs to be navigated,
     * maybe from here or from the parent app itself can be decided.
     */
    const navigateTo = '/login'

    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          isNewPasswordChangeSuccess: true,
          navigateTo,
          isParentAppLinkRequest: this.isParentAppLinkRequest(),
        },
      } as MessageContent,
    })
  }
}
