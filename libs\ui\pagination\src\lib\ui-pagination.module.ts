import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { PaginationComponent } from './pagination/pagination.component'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  InputsModule,
  NumericTextBoxModule,
} from '@progress/kendo-angular-inputs'
import { FormsModule } from '@angular/forms'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

@NgModule({
  imports: [
    CommonModule,
    ButtonsModule,
    FormsModule,
    NumericTextBoxModule,
    DropDownsModule,
    LabelModule,
    InputsModule,
    TooltipsModule,
    SvgLoaderDirective,
    IndicatorsModule,
  ],
  declarations: [PaginationComponent],
  exports: [PaginationComponent],
})
export class UiPaginationModule {}
