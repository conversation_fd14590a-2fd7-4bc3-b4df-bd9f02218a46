{"name": "feature-delete-document", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/delete-document/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/delete-document/ng-package.json", "tailwindConfig": "libs/feature/delete-document/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/delete-document/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/delete-document/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/delete-document/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}