import { SvgIconNames } from './svg-icon-names'
import { SVGIcon } from '@progress/kendo-svg-icons'
export const svgIconContents = (): Record<
  SvgIconNames,
  Record<string, string>
> => {
  return {
    browseIcon: {
      content: `<defs>
    <filter id="Icon_ionic-md-folder" x="4.767" y="7" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feFlood flood-color="#858585" flood-opacity="0.451"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="ios-browsers-outline" transform="translate(-6 -6)">
    <path id="Path_69" data-name="Path 69" d="M6,13.5V28.779H21.274V13.5ZM20.47,27.975H6.8V14.3H20.47Z" transform="translate(0 -3.479)"/>
    <path id="Path_70" data-name="Path 70" d="M28.779,6H13.5V9.217h.8V6.8H27.975V20.475H25.563v.8h3.217Z" transform="translate(-3.479 0)"/>
  </g>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Icon_ionic-md-folder)">
    <path id="Icon_ionic-md-folder-2" data-name="Icon ionic-md-folder" d="M6.549,6.75H3.261A1.014,1.014,0,0,0,2.25,7.761v5.563a1.014,1.014,0,0,0,1.011,1.011h8.6a1.014,1.014,0,0,0,1.011-1.011V8.9a1.014,1.014,0,0,0-1.011-1.011H7.56Z" transform="translate(8.52 5.25)" fill="#00a9d3"/>
  </g>
`,
      viewBox: '0 0 22 20',
    },
    searchPanel: {
      content: `
  <path id="Icon_metro-search" data-name="Icon metro-search" d="M19.189,16.522l-4.062-3.455a1.813,1.813,0,0,0-1.232-.535,6.432,6.432,0,1,0-.72.72,1.813,1.813,0,0,0,.535,1.232l3.455,4.062a1.441,1.441,0,1,0,2.024-2.024ZM9,12.647A4.288,4.288,0,1,1,13.29,8.359,4.288,4.288,0,0,1,9,12.647Z" transform="translate(-2.571 -1.928)" fill="#2f3080"/>
    `,
      viewBox: '0 0 22 20',
    },
    documentPanel: {
      content: `
  <path id="Path_71" data-name="Path 71" d="M28.408,18.226c-2.759-5.1-7.6-8.186-12.951-8.186A14.741,14.741,0,0,0,2.523,18.226l-.229.385L2.506,19c2.759,5.1,7.6,8.186,12.951,8.186A14.707,14.707,0,0,0,28.408,19l.213-.393ZM15.457,25.512a13.022,13.022,0,0,1-11.3-6.9,13.022,13.022,0,0,1,11.3-6.9,13.075,13.075,0,0,1,11.289,6.9A13.068,13.068,0,0,1,15.457,25.512Z" transform="translate(-2.293 -10.04)" fill="#2f3080"/>
  <path id="Path_72" data-name="Path 72" d="M20.615,14.893A5.616,5.616,0,1,0,24.6,16.515a5.616,5.616,0,0,0-3.989-1.622Zm0,9.594a3.987,3.987,0,1,1,2.846-1.162,3.979,3.979,0,0,1-2.846,1.162Z" transform="translate(-7.214 -11.913)" fill="#2f3080"/>
`,
      viewBox: '1 0 25 20',
    },
    documentLocation: {
      content: ` <g id="Folder_Icon" data-name="Folder Icon" transform="translate(0.5 0.5)">
    <path id="Icon_ionic-md-folder" data-name="Icon ionic-md-folder" d="M11.78,6.75H4.492A2.249,2.249,0,0,0,2.25,8.992V21.325a2.249,2.249,0,0,0,2.242,2.242H23.551a2.249,2.249,0,0,0,2.242-2.242v-9.81a2.249,2.249,0,0,0-2.242-2.242H14.022Z" transform="translate(-2.25 -6.75)" fill="none" stroke="#2f3080" stroke-width="1"/>
  </g>`,
      viewBox: '0 0 25 20',
    },
    upload: {
      content: `<g id="Upload" transform="translate(1 1)">
    <path id="Path_120" data-name="Path 120" d="M21.65,22.5v3.811a1.906,1.906,0,0,1-1.906,1.906H6.406A1.906,1.906,0,0,1,4.5,26.311V22.5" transform="translate(-4.5 -11.066)" fill="none" stroke="#2f3080" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_121" data-name="Path 121" d="M20.028,9.264,15.264,4.5,10.5,9.264" transform="translate(-6.689 -4.5)" fill="none" stroke="#2f3080" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_122" data-name="Path 122" d="M18,4.5V15.934" transform="translate(-9.425 -4.5)" fill="none" stroke="#2f3080" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
  </g>`,
      viewBox: '0 0 25 20',
    },
    widget: {
      content: `
  <path id="Path_75" data-name="Path 75" d="M9.079,10.734h7.289v7.289H9.079ZM0,18.024V10.734H7.289v7.289ZM0,1.655H7.289V8.944H0ZM12.4.462l5.158,5.158L12.4,10.734,7.289,5.619Z" transform="translate(0 -0.462)" fill="#2f3080"/>
`,
      viewBox: '0 0 25 20',
    },
  }
}

export const svgScatterIcon: SVGIcon = {
  name: 'scatter',
  content: `<path id="Path_4" data-name="Path 4" d="M28.814,3a4.2,4.2,0,0,0-2.255,7.747l-2.743,9.6q-.128-.008-.256-.009a4.181,4.181,0,0,0-2.508.833l-5.067-4.222A4.2,4.2,0,1,0,9.064,18.61L6.09,24.559q-.2-.019-.392-.019a4.208,4.208,0,1,0,2.38.741l2.811-5.621a4.193,4.193,0,0,0,3.939-.943l4.906,4.088A4.2,4.2,0,1,0,25.816,21l2.742-9.6c.085.005.17.009.256.009a4.2,4.2,0,0,0,0-8.406ZM5.7,30.845a2.1,2.1,0,1,1,2.1-2.1A2.1,2.1,0,0,1,5.7,30.845ZM12,17.71a2.1,2.1,0,1,1,2.1-2.1,2.1,2.1,0,0,1-2.1,2.1Zm11.558,8.931a2.1,2.1,0,1,1,2.1-2.1,2.1,2.1,0,0,1-2.1,2.1ZM28.814,9.3a2.1,2.1,0,1,1,2.1-2.1A2.1,2.1,0,0,1,28.814,9.3Z" transform="translate(-1.484 -3)" fill="#979797"/>
`,
  viewBox: '0 0 50 50',
}
