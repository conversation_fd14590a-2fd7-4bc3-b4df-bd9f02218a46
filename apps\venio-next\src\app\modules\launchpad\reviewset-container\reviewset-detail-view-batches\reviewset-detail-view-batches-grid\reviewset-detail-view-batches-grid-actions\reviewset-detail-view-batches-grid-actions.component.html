<kendo-buttongroup>
  <button
    kendoButton
    #reassign
    class="v-custom-show-title-disabled-btn t-rounded-tr-none t-rounded-br-none t-w-[27px] t-h-[26px]"
    [ngClass]="{
      'hover:t-border-[#2F3080] hover:t-bg-[#2F3080]': !shouldDisableReassign()
    }"
    [disabled]="shouldDisableReassign()"
    (click)="actionButtonClick(commonActionTypes.REASSIGN)"
    size="none">
    <div
      kendoTooltip
      [title]="reassignTitle()"
      class="!t-py-[0.35rem] !t-px-[0.5rem] !t-w-full !-t-h-full">
      <span
        [parentElement]="reassign.element"
        venioSvgLoader
        hoverColor="#FFFFFF"
        color="#979797"
        svgUrl="assets/svg/icon-shuffle-communicate.svg"
        height="0.85rem"
        width="0.85rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
    </div>
  </button>
  <button
    kendoButton
    #rebatch
    class="v-custom-show-title-disabled-btn t-w-[27px] t-h-[26px]"
    [ngClass]="{
      't-rounded-tr-none t-rounded-br-none hover:t-border-[#2F3080] hover:t-bg-[#2F3080]':
        !shouldDisableRebatch()
    }"
    *ngIf="canRebatch()"
    [disabled]="shouldDisableRebatch()"
    (click)="actionButtonClick(commonActionTypes.REBATCH)"
    size="none">
    <div
      kendoTooltip
      [title]="rebatchTitle()"
      class="!t-py-[0.38rem] !t-px-[0.5rem] !t-w-full !-t-h-full">
      <span
        [parentElement]="rebatch.element"
        venioSvgLoader
        hoverColor="#FFFFFF"
        color="#979797"
        svgUrl="assets/svg/icon-fast-forward-arrow.svg"
        height="0.85rem"
        width="0.85rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
    </div>
  </button>
  <button
    kendoButton
    #delete
    class="v-custom-show-title-disabled-btn t-w-[27px] t-h-[26px]"
    [ngClass]="{
      't-rounded-tr-none t-rounded-br-none hover:t-border-error hover:t-bg-error':
        !shouldDisableDelete()
    }"
    [title]="deleteTitle()"
    [disabled]="shouldDisableDelete()"
    (click)="actionButtonClick(commonActionTypes.DELETE)"
    size="none">
    <div
      kendoTooltip
      [title]="deleteTitle()"
      class="!t-py-[0.38rem] !t-px-[0.5rem] !t-w-full !-t-h-full">
      <span
        [parentElement]="delete.element"
        venioSvgLoader
        applyEffectsTo="fill"
        hoverColor="#FFFFFF"
        color="#979797"
        svgUrl="assets/svg/Icon-material-delete.svg"
        height="0.75rem"
        width="0.8rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
    </div>
  </button>
</kendo-buttongroup>
