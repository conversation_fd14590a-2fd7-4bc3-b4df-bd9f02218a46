import { Component, inject, input, output, OnDestroy } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  InputsModule,
  RadioButtonComponent,
} from '@progress/kendo-angular-inputs'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { FormsModule } from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'
import { xIcon, checkIcon } from '@progress/kendo-svg-icons'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ProductionFacade } from '@venio/data-access/common'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-delete-production',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    IconsModule,
    ButtonModule,
    FormsModule,
    LabelModule,
    RadioButtonComponent,
    DialogModule,
    IconsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './delete-production.component.html',
  styleUrl: './delete-production.component.scss',
})
export class DeleteProductionComponent implements OnDestroy {
  public exportId = input<number>()

  public projectId = input<number>()

  public closeDeletePopup = output<boolean>()

  public icons = { closeIcon: xIcon, tickIcon: checkIcon }

  private notificationService = inject(NotificationService)

  private readonly productionFacade = inject(ProductionFacade)

  public deletionType = 'DeleteAll'

  private readonly toDestroy$ = new Subject<void>()

  public deleteProduction(): void {
    this.productionFacade.deleteProduction(
      this.projectId(),
      this.exportId(),
      this.deletionType
    )

    this.productionFacade.selectDeleteProductionSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.#showMessage('Deleted Production Successfully', {
            style: 'success',
          })
          this.closeDeletePopup.emit(true)
        }
      })

    this.productionFacade.selectDeleteProductionSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.#showMessage(res?.message, { style: 'error' })
        }
      })
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
