import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional,
  signal,
  TrackByFunction,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import {
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule, SVGIcon } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  GridItem,
  GridModule,
  PagerSettings,
  PagerType,
} from '@progress/kendo-angular-grid'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ActivatedRoute } from '@angular/router'
import {
  TranscriptState,
  TRANSCRIPT_REPORTS,
  TranscriptReport,
  TRANSCRIPT_REPORT,
  TranscriptPrintOption,
  TranscriptAnnotationData,
  TRANSCRIPT_REPORT_MENU,
  TranscriptReportMenu,
  TranscriptReportType,
} from '@venio/data-access/review'
import { Subject } from 'rxjs'
import { FormsModule } from '@angular/forms'
import {
  chevronDownIcon,
  xIcon,
  printIcon,
  copyIcon,
  fileIcon,
  caretAltDownIcon,
} from '@progress/kendo-svg-icons'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { LabelModule } from '@progress/kendo-angular-label'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { TranscriptWorkerService } from '../worker/transcript-worker-service'
import { TranscriptReportData } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-feature-transcript-report',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    SvgLoaderDirective,
    TooltipsModule,
    DropDownsModule,
    DialogsModule,
    IconsModule,
    GridModule,
    FormsModule,
    UiPaginationModule,
    LabelModule,
  ],
  templateUrl: './feature-transcript-report.component.html',
  styleUrl: './feature-transcript-report.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeatureTranscriptReportComponent implements OnInit, OnDestroy {
  public dialogTitle = 'HIGHLIGHT REPORT'

  public opened = false

  public pageable: boolean | PagerSettings = {
    type: 'numeric' as PagerType,
    position: 'top',
  }

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public downIcon: SVGIcon = chevronDownIcon

  public xIcon: SVGIcon = xIcon

  public printIcon: SVGIcon = printIcon

  public chevronDownIcon = caretAltDownIcon

  public clonedPageSize = 10

  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public currentPage = 1

  public isPrint = false

  public printOptions: TranscriptPrintOption[] = [
    {
      text: 'Print Current Page',
      svgIcon: fileIcon,
      actiontype: CommonActionTypes.PRINT,
    },
    {
      text: 'Print All Pages',
      svgIcon: copyIcon,
      actiontype: CommonActionTypes.PRINT_ALL,
    },
  ]

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public readonly filteredReports = signal<TranscriptReport[]>([])

  public reportMenuItems = signal<TranscriptReportMenu[]>(
    TRANSCRIPT_REPORT_MENU
  )

  public reportItems = signal<TranscriptReport[]>(TRANSCRIPT_REPORTS)

  public transcriptReportType = TranscriptReportType

  // Set, Initial selected report type
  public selectedReportType: TranscriptReportMenu = this.reportMenuItems[0]

  private transcriptAnnotationData: TranscriptAnnotationData

  public reportData: TranscriptReportData[] = []

  public gridData: TranscriptReportData[] = []

  public readonly isAllReportChecked = signal<boolean>(false)

  public readonly selectedReports = signal<number[]>([])

  public readonly selectedReportCount = signal<number>(0)

  public readonly isDataLoading = signal<boolean>(true)

  public readonly canShowNotes = signal<boolean>(true)

  public readonly canShowFileDetail = signal<boolean>(true)

  public transcriptReportTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['rowId'] as TrackByFunction<GridItem>

  public toDestory$: Subject<void> = new Subject<void>()

  constructor(
    private transcriptState: TranscriptState,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#getReportType()
  }

  #getReportType(): void {
    this.selectedReportType = this.reportMenuItems().find(
      (item) => item.id === this.transcriptState.selectedReportType()
    )
    if (this.selectedReportType) {
      this.filteredReports.set(this.reportItems())
      if (this.selectedReportType.id === TranscriptReportType.ALL) {
        this.selectedReports.set([])
        this.isAllReportChecked.set(true)
      } else {
        this.selectedReports.set([this.selectedReportType.id])
      }
      this.#setReportTitle()
      this.#generateReportData()
    }
  }

  #setReportTitle(): void {
    const reportName = this.#getReportLabels()
    this.dialogTitle = TRANSCRIPT_REPORT.TITLE(reportName)
  }

  #generateReportData(): void {
    const serviceworker = new TranscriptWorkerService()
    this.transcriptAnnotationData = {
      highlights: this.transcriptState.highlights(),
      notes: this.transcriptState.notes(),
      linkDocuments: this.transcriptState.documentArray(),
    }
    serviceworker
      .generateTranscriptReport(this.transcriptAnnotationData)
      .then((transcriptReport: TranscriptReportData[]) => {
        if (!transcriptReport) return
        this.reportData = transcriptReport
        this.isDataLoading.set(false)
        this.#getReportData()
      })
  }

  #getReportData(): void {
    this.gridData = this.getPageData()
    this.#showHideReportColumns()
  }

  public chevDownIconClick(reportSelection: MultiSelectComponent): void {
    reportSelection.toggle()
    reportSelection.focus()
  }

  public reportTypeChange(reportIds: number[]): void {
    this.selectedReports.set(reportIds)
    if (this.selectedReports().length) {
      this.isAllReportChecked.set(false)
    }
    this.#setReportTitle()
    this.#getReportData()
  }

  public filterReports(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredReports.set(
      this.reportItems().filter((report) =>
        report.label.toLowerCase().includes(filterValue)
      )
    )
  }

  public tagMapper = (
    items: TranscriptReport[]
  ): TranscriptReport[] | TranscriptReport[][] => {
    if (this.isAllReportChecked()) {
      // if there is a default value or all users, which in this case, should be shown as well.
      return [
        {
          id: TranscriptReportType.ALL,
          label: 'All',
          description: 'All',
        } as TranscriptReport,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public removeTag(event: RemoveTagEvent): void {
    if (event.dataItem.length === this.selectedReports().length) {
      this.allReportSelectionChange(true)
    }
  }

  public allReportSelectionChange(checked: boolean): void {
    this.isAllReportChecked.set(checked)
    if (checked) {
      this.selectedReports.set([])
      this.#getReportData()
    } else {
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.id !== -1
      )
    }
    this.#setReportTitle()
    this.#showHideReportColumns()
  }

  public handleAllReportClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allReportSelectionChange(input.checked)
  }

  public close(): void {
    this.opened = false
    this.dialogRef.close()
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.currentPage = args.pageNumber
      this.gridData = this.getPageData()
      this.changeDetectorRef.markForCheck()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    this.currentPage = args.pageNumber
    this.pageSize = args.pageSize
    this.gridData = this.getPageData()
    this.changeDetectorRef.markForCheck()
  }

  public getPageData(): TranscriptReportData[] {
    const startIndex = (this.currentPage - 1) * this.pageSize
    const endIndex = startIndex + this.pageSize
    const data = this.selectedReports()?.[0]
      ? this.reportData.filter((item) =>
          this.selectedReports().includes(item.reportId)
        )
      : this.reportData
    this.selectedReportCount.set(data.length)
    return data.slice(startIndex, endIndex)
  }

  public hidePageActionControls(): void {
    this.isPrint = true
    this.pageable = false
    this.clonedPageSize = this.pageSize
    this.pageSize = 0
    this.changeDetectorRef.markForCheck()
  }

  public print(event: object): void {
    this.hidePageActionControls()
    const printType = event['actiontype'] as CommonActionTypes
    if (printType === CommonActionTypes.PRINT_ALL) {
      this.#setPrintAllData()
    }
    setTimeout(() => {
      this.printData()
    })
  }

  public printData(): void {
    this.#setLayout()
    // Delay the print action to allow the layout to be set
    setTimeout(() => {
      window.print()
      this.#setNormalLayout()
    }, 10)
  }

  #setPrintAllData(): void {
    this.gridData = this.selectedReports()?.[0]
      ? this.reportData.filter((item) =>
          this.selectedReports().includes(item.reportId)
        )
      : this.reportData
    this.changeDetectorRef.markForCheck()
  }

  #setLayout(): void {
    document.body.classList.add('t-invisible', 't-py-[5px]', 't-m-0')
    const reportGrid = document.getElementById('reportGrid')
    if (!reportGrid) return

    const domClone = reportGrid.cloneNode(true)
    const printSection = this.#getPrintSection()

    printSection.innerHTML = ''
    printSection.appendChild(domClone)

    // Prevents the display of an empty page during printing.
    // This class will be removed after the data is printed.
    const venioRootElement = document.querySelector('venio-root')
    if (venioRootElement) {
      venioRootElement.classList.add('t-hidden')
    }
  }

  #getPrintSection(): HTMLElement {
    let printSection = document.getElementById('printSection')
    if (!printSection) {
      printSection = document.createElement('div')
      printSection.id = 'printSection'
      document.body.appendChild(printSection)
    }
    printSection.className = 't-absolute t-left-[0] t-top-[0] t-visible'
    return printSection
  }

  #setNormalLayout(): void {
    this.pageable = {
      type: 'numeric' as PagerType,
      position: 'top',
    }
    this.isPrint = false
    this.pageSize = this.clonedPageSize
    document.body.classList.remove('t-invisible', 't-py-[72px]')

    const venioRootElement = document.querySelector('venio-root')
    if (venioRootElement) {
      venioRootElement.classList.remove('t-hidden')
    }

    const printSection = document.getElementById('printSection')
    if (printSection) {
      printSection.className = ''
    }

    const divToRemove = document.getElementById('printSection')
    // Check if the element exists
    if (divToRemove) {
      // Remove the element from the DOM
      divToRemove.parentNode.removeChild(divToRemove)
    }

    this.gridData = this.getPageData()
    this.changeDetectorRef.markForCheck()
  }

  #showHideReportColumns(): void {
    const selected = this.selectedReports()

    if (selected.length === 0) {
      // If All reports are selected, show all columns
      this.canShowNotes.set(true)
      this.canShowFileDetail.set(true)
    } else {
      // Check for specific report types in the selection
      this.canShowNotes.set(selected.includes(TranscriptReportType.NOTES))
      this.canShowFileDetail.set(
        selected.includes(TranscriptReportType.DOCUMENTLINK)
      )
    }
  }

  #getReportLabels(): string {
    const reportIds: number[] = this.selectedReports()
    // Check if "All" is selected or if all options are selected
    if (
      reportIds.length === TRANSCRIPT_REPORT_MENU.length - 1 ||
      !reportIds?.[0]
    ) {
      return 'All'
    }

    const selectedReports = TRANSCRIPT_REPORT_MENU.filter(
      (item) =>
        reportIds.includes(item.id) && item.id !== TranscriptReportType.ALL
    )

    const labels = selectedReports.map((report) => report.label)

    return labels.join(' & ')
  }

  public ngOnDestroy(): void {
    this.toDestory$.next()
    this.toDestory$.complete()
  }
}
