export interface UserModel {
  userId: number
  fullName: string
  userName: string
  address: string
  email: string
  phone: string
  mobile: string
  fax: string
  globalRoleId: number
  isUserLocked: boolean
  isUserDeactivated: boolean
  failedLoginAttempts: number
  userLockValidUpto: Date
  userLockType: string
  forceUserToChangePassword: boolean
  hasDesktopAccess: boolean
  hasWebECAAccess: boolean
  hasReviewAccess: boolean
  hasTouchAccess: boolean
  isADUser: boolean
  adUserSID: string
  adUserGUID: string
  hasOnDemandAccess: boolean
  clientId: number
  clientName: string
  showNotification: boolean
  isUserApproved: boolean
  createdByName: string
  createdDate: Date
  userRole: string
  globalRoleName: string
  isUserAdmin: boolean
  userCaseAssignmentModel: string
  eulaAcceptance: boolean
  userlayoutId: number
  passwordExpired: boolean
  activeSessionId: number
  disablePasswordReset: boolean
}
