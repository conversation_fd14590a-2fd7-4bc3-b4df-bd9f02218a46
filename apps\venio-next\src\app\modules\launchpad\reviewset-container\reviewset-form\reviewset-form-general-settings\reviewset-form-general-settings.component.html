<div class="t-flex t-flex-row t-gap-4" [formGroup]="reviewSetForm()">
  <div class="t-basis-1/2">
    <div class="t-flex t-flex-col t-gap-2">
      <div class="t-flex">
        <kendo-textbox formControlName="name" placeholder="Name">
          <ng-template kendoTextBoxSuffixTemplate>
            <kendo-loader
              kendoTooltip
              title="Validating review set name. Please wait..."
              size="small"
              themeColor="secondary"
              class="t-mr-2"
              *ngIf="isReviewSetsLoading() || isReviewSetNameChecking()" />
          </ng-template>
        </kendo-textbox>
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      @if(nameInvalid()){
      <div class="t-text-error t-mb-2">Name is required</div>
      } @if(nameAlreadyExists()){
      <div class="t-text-error t-mb-2">
        Review set with same name already exists. Please try another.
      </div>
      }
      <p class="t-font-semibold t-mt-2 t-mb-1 t-text-base">Batch Name</p>
      <div class="t-flex">
        <kendo-textbox formControlName="batchPrefix" placeholder="Prefix" />
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      @if (batchPrefixInvalid()) {
      <div class="t-text-error t-mb-2">Prefix is required</div>
      }
      <div class="t-flex">
        <kendo-numerictextbox
          formControlName="batchStartNumber"
          placeholder="Start Number"
          format="#"
          [min]="1"
          [step]="1" />
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      @if (batchStartNumberInvalid()) {
      <div class="t-text-error t-mb-2">Start Number is required</div>
      }
      <div class="t-flex">
        <kendo-numerictextbox
          formControlName="batchPaddingLength"
          placeholder="Padding"
          format="#"
          [min]="1"
          [step]="1" />
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      @if (batchPadLenInvalid()) {
      <div class="t-text-error t-mb-2">Padding is required</div>
      }
      <div class="t-flex">
        <kendo-numerictextbox
          formControlName="batchSize"
          placeholder="Batch size"
          format="#"
          [min]="1"
          [step]="1" />
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      @if (batchSizeInvalid()) {
      <div class="t-text-error t-mb-2">Batch size is required</div>
      }
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-basis-2/3 t-flex t-flex-col t-gap-3">
    <p>Instruction</p>
    <kendo-textarea
      formControlName="purpose"
      class="t-w-full t-h-full"
      [rows]="10"
      resizable="none"
      rounded="medium" />
  </div>
</div>
