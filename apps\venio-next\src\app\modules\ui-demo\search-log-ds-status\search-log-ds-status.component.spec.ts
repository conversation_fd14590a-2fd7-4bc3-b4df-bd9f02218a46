import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchLogDsStatusComponent } from './search-log-ds-status.component'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchLogDsStatusComponent', () => {
  let component: SearchLogDsStatusComponent
  let fixture: ComponentFixture<SearchLogDsStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchLogDsStatusComponent, BrowserAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchLogDsStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
