<div class="t-flex t-flex-1 t-bg-[#FAFAFAB5] t-h-full t-p-5">
  <div class="t-flex t-w-[85%] t-gap-5 t-flex-1">
    <div class="t-flex t-flex-col t-flex-1 t-gap-3">
      <p>Select a Case & Service</p>
      <div class="t-flex t-flex-row t-gap-3">
        <kendo-textbox
          id="case-name"
          placeholder="Name"
          class="t-rounded v-input-shadow t-flex-1">
        </kendo-textbox>
        <kendo-dropdownlist
          id="template-select"
          textField="name"
          valueField="id"
          [defaultItem]="defaultTemplate"
          [valuePrimitive]="true"
          class="t-flex-1">
        </kendo-dropdownlist>
        <kendo-textbox
          id="client-matter-number"
          placeholder="Client Matter Number"
          class="t-rounded v-input-shadow t-flex-1">
        </kendo-textbox>

        <kendo-textbox
          id="internal-domain"
          placeholder="Internal Domain"
          class="t-rounded v-input-shadow t-flex-1">
        </kendo-textbox>

        <kendo-dropdownlist
          id="timezone-select"
          textField="name"
          valueField="id"
          [defaultItem]="defaultTimezone"
          [valuePrimitive]="true"
          class="t-flex-1">
        </kendo-dropdownlist>
      </div>
      <div class="t-flex t-flex-row">
        <kendo-dropdownlist
          id="template-select"
          textField="name"
          valueField="id"
          [defaultItem]="defaultScope"
          [valuePrimitive]="true"
          class="t-w-[604px]">
        </kendo-dropdownlist>
        <div class="t-flex-3"></div>
      </div>
      <div class="t-flex t-flex-col t-flex-1">
        <kendo-expansionpanel
          id="file-filters"
          data-qa="file-filters"
          [expanded]="currentExpandedPanel === 'file-filters'"
          (expandedChange)="onPanelToggle('file-filters', $event)"
          class="v-custom-expansion-panel"
          [animation]="true"
          subtitle="File Filters">
          <div class="t-flex t-flex-col t-gap-4">
            <venio-file-filters></venio-file-filters>
          </div>
        </kendo-expansionpanel>
        <kendo-expansionpanel
          id="image-setting"
          data-qa="image-setting"
          [expanded]="currentExpandedPanel === 'image-setting'"
          (expandedChange)="onPanelToggle('image-setting', $event)"
          class="v-custom-expansion-panel"
          [animation]="true"
          subtitle="Image Settings">
          <venio-image-settings />
        </kendo-expansionpanel>
        <kendo-expansionpanel
          id="processing"
          data-qa="processing"
          [expanded]="currentExpandedPanel === 'processing'"
          (expandedChange)="onPanelToggle('processing', $event)"
          class="v-custom-expansion-panel"
          [animation]="true"
          subtitle="Processing">
          <venio-processing />
        </kendo-expansionpanel>
        <kendo-expansionpanel
          id="general-setting"
          data-qa="general-setting"
          [expanded]="currentExpandedPanel === 'general-setting'"
          (expandedChange)="onPanelToggle('general-setting', $event)"
          class="v-custom-expansion-panel"
          [animation]="true"
          subtitle="General Settings">
          <venio-general-setting />
        </kendo-expansionpanel>
      </div>
    </div>
    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      SAVE
    </button>
  </div>
</div>
