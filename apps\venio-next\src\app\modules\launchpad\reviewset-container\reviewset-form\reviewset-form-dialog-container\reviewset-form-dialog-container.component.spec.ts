import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormDialogContainerComponent } from './reviewset-form-dialog-container.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { CaseDetailResponseModel } from '@venio/shared/models/interfaces'

describe('ReviewsetFormContainerComponent', () => {
  let component: ReviewsetFormDialogContainerComponent
  let fixture: ComponentFixture<ReviewsetFormDialogContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormDialogContainerComponent],
      providers: [
        provideNoopAnimations(),
        provideMockStore(),
        ReviewsetFormService,
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ProjectFacade,
          useValue: {
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({} as CaseDetailResponseModel),
            resetProjectState: jest.fn(),
            updateCaseDetailRequestInfo: jest.fn(),
            fetchCaseDetail: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormDialogContainerComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetActionEvent', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
