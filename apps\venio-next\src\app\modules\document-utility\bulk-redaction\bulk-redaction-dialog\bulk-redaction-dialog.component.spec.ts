import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkRedactionDialogComponent } from './bulk-redaction-dialog.component'
import { ActivatedRoute } from '@angular/router'
import {
  BulkRedactFacade,
  DocumentsFacade,
  FieldFacade,
  SearchDupOption,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { ConditionType, GroupStackType } from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('BulkRedactionDialogComponent', () => {
  let component: BulkRedactionDialogComponent
  let fixture: ComponentFixture<BulkRedactionDialogComponent>

  let breadcrumbFacade: BreadcrumbFacade
  let searchFacade: SearchFacade
  let breadcrumbService: BreadcrumbService

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkRedactionDialogComponent, NoopAnimationsModule],
      providers: [
        provideMockStore({}),
        provideHttpClient(withInterceptorsFromDi()),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        BulkRedactFacade,
        BreadcrumbFacade,
        VenioNotificationService,
        NotificationService,
        StartupsFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
        {
          provide: DialogRef,
          useValue: { close: jest.fn() },
        },
        // Provide a mock for breadcrumbService if it's not part of BreadcrumbFacade
        {
          provide: BreadcrumbService,
          useValue: {
            setConditionChecked: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkRedactionDialogComponent)
    component = fixture.componentInstance

    // Access injected services from the component if needed
    breadcrumbFacade = TestBed.inject(BreadcrumbFacade)
    searchFacade = TestBed.inject(SearchFacade)
    breadcrumbService = TestBed.inject(BreadcrumbService)

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should trigger search, update breadcrumbs, uncheck view search, and close the dialog when viewing failed documents', () => {
    const searchExpression = 'test search'

    // Override the getter for UuidGenerator.uuid
    Object.defineProperty(UuidGenerator, 'uuid', { get: () => 'fake-uuid' })

    // Use Jest's spyOn
    jest.spyOn(breadcrumbFacade, 'resetBreadcrumbCurrentStates')
    jest.spyOn(breadcrumbFacade, 'storeBreadcrumbs')
    jest.spyOn(searchFacade, 'search')
    jest.spyOn(breadcrumbService, 'setConditionChecked')
    jest.spyOn(component, 'close')

    // Call the method under test
    component.onViewFailedDocumentsClick(searchExpression)

    const expectedSearchInputParams = {
      searchExpression: searchExpression,
      includePC: false,
      projectId: '2',
      isForwardFilter: false,
      isResetBaseGuid: true,
      searchDuplicateOption: SearchDupOption.SHOW_ALL_DUPS,
    }

    expect(breadcrumbFacade.resetBreadcrumbCurrentStates).toHaveBeenCalled()
    expect(searchFacade.search).toHaveBeenCalledWith(expectedSearchInputParams)
    expect(breadcrumbFacade.storeBreadcrumbs).toHaveBeenCalledWith([
      expect.objectContaining({
        id: 'fake-uuid',
        groupStackType: GroupStackType.BULK_REDACT,
        checked: true,
        conditionType: ConditionType.Group,
        conditions: [{ conditionSyntax: searchExpression }],
      }),
    ])
    expect(breadcrumbService.setConditionChecked).toHaveBeenCalledWith(
      GroupStackType.VIEW_SEARCH,
      false
    )
    expect(component.close).toHaveBeenCalled()
  })
})
