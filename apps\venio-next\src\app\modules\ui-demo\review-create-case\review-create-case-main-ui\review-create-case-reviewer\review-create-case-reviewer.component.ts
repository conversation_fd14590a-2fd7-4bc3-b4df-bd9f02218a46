import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { Observable, of } from 'rxjs'

@Component({
  selector: 'venio-review-create-case-reviewer',
  standalone: true,
  imports: [CommonModule, TreeViewModule],
  templateUrl: './review-create-case-reviewer.component.html',
  styleUrl: './review-create-case-reviewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseReviewerComponent implements OnInit {
  public treeViewData: any[] = [
    {
      text: 'Custodian Name - A Social Media',
      id: '0',
      items: [
        { text: 'Facebook few', id: '1' },
        { text: 'Excel', id: '2' },
        { text: 'Slack Message', id: '3' },
        { text: 'Cellebrite', id: '4' },
      ],
    },
    {
      text: 'Custodian Name - Abricto',
      id: '5',
      items: [{ text: 'M001', id: '6' }],
    },
  ]

  public checkedKeys: any[] = ['1', '3', '6']

  public expandedKeys: any[] = []

  public ngOnInit(): void {
    this.expandedKeys = this.getAllNodeIds(this.treeViewData)
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  private getAllNodeIds(nodes: any[]): string[] {
    let keys: string[] = []

    for (const node of nodes) {
      if (node.id) {
        keys.push(node.id) // Add current node ID
      }

      if (node.items) {
        keys = keys.concat(this.getAllNodeIds(node.items)) // Add children IDs recursively
      }
    }

    return keys
  }
}
