import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  Ng<PERSON>one,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ReviewSetFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { GridComponent, GridModule } from '@progress/kendo-angular-grid'
import { take } from 'rxjs'
@Component({
  selector: 'venio-batches-status',
  standalone: true,
  imports: [CommonModule, GridModule],
  templateUrl: './batches-status.component.html',
  styleUrl: './batches-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BatchesStatusComponent implements AfterViewChecked {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  private readonly ngZone = inject(NgZone)

  private lastKnownWidth = 0

  @ViewChild(GridComponent)
  public grid: GridComponent

  /** Signal for the review set batch summary loading state */
  public isReviewSetBatchDashboardLoading = toSignal(
    this.reviewSetFacade.selectIsReviewSetBatchSummaryDetailLoading$,
    { initialValue: true }
  )

  /** Signal for the review set batch summary detail */
  private readonly reviewSetBatchSummaryDetail = toSignal(
    this.reviewSetFacade.selectReviewSetBatchSummaryDetailSuccess$
  )

  /** Computed property for the review set batch summary detail */
  public readonly loadedReviewSetBatchSummaryDetail = computed(() => {
    const batchSummary = this.reviewSetBatchSummaryDetail() || []
    return batchSummary.length > 0
      ? batchSummary.slice().sort((a, b) => a.userId - b.userId)
      : []
  })

  // Workaround solution for the issue where the treelist does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.grid && this.lastKnownWidth === 0) {
      const currentWidth = this.grid.wrapper.nativeElement.offsetWidth
      // Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  public onDataStateChange(): void {
    this.fitColumns()
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.grid.autoFitColumns()
      })
  }
}
