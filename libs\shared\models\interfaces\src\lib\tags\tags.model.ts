import {
  DuplicateTagPropagation,
  NearDuplicateTagPropagation,
} from './tag-propagation-options.model'

export interface TagsModel {
  treeKeyId: string
  treeParentId: string
  description: string
  sortOrder: number
  parentTagId: number
  tagIdLineage: ''
  enforceChildTagRule: boolean
  tagSecurityLevel: string
  tagGroupId: number
  groupAccess: unknown[]
  isInUse: boolean
  tagGroupName: string
  parentTagName: string
  tagGroupPermission: string
  color: string
  parentFileCount: number
  reviewSetId: number
  tagCommentRequirement: string
  commentLabel: string
  fileCount: number
  tagId: number
  tagName: string
  id: number
  parentId: number
  isSystemTag: boolean
  tagPropagationSetting?: {
    dupTagOption: DuplicateTagPropagation
    propagatePCSet: boolean
    includeEmailThread: boolean
    nddTagOption: NearDuplicateTagPropagation
  }
}

export interface TagReOrderModel {
  tagOrderXml: string
}
