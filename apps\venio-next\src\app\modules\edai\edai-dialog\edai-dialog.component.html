<kendo-dialog-titlebar (close)="closeDialog()">
  <div class="t-flex t-flex-row t-gap-2">
    <div class="t-bg-[#F2F2F2] t-w-10 t-h-10 t-flex t-items-center t-justify-center t-rounded-full">
      <span class="t-mt-0.5" venioSvgLoader color="#B8B8B8" svgUrl="assets/svg/icon-discovery-ai.svg" width="1.2rem"
        height="1.2rem">
      </span>
    </div>
    <div class="t-flex t-text-[#2F3080] t-opacity-87 t-text-base t-font-medium t-relative t-top-2.5 t-ml-2">
      eDiscovery AI
    </div>
  </div>
</kendo-dialog-titlebar>

<kendo-tabstrip (tabSelect)="tabSelectionChange($event)" [keepTabContent]="false">
  <kendo-tabstrip-tab title="AI Job" [selected]="true">
    <ng-template kendoTabContent>
      @defer{
      <venio-edai-form-container [edaiFormGroup]="edaiFormGroup" />
      } @placeholder {
      <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
        <kendo-skeleton shape="rectangle" width="80px" [height]="70"></kendo-skeleton>
        <kendo-skeleton shape="rectangle" [height]="70" width="calc(100% - 80px)"></kendo-skeleton>
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>
  <kendo-tabstrip-tab title="Status">
    <ng-template kendoTabContent>
      @defer{
      <venio-edai-status />
      } @placeholder {
      <div class="t-flex t-flex-row t-gap-5">
        <kendo-skeleton *ngFor="let n of [1, 2, 3]" shape="rectangle" width="100px" [height]="35"></kendo-skeleton>
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>
  <kendo-tabstrip-tab title="ECI Dashboard">
    <ng-template kendoTabContent>
      @defer{
      <venio-eci-dashboard />
      } @placeholder {
      <div class="t-flex t-flex-col t-gap-5 t-my-3" *ngFor="let n of [1, 2, 3]">
        <kendo-skeleton shape="rectangle" width="100%" [height]="200"></kendo-skeleton>
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    @if(!isStatusTab() && !isEciDashboardTab()) {
    <button kendoButton [disabled]="isJobFormInvalid() || isCreateJobEdaiLoading()" themeColor="secondary"
      data-qa="save-button" (click)="submit()">
      <kendo-loader *ngIf="isCreateJobEdaiLoading()" size="small"></kendo-loader>
      Submit
    </button>
    }
  </div>
</kendo-dialog-actions>