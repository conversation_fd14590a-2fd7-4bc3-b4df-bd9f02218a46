<kendo-dialog-titlebar>
  <div class="t-flex t-flex-row t-gap-2">
    <div
      class="t-bg-[#F2F2F2] t-w-10 t-h-10 t-flex t-items-center t-justify-center t-rounded-full">
      <span
        venioSvgLoader
        color="#B8B8B8"
        svgUrl="assets/svg/icon-folder-outline.svg"
        width="1.3rem"
        height="1.3rem">
      </span>
    </div>
    <div
      class="t-flex t-text-[#2F3080] t-opacity-87 t-text-base t-font-medium t-relative t-top-2.5 t-ml-2">
      Folder
    </div>
  </div>
</kendo-dialog-titlebar>

<div
  class="t-flex t-flex-col t-border t-border-t-[#ebebeb] t-border-x-0 t-border-b-0">
  <div class="t-flex t-gap-5">
    <div class="t-flex t-flex-1 t-flex-col t-w-full">
      <kendo-tabstrip
        #mainTabStrip
        class="v-tabstrip-custom t-w-full t-h-full"
        (tabSelect)="onTabSelect($event)">
        <kendo-tabstrip-tab
          title="Custom Folder"
          *venioHasUserGroupRights="UserRights.ALLOW_TO_CREATE_NEW_FOLDER">
          <ng-template kendoTabContent>
            <venio-custom-folder></venio-custom-folder>
          </ng-template>
        </kendo-tabstrip-tab>
        <ng-container *ngIf="!reviewSetState.isBatchReview()">
          <kendo-tabstrip-tab
            title="Save a Dynamic Folder"
            *venioHasUserGroupRights="
              [
                UserRights.ALLOW_TO_ADD_GLOBAL_DYNAMIC_FOLDER,
                UserRights.ALLOW_TO_ADD_LOCAL_DYNAMIC_FOLDER
              ];
              anyOfTheGivenPermission: true
            ">
            <ng-template kendoTabContent>
              <venio-dynamic-folder></venio-dynamic-folder>
            </ng-template>
          </kendo-tabstrip-tab>
        </ng-container>

        <ng-container *ngIf="!reviewSetState.isBatchReview()">
          <kendo-tabstrip-tab
            title="Auto Folder"
            *venioHasUserGroupRights="UserRights.ALLOW_TO_AUTO_FOLDER">
            <ng-template kendoTabContent>
              <venio-auto-folder></venio-auto-folder>
            </ng-template>
          </kendo-tabstrip-tab>
        </ng-container>
      </kendo-tabstrip>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="save()"
      [disabled]="
        (isCreateFolderLoading | async) && folderTabType === FolderTabType.AUTO
      "
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      <kendo-loader
        *ngIf="
          (isCreateFolderLoading | async) &&
          folderTabType === FolderTabType.AUTO
        "
        size="small">
      </kendo-loader>
      {{ saveTitle }}
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
