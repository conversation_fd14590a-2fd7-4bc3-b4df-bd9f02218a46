<div
  class="t-bg-[#FAFDF6] t-p-4 t-rounded t-flex t-items-center t-gap-10 t-my-3">
  <div class="t-flex t-flex-col t-gap-4">
    <div class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base">
      Layout
    </div>
    <div class="t-text-base t-text-gray-600">DefaultReviewLayout</div>
  </div>

  <div class="t-flex t-flex-col t-gap-4">
    <div class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base">
      Highlight Group
    </div>
    <div class="t-text-base t-text-gray-600">Default</div>
  </div>

  <div class="t-flex t-items-end t-gap-4">
    <div class="t-flex t-flex-col t-gap-1.5">
      <label class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base"
        >Search By</label
      >
      <kendo-dropdownlist
        (valueChange)="clearSearchValues($event)"
        [(ngModel)]="selectedSearchOptionType"
        [data]="searchOptionTypes()"
        class="t-w-56"></kendo-dropdownlist>
    </div>

    <div class="t-flex t-flex-col t-gap-4">
      @switch (selectedSearchOptionType()){ @case
      (reviewSetViewDetailSearchTypes.TAGS){
      <kendo-multiselecttree
        [disabled]="isReviewSetDocumentViewLoading()"
        [(ngModel)]="searchTermOrTags"
        [kendoMultiSelectTreeFlatBinding]="taggedDocumentTags()"
        [filterable]="true"
        kendoMultiSelectTreeExpandable
        [checkAll]="true"
        [tagMapper]="tagMapper"
        parentIdField="parentId"
        textField="tagName"
        valueField="id"
        class="!t-w-56"
        placeholder="Select Tags"
        [popupSettings]="{ popupClass: 'v-tags-multiselect !t-w-96' }">
        <ng-template kendoSuffixTemplate>
          <button
            kendoButton
            [svgIcon]="downIcon"
            fillMode="link"
            class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
        </ng-template>
      </kendo-multiselecttree>
      } @case (reviewSetViewDetailSearchTypes.QUERY){

      <kendo-textbox
        [(ngModel)]="searchTermOrTags"
        placeholder="Input Query"
        class="t-w-56"
        [clearButton]="true" />
      } }
    </div>

    <button
      kendoButton
      [disabled]="!isValidSearch() || isReviewSetDocumentViewLoading()"
      class="t-w-4"
      themeColor="info"
      fillMode="outline"
      #search
      (click)="searchReviewSetViewDetail()">
      <span
        venioSvgLoader
        applyEffectsTo="both"
        [parentElement]="search.element"
        hoverColor="#ffffff"
        svgUrl="assets/svg/icon-updated-search.svg"
        width="16px"
        height="16px"></span>
    </button>
  </div>
</div>
