import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { ReviewSetFacade } from '@venio/data-access/common'
import { ReviewSetEntry } from '@venio/shared/models/interfaces'
import { SummaryComponent } from './summary/summary.component'
import { BatchesStatusComponent } from './batches-status/batches-status.component'
import { TagRateComponent } from './tag-rate/tag-rate.component'
import { TagStatusComponent } from './tag-status/tag-status.component'
import { ReportsFacade } from '@venio/data-access/reports'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { ReviewerComponent } from './reviewer/reviewer.component'
import { ProgressComponent } from './progress/progress.component'

@Component({
  selector: 'venio-reviewset-batches-view-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    SkeletonModule,
    TooltipModule,
    IconsModule,
    SVGIconModule,
    ButtonsModule,
    SummaryComponent,
    TagRateComponent,
    BatchesStatusComponent,
    TagStatusComponent,
    ReviewerComponent,
    ProgressComponent,
  ],
  templateUrl: './reviewset-batches-view-dashboard.component.html',
  styleUrl: './reviewset-batches-view-dashboard.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetBatchesViewDashboardComponent
  implements OnInit, OnDestroy
{
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  private readonly reviewSetFacade = inject(ReviewSetFacade)

  private readonly reportFacade = inject(ReportsFacade)

  public icons = {
    eyeIcon: eyeIcon,
  }

  public ngOnInit(): void {
    this.#fetchReviewsetBatchSummary()
  }

  #fetchReviewsetBatchSummary(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.reviewSetFacade.fetchReviewSetBatchSummary(projectId, reviewSetId)
    this.reviewSetFacade.fetchReviewSetBatchSummaryRate(projectId, reviewSetId)
  }

  #clearReviewSetState(): void {
    this.reviewSetFacade.clearReviewSetState()
  }

  #resetDateRange(): void {
    this.reportFacade.resetReportState(['dateRange'])
  }

  public ngOnDestroy(): void {
    this.#resetDateRange()
    this.#clearReviewSetState()
  }
}
