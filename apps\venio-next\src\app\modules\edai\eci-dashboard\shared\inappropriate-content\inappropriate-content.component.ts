import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'venio-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss'
})
export class InappropriateContentComponent {
  public graph = {
    data: [
      {
        x: ['Profanity', 'Anger', 'Racism', 'Threats', 'Harassment', 'Discrimination', 'Bullying'],
        y: [350, 280, 200, 150, 120, 80, 60],
        type: 'bar',
        marker: {
          color: '#6366f1'
        }
      },
    ],
    layout: {
      autosize: true,
      title: '',
      automargin: true,
      margin: { t: 20, r: 20, b: 60, l: 60 },
      showlegend: false,
      xaxis: {
        title: '',
        tickangle: -45
      },
      yaxis: {
        title: 'Count'
      },
      plot_bgcolor: 'rgba(0,0,0,0)',
      paper_bgcolor: 'rgba(0,0,0,0)'
    }
  };
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: ['toImage', 'sendDataToCloud', 'editInChartStudio', 'zoom2d', 'select2d', 'pan2d', 'lasso2d', 'autoScale2d', 'resetScale2d']
  };
}
