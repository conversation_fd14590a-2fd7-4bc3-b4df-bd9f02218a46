import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'app-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss'
})
export class InappropriateContentComponent {
  public graph = {
    data: [
      {
        x: ['Frequency', 'Actor', 'Factum', 'Firewall', 'Illegal Activities', 'Harassment', 'Substance Abuse', 'Fraud', 'Military'],
        y: [200, 150, 120, 80, 70, 60, 50, 40, 20],
        type: 'bar',
        marker: {
          color: '#8B5CF6' // Purple color to match the design
        }
      },
    ],
    layout: {
      autosize: true,
      automargin: true,
      margin: { t: 20, r: 20, b: 60, l: 60 },
      showlegend: false,
      xaxis: {
        tickangle: -45,
        tickfont: {
          size: 12
        }
      },
      yaxis: {
        tickfont: {
          size: 12
        },
        title: {
          text: '',
          font: {
            size: 12
          }
        }
      },
      plot_bgcolor: 'white',
      paper_bgcolor: 'white'
    }
  };
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: ['toImage', 'sendDataToCloud', 'editInChartStudio', 'zoom2d', 'select2d', 'pan2d', 'lasso2d', 'autoScale2d', 'resetScale2d']
  };
}
