import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'venio-eci-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss'
})
export class EciInappropriateContentComponent {
  public graph = {
    data: [
      {
        x: ['giraffes', 'orangutans', 'monkeys', 'gorillas', 'chimpanzees'],
        y: [20, 14, 23, 34, 26],
        type: 'bar'
      },
    ],
    layout: { autosize: true, title: 'A Fancy Plot', automargin: true, margin: { t: 0, r: 0, b: 20, l: 20 }, showlegend: false }
  };
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: ['toImage', 'sendDataToCloud', 'editInChartStudio', 'zoom2d', 'select2d', 'pan2d', 'lasso2d', 'autoScale2d', 'resetScale2d']
  };
}
