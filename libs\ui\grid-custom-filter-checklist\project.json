{"name": "grid-custom-filter-checklist", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/grid-custom-filter-checklist/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/ui/grid-custom-filter-checklist/ng-package.json", "tailwindConfig": "libs/ui/grid-custom-filter-checklist/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/ui/grid-custom-filter-checklist/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/ui/grid-custom-filter-checklist/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui/grid-custom-filter-checklist/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}