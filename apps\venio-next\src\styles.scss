/* You can add app level global styles to this file, and also import other style files */
// NOTE: for kendo related overrides, please see libs/shared/styles/src/lib/_rel-global.scss

@import 'node_modules/@syncfusion/ej2-base/styles/material.css';
@import 'node_modules/@syncfusion/ej2-inputs/styles/material.css';
@import 'node_modules/@syncfusion/ej2-buttons/styles/material.css';
@import 'node_modules/@syncfusion/ej2-splitbuttons/styles/material.css';
@import 'node_modules/@syncfusion/ej2-lists/styles/material.css';
@import 'node_modules/@syncfusion/ej2-navigations/styles/material.css';
@import 'node_modules/@syncfusion/ej2-popups/styles/material.css';
@import 'node_modules/@syncfusion/ej2-dropdowns/styles/material.css';
@import 'node_modules/@syncfusion/ej2-spreadsheet/styles/material.css';
@import 'node_modules/@syncfusion/ej2-grids/styles/material.css';

/* ECI Dashboard Global Styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
main {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

/* Legacy styles - consider migrating to Tailwind t- classes */
.row {
    display: flex;
    align-items: center;
}

.column {
    display: flex;
    flex-direction: column;
}

.no-m {
    margin: 0;
}

.no-p {
    padding: 0;
}

.xl-text {
    font-size: 40px;
}

.gap-24 {
    gap: 24px;
}

.gap-48 {
    gap: 48px;
}

:root {
    --primary-01: #af26c6;
    --primary-02: #4b3af5;
    --primary-03: #121542;
    --secondary-01: #efefef;
    --secondary-02: #777777;
    --secondary-03: #231f20;
    --viz-bg: #fcfbff;
    --viz-border: #ccc9da;
    --viz-primary: #b940e5;
    --viz-secondary: #6305ff;
    --viz-accent-pink: #ff00ff;
    --viz-black: #272829;
    --viz-light-grey: #f9f9fa;
    --chart-purple: #6305ff;
    --viz-text: #0f0f0f;
    --viz-text-light: #b3b2b8;
    --viz-active: #fbf0ff;
    --viz-legend-bg: #fcfcfd;
}