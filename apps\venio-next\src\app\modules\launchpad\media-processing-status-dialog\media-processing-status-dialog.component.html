<kendo-dialog-titlebar (close)="close()">
  <div
    class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-ml-2 t-mr-1 t-flex t-items-center t-justify-center t-rounded-full">
    <img
      src="assets/svg/icon-warning-error-theme-triangle.svg"
      alt="Share Icon"
      style="width: 20px; height: 20px" />
  </div>
  <div>
    {{ dialogTitle() }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col t-p-[20px]">
  <div class="t-flex t-gap-5">
    <div class="t-flex t-flex-1 t-flex-col t-w-full">
      <div class="t-bg-[#f9d5bd] t-px-4 t-text-sm t-rounded t-p-3 t-mb-4">
        Following media included in search are not completely indexed. Searching
        on fulltext &
        <span class="t-font-bold t-underline t-text-[#ED7425]"
          >indexed fields</span
        >
        may give inconsistent search results.
      </div>
      <div>
        <kendo-grid
          class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
          [kendoGridBinding]="unIndexMediaList()"
          [loading]="isMediaStatusLoading()"
          filterable="menu">
          <div
            *kendoGridNoRecordsTemplate
            class="t-flex t-justify-center t-items-center">
            @if(!isMediaStatusLoading()) {
            <span>No records found</span>
            }
          </div>
          <kendo-grid-column
            field="sn"
            [width]="50"
            title="#"
            [filterable]="false"
            headerClass="t-text-primary">
            <ng-template kendoGridHeaderTemplate let-column>
              <span kendoTooltip class="t-text-ellipsis t-overflow-hidden"
                >#</span
              >
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="custodianName"
            [filterable]="false"
            title="Custodian"
            headerClass="t-text-primary">
          </kendo-grid-column>
          <kendo-grid-column
            field="mediaName"
            [filterable]="false"
            title="Media"
            headerClass="t-text-primary">
          </kendo-grid-column>
          <kendo-grid-column
            field="mediaStatus"
            [filterable]="false"
            title="Status"
            headerClass="t-text-primary">
          </kendo-grid-column>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="proceed()"
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      OK
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
