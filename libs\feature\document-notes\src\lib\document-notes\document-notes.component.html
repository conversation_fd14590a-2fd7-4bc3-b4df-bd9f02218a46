<div
  class="t-flex t-w-full t-min-h-[260px] t-flex-col t-gap-1 t-relative t-overflow-y-hidden t-h-max t-min-h-full">
  <div
    class="t-flex t-sticky t-justify-end t-mt-2 t-top-0 t-bg-white t-z-[1] t-drop-shadow-sm t-pr-2">
    <button
      kendoButton
      #fixedHeader
      kendoTooltip
      position="left"
      *ngIf="hasAnyRightToAddNotes"
      [title]="commonActionTypes.NEW_NOTE | titlecase"
      class="!t-p-[0.2rem]"
      (click)="onDocumentNoteAction(commonActionTypes.NEW_NOTE, null)"
      fillMode="clear"
      size="none">
      <span
        [parentElement]="fixedHeader.element"
        venioSvgLoader
        [hoverColor]="'#FFBB12'"
        color="#979797"
        [svgUrl]="'assets/svg/icon-new-note-ui.svg'"
        height="1.2rem"
        width="1.2rem">
        <!-- loader if required -->
        <!-- <kendo-loader size="small"></kendo-loader> -->
      </span>
    </button>
  </div>

  <div class="t-block t-w-full t-min-h-[260px]">
    <kendo-treeview
      [nodes]="documentNotes"
      [children]="getChildren"
      textField="text"
      kendoTreeViewExpandable
      [hasChildren]="hasChildren"
      class="v-note-thread-tree t-w-full t-min-h-[260px] v-hide-scrollbar"
      (expand)="handleScroll($event)"
      (collapse)="handleScroll($event)">
      <ng-template kendoTreeViewNodeTemplate let-dataItem>
        <div
          class="t-flex t-border t-border-1 t-border-[#dbdbdb] t-flex-col t-p-2 t-rounded-lg t-gap2 t-w-full">
          <div class="t-flex t-gap-2 t-items-center t-justify-between">
            <div class="t-flex t-gap-2 t-items-center t-py-1">
              <span
                class="t-font-semibold t-max-w-[10.5rem] t-truncate"
                kendoTooltip
                [title]="dataItem.commentedBy"
                >{{ dataItem.commentedBy }}</span
              >
              <span class="t-text-xs">{{
                dataItem.timeStamp | date : 'short'
              }}</span>
            </div>
            <div class="t-flex t-absolute t-right-3 t-top-4.5">
              <kendo-buttongroup kendoTooltip position="left">
                @for (icon of docNotesSvgIcons; track icon.actionType) {
                <button
                  kendoButton
                  #actionGrid
                  [title]="icon.actionType | titlecase"
                  *ngIf="shouldShowButton(icon, dataItem)"
                  class="!t-p-[0.2rem]"
                  (click)="onDocumentNoteAction(icon.actionType, dataItem)"
                  fillMode="clear"
                  size="none">
                  <span
                    [parentElement]="actionGrid.element"
                    venioSvgLoader
                    [hoverColor]="
                      icon.actionType === commonActionTypes.DELETE
                        ? '#ED7425'
                        : '#FFBB12'
                    "
                    color="#979797"
                    [svgUrl]="icon.iconPath"
                    [height]="
                      icon.actionType === commonActionTypes.DELETE
                        ? '0.9rem'
                        : icon.actionType === commonActionTypes.EDIT
                        ? '1rem'
                        : '1.2rem'
                    "
                    [width]="
                      icon.actionType === commonActionTypes.DELETE
                        ? '0.9rem'
                        : icon.actionType === commonActionTypes.EDIT
                        ? '1rem'
                        : '1.2rem'
                    ">
                    <!-- loader if required -->
                    <!-- <kendo-loader size="small"></kendo-loader> -->
                  </span>
                </button>
                }
              </kendo-buttongroup>
            </div>
          </div>
          <p class="t-text-wrap">
            {{ dataItem.comment }}
          </p>
          <!-- Insert expand/collapse icons or custom logic if needed -->
        </div>
        <!-- Nested replies will automatically be taken care of by the TreeView -->
      </ng-template>
    </kendo-treeview>

    <div
      *ngIf="!documentNotes?.length"
      class="t-grid t-text-center t-place-content-center t-text-[#979797] t-min-h-[125px] t-absolute t-top-16 t-w-full">
      No records found.
      <div class="t-flex t-text-sm t-mt-1">
        (Click on the + icon to add a new note.)
      </div>
    </div>
  </div>

  <div class="t-relative" #addEditNotePosition></div>

  <div
    class="t-flex t-absolute t-z-[2] t-bg-white t-flex-col t-p-4 t-gap-3 t-w-full t-bottom-0 t-border t-border-[#cccccc] t-border-r-0 t-border-l-0 t-border-t-1 t-border-b-0"
    *ngIf="showNewNotePanel()">
    <div class="t-flex t-flex-col t-gap-2" [formGroup]="documentNoteForm">
      <!-- Change the placeholder logic for the textarea in the development as Edit note won't be required however we need to show a Add new note for the new note-->
      <kendo-textarea
        [rows]="3"
        resizable="vertical"
        formControlName="comment"
        [placeholder]="'Add a new note...'"></kendo-textarea>
      <span class="t-text-error">{{ errorMessages()?.comment }}</span>

      <kendo-dropdownlist
        [hidden]="!showAccessibilityOption()"
        formControlName="accessibility"
        [valuePrimitive]="true"
        [data]="documentNoteAccessibility"
        textField="text"
        valueField="value">
      </kendo-dropdownlist>
      <span class="t-text-error">{{ errorMessages()?.accessibility }}</span>
      <input type="hidden" formControlName="isReply" />
    </div>

    <div class="t-flex t-justify-end t-gap-3">
      <kendo-buttongroup class="t-flex t-gap-3">
        <button
          kendoButton
          fillMode="clear"
          kendoTooltip
          position="left"
          title="Save"
          [toggleable]="true"
          [svgIcon]="checkIcon"
          (click)="addNewNote()"
          class="t-bg-[#BAE36E3D] t-text-[#88B13F]"></button>
        <button
          kendoButton
          kendoTooltip
          position="left"
          title="Cancel"
          fillMode="clear"
          [toggleable]="true"
          [svgIcon]="xIcon"
          class="t-bg-[#FF5F521A] t-text-[#EC3737]"
          (click)="
            onDocumentNoteAction(commonActionTypes.CANCEL, null)
          "></button>
      </kendo-buttongroup>
    </div>
  </div>
</div>
