<div class="content">
  <div class="t-flex t-mt-4 t-w-full t-flex-col">
    <div
      *ngIf="dynamicFolderErrorMessage"
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3">
      <span>{{ dynamicFolderErrorMessage }}</span>
      <button
        type="button"
        (click)="dynamicFolderErrorMessage = null"
        class="t-border-0 t-cursor-pointer t-text-error">
        close
      </button>
    </div>
    <div class="t-flex t-flex-col t-w-full">
      <div
        class="t-flex t-flex-wrap t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
        <ng-container [formGroup]="dynamicFolderFormGroup">
          <div class="t-flex t-w-[32.5%] t-flex-col t-gap-1">
            <kendo-label
              for="DynamicName"
              class="t-text-xs t-uppercase t-tracking-widest">
              Name <span class="t-text-error">*</span>
            </kendo-label>

            <kendo-textbox
              placeholder="Enter dynamic folder name"
              #DynamicName
              formControlName="dynamicFolderName"></kendo-textbox>
          </div>

          <div class="t-flex t-gap-1 t-flex-col">
            <kendo-label
              for="dynamicNote"
              class="t-text-xs t-uppercase t-tracking-widest">
              Note
            </kendo-label>
            <kendo-textarea
              #dynamicNote
              placeholder="Enter dynamic folder note"
              [rows]="3"
              resizable="vertical"
              formControlName="dynamicFolderNote"></kendo-textarea>
          </div>
        </ng-container>
      </div>

      <div class="t-flex t-w-full t-flex-col t-mt-3">
        <kendo-expansionpanel
          class="v-custom-expansion-panel"
          [expanded]="true">
          <ng-template kendoExpansionPanelTitleDirective>
            <div class="t-text-[#1DBADC] t-text-sm">
              <span>Advance Options</span>
            </div>
          </ng-template>

          <div class="t-flex t-w-full t-flex-col t-gap-3 v-custom-grey-bg">
            <div class="t-flex t-w-[32.5%] t-gap-1 t-flex-col">
              <kendo-label
                for="advanceContainer"
                class="t-text-xs t-uppercase t-tracking-widest">
                Container <span class="t-text-error">*</span>
              </kendo-label>
              <kendo-dropdownlist
                #advanceContainer
                [data]="containers"
                textField="lineage"
                valueField="id"
                [(value)]="selectedContainer">
              </kendo-dropdownlist>
            </div>
          </div>

          <div class="t-flex t-mt-4 t-flex-col t-gap-3">
            <div class="t-flex t-flex-col t-flex-1 t-gap-1">
              <div class="t-flex t-mt-4 t-pb-3 t-text-primary t-font-semibold">
                Security
              </div>

              <div class="t-flex t-w-full">
                <kendo-grid
                  class="!t-w-full"
                  [resizable]="true"
                  [data]="userRolePermissions">
                  <kendo-grid-column
                    field="globalRoleName"
                    headerClass="t-text-primary"
                    title="Role"
                    [width]="300">
                  </kendo-grid-column>
                  <kendo-grid-column
                    field="permission"
                    headerClass="t-text-primary"
                    title="Permission">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <kendo-dropdownlist
                        class="t-w-1/2"
                        [data]="rolePermissionData"
                        [valuePrimitive]="true"
                        textField="text"
                        valueField="value"
                        [(ngModel)]="dataItem.permission">
                      </kendo-dropdownlist>
                    </ng-template>
                  </kendo-grid-column>
                </kendo-grid>
              </div>
            </div>
          </div>
        </kendo-expansionpanel>
      </div>
    </div>
  </div>
</div>
