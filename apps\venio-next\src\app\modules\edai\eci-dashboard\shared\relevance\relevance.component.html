<div id="relevance-chart-container" class="t-bg-white t-border t-flex t-gap-6 t-p-7 t-rounded-3xl t-flex-col">
  <venio-title-and-download [title]="'Relevance'" [iconButton]="'your-css-classes'"></venio-title-and-download>
  <div class="t-relative">
    <venio-center-text></venio-center-text>
    <plotly-plot id="relevance-chart" [data]="graph.data" [layout]="graph.layout" [config]="config"></plotly-plot>
  </div>
  <div class="t-w-full t-flex t-flex-row t-justify-start t-flex-wrap t-gap-1">
    <ng-container *ngFor="let legend of labels; let i = index">
      <div class="t-flex t-items-center t-flex-row t-mb-2 t-mr-2">
        <div class="t-h-4 t-w-4 t-rounded-sm t-mr-1" [style]="{ background: getColor(i) }"></div>
        <p class="t-font-medium t-text-xs">{{ legend }}</p>
      </div>
    </ng-container>
  </div>
</div>