<div class="t-bg-white t-border t-border-gray-200 t-rounded-lg t-p-6 t-h-full">
  <!-- Header -->
  <div class="t-flex t-justify-between t-items-center t-mb-4">
    <h3 class="t-text-lg t-font-semibold t-text-gray-900">Relevance</h3>
    <button kendoButton fillMode="flat" size="small" class="t-text-gray-500">
      <span class="t-text-sm">⋯</span>
    </button>
  </div>

  <!-- Chart Container -->
  <div class="t-relative t-flex t-justify-center t-items-center t-mb-4" style="height: 300px;">
    <!-- Center Text -->
    <div class="t-absolute t-z-10 t-flex t-flex-col t-items-center t-justify-center">
      <div class="t-text-2xl t-font-bold t-text-gray-900">82,112</div>
      <div class="t-text-sm t-text-gray-600">View Details</div>
      <button kendoButton fillMode="flat" size="small" class="t-mt-1 t-text-purple-600">
        <span class="t-text-xs">↗</span>
      </button>
    </div>

    <!-- Plotly Chart -->
    <plotly-plot id="relevance-chart" [data]="graph.data" [layout]="graph.layout" [config]="config"
      (plotlyClick)="onChartClick($event)" class="t-w-full t-h-full">
    </plotly-plot>
  </div>

  <!-- Legend -->
  <div class="t-grid t-grid-cols-2 t-gap-2">
    <div *ngFor="let legend of labels; let i = index" class="t-flex t-items-center t-gap-2">
      <div class="t-w-3 t-h-3 t-rounded-sm" [style.background-color]="getColor(i)">
      </div>
      <span class="t-text-xs t-text-gray-700">{{ legend }}</span>
    </div>
  </div>
</div>