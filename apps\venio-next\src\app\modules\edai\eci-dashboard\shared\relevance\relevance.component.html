<div id="relevance-chart-container" class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm">
  <app-title-and-download [title]="'Relevance'"></app-title-and-download>
  <div class="t-relative t-h-[350px] t-overflow-hidden">
    <app-center-text></app-center-text>
    <div class="t-w-full t-h-full">
      <plotly-plot id="relevance-chart" [data]="graph.data" [layout]="graph.layout" [config]="config"
        class="t-w-full t-h-full"></plotly-plot>
    </div>
  </div>
  <div class="t-w-full t-flex t-flex-row t-justify-start t-flex-wrap t-gap-2 t-min-h-[60px] t-items-center">
    <ng-container *ngFor="let legend of labels; let i = index">
      <div class="t-flex t-items-center t-flex-row t-mb-2 t-mr-3">
        <div class="t-h-3 t-w-3 t-rounded-sm t-mr-2" [style]="{ background: getColor(i) }"></div>
        <p class="t-font-medium t-text-xs t-text-gray-700">{{ legend }}</p>
      </div>
    </ng-container>
  </div>
</div>