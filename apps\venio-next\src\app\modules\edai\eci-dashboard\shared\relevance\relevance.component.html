<div id="relevance-chart-container"
  class="t-bg-white t-border t-border-gray-200 t-rounded-3xl t-p-7 t-flex t-flex-col t-gap-6">
  <app-title-and-download [title]="'Relevance'" [iconButton]="'your-css-classes'"></app-title-and-download>
  <div class="t-relative">
    <app-center-text></app-center-text>
    <plotly-plot id="relevance-chart" [data]="graph.data" [layout]="graph.layout" [config]="config"></plotly-plot>
  </div>
  <div class="t-w-full t-flex t-flex-row t-justify-start t-flex-wrap t-gap-1">
    <ng-container *ngFor="let legend of labels; let i = index">
      <div class="t-flex t-items-center t-flex-row t-mb-2 t-mr-2">
        <div class="t-h-4 t-w-4 t-rounded-sm t-mr-1" [style]="{ background: getColor(i) }"></div>
        <p class="t-font-medium t-text-xs">{{ legend }}</p>
      </div>
    </ng-container>
  </div>
</div>