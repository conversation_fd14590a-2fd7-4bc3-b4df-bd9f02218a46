<div id="relevance-chart-container" class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm">
  <app-title-and-download [title]="'Relevance'"></app-title-and-download>
  <div class="t-w-full t-flex t-flex-col t-gap-4">
    <div class="t-relative t-h-[350px] t-w-full t-flex t-justify-center t-items-center">
      <app-center-text></app-center-text>
      <plotly-plot id="relevance-chart" [data]="graph.data" [layout]="graph.layout" [config]="config"
        class="t-w-full t-h-full">
      </plotly-plot>
    </div>
    <div class="t-w-full t-flex t-flex-wrap t-gap-x-6 t-gap-y-2 t-justify-center t-items-center">
      <ng-container *ngFor="let legend of labels; let i = index">
        <div class="t-flex t-items-center t-flex-row t-gap-2">
          <div class="t-h-3 t-w-3 t-rounded-sm t-flex-shrink-0" [style]="{ background: getColor(i) }"></div>
          <p class="t-font-medium t-text-xs t-text-gray-700 t-whitespace-nowrap">{{ legend }}</p>
        </div>
      </ng-container>
    </div>
  </div>
</div>