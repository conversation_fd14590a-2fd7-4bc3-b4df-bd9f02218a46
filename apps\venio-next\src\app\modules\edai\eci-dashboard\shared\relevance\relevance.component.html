<div id="relevance-chart-container" class="bg-white border flex gap-6 p-7 rounded-3xl flex-col">
  <app-title-and-download
    [title]="'Relevance'"
    [iconButton]="'your-css-classes'"
  ></app-title-and-download>
  <div class="relative">
    <app-center-text></app-center-text>
    <plotly-plot
      id="relevance-chart"
      [data]="graph.data"
      [layout]="graph.layout"
      [config]="config"
    ></plotly-plot>
  </div>
  <div class="w-full flex flex-row justify-start flex-wrap gap-1">
    <ng-container *ngFor="let legend of labels; let i = index">
      <div class="flex items-center flex-row mb-2 mr-2">
        <div class="h-4 w-4 rounded-sm mr-1" [style]="{ background: getColor(i) }"></div>
        <p class="font-interphases font-medium text-xs">{{ legend }}</p>
      </div>
    </ng-container>
  </div>
</div>
