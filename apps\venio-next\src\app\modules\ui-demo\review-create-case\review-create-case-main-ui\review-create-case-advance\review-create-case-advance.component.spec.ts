import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseAdvanceComponent } from './review-create-case-advance.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseAdvanceComponent', () => {
  let component: ReviewCreateCaseAdvanceComponent
  let fixture: ComponentFixture<ReviewCreateCaseAdvanceComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseAdvanceComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseAdvanceComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
