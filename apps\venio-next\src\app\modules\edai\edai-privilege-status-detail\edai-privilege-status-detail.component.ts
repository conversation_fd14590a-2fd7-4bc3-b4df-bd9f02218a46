import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ColumnComponent,
  GridComponent,
  HeaderTemplateDirective,
} from '@progress/kendo-angular-grid'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { AiFacade } from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-edai-privilege-status-detail',
  standalone: true,
  imports: [
    CommonModule,
    ColumnComponent,
    GridComponent,
    TooltipDirective,
    HeaderTemplateDirective,
  ],
  templateUrl: './edai-privilege-status-detail.component.html',
  styleUrl: './edai-privilege-status-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiPrivilegeStatusDetailComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private notificationService = inject(NotificationService)

  public readonly statusDetailData = signal([])

  public readonly dynamicPrivilegeColumns = signal<string[]>([])

  public ngOnInit(): void {
    this.#selectStatusJobDetail()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectStatusJobDetail(): void {
    combineLatest([
      this.aiFacade.selectEdaiJobStatusDetailsSuccess$,
      this.aiFacade.selectEdaiJobStatusDetailsError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message
        const results = success?.data
        const privTypeDocCount = results?.privilegeTypeDocCount || {}

        const privTypeDocCountCols = Object.keys(privTypeDocCount).map((key) =>
          key.replace(/[-|_]/g, ' ')
        )

        const merged = {
          ...results,
          ...Object.keys(privTypeDocCount).reduce((o, originalKey, index) => {
            o[privTypeDocCountCols[index]] = privTypeDocCount[originalKey]
            return o
          }, {}),
        }

        this.statusDetailData.set([merged])

        this.dynamicPrivilegeColumns.set(privTypeDocCountCols)

        // Only display the message if it is an error
        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
