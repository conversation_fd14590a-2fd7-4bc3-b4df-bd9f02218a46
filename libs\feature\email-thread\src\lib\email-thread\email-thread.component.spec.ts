import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EmailThreadComponent } from './email-thread.component'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('EmailThreadComponent', () => {
  let component: EmailThreadComponent
  let fixture: ComponentFixture<EmailThreadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        EmailThreadComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EmailThreadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
