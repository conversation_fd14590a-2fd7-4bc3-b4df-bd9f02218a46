import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingFilterContainerComponent } from './tag-coding-filter-container.component'
import { DocumentTagFacade } from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import { DialogModule } from '@progress/kendo-angular-dialog'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'

describe('TagCodingFilterContainerComponent', () => {
  let component: TagCodingFilterContainerComponent
  let fixture: ComponentFixture<TagCodingFilterContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TagCodingFilterContainerComponent,
        DialogModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        DocumentTagFacade,
        provideMockStore({}),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagCodingFilterContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
