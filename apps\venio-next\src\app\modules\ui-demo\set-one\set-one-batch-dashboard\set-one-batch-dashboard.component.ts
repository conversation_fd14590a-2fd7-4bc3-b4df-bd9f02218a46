import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  AxisLabelContentArgs,
  ChartsModule,
  SeriesLabelsContentArgs,
} from '@progress/kendo-angular-charts'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule } from '@progress/kendo-angular-grid'

@Component({
  selector: 'venio-set-one-batch-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    ChartsModule,
    SvgLoaderDirective,
    SVGIconModule,
    ButtonsModule,
    GridModule,
  ],
  templateUrl: './set-one-batch-dashboard.component.html',
  styleUrl: './set-one-batch-dashboard.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetOneBatchDashboardComponent {
  public graphId?: number

  public theme = 'default'

  public chartData: any[] = [
    { value: 1, color: '#ED7425' },
    { value: 1, color: '#718792' },
    { value: 5, color: '#FFB300' },
    { value: 1, color: '#9BD2A7' },
  ]

  public chartDataMo = [
    { date: 'Jan 1', value: 0, color: '#8E8E93' },
    { date: 'Jan 2', value: 1600, color: '#A3D9A5' },
    { date: 'Jan 3', value: 2800, color: '#F4A261' },
    { date: 'Jan 4', value: 100, color: '#6B7280' },
    { date: 'Jan 5', value: 150, color: '#6B7280' },
    { date: 'Jan 6', value: 900, color: '#E76F51' },
    { date: 'Jan 7', value: 0, color: '#8E8E93' },
    { date: 'Jan 8', value: 0, color: '#8E8E93' },
  ]

  public categories = this.chartDataMo.map((item) => item.date)

  public formatLabel(e: AxisLabelContentArgs): string {
    return e.value >= 1000 ? `${e.value / 1000}K` : `${e.value}`
  }

  public pieData: any[] = [
    { category: 'In Progress', value: 44315, color: '#FFB43C' },
    { category: 'Completed', value: 45685, color: '#BEF8BA' },
    { category: 'Not Started', value: 20000, color: '#718792' },
  ]

  public labelContent(args: SeriesLabelsContentArgs): string {
    return `${args.dataItem.value}`
  }

  // stack

  public clothingData = [8000, 7000, 5000, 4000, 3000]

  public equipmentData = [3000, 4000, 3000, 2000, 1500]

  public accessoriesData = [1000, 1200, 800, 1000, 700]

  // Colors for each series
  public clothingColor = '#ED7428' // Blue

  public equipmentColor = '#FEB43C' // Golden

  public accessoriesColor = '#9BD2A7'

  public icons = {
    eyeIcon: eyeIcon,
  }

  public gridData = [
    { tag: 'Responsive', johnDoe: '20%', melissa: '89%' },
    { tag: 'Nonresponsive', johnDoe: '78%', melissa: '56%' },
  ]
}
