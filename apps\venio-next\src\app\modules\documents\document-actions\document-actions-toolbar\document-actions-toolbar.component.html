<div
  class="t-p-2 t-flex t-justify-between t-items-center t-border-b-[1px] t-border-b-[#ebebeb] t-relative">
  <div class="t-w-1/3 t-flex t-items-center t-gap-2 t-font-normal">
    <!--    NOTE: commented elements will be reused in future-->
    <!--    @for (item of iconCollection(); track item) {-->
    <!--    <button
    kendoButton-->
    <!--      fillMode="flat"-->
    <!--      rounded="medium"-->
    <!--      size="none"-->
    <!--      (click)="actionClicked(item)">-->
    <!--      <div class="t-p-1">-->
    <!--        <kendo-svg-icon size="large" [icon]="item" />-->
    <!--      </div>-->
    <!--    </button>-->
    <!--    }-->
    <ng-container
      *ngComponentOutlet="documentDesignerDropdownLazyComp | async" />
    <!--    <kendo-dropdownlist-->
    <!--      iconClass="k-i-arrow-chevron-down"-->
    <!--      class="!t-w-[15rem]"-->
    <!--      [showStickyHeader]="true"-->
    <!--      [data]="[-->
    <!--        { name: 'No Related Items' },-->
    <!--        { name: 'Including Family' },-->
    <!--        { name: 'New Relation' }-->
    <!--      ]"-->
    <!--      textField="name"-->
    <!--      valueField="name"-->
    <!--      [value]="{ name: 'No Related Items' }">-->
    <!--      <ng-template kendoDropDownListValueTemplate let-dataItem>-->
    <!--        <kendo-svg-icon [icon]="svgPlusIcon"></kendo-svg-icon>-->
    <!--        {{ dataItem.name }}-->
    <!--      </ng-template>-->
    <!--    </kendo-dropdownlist>-->
    <!--    <kendo-dropdownlist-->
    <!--      iconClass="k-i-arrow-chevron-down"-->
    <!--      class="!t-w-[15rem]"-->
    <!--      [showStickyHeader]="true"-->
    <!--      [data]="[-->
    <!--        { name: 'Layout 01' },-->
    <!--        { name: 'layout 02' },-->
    <!--        { name: 'Layout 03' }-->
    <!--      ]"-->
    <!--      textField="name"-->
    <!--      valueField="name"-->
    <!--      [value]="{ name: 'Layout 01' }">-->
    <!--    </kendo-dropdownlist>-->
  </div>
  <div
    class="t-w-1/3 t-gap-2 t-flex t-justify-center"
    *ngIf="!reviewSetState.isBatchReview()">
    <ng-container *ngComponentOutlet="projectInfoComp | async"></ng-container>
  </div>
  <div
    class="t-w-1/3 t-gap-2 t-flex t-justify-center"
    *ngIf="
      reviewSetState.isBatchReview() && reviewSetState.reviewSetBasicInfo()
    ">
    <div class="t-flex t-gap-2 t-items-center">
      <span class="t-font-medium t-text-[#979797]"
        >Review set name -
        {{ reviewSetState.reviewSetBasicInfo().reviewSetName }}</span
      >
    </div>
  </div>
  <div class="t-w-1/3 t-gap-1 t-flex t-justify-end t-items-center">
    <venio-redaction-progress-bar
      *ngIf="!reviewSetState.isBatchReview()"></venio-redaction-progress-bar>
    <venio-shortcut-key-dictionary
      [type]="'home'"></venio-shortcut-key-dictionary>

    <ng-container *ngIf="!reviewSetState.isBatchReview()">
      <ng-container
        *venioHasUserGroupRights="UserRights.ALLOW_TO_VIEW_SEARCH_HISTORY">
        <ng-container
          *ngComponentOutlet="historyLogIconComp | async"></ng-container>
      </ng-container>
    </ng-container>

    <!--    <kendo-dropdownbutton-->
    <!--      *ngIf="widgetSvgIcon?.name"-->
    <!--      fillMode="clear"-->
    <!--      rounded="medium"-->
    <!--      themeColor="secondary"-->
    <!--      size="none">-->
    <!--      <div class="t-p-1 t-font-normal">-->
    <!--        <kendo-svg-icon size="large" [icon]="widgetSvgIcon" /> Add Widget-->
    <!--      </div>-->
    <!--    </kendo-dropdownbutton>-->
    <!--    <kendo-dropdownlist-->
    <!--      [popupSettings]="{ appendTo: 'component' }"-->
    <!--      iconClass="k-i-arrow-chevron-down"-->
    <!--      *ngIf="svgScatterIcon"-->
    <!--      class="!t-w-[12rem]"-->
    <!--      [showStickyHeader]="true"-->
    <!--      [data]="[{ name: 'Dashboard' }]"-->
    <!--      textField="name"-->
    <!--      valueField="name"-->
    <!--      [value]="{ name: 'Dashboard' }">-->
    <!--      <ng-template #reportIcon>-->
    <!--        <span-->
    <!--          class="t-inline-block"-->
    <!--          venioSvgLoader-->
    <!--          svgUrl="assets/svg/icon-reports.svg"-->
    <!--          height="1rem"-->
    <!--          width="1rem"></span>-->
    <!--      </ng-template>-->
    <!--      <ng-template kendoDropDownListValueTemplate let-dataItem>-->
    <!--        <ng-container *ngTemplateOutlet="reportIcon"></ng-container>-->
    <!--        {{ dataItem.name }}-->
    <!--      </ng-template>-->
    <!--    </kendo-dropdownlist>-->
  </div>
</div>
