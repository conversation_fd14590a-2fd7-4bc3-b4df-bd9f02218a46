import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentShareOptionsComponent } from './document-share-options.component'
import { ChangeDetectorRef } from '@angular/core'
import {
  DocumentShareFacade,
  ReviewParamService,
} from '@venio/data-access/review'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { CommonModule } from '@angular/common'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'

describe('DocumentShareOptionsComponent', () => {
  let component: DocumentShareOptionsComponent
  let fixture: ComponentFixture<DocumentShareOptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentShareOptionsComponent],
      imports: [
        CommonModule,
        InputsModule,
        LabelModule,
        FormsModule,
        ReactiveFormsModule,
        ButtonsModule,
        DateInputsModule,
        NoopAnimationsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
        DocumentShareFacade,
        DocumentShareFormService,
        ReviewParamService,
        ChangeDetectorRef,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentShareOptionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
