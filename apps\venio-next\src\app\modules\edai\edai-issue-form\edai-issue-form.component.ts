import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  input,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { SVGIconComponent } from '@progress/kendo-angular-icons'

import { minusIcon, plusIcon, xIcon } from '@progress/kendo-svg-icons'
import { EdaiIssueRowComponent } from '../edai-issue-row/edai-issue-row.component'
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import {
  AiFacade,
  AIRelevanceJobsResponseModel,
  JobForm,
} from '@venio/data-access/ai'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { EdaiRelevanceJobListComponent } from '../edai-form-container/edai-relevance-job-list.component'
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'
import { filter } from 'rxjs'

@Component({
  selector: 'venio-edai-issue-form',
  standalone: true,
  imports: [
    CommonModule,
    SVGIconComponent,
    EdaiIssueRowComponent,
    ReactiveFormsModule,
    TooltipsModule,
    EdaiRelevanceJobListComponent,
  ],
  templateUrl: './edai-issue-form.component.html',
  styleUrl: './edai-issue-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiIssueFormComponent implements OnInit {
  public edaiFormGroup = input.required<FormGroup<JobForm>>()

  private readonly aiFacade = inject(AiFacade)

  private readonly formBuilder = inject(FormBuilder)

  private activatedRoute = inject(ActivatedRoute)

  public edaiRelevanceCompletedJobs = toSignal(
    this.aiFacade.selectEdaiRelevanceCompletedJobs
  )

  public readonly changeDetectorRef = inject(ChangeDetectorRef)

  private destroyRef = inject(DestroyRef)

  public readonly icons = {
    closeIcon: xIcon,
    plusIcon: plusIcon,
    minusIcon: minusIcon,
  }

  public get issues(): FormArray {
    return this.edaiFormGroup()
      ?.get('relevanceJobModel')
      ?.get('issues') as FormArray
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.aiFacade.fetchEdaiRelevanceCompletedJobs(this.projectId)
    this.#loadRelevanceJob()
  }

  public getFormGroup(index: number): FormGroup {
    return this.issues.at(index) as FormGroup
  }

  #loadRelevanceJob(): void {
    this.aiFacade.loadRelevanceJob$
      .pipe(
        filter((d) => Boolean(d)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((jobData: AIRelevanceJobsResponseModel) => {
        this.issues.clear()
        jobData.issueDetail.forEach((issue) => {
          const newIssueGroup = this.#createIssueFormGroup()
          newIssueGroup.get('description').setValue(issue.issuePrompt)
          this.issues.push(newIssueGroup)
        })
        this.#reNumberIssues()
      })
  }

  public addNewIssue(): void {
    if (this.issues.length < 5) {
      const newIssueGroup = this.#createIssueFormGroup()
      this.issues.push(newIssueGroup)
    }
    this.#reNumberIssues()
  }

  public removeIssue(index: number): void {
    if (this.issues.length > 1) {
      this.issues.removeAt(index)
    }
    this.#reNumberIssues()
  }

  #createIssueFormGroup(): FormGroup {
    this.changeDetectorRef.markForCheck()
    return this.formBuilder.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
    })
  }

  #reNumberIssues(): void {
    this.issues.controls.forEach((issueGroup, index) => {
      this.changeDetectorRef.markForCheck()
      const issueCount = index + 1 >= 10 ? index + 1 : `0${index + 1}`
      issueGroup.get('name')?.setValue(`${issueCount} Issue`)
      issueGroup.updateValueAndValidity()
    })
  }
}
