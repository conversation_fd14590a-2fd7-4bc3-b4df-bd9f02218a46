import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SendToAnalyzeComponent } from './send-to-analyze.component'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { IframeMessengerService } from '@venio/data-access/iframe-messenger'
import { BehaviorSubject, of } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

describe('SendToAnalyzeComponent', () => {
  let component: SendToAnalyzeComponent
  let fixture: ComponentFixture<SendToAnalyzeComponent>
  let mockSearchFacade: any
  let mockIframeMessengerService: any
  let mockDocumentsFacade: any
  let mockBreadcrumbFacade: any
  let mockStartupsFacade: any

  const menuEvent$ = new BehaviorSubject<DocumentMenuType>(null)

  const isBatchSelected$ = new BehaviorSubject<boolean>(false)
  const selectedDocuments$ = new BehaviorSubject<number[]>([1, 2, 3])
  const unselectedDocuments$ = new BehaviorSubject<number[]>([])

  beforeEach(async () => {
    mockSearchFacade = {
      getSearchTempTables$: of({ searchGuid: '1231231' }),
      getTotalHitCount$: of(55),
      getSearchDupOption$: of(''),
      getSearchInitialParameters$: of({}),
      IsSearchLoading: jest.fn(),
      getIncludePC$: of(false),
    }
    mockIframeMessengerService = {
      sendMessage: jest.fn(),
    }

    mockDocumentsFacade = {
      selectDocumentMenuEvent$: menuEvent$.asObservable(),
      getIsBatchSelected$: isBatchSelected$.asObservable(),
      getSelectedDocuments$: selectedDocuments$.asObservable(),
      getUnselectedDocuments$: unselectedDocuments$.asObservable(),
      resetDocumentState: jest.fn(),
    }

    mockBreadcrumbFacade = {
      getBreadcrumbs$: of([]),
      selectBreadcrumbStack$: of([
        {
          query: 'fileid>100',
          filterText: 'fileid>100',
          itemIndex: 1,
          isFilterSearch: true,
        },
      ]),
      selectCompleteBreadcrumbSyntax$: of('(FileId>0)'),
    }

    mockStartupsFacade = {
      fetchDefaultGroups: jest.fn(),
      getSearchParams$: of({}),
      getSelectedMediaScope$: of([]),
      getUserRights$: of({}),
      hasGroupRight$: jest.fn(),
      hasGlobalRight$: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [SendToAnalyzeComponent],
      providers: [
        FieldFacade,
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: IframeMessengerService,
          useValue: mockIframeMessengerService,
        },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: BreadcrumbFacade, useValue: mockBreadcrumbFacade },
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '2',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SendToAnalyzeComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should open launch analyze page when "send to analyze" menu is clicked', () => {
    // Spy on searchFacade.saveSearch
    const sendMessageSpy = jest.spyOn(mockIframeMessengerService, 'sendMessage')

    fixture.detectChanges()

    // Trigger the "send to analyze" menu click event
    menuEvent$.next(DocumentMenuType.SEND_TO_ANALYZE)

    fixture.detectChanges()

    // Expect sendMessage method to have been called with the expected arguments
    expect(sendMessageSpy).toHaveBeenCalled()
  })
})
