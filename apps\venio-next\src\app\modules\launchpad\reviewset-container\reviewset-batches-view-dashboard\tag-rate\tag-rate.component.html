<kendo-grid
  [kendoGridBinding]="tagRate()"
  class="t-border-none t-mt-4 t-border-1 t-border-t-[1px]"
  [loading]="isReviewSetTagRateLoading()"
  [resizable]="false"
  [sortable]="false">
  <kendo-grid-column
    field="tag"
    title="Tags"
    [width]="150"
    headerClass="t-text-primary">
    <ng-template kendoGridHeaderTemplate>
      <span class="t-font-bold t-text-indigo-700">Tags</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <span class="t-text-gray-700">{{ dataItem.tag }}</span>
    </ng-template>
  </kendo-grid-column>

  @for (column of columns(); track column){
  <kendo-grid-column
    [resizable]="true"
    [field]="column"
    [title]="column"
    headerClass="t-text-primary"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem[column] }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  }
  <ng-template kendoGridNoRecordsTemplate> No record found. </ng-template>
</kendo-grid>
