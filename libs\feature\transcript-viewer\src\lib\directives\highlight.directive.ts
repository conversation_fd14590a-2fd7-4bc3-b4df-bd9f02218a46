import {
  Directive,
  ElementRef,
  Input,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
  OnDestroy,
  Renderer2,
} from '@angular/core'
import { Subject, debounceTime, takeUntil } from 'rxjs'

@Directive({
  selector: '[venioHighlight]',
  standalone: true,
})
export class HighlightDirective implements OnChanges, AfterViewInit, OnDestroy {
  @Input()
  public highlight = undefined

  private toDestroy$: Subject<void> = new Subject<void>()

  public textChanged$: Subject<void> = new Subject<void>()

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnChanges(changes: SimpleChanges): void {
    this.textChanged$.next()
  }

  public ngAfterViewInit(): void {
    this.textChanged$
      .pipe(
        debounceTime(1000),
        //distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        if (this.highlight) this.highlightTextWithRegex()
        else {
          this.#clearNodes('mark')
          return
        }
      })
  }

  #getRegexPattern(search): string {
    const searchTerms = search.split(' ').map((term) => term.trim())
    const regexPatterns = searchTerms.map((term) => term.split('').join('\\s*'))
    const combinedRegexPattern = `(${regexPatterns.join('|')})`
    return combinedRegexPattern
  }

  public highlightTextWithRegex(): void {
    const node = this.el.nativeElement
    const pattern = this.#getRegexPattern(this.highlight)
    const textNodes = this.#getTextNodes(node)
    this.#clearNodes('mark')
    textNodes.forEach((node) => {
      const regex = new RegExp(pattern, 'gi')
      if (regex.test(node.textContent)) {
        this.#highlightRegex(node, pattern)
      }
    })
  }

  #getTextNodes(node: Node): Array<Node> {
    let textNodes = []
    if (node.nodeType === Node.TEXT_NODE) {
      textNodes.push(node)
    } else {
      for (const child of Array.from(node.childNodes)) {
        textNodes = textNodes.concat(this.#getTextNodes(child))
      }
    }
    return textNodes
  }

  #highlightRegex(node: Node, pattern: string): void {
    const parent = node.parentNode
    const text = node.textContent.toString()

    const parts = []
    let lastIndex = 0
    let match

    const regex = new RegExp(pattern, 'gi')
    while ((match = regex.exec(text)) !== null) {
      const beforeMatch = text.slice(lastIndex, match.index)
      if (beforeMatch) {
        parts.push(document.createTextNode(beforeMatch))
      }

      const mark = document.createElement('mark')
      mark.textContent = match[0]
      parts.push(mark)
      lastIndex = regex.lastIndex
    }

    // Add any remaining text after the last match
    const afterLastMatch = text.slice(lastIndex)
    if (afterLastMatch) {
      parts.push(document.createTextNode(afterLastMatch))
    }

    // Replace the original text node with the new nodes
    node.nodeValue = ''
    parts.forEach((part) => parent.insertBefore(part, node))
    //parent.removeChild(node);
  }

  #clearNodes(selector): void {
    const selectedElements = this.el.nativeElement.querySelectorAll(selector)
    selectedElements.forEach((element) => {
      const parent = element.parentNode
      while (element.firstChild) {
        parent.insertBefore(element.firstChild, element)
      }
      this.renderer.removeChild(parent, element)
    })
  }
}
