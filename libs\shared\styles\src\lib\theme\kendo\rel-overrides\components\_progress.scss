@layer {
  kendo-progressbar {
    transition: all 0.4s ease;
    // default values
    .k-progress-status-wrap {
      grid-row: 1 / -1 !important;
      @apply t-m-0 #{!important};
    }
    .k-progress-status {
      @apply t-font-semibold t-text-[#4A4B90] t-pr-[4px] t-text-[11px] t-mt-[-1px] #{!important};
    }
    .k-progressbar-value {
      @apply t-rounded-md #{!important};
    }
    &.v-custom-progress-bar {
      @apply t-rounded-full #{!important};
      .k-progressbar-value {
        @apply t-rounded-full t-bg-[var(--kendo-custom-secondary-100)];
      }
    }
  }

  .v-custom-progressbar {
    svg {
      .k-circular-progressbar-arc,
      .k-circular-progressbar-scale {
        stroke-width: 5px !important; /* Adjust the thickness */
      }
    }
  }
}
