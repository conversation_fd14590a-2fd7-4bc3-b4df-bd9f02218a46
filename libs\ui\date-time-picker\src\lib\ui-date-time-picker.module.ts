import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule, ButtonsModule } from '@progress/kendo-angular-buttons'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { DateTimePickerComponent } from './date-time-picker/date-time-picker.component'
import {
  DateInputsModule,
  DateRangeModule,
} from '@progress/kendo-angular-dateinputs'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { IntlModule } from '@progress/kendo-angular-intl'

@NgModule({
  imports: [
    CommonModule,
    ButtonModule,
    LoaderModule,
    SVGIconModule,
    IntlModule,
    DateInputsModule,
    DateRangeModule,
    ButtonsModule,
    TooltipsModule,
  ],
  declarations: [DateTimePickerComponent],
  exports: [DateTimePickerComponent],
})
export class UiDateTimePickerModule {}
