import { ComponentFixture, TestBed } from '@angular/core/testing'
import { NativeDownloadComponent } from './native-download.component'
import { CommonModule } from '@angular/common'
import { ReactiveFormsModule } from '@angular/forms'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  TextBoxModule,
  NumericTextBoxModule,
  InputsModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { provideMockStore } from '@ngrx/store/testing'
import { PrintImageActionTemplateService } from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('NativeDownloadComponent', () => {
  let component: NativeDownloadComponent
  let fixture: ComponentFixture<NativeDownloadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NativeDownloadComponent,
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        LabelModule,
        TextBoxModule,
        NumericTextBoxModule,
        InputsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({}),
        PrintImageActionTemplateService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(NativeDownloadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
