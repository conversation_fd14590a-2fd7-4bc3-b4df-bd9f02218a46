import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CasemulticheckFilterComponent } from './casemulticheck-filter.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('CasemulticheckFilterComponent', () => {
  let component: CasemulticheckFilterComponent
  let fixture: ComponentFixture<CasemulticheckFilterComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CasemulticheckFilterComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CasemulticheckFilterComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
