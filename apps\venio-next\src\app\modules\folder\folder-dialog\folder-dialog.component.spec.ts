import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FolderDialogComponent } from './folder-dialog.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  DynamicFolderFacade,
  FolderFacade,
  ReviewSetStateService,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FolderDialogComponent', () => {
  let component: FolderDialogComponent
  let fixture: ComponentFixture<FolderDialogComponent>

  beforeEach(async () => {
    const mockReviewSetStateService = {
      isBatchReview: jest.fn().mockReturnValue(false),
    }

    await TestBed.configureTestingModule({
      imports: [FolderDialogComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DialogRef,
        FolderFacade,
        DocumentsFacade,
        DynamicFolderFacade,
        StartupsFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
        provideMockStore({}),
        {
          provide: ReviewSetStateService,
          useValue: mockReviewSetStateService,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FolderDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
