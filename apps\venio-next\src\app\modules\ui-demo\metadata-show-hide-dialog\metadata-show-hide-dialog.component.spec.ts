import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MetadataShowHideDialogComponent } from './metadata-show-hide-dialog.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('MetadataShowHideDialogComponent', () => {
  let component: MetadataShowHideDialogComponent
  let fixture: ComponentFixture<MetadataShowHideDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MetadataShowHideDialogComponent,
        HttpClientTestingModule,
        NoopAnimationsModule,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(MetadataShowHideDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
