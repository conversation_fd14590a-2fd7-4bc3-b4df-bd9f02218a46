#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

function changed {
  git diff --name-only HEAD@{1} HEAD | grep "^$1" > /dev/null 2>&1
}

if changed 'package-lock.json'; then
  echo "📦 Package changes found. Executing npm clean install to bring your dependencies up to date."
  npm install

  echo "🧩 Repairing NX"
  npm run nx:repair

  echo "🗑 Clear NX cache"
  npm run nx:reset:cache

  echo "🔨 Running migrations"
  npm run auto:migrate:lib:deps

  echo "🎫 Activate kendo license"
  npm run activate:kendo:license
fi


