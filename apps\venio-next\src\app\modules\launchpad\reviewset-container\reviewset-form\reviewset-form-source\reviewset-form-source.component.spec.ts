import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormSourceComponent } from './reviewset-form-source.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { createMockReviewSetForm } from '../review-set-form.mock'
import { FormBuilder } from '@angular/forms'
import { TagsFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ReviewSetPayloadService } from '../reviewset-payload.service'

describe('ReviewsetFormSourceComponent', () => {
  let component: ReviewsetFormSourceComponent
  let fixture: ComponentFixture<ReviewsetFormSourceComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormSourceComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideMockStore(),
        ReviewsetFormService,
        ReviewSetPayloadService,
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: TagsFacade,
          useValue: {
            selectIsTagTreeLoading$: of(false),
            selectTagTreeSuccessResponse$: of(undefined as ResponseModel),
          } satisfies Partial<TagsFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormSourceComponent)
    component = fixture.componentInstance
    const reviewSetFormService = TestBed.inject(ReviewsetFormService)

    const formBuilder = TestBed.inject(FormBuilder)
    const mockForm = createMockReviewSetForm(formBuilder)
    reviewSetFormService.reviewSetForm = mockForm
    fixture.componentRef.setInput('reviewSetForm', mockForm)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
