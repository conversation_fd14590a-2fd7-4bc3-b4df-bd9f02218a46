<div class="t-flex t-justify-end t-gap-3 t-pl-3 t-pr-5 t-py-3 t-items-center">
  <div class="t-flex t-items-center t-mr-auto">
    <kendo-dropdownlist
      [data]="launchpadGroupFilterOptions"
      [textField]="'name'"
      [valueField]="'name'"
      [(ngModel)]="launchpadFilterSelectedOption"
      title="Case Type Filter"
      kendoTooltip
      (selectionChange)="toolbarActionClicked(commonActionTypes.SEARCH, $event)"
      [popupSettings]="{
        popupClass: 'v-fixed-header-dropdown'
      }"
      class="t-w-[200px]">
      <ng-template kendoDropDownListGroupTemplate let-groupName>
        <div
          *ngIf="groupName !== null"
          class="t-pb-1 t-text-[var(--v-custom-sky-blue)] t-font-medium">
          {{ groupName }}
        </div>
      </ng-template>
    </kendo-dropdownlist>
  </div>
  @for(action of updatedToolbarActions(); track action.uniqueId){
  <button
    [id]="action.uniqueId"
    *ngIf="action.type === 'icon-button'"
    kendoButton
    (click)="toolbarActionClicked(action.actionType, action)"
    fillMode="outline"
    rounded="medium"
    kendoTooltip
    [title]="getFavoriteTooltip(action)"
    [ngClass]="getButtonClass(action)"
    class="t-w-8 t-px-6">
    @if(action.actionType === commonActionTypes.FAVOURITE){
    <span
      applyEffectsTo="fill"
      [color]="isFavoriteProjectFilter() ? '#ED7425' : 'none'"
      class="t-inline-flex t-w-8 t-justify-center t-align-text-top"
      [ngClass]="action.cssClass"
      venioSvgLoader
      height="1.2rem"
      width="1.4rem"
      [svgUrl]="action.svgIconPath">
    </span>
    } @else {
    <span
      class="t-inline-flex t-w-8 t-justify-center t-align-text-top"
      [ngClass]="action.cssClass"
      venioSvgLoader
      height="1.2rem"
      width="1.4rem"
      [svgUrl]="action.svgIconPath">
    </span>
    }
  </button>
  <kendo-dropdownbutton
    [id]="action.uniqueId"
    *ngIf="action.type === 'dropdown-button'"
    [data]="action.menuItems"
    fillMode="outline"
    rounded="medium"
    kendoTooltip
    [ngClass]="getButtonClass(action)"
    [title]="action.title || ''"
    buttonClass="t-w-12 t-px-5 hover:!t-border-[#1ebadc] hover:!t-bg-[#1ebadc]"
    (itemClick)="toolbarActionClicked($event['actionType'], $event)"
    class="">
    <div class="t-flex t-justify-between">
      <span
        class="t-inline-flex t-justify-self-start t-align-text-top"
        [ngClass]="action.cssClass"
        venioSvgLoader
        height="1.1rem"
        width="1.3rem"
        [svgUrl]="action.svgIconPath">
      </span>
      <kendo-svg-icon
        [ngClass]="action.cssClass"
        class="t-text-[#1EBADC]"
        [icon]="chevronDownIcon"></kendo-svg-icon>
    </div>

    <ng-template kendoDropDownButtonItemTemplate let-dataItem>
      <span
        venioSvgLoader
        color="#979797"
        applyEffectsTo="fill"
        hoverColor="#ffffff"
        height="1.1rem"
        width="1.3rem"
        [svgUrl]="dataItem.icon">
      </span>
      {{ dataItem.text }}
    </ng-template>
  </kendo-dropdownbutton>
  <button
    [id]="action.uniqueId"
    *ngIf="action.type === 'label-button'"
    kendoButton
    (click)="toolbarActionClicked(action.actionType, action)"
    [ngClass]="action.cssClass"
    fillMode="outline"
    class="v-custom-secondary-button"
    kendoTooltip
    [title]="action.title || ''"
    themeColor="secondary"
    [attr.data-qa]="action['label']">
    {{ action['label'] }}
  </button>
  }
</div>
