import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import {
  calendarIcon,
  caretAltDownIcon,
  checkIcon,
  pencilIcon,
  xIcon,
} from '@progress/kendo-svg-icons'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { VenioNotificationService } from '@venio/feature/notification'
import dayjs from 'dayjs'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import {
  BulkPdfRedactionDataModel,
  BulkRedactFacade,
  BulkRedactionDetailModel,
  BulkRedactionGridItem,
  BulkRedactionJobDetailModel,
  DocSelectionTypeEnum,
  DocumentsFacade,
  SearchDupOption,
  SearchFacade,
  SearchInputParams,
  SearchRequestForBulkRedactModel,
  SearchRequestModel,
  SearchResponseModel,
  StartupsFacade,
  TempTableResponseModel,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { FormsModule } from '@angular/forms'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import { BulkRedactionGridComponent } from '../bulk-redaction-grid/bulk-redaction-grid.component'
import { CSVGeneratorWorkerService } from '@venio/util/utilities'
import {
  BULK_REDACTION_REPORTS_COLUMNS,
  ReportDownloadFileNames,
} from '@venio/shared/models/constants'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { UuidGenerator } from '@venio/util/uuid'

@Component({
  selector: 'venio-bulk-redaction-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    SvgLoaderDirective,
    FormsModule,
    BulkRedactionGridComponent,
    DynamicHeightDirective,
  ],
  templateUrl: './bulk-redaction-dialog.component.html',
  styleUrl: './bulk-redaction-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkRedactionDialogComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @ViewChild(TextBoxComponent, { static: false })
  public termInput: TextBoxComponent

  public dialogTitle = 'Bulk Redaction'

  private readonly toDestroy$ = new Subject<void>()

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public searchTempTable: TempTableResponseModel

  public searchResponse: SearchResponseModel

  public searchTerm = ''

  public isSearchingForBulkRedaction =
    this.bulkRedactFacade.getIsSearchingForBulkRedaction$

  public isQueuingFilesForBulkRedaction =
    this.bulkRedactFacade.getIsQueuingFilesForBulkRedaction$

  public selectedDocCount: number

  public selectionTypeEnum: DocSelectionTypeEnum

  public fileIdList: number[] = []

  public bulkPdfRedactionDataModels: BulkPdfRedactionDataModel[] = []

  public showBulkRedactTab = true

  public selectedTabIndex = 0

  public indexOfDataToUpdate: number

  public isEditingTerm = false

  public disabledRedactButton = false

  public tabStatus = 0

  public icons = {
    closeIcon: xIcon,
    pencilIcon: pencilIcon,
    checkIcon: checkIcon,
    calendarIcon: calendarIcon,
    downIcon: caretAltDownIcon,
  }

  public termGridData: BulkRedactionGridItem[] = []

  private searchParams: Partial<SearchRequestModel>

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private bulkRedactFacade: BulkRedactFacade,
    private breadcrumbFacade: BreadcrumbFacade,
    private breadcrumbService: BreadcrumbService,
    private notificationService: VenioNotificationService,
    private confirmationDialogService: ConfirmationDialogService,
    private startupsFacade: StartupsFacade,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#handleDocumentSelection()
    this.#populateSearchResultForRedact()
    this.#handleSuccessResponse()
    this.#handleErrorResponse()
    this.#selectSearchParams()
  }

  #handleDocumentSelection(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          isBatchSelected,
          selectedDocs,
          unselectedDocs,
          totalHitCount,
          searchTempTables,
        ]) => {
          this.selectedDocCount = isBatchSelected
            ? totalHitCount - unselectedDocs.length
            : selectedDocs.length

          this.searchTempTable = searchTempTables

          if (isBatchSelected && unselectedDocs.length > 0) {
            this.selectionTypeEnum = DocSelectionTypeEnum.AllFilesExceptSelected
            this.fileIdList = unselectedDocs
          } else if (isBatchSelected && unselectedDocs.length <= 0) {
            this.selectionTypeEnum = DocSelectionTypeEnum.AllFiles
          } else {
            this.selectionTypeEnum = DocSelectionTypeEnum.SelectedFilesOnly
            this.fileIdList = selectedDocs
          }

          this.showBulkRedactTab = this.selectedDocCount > 0
          this.selectedTabIndex = this.showBulkRedactTab ? 0 : 1
        }
      )
  }

  #populateSearchResultForRedact(): void {
    this.bulkRedactFacade.getSearchForBulkRedactionSuccessResponse$
      .pipe(
        filter((response) => !!response),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.searchResponse = response.data

        if (this.isEditingTerm) {
          this.termGridData[this.indexOfDataToUpdate] = {
            term: this.searchTerm,
            hits: this.GetCommaSeparateHighlightList(),
            numOfDoc:
              this.searchResponse.searchResultIntialParameters.totalHitCount,
            action: true,
          }

          this.bulkPdfRedactionDataModels[this.indexOfDataToUpdate] = {
            keywords: this.GetUniqueSearchHighlightList(),
            documentTempTable:
              this.searchResponse.searchResultIntialParameters
                .globalTempTableName,
          }

          this.isEditingTerm = false
        } else {
          this.termGridData.push({
            term: this.searchTerm,
            hits: this.GetCommaSeparateHighlightList(),
            numOfDoc:
              this.searchResponse.searchResultIntialParameters.totalHitCount,
            action: true,
          })

          this.bulkPdfRedactionDataModels.push({
            keywords: this.GetUniqueSearchHighlightList(),
            documentTempTable:
              this.searchResponse.searchResultIntialParameters
                .globalTempTableName,
          })
        }

        this.searchTerm = ''
        this.#resetBulkRedactState()
        this.setRedactButtonState()
        this.focusOnTermInput()
      })
  }

  // Function to convert string array to comma-separated string without duplicates
  private GetCommaSeparateHighlightList(): string {
    // Create a Set from the array to remove duplicates
    const uniqueValues = this.GetUniqueSearchHighlightList()

    // Join the unique values with a comma
    return uniqueValues.join(', ')
  }

  public onEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchTerm = this.searchTerm.trim()

      if (this.searchTermExistInGrid()) {
        this.notificationService.showWarning(
          'Search Term "' + this.searchTerm + '" already exists.'
        )
        return
      }

      const searchRequestModel: SearchRequestModel = {
        searchExpression: this.GetSearchExpression(),
        includePC: false,
        projectId: this.projectId.toString(),
        lstMedia: null,
        searchGuid: this.searchTempTable.searchGuid,
        userType:
          localStorage.getItem('DocShareUserRole')?.toLowerCase() === 'external'
            ? 'EXTERNAL'
            : 'INTERNAL',
        baseGUID: '',
        isForwardFilter: false,
        searchDuplicateOption: this.searchParams.searchDuplicateOption,
      }

      const searchRequestForBulkRedactModel: SearchRequestForBulkRedactModel = {
        searchModel: searchRequestModel,
        selectionTypeEnum: this.selectionTypeEnum,
        fileIds: this.fileIdList,
      }

      this.bulkRedactFacade.searchForBulkRedaction(
        searchRequestForBulkRedactModel,
        this.projectId
      )
    }
  }

  private GetSearchExpression(): string {
    const termWithOr = this.searchTerm.split(/\s+/).join(' OR ')
    return 'FULLTEXT(' + termWithOr + ')'
  }

  private searchTermExistInGrid(): boolean {
    return this.termGridData.some((item) => item.term === this.searchTerm)
  }

  public onEditTerm(dataItem: any, rowIndex: number): void {
    this.searchTerm = dataItem.term
    this.indexOfDataToUpdate = rowIndex
    this.isEditingTerm = true
    this.focusOnTermInput()
  }

  public onDelete(dataItem: any, rowIndex: number): void {
    const title = 'Delete Term Row'
    const content =
      'Are you sure you want to delete this term - "' + dataItem.term + '"?'

    this.confirmationDialogService
      .showConfirmationDialog(title, content)
      .pipe(
        filter((confirmed) => confirmed),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.cdr.markForCheck()
        // Remove item from the gridData array and bulkPdfRedactionDataModels
        this.termGridData.splice(rowIndex, 1)
        this.bulkPdfRedactionDataModels.splice(rowIndex, 1)

        this.setRedactButtonState()
        this.focusOnTermInput()
      })
  }

  private focusOnTermInput(): void {
    if (this.termInput) {
      setTimeout(() => this.termInput.focus(), 500)
    }
  }

  private setRedactButtonState(): void {
    this.disabledRedactButton =
      this.termGridData.filter((item) => item.numOfDoc > 0).length <= 0
  }

  public QueueBulkRedaction(): void {
    // Filter items where numOfDoc > 0 and map to the desired format with numbering
    const termsToRedact = this.termGridData
      .filter((item) => item.numOfDoc > 0)
      .map((item, index) => `${index + 1}. ${item.term}`)
      .join('<br>')
    const title = 'Redact'
    const content = `Following terms will be redacted : <br><br> ${termsToRedact}`

    this.confirmationDialogService
      .showConfirmationDialog(title, content)
      .pipe(
        filter((confirmed) => confirmed),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        // Create a new list for the filtered BulkPdfRedactionDataModel items
        const filteredBulkPdfRedactionDataModels = this.termGridData.reduce(
          (acc: BulkPdfRedactionDataModel[], item, index) => {
            if (item.numOfDoc > 0) {
              acc.push(this.bulkPdfRedactionDataModels[index])
            }
            return acc
          },
          []
        )

        this.bulkRedactFacade.queueFilesForBulkRedaction(
          filteredBulkPdfRedactionDataModels,
          this.projectId
        )
      })
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
  }

  private GetUniqueSearchHighlightList(): string[] {
    // Create a Set from the array to remove duplicates
    return Array.from(
      new Set(
        this.searchResponse.searchResultIntialParameters.searchHighlightList
      )
    )
  }

  public onViewFailedDocumentsClick(searchExpression: string): void {
    const searchRequestModel: SearchInputParams = {
      searchExpression: searchExpression,
      includePC: false,
      projectId: this.projectId.toString(),
      isForwardFilter: false,
      isResetBaseGuid: true,
      searchDuplicateOption: SearchDupOption.SHOW_ALL_DUPS,
    }

    this.breadcrumbFacade.resetBreadcrumbCurrentStates()
    this.searchFacade.search(searchRequestModel)

    const breadcrumb = {
      id: UuidGenerator.uuid,
      groupStackType: GroupStackType.BULK_REDACT,
      checked: true,
      conditionType: ConditionType.Group,
      conditions: [{ conditionSyntax: searchExpression }] as ConditionElement[],
    } as ConditionGroup

    this.breadcrumbFacade.storeBreadcrumbs([breadcrumb])

    this.breadcrumbService.setConditionChecked(
      GroupStackType.VIEW_SEARCH,
      false
    )

    this.close()
  }

  public downloadBulkRedactedFileDetails(
    dataItem: BulkRedactionJobDetailModel
  ): void {
    this.bulkRedactFacade.fetchBulkRedactedFileDetails(
      this.projectId,
      dataItem.jobId
    )
  }

  #resetBulkRedactState(): void {
    this.bulkRedactFacade.resetBulkRedactState([
      'searchForBulkRedactionSuccessResponse',
      'searchForBulkRedactionFailureResponse',
      'queueFilesForBulkRedactionSuccessResponse',
      'queueFilesForBulkRedactionFailureResponse',
    ])
  }

  #resetBulkRedactionFileDetialsState(): void {
    this.bulkRedactFacade.resetBulkRedactState([
      'bulkRedactionFileDataSuccessResponse',
      'bulkRedactionFileDataErrorResponse',
    ])
  }

  #handleSuccessResponse(): void {
    this.bulkRedactFacade.getQueueFilesForBulkRedactionSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.notificationService.showSuccess(success.message)
        this.#resetBulkRedactState()
        this.close()
      })

    this.bulkRedactFacade.selectBulkRedactionFileDataSuccessResponse$
      .pipe(
        filter((result) => Boolean(result)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result: ResponseModel) => {
        const data: BulkRedactionDetailModel[] =
          result.data.bulkRedactionDetails
        this.#exportBulkRedactionReport(data)
      })
  }

  #handleErrorResponse(): void {
    this.bulkRedactFacade.getSearchForBulkRedactionFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)

        this.#resetBulkRedactState()
      })

    this.bulkRedactFacade.getQueueFilesForBulkRedactionFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)

        this.#resetBulkRedactState()
      })

    this.bulkRedactFacade.selectBulkRedactionFileDataErrorResponse$
      .pipe(
        filter((error) => Boolean(error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)

        this.#resetBulkRedactionFileDetialsState()
      })
  }

  public isSelected(tabIndex: number): boolean {
    return this.selectedTabIndex === tabIndex
  }

  #exportBulkRedactionReport(data: BulkRedactionDetailModel[]): void {
    const csvWorkerService = new CSVGeneratorWorkerService()
    const currentDate = dayjs().format('YYYYMMDD_HHmmss')
    csvWorkerService
      .generateCSV(
        `${this.projectId}_${ReportDownloadFileNames.BULK_REDACTION_REPORT}_${currentDate}`,
        BULK_REDACTION_REPORTS_COLUMNS,
        data
      )
      .then((isSuccess) => {
        this.notificationService.showSuccess('File Downloaded Successfully')
      })
      .catch(() => {
        this.notificationService.showError('Failed to export CSV file')
      })
  }

  #selectSearchParams(): void {
    this.startupsFacade.getSearchParams$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((searchParams) => {
        this.searchParams = searchParams
      })
  }

  public close(): void {
    this.dialogRef.close()
  }

  public ngAfterViewInit(): void {
    this.focusOnTermInput()
    this.setRedactButtonState()
  }

  public ngOnDestroy(): void {
    this.#resetBulkRedactionFileDetialsState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
