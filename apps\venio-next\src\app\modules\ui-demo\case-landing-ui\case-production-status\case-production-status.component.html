<kendo-dialog-titlebar (close)="close('cancel')">
  <div class="t-flex t-justify-between t-w-full t-items-center mt-ml-[10px]">
    <div
      class="t-block t-tracking-[0.14px] t-text-[#2F3080DE] t-font-medium t-gap-3">
      <span
        class="t-w-10 t-h-10 t-p-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
        <img src="assets/svg/icon-production-status.svg" alt="upload icon" />
      </span>
      {{ dialogTitle }}
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="t-block t-mt-1 t-p-2" *ngIf="!ifProductionShare">
  <kendo-dropdownlist
    [data]="externalUsers"
    [defaultItem]="defaultItemExternal"
    textField="text"
    valueField="value"
    class="t-w-52">
  </kendo-dropdownlist>

  <div class="t-flex t-my-5">
    <h3
      class="t-text-[16px] t-tracking-[0.75px] t-font-bold t-text-[#000000] t-uppercase">
      Prod One
    </h3>
    <span
      class="t-grid t-text-[#5F3F0B] t-place-content-center t-p-1 t-text-[10px] t-leading-[16px] t-font-medium t-rounded-[4px] t-ml-2 t-tracking-[1.5px] t-uppercase"
      [ngClass]="badgeClassMap[status] || 't-bg-gray-300'"
      >Inprogress</span
    >
  </div>

  <div
    class="t-flex t-items-center t-gap-4 t-my-4 t-justify-start t-mt-3 t-font-medium">
    <div class="t-flex t-items-center">
      <span class="t-w-[11px] t-h-[9px] t-bg-[#ED7425] t-inline-block"></span>
      <span
        class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
        >FAILED</span
      >
    </div>
    <div class="t-flex t-items-center">
      <span class="t-w-[11px] t-h-[9px] t-bg-[#9BD2A7] t-inline-block"></span>
      <span
        class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
        >COMPLETED</span
      >
    </div>
    <div class="t-flex t-items-center">
      <span class="t-w-[11px] t-h-[9px] t-bg-[#FFB300] t-inline-block"></span>
      <span
        class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
        >INPROGRESS</span
      >
    </div>
    <div class="t-flex t-items-center">
      <span class="t-w-[11px] t-h-[9px] t-bg-[#718792] t-inline-block"></span>
      <span
        class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
        >NOT STARTED</span
      >
    </div>
  </div>

  <!-- chart section-->

  <div class="t-flex t-mt-5 t-w-full t-gap-5">
    <venio-case-production-status-graph
      [graphId]="1"
      [chartData]="chartData1"
      [theme]="'INPROGRESS'"></venio-case-production-status-graph>
    <venio-case-production-status-graph
      [graphId]="2"
      [chartData]="chartData2"
      [theme]="'COMPLETED'"></venio-case-production-status-graph>
    <venio-case-production-status-graph
      [graphId]="3"
      [chartData]="chartData3"
      [theme]="'NOT STARTED'"></venio-case-production-status-graph>
    <venio-case-production-status-graph
      [graphId]="4"
      [chartData]="chartData4"
      [theme]="'FAILED'"></venio-case-production-status-graph>
  </div>

  <div
    class="t-bg-[#BAE36E0F] t-shadow-[0_3px_20px_rgba(0,0,0,0.16)] t-px-[40px] t-py-5 t-rounded-md t-mt-5">
    <!-- Header -->
    <div class="t-flex t-items-center t-justify-between">
      <div class="t-flex t-items-center t-gap-2">
        <span
          class="t-font-bold t-text-[#000000] t-text-[16px] t-tracking-[0.75px]"
          >SLIPSHEET</span
        >
        <span
          class="t-grid t-place-content-center t-p-1 t-rounded-[4px] t-font-medium t-ml-2 t-tracking-[1.5px] t-text-[#0F4B1B] t-bg-[--tb-kendo-custom-secondary-100] t-text-[10px] t-leading-[16px]">
          COMPLETED
        </span>
        <span
          class="t-text-[#000000] t-text-[12px] t-font-medium t-tracking-[0.4px] t-leading-[16px]"
          >Downloaded
          <span
            class="t-text-[#00000099] t-text-[12px] t-font-normal t-tracking-[0.4px] t-leading-[16px]"
            >1</span
          ></span
        >
      </div>
    </div>

    <!-- Content -->
    <div class="t-grid t-grid-cols-4 t-gap-6 t-mt-4">
      <!-- Folder Structure -->
      <div>
        <h3
          class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
          Folder Structure
        </h3>
        <ul class="t-mt-2 t-space-y-2">
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Full Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >289 of 289</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Native Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >289 of 289</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Image Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >Image not produced</span
            >
          </li>
        </ul>
      </div>

      <!-- Relativity Counts -->
      <div>
        <ul class="t-mt-6 t-space-y-2">
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Relativity Fulltext Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >289 of 289</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Relativity Native Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >289 of 289</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Relativity Image Count</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >Image not produced</span
            >
          </li>
        </ul>
      </div>

      <!-- Other Details -->
      <div>
        <h3
          class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
          Other details
        </h3>
        <ul class="t-mt-2 t-space-y-2">
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Started on</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >October 1 11:53 AM</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Completed on</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >October 1 4:32 PM</span
            >
          </li>
          <li class="t-flex t-flex-col">
            <span
              class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
              >Created by</span
            >
            <span
              class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
              >John Doe</span
            >
          </li>
        </ul>
      </div>

      <!-- Action -->
      <div>
        <h3
          class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
          Action
        </h3>
        <div class="t-mt-2 t-flex t-gap-2">
          <kendo-buttongroup class="v-custom-button-group">
            <button
              kendoButton
              *ngFor="let icon of svgIconForGridControls"
              #actionGridGroup
              class="!t-p-[0.3rem] t-w-[1.5rem]"
              [ngClass]="{
                'hover:!t-bg-[#1EBADC]': icon.hoverColor === '#1EBADC',
                'hover:!t-bg-[#FFBB10]': icon.hoverColor === '#FFBB10',
                'hover:!t-bg-[#2F3080]': icon.hoverColor === '#2F3080',
                'hover:!t-bg-[#9AD3A6]': icon.hoverColor === '#9AD3A6',
                'hover:!t-bg-[#ED7425]': icon.hoverColor === '#ED7425'
              }"
              (click)="browseActionClicked(icon.actionType, 'dataItem')"
              fillMode="outline"
              kendoTooltip
              [title]="icon.actionType"
              size="none">
              <span
                [parentElement]="actionGridGroup.element"
                venioSvgLoader
                [applyEffectsTo]="icon.applyFill"
                hoverColor="#FFFFFF"
                [color]="'#979797'"
                [svgUrl]="icon.iconPath"
                height="0.9rem"
                width="1rem"></span>
            </button>
          </kendo-buttongroup>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="t-block t-w-full" *ngIf="ifProductionShare">
  <venio-case-production-status-share></venio-case-production-status-share>
</div>

<kendo-dialog-actions *ngIf="ifProductionShare">
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="close('no')"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      SHARE
    </button>
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
