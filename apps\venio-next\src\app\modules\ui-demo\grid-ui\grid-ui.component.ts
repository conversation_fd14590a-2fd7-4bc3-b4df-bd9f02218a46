import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core'

/**
 * NOTE: the grid UI is basically a prototype for now, and some spacing are done inside the
 * grid column for proper styling of spaces.
 * Implementation details are just for demo purposes and can be removed entirely.
 * Such as header color, filter icon, hover effect is done below file:
 * @see libs/shared/styles/src/lib/theme/kendo/rel-overrides/components/_grid.scss
 */
@Component({
  selector: 'venio-grid-demo',
  templateUrl: './grid-ui.component.html',
  styleUrls: ['./grid-ui.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridUiComponent implements OnInit {
  public selectedKeys: any[]

  public data: any

  public columns: string[]

  public isLoading: boolean

  constructor(private changeDetectorRef: ChangeDetectorRef) {}

  public ngOnInit(): void {
    this.changeDetectorRef.markForCheck()
    this.isLoading = true
    // simulate loading from API
    setTimeout(() => {
      this.changeDetectorRef.markForCheck()
      this.isLoading = false
      const rows = 10000
      const data = []
      for (let i = 0; i < rows; i++) {
        const row = {}
        for (let j = 0; j < 10; j++) {
          const key = `c-${j}`
          row[key] = `val-${i + 1}`
        }
        data.push(row)
      }

      this.data = data
      this.columns = Object.keys(data[0])
    })
  }
}
