import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'
import { filter, Subject, takeUntil } from 'rxjs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { DirectExportFacade } from '@venio/data-access/common'
import {
  RelativitySettingsDataModel,
  UserEnvironmentModel,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'

/**
 * @see libs/shared/assets/src/files/js/AESEnc.js
 * @see libs/util/uuid/src/uuid-generator.ts
 */
declare let encryptStr: (plainText: string, key: string) => string

interface LoginFormModel {
  username?: FormControl<string>
  password?: FormControl<string>
  apiClientId?: FormControl<string>
  apiClientSecret?: FormControl<string>
  rememberMe: FormControl<boolean>
}
@Component({
  selector: 'venio-relativity-login',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    LabelModule,
    DialogsModule,
    IconsModule,
    ButtonComponent,
    ReactiveFormsModule,
    LoaderComponent,
    SvgLoaderDirective,
  ],
  templateUrl: './relativity-login.component.html',
  styleUrl: './relativity-login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RelativityLoginComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  @ViewChild('username')
  private username: TextBoxComponent

  @ViewChild('apiClientId')
  private apiClientId: TextBoxComponent

  private dialog = inject(DialogRef)

  @Input() public relativityData: RelativitySettingsDataModel

  private readonly directExportFacade = inject(DirectExportFacade)

  private readonly formBuilder = inject(FormBuilder)

  private readonly cdr = inject(ChangeDetectorRef)

  public readonly iconEye = eyeIcon

  public readonly iconSlashEye = eyeSlashIcon

  public loginFormGroup: FormGroup<LoginFormModel>

  public readonly isLoginLoading = signal(false)

  public readonly togglePasswordVisibility = signal(false)

  public get formControls(): LoginFormModel {
    return this.loginFormGroup?.controls
  }

  public readonly loginServerMessage = signal<string>('')

  public ngOnInit(): void {
    this.#initForm()
  }

  public ngAfterViewInit(): void {
    this.#selectLoginResponses()
    this.#focusOnInput()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public encryptStr(plainText: string, key: string): string {
    return encryptStr(plainText, key)
  }

  public loginClick(): void {
    this.loginServerMessage.set('')

    if (this.isLoginLoading()) {
      return
    }

    Object.entries(this.formControls).forEach(([key, control]) => {
      control.markAsTouched()
      control.markAsDirty()
    })

    if (this.loginFormGroup.invalid) {
      return
    }

    const sessionId = UuidGenerator.uuid.replace(/-/g, '')

    const { username, password, apiClientId, apiClientSecret, rememberMe } =
      this.loginFormGroup.getRawValue()

    const isRelativityOne = this.relativityData?.isRelativityOne
    const environmentId = this.relativityData?.id
    const id = this.relativityData?.environmentId

    // Early return if credentials are missing
    if (
      (!isRelativityOne && (!username.trim() || !password.trim())) ||
      (isRelativityOne && (!apiClientId.trim() || !apiClientSecret.trim()))
    ) {
      return
    }

    const payload: UserEnvironmentModel = {
      id: isRelativityOne ? id : undefined,
      environmentId: environmentId,
      userName: !isRelativityOne ? username : '',
      password: !isRelativityOne ? encryptStr(password, sessionId) : '',
      encryptionKey: sessionId,
      isDefaultAccount: rememberMe,
      isRelativityOne: isRelativityOne,
      apiClientId: isRelativityOne ? apiClientId : '',
      apiClientSecret: isRelativityOne
        ? encryptStr(apiClientSecret, sessionId)
        : '',
    }

    this.isLoginLoading.set(true)
    this.directExportFacade.createUserEnvironment(payload)
  }

  public passwordVisibilityClicked(event: KeyboardEvent): void {
    event.preventDefault()
    event.stopPropagation()
    this.togglePasswordVisibility.set(!this.togglePasswordVisibility())
  }

  public onCancelAction(): void {
    this.dialog.close()
  }

  #initForm(): void {
    this.loginFormGroup = this.formBuilder.group<LoginFormModel>({
      username: new FormControl('', [Validators.required]),
      password: new FormControl('', [Validators.required]),
      apiClientId: new FormControl('', [Validators.required]),
      apiClientSecret: new FormControl('', [Validators.required]),
      rememberMe: new FormControl(false),
    })

    this.#updateFormValidation()

    // if (this.relativityData.isRelativityOne) {
    //   this.loginFormGroup.patchValue({
    //     apiClientId: this.relativityData?.relativityOneClientId,
    //     rememberMe: this.relativityData?.rememberMe,
    //   })
    // }

    this.cdr.detectChanges()
  }

  #updateFormValidation(): void {
    if (this.relativityData?.isRelativityOne) {
      // Remove validators from username and password
      this.loginFormGroup.get('username')?.clearValidators()
      this.loginFormGroup.get('password')?.clearValidators()
      this.loginFormGroup.get('username')?.updateValueAndValidity()
      this.loginFormGroup.get('password')?.updateValueAndValidity()

      // Add required validators to relativityOneClientId and apiClientSecret
      this.loginFormGroup
        .get('apiClientId')
        ?.setValidators([Validators.required])
      this.loginFormGroup
        .get('apiClientSecret')
        ?.setValidators([Validators.required])
      this.loginFormGroup.get('apiClientId')?.updateValueAndValidity()
      this.loginFormGroup.get('apiClientSecret')?.updateValueAndValidity()
    } else {
      // Remove validators from apiClientId and apiClientSecret
      this.loginFormGroup.get('apiClientId')?.clearValidators()
      this.loginFormGroup.get('apiClientSecret')?.clearValidators()
      this.loginFormGroup.get('apiClientId')?.updateValueAndValidity()
      this.loginFormGroup.get('apiClientSecret')?.updateValueAndValidity()

      // Add required validators to username and password
      this.loginFormGroup.get('username')?.setValidators([Validators.required])
      this.loginFormGroup.get('password')?.setValidators([Validators.required])
      this.loginFormGroup.get('username')?.updateValueAndValidity()
      this.loginFormGroup.get('password')?.updateValueAndValidity()
    }
  }

  #focusOnInput(): void {
    setTimeout(() => {
      if (this.relativityData.isRelativityOne && this.apiClientId) {
        this.apiClientId.focus()
      } else if (!this.relativityData.isRelativityOne && this.username) {
        this.username.focus()
      }
    }, 300)
  }

  #selectLoginResponses(): void {
    this.directExportFacade.selectCreateUserEnvironmentSuccess$
      .pipe(
        filter((r) => !!r),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.directExportFacade.clearCreateEnvironmentSuccessResponse()
        this.isLoginLoading.set(false)
        if (res?.status === 'Success') {
          this.dialog.close({
            isLoggedInSuccessfully: true,
            userEnvironmentId: +res.data,
          })
        }
      })

    this.directExportFacade.selectCreateUserEnvironmentError$
      .pipe(
        filter((r) => !!r),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error: any) => {
        this.directExportFacade.clearCreateEnvironmentErrorResponse()
        this.isLoginLoading.set(false)
        const isError = error?.error?.['message']?.toLowerCase()?.length > 0
        if (isError) {
          this.loginServerMessage.set('Failed to connect relativity')
        }
      })
  }
}
