import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'
import {
  DateInputsModule,
  DateRangePopupComponent,
  SelectionRange,
} from '@progress/kendo-angular-dateinputs'
import {
  SVGIcon,
  calendarIcon,
  checkIcon,
  chevronDownIcon,
  fileCsvIcon,
  fileExcelIcon,
  filePdfIcon,
  imageIcon,
  printIcon,
  xCircleIcon,
  xIcon,
} from '@progress/kendo-svg-icons'
import { LabelModule } from '@progress/kendo-angular-label'
import { IconsModule } from '@progress/kendo-angular-icons'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { UiPaginationModule } from '@venio/ui/pagination'

interface GridDataItem {
  userName: string
  ipAddress: string
  loginDateTime: Date
  logoutDateTime: Date
}

@Component({
  selector: 'venio-login-report-caselaunchpad',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    DropDownsModule,
    FormsModule,
    DateInputsModule,
    LabelModule,
    ReactiveFormsModule,
    IconsModule,
    GridModule,
    InputsModule,
    UiPaginationModule,
  ],
  templateUrl: './login-report-caselaunchpad.component.html',
  styleUrl: './login-report-caselaunchpad.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginReportCaselaunchpadComponent implements OnInit {
  @ViewChild('popup') public calendarPopup: DateRangePopupComponent

  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public genders: Array<{ text: string; value: number }> = [
    { text: 'John Doe', value: 1 },
    { text: 'Ronnie', value: 2 },
    { text: 'Anthony', value: 3 },
  ]

  public myForm: FormGroup = new FormGroup({
    gender: new FormControl([2]),
  })

  public range = { start: null, end: null }

  public imageSVG: SVGIcon = imageIcon

  public closeIcon: SVGIcon = xCircleIcon

  public downIcon: SVGIcon = chevronDownIcon

  public calendarIcon: SVGIcon = calendarIcon

  public checkIcon: SVGIcon = checkIcon

  public xIcon: SVGIcon = xIcon

  public pdfIcon: SVGIcon = filePdfIcon

  public printIcon: SVGIcon = printIcon

  public pageSize = 10

  public buttonCount = 2

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public gridData: GridDataItem[] = []

  public dialogTitle = 'Login & Logout Report'

  public opened: boolean

  public settings: any[] = [
    {
      text: 'Save as PDF',
      svgIcon: filePdfIcon,
    },
    {
      text: 'Save as Excel',
      svgIcon: fileExcelIcon,
    },
    {
      text: 'Save as CSV',
      svgIcon: fileCsvIcon,
    },
  ]

  constructor() {
    this.generateData()
  }

  public ngOnInit(): void {
    this.openDialog()
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public onChange(e: SelectionRange): void {
    this.range = e
  }

  public generateData(): void {
    const names = ['John Doe', 'Jane Smith', 'Alice Johnson', 'Bob Lee']
    const ips = ['***********', '***********', '***********', '***********']

    for (let i = 0; i < 20; i++) {
      const nameIndex = Math.floor(Math.random() * names.length)
      const ipIndex = Math.floor(Math.random() * ips.length)
      const hours1 = Math.floor(Math.random() * 24)
      const hours2 = hours1 + Math.floor(Math.random() * (24 - hours1))
      this.gridData.push({
        userName: names[nameIndex],
        ipAddress: ips[ipIndex],
        loginDateTime: new Date(new Date().setHours(hours1, 0, 0, 0)),
        logoutDateTime: new Date(new Date().setHours(hours2, 0, 0, 0)),
      })
    }
  }
}
