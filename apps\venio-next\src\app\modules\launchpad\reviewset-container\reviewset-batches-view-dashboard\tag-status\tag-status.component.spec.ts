import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagStatusComponent } from './tag-status.component'
import { of } from 'rxjs'
import { ReviewSetFacade } from '@venio/data-access/common'

jest.mock('@venio/util/utilities', () => {
  return {
    ReviewSetBatchChartWorkerService: jest.fn().mockImplementation(() => {
      return {
        generateTagStatusChartData: jest.fn().mockResolvedValue([]),
        terminate: jest.fn(),
      }
    }),
  }
})

describe('TagStatusComponent', () => {
  let component: TagStatusComponent
  let fixture: ComponentFixture<TagStatusComponent>

  const mockReviewSetFacade = {
    selectFilterFields$: of(undefined),
    isReviewSetTagStatusLoading$: of(false),
    selectReviewSetTagStatusSuccess$: of({ message: '', success: true }),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagStatusComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(TagStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
