import { TemplateRef } from '@angular/core'
import { ColumnSortSettings } from '@progress/kendo-angular-treelist'

export interface TreeListColumnModel {
  field: string

  title: string

  width: number

  resizable: boolean

  reorderable: boolean

  autoSize: boolean

  hidden: boolean

  format: any

  sortable: boolean | ColumnSortSettings

  cssClass: string | string[] | Set<string> | { [key: string]: any }

  columnTemplate: TemplateRef<any>
}
