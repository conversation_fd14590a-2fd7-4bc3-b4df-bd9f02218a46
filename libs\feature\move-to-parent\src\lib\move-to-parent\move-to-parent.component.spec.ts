import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MoveToParentComponent } from './move-to-parent.component'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogModule,
  DialogRef,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  NumericTextBoxModule,
  TextAreaModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import {
  MoveToParentFacade,
  ReviewParamService,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import { AnimationBuilder } from '@angular/animations'
import { HttpClientTestingModule } from '@angular/common/http/testing'

describe('MoveToParentComponent', () => {
  let component: MoveToParentComponent
  let fixture: ComponentFixture<MoveToParentComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        MoveToParentComponent,
        CommonModule,
        LabelModule,
        ButtonsModule,
        NumericTextBoxModule,
        DialogModule,
        DialogsModule,
        TextAreaModule,
        LoaderModule,
        ExpansionPanelModule,
      ],
      providers: [
        AnimationBuilder,
        MoveToParentFacade,
        ReviewParamService,
        DialogRef,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(MoveToParentComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
