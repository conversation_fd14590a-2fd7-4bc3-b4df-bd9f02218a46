@for(breadcrumb of breadcrumbStacks(); track trackBySyntax(breadcrumb); let
count = $count; let last = $last){
<venio-breadcrumb-stack-content
  *ngIf="breadcrumb.conditionType === conditionType.Group"
  [breadcrumb]="breadcrumb" />
@if(count > 1 && !last){
<venio-breadcrumb-stack-group-operator
  [conditionOperatorGroup]="breadcrumb"
  *ngIf="breadcrumb.conditionType === conditionType.Operator" />
} @if(last){
<div class="t-flex-row t-w-full t-py-2 t-relative"></div>
} }

<venio-breadcrumb-bottom-actions
  *ngIf="hasAnyBreadcrumbStacks()"
  class="t-mt-auto t-sticky t-z-10 t-bottom-0 t-py-2 !t-bg-[transparent]" />
