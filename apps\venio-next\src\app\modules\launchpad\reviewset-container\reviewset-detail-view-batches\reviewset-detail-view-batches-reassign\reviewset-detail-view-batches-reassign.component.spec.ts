import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewBatchesReassignComponent } from './reviewset-detail-view-batches-reassign.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { SelectedReviewSetBatchModel } from '@venio/shared/models/interfaces'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewsetDetailViewBatchesReassignComponent', () => {
  let component: ReviewsetDetailViewBatchesReassignComponent
  let fixture: ComponentFixture<ReviewsetDetailViewBatchesReassignComponent>

  const mockSelectedReviewSetBatch: SelectedReviewSetBatchModel = {
    reviewSetId: 1,
    projectId: 1,
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewBatchesReassignComponent,
        BrowserAnimationsModule,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectReviewSetUserGroupDetail$: of([]),
            selectIsReviewSetUserGroupLoading$: of(false),
            fetchReviewSetUserGroups: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      ReviewsetDetailViewBatchesReassignComponent
    )
    component = fixture.componentInstance
    fixture.componentRef.setInput(
      'selectedReviewSetBatch',
      mockSelectedReviewSetBatch
    )
    fixture.componentRef.setInput('isRessaginWindowActive', true)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
