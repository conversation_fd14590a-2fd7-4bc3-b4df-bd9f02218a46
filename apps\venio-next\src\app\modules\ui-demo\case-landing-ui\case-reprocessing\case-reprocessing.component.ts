import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { PopoverModule, TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { UiPaginationModule } from '@venio/ui/pagination'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  DialogRef,
  DialogService,
  DialogsModule,
  DialogCloseResult,
  DialogAnimation,
} from '@progress/kendo-angular-dialog'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { CaseReprocessingCustodianDialogComponent } from './case-reprocessing-custodian-dialog/case-reprocessing-custodian-dialog.component'
import { CaseReprocessingUpdateComponent } from './case-reprocessing-update/case-reprocessing-update.component'
import { CaseReprocessingSettingsComponent } from './case-repreocessing-settings/case-reprocessing-settings.component'
import { IconsModule } from '@progress/kendo-angular-icons'
import { xIcon } from '@progress/kendo-svg-icons'
import { ActivatedRoute } from '@angular/router'

export interface SearchTags {
  id: number
  ParentTagId: number
  TagName: string
  TagType: string
  createDate?: Date
  TagCount: number
}

export interface Password {
  NsfUserId: number
  password: string
}
@Component({
  selector: 'venio-case-reprocessing',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    LabelModule,
    FormsModule,
    GridModule,
    TooltipModule,
    DynamicHeightDirective,
    ButtonsModule,
    UiPaginationModule,
    LoaderModule,
    SvgLoaderDirective,
    DialogsModule,
    IconsModule,
    CaseReprocessingCustodianDialogComponent,
    CaseReprocessingUpdateComponent,
    CaseReprocessingSettingsComponent,
    DropDownsModule,
    TreeListModule,
    LayoutModule,
    PopoverModule,
  ],
  templateUrl: './case-reprocessing.component.html',
  styleUrl: './case-reprocessing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingComponent implements OnInit {
  public extractionData: any[] = []

  public allFilesData: any[] = []

  public isOverlayActive = false

  public animation: boolean | DialogAnimation = {
    type: 'slide',
    direction: 'left',
    duration: 300,
  }

  public activeComponent: string | null = null

  public overlayTitle = ''

  public overlayIconUrl = ''

  public bulkSettingOptions: string[] = ['Bulk Settings', 'Item 1', 'Item 2']

  public selectedBulkSetting = 'Bulk Settings'

  public selectedRadioOption = 'execptionType'

  public openLoadFileDialog = false

  public passwordBankDialog = false

  public importPasswordDialog = false

  public reprocessInNewMediaChecked = false

  public importPasswordGridData: Array<{
    id: number
    password: string
    added: string
    error: string
  }> = []

  public importNsfUserIdPasswordDialog = false

  public importNsfUserIdPasswordGridData: Array<{
    id: number
    nsfuserid: string
    password: string
    added: string
    error: string
  }> = []

  public passwordBankHelpDialog = false

  public passwordBankNsfUserIdHelpDialog = false

  public searchtagstree: SearchTags[] = [
    {
      id: 1,
      ParentTagId: null,
      TagType: 'System Tags',
      TagName: 'System Tags',
      createDate: new Date('2019-01-15'),
      TagCount: 0,
    },
    {
      id: 2,
      ParentTagId: null,
      TagName: 'User Tags',
      TagType: 'User Tgs',
      createDate: new Date('2019-02-19'),
      TagCount: 0,
    },
    {
      id: 4,
      ParentTagId: null,
      TagName: 'Not Tagged',
      TagType: null,
      createDate: new Date('2018-01-17'),
      TagCount: 55,
    },
  ]

  public data: SearchTags[] = this.searchtagstree

  public selectedFileName = ''

  public passwordInput = ''

  public tabStatus = 0

  public selectedLotusRadioOption = 'noAssociatedPassword' // for Lotus notes User Id password selection in Password Bank dialog>tab

  public passwordDummyData: Array<{ password: string }> = []

  public icons = {
    closeIcon: xIcon,
    custodianIcon: 'assets/svg/icon-custodian-or-media.svg',
    updateIcon: 'assets/svg/icon-reprocessing-update.svg',
  }

  public LoadFileHelpDialog = false

  public passwords: Password[] = []

  constructor(
    private dialogService: DialogService,
    private route: ActivatedRoute
  ) {}

  public ngOnInit(): void {
    this.generateDummyDataForExtraction()
    this.generateDummyDataAllFiles()
    this.generateDummyDataForSearchTags()

    // Extract URL parameter and trigger the appropriate overlay
    this.route.queryParams.subscribe((params) => {
      const overlayType = params['ind'] // Get the value of 'ind' from the URL
      if (overlayType === 'settings') {
        this.openOverlay('settings')
      } else if (overlayType === 'update') {
        this.openOverlay('update')
      } else if (overlayType === 'custodian') {
        this.openOverlay('custodian')
      }
    })
  }

  public generateDummyDataForExtraction(): void {
    const statuses = [
      'Password Protected',
      'Corrupted',
      'Crashed',
      'Timed Out',
      'Non Processed',
      'Item Missing',
      'Partial Meta Extracted',
    ]

    for (let i = 1; i <= 40; i++) {
      this.extractionData.push({
        id: i,
        status: statuses[i % statuses.length],
      })
    }
  }

  public generateDummyDataForSearchTags(): void {
    const searchTags = [
      'System Tags',
      'User Tags',
      'Responsive',
      'Unresponsive',
      'Confidential',
      'Priveleged',
      'Not Tagged',
    ]

    for (let i = 1; i <= 10; i++) {
      this.searchtagstree.push({
        id: i,
        ParentTagId: Math.random() < 0.5 ? 1 : null,
        TagName: searchTags[i % searchTags.length],
        TagType: 'System Tags',
        createDate: new Date('2019-04-13'),
        TagCount: i * 8 - 3,
      })
    }
  }

  public generateDummyDataAllFiles(): void {
    for (let i = 1; i <= 40; i++) {
      this.allFilesData.push({
        fileId: 16000 + i,
        mediaName: 'Andy Zipper',
        fileName: `#${i}.Dat`,
        originalFilePath: `\\\\EC2AMAZ-EM8M8UP\\SourceFolder\\File${i}.dat`,
        fileType: 'Unknown',
        exceptionType: 'NOT_PROCESSED',
      })
    }
  }

  /**
   * Opens the overlay and sets the active component.
   * @param component - The component to load ('custodian' or 'update')
   */
  public openOverlay(component: string): void {
    this.isOverlayActive = true
    this.activeComponent = component

    // Set the overlay title based on the active component
    if (component === 'custodian') {
      this.overlayTitle = 'Custodian or Media'
      this.overlayIconUrl = this.icons.custodianIcon // Set custodian icon
    } else if (component === 'update') {
      this.overlayTitle = 'Update Meta'
      this.overlayIconUrl = this.icons.updateIcon // Set update icon
    } else if (component === 'settings') {
      this.overlayTitle = 'Change Reprocessing Settings'
      this.overlayIconUrl = this.icons.updateIcon // Set update icon
    }
  }

  /**
   * Closes the overlay and resets the active component.
   */
  public closeOverlay(): void {
    this.isOverlayActive = false
    this.activeComponent = null
    this.overlayTitle = ''
    this.overlayIconUrl = ''
  }

  public openCustodianDialog(): void {
    import(
      './case-reprocessing-custodian-dialog/case-reprocessing-custodian-dialog.component'
    ).then((td) => {
      const dialog: DialogRef = this.dialogService.open({
        content: td.CaseReprocessingCustodianDialogComponent,
        maxWidth: '1100px',
        maxHeight: '710px',
        width: '80%',
        height: '90vh',
        animation: this.animation,
        cssClass: 'v-dialog-custodian t-right-[220px]',
      })

      dialog.result.subscribe((result) => {
        if (result instanceof DialogCloseResult) {
          console.log('Dialog closed')
        }
      })
    })
  }

  public openLoadfileDialog(): void {
    this.openLoadFileDialog = true
  }

  public closeLoadfileDialog(): void {
    this.openLoadFileDialog = false
  }

  public onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      const file = input.files[0]
      this.selectedFileName = file.name // Update the Kendo TextBox with the file name
      console.log('Selected file:', file)
    }
  }

  public openPasswordBankDialog(): void {
    this.passwordBankDialog = true
  }

  public closePasswordBankDialog(): void {
    this.passwordBankDialog = false
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
    console.log(this.tabStatus)
  }

  public addPassword(): void {
    if (this.passwordInput.trim() !== '') {
      this.passwordDummyData.push({ password: this.passwordInput })
      this.passwordInput = '' // Clear the input field after adding password in tab 1
    } else {
      alert('Password cannot be empty!')
    }
  }

  public deleteRow(rowIndex: number): void {
    this.passwordDummyData.splice(rowIndex, 1)
  }

  public openImportPasswordDialog(): void {
    this.importPasswordDialog = true
  }

  public closeImportPasswordDialog(): void {
    this.importPasswordDialog = false
  }

  public openimportNsfUserIdPasswordDialog(): void {
    this.importNsfUserIdPasswordDialog = true
  }

  public closeimportNsfUserIdPasswordDialog(): void {
    this.importNsfUserIdPasswordDialog = false
  }

  public openpasswordBankHelpDialog(): void {
    this.passwordBankHelpDialog = true
  }

  public closepasswordBankHelpDialog(): void {
    this.passwordBankHelpDialog = false
  }

  public openpasswordBankNsfUserIdHelpDialog(): void {
    this.passwordBankNsfUserIdHelpDialog = true
  }

  public closepasswordBankNsfUserIdHelpDialog(): void {
    this.passwordBankNsfUserIdHelpDialog = false
  }

  public openLoadFileHelpDialog(): void {
    this.LoadFileHelpDialog = true
  }

  public closeLoadFileHelpDialog(): void {
    this.LoadFileHelpDialog = false
  }
}
