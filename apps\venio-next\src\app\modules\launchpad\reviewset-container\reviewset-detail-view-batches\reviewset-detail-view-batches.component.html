<div class="t-flex t-flex-col t-relative t-max-h-[calc(100vh_-_51px)] t-grow">
  @defer {
  <venio-reviewset-detail-view-batches-toolbar
    (pagingChange)="pagingChanged($event)"
    (bulkActionInvoked)="reviewSetBatchBulkActionClick($event)" />
  <venio-reviewset-detail-view-batches-grid
    (actionInvoked)="reviewSetBatchActionClick($event)" />
  }@placeholder {
  <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
    <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
    <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
    <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
  </div>
  }
</div>
<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isRessaginWindowActive()"></div>
<div
  class="t-fixed t-top-[1px] t-w-[36%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isRessaginWindowActive(),
    't-right-[-36%]': !isRessaginWindowActive()
  }">
  <div class="t-flex t-justify-between t-items-center t-w-full">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="icons.custodianIcon"></button>
      Reassign
      <span class="t-text-[#263238] t-pl-1 t-text-[#263238]">{{
        selectedBatchName()
      }}</span>
    </span>
    <div
      class="t-my-2 t-flex t-flex-1 t-items-center t-w-full t-relative t-h-[50px]">
      <div class="t-block t-w-full t-relative t-grid t-mt-[4px]">
        <div
          #appendNotification
          class="v-append-notification-container t-mt-[4px] t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-14 t-w-full"></div>
      </div>
    </div>
    <button
      (click)="closeReassignWindow()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-mt-[-7px] t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>

  @defer {
  <venio-reviewset-detail-view-batches-reassign
    [selectedReviewSetBatch]="selectedReviewSetBatch()"
    [isRessaginWindowActive]="isRessaginWindowActive()"
    (actionInvoked)="reviewSetReassignActionClick($event)" />
  }@placeholder {
  <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
    <kendo-skeleton [height]="30" shape="rectangle" [width]="'99%'" />
  </div>
  }
</div>
