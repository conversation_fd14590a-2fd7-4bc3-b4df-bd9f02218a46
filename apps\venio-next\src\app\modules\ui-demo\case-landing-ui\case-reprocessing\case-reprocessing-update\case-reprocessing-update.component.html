<div
  class="t-flex t-flex-col t-flex-wrap t-gap-4 t-p-4"
  venioDynamicHeight
  [extraSpacing]="70">
  <!-- Update Meta Checkbox -->
  <div class="t-w-full t-flex t-gap-2 t-items-center">
    <input type="checkbox" #updateMeta kendoCheckBox [checked]="true" />
    <kendo-label
      class="t-flex t-items-center t-space-x-2"
      [for]="updateMeta"
      text="Update Meta"></kendo-label>
  </div>

  <!-- Metadata Grids -->
  <div class="t-flex t-w-full t-gap-4">
    <!-- Edoc Meta -->
    <div class="t-w-2/4">
      <kendo-grid
        [kendoGridBinding]="edocMeta"
        [sortable]="true"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true"
        [filterable]="true"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        kendoGridSelectBy="id"
        [height]="245"
        class="t-w-full t-h-full t-relative t-overflow-y-auto t-border t-border-gray-200">
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="50">
        </kendo-grid-checkbox-column>
        <kendo-grid-column
          field="text"
          title="Edoc Meta"
          headerClass="t-text-primary">
          <!-- <ng-template kendoGridHeaderTemplate>
                        <div class="t-flex t-items-center t-gap-2 t-w-full">
                          <span>Edoc Meta</span>
                          

                          <kendo-textbox
                          class="!t-border-[#ccc] t-flex-1 t-w-full"
                          placeholder=""
                          [clearButton]="true">
                          <ng-template kendoTextBoxPrefixTemplate>
                            <div class="t-w-4 t-h-2"></div>
                          </ng-template>
                          <ng-template kendoTextBoxSuffixTemplate>
                            <button
                              kendoButton
                              fillMode="clear"
                              class="t-text-[#1EBADC]"
                              imageUrl="assets/svg/icon-updated-search.svg"></button>
                          </ng-template>
                        </kendo-textbox>

                    

                        </div>
                      </ng-template> -->
        </kendo-grid-column>
      </kendo-grid>
    </div>

    <!-- Email Meta -->
    <div class="t-w-2/4">
      <kendo-grid
        [kendoGridBinding]="emailMeta"
        [sortable]="true"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true"
        [filterable]="true"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        kendoGridSelectBy="id"
        [height]="245"
        class="t-w-full t-h-full t-relative t-overflow-y-auto t-border t-border-gray-200">
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="50">
        </kendo-grid-checkbox-column>

        <kendo-grid-column
          field="text"
          title="Email Meta"
          headerClass="t-text-primary">
          <!-- <ng-template kendoGridHeaderTemplate>
                        <div class="t-flex t-items-center t-gap-2 t-w-full">
                          <span>Email Meta</span>
                        
                          <kendo-textbox
                          class="!t-border-[#ccc] t-flex-1 t-w-full"
                          placeholder=""
                          [clearButton]="true">
                          <ng-template kendoTextBoxPrefixTemplate>
                            <div class="t-w-4 t-h-2"></div>
                          </ng-template>
                          <ng-template kendoTextBoxSuffixTemplate>
                            <button
                              kendoButton
                              fillMode="clear"
                              class="t-text-[#1EBADC]"
                              imageUrl="assets/svg/icon-updated-search.svg"></button>
                          </ng-template>
                        </kendo-textbox>
                        </div>
                      </ng-template> -->
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>

  <!-- Additional Options -->
  <div class="t-w-1/2 t-flex t-gap-5 t-mt-2">
    <div class="t-w-1/2 t-flex t-flex-col t-gap-0 t-space-y-1">
      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input type="checkbox" #updateFulltext kendoCheckBox [checked]="true" />
        <kendo-label
          [for]="updateFulltext"
          text="Update Fulltext"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #updateHashValue
          kendoCheckBox
          [checked]="false" />
        <kendo-label
          [for]="updateHashValue"
          text="Update Hashvalue"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input type="checkbox" #allFiles kendoCheckBox [checked]="true" />
        <kendo-label
          class="t-flex t-items-center t-w-full"
          [for]="allFiles"
          text="">
          <kendo-dropdownlist
            [data]="dropdownOptions"
            [(ngModel)]="selectedDropdownOption"
            class="t-w-full">
          </kendo-dropdownlist>
        </kendo-label>
      </div>
    </div>

    <div class="t-w-1/2 t-flex t-flex-col t-gap-0 t-space-y-1">
      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input type="checkbox" #setTimeout kendoCheckBox [checked]="true" />
        <kendo-label [for]="setTimeout" text="" class="t-flex t-w-full">
          <kendo-numerictextbox
            format="# mins"
            [step]="1"
            [placeholder]="'Set Timeout (In Mins)'"></kendo-numerictextbox>
        </kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #usePasswordBank
          kendoCheckBox
          [checked]="true" />
        <kendo-label
          [for]="usePasswordBank"
          text="Use Password Bank"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input type="checkbox" #repairPST kendoCheckBox [checked]="false" />
        <kendo-label [for]="repairPST" text="Repair PST"></kendo-label>
      </div>
    </div>
  </div>
</div>

<!-- footer-->
<div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="save">
    SAVE
  </button>
  <button data-qa="cancel" kendoButton themeColor="dark" fillMode="outline">
    CANCEL
  </button>
</div>
