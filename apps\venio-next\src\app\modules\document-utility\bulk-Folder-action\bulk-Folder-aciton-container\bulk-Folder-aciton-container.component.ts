import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { DocumentsFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { NotificationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-bulk-folder-aciton-container',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  templateUrl: './bulk-Folder-aciton-container.component.html',
  styleUrl: './bulk-Folder-aciton-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkFolderAcitonContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  public MenuType: DocumentMenuType

  @ViewChild('dialogContent', { static: true })
  private readonly dialogContent: TemplateRef<any>

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxWidth: '40vw',
      minWidth: '35%',
    })
    const dialogInstance = this.dialogRef.content.instance
    dialogInstance.MenuType = this.MenuType
  }

  #handleLazyLoadedDialog(): void {
    import(
      '../bulk-Folder-aciton-dialog/bulk-Folder-aciton-dialog.component'
    ).then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.BulkFolderAcitonDialogComponent)
      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */

  #selectedDocumentEvent(): void {
    combineLatest([
      this.documentsFacade.selectDocumentMenuEvent$,
      this.documentsFacade.getSelectedDocuments$,
    ])
      .pipe(
        filter(([menuEventType, selectedDocuments]) =>
          Boolean(
            menuEventType === DocumentMenuType.SEND_REMOVE && selectedDocuments
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([menuEventType, selectedDocuments]) => {
        if (selectedDocuments && selectedDocuments.length === 0) {
          this.#showNotificationMessage()
          return
        }
        this.MenuType = menuEventType
        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }

  #showNotificationMessage(): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetMenuLoadingState()
        this.#resetMenuEventState()
      })
  }

  #setDialogInput(instance: NotificationDialogComponent): void {
    instance.title = 'Send / Remove Folder'
    instance.message = `Please select at least one document to add or remove from folder`
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
