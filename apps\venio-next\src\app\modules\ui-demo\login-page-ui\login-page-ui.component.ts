import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  chevronLeftIcon,
  eyeIcon,
  eyeSlashIcon,
} from '@progress/kendo-svg-icons'
import { LoginPageForgotPwdComponent } from './login-page-forgot-pwd/login-page-forgot-pwd.component'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'venio-login-page-ui',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputsModule,
    LabelModule,
    DialogsModule,
    IconsModule,
    LoginPageForgotPwdComponent,
  ],
  templateUrl: './login-page-ui.component.html',
  styleUrl: './login-page-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginPageUiComponent implements OnInit {
  public icons = {
    eyeIcon: eyeIcon,
    slashIcon: eyeSlashIcon,
    leftIcon: chevronLeftIcon,
  }

  public loginForm: FormGroup

  public isSubmitting = false // for disabling multiple submission

  public forgotPassword = false

  public emailSent = false

  public termsOfUse = false

  public resetPassword = false

  public successResetPassword = false

  public passwordDontMatch = false

  public PasswordValid = false

  public newPasswordValid = false

  public retypeNewPasswordValid = false

  public passwordChangeStatus = 'Password change successful'

  public dialogTitle = 'LOGIN'

  public forgotPwd = false

  constructor(private fb: FormBuilder, private route: ActivatedRoute) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false],
      forgotPasswordUsername: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      retypeNewPassword: ['', [Validators.required, Validators.minLength(6)]],
    })
  }

  public ngOnInit(): void {
    this.route.queryParams.subscribe((queryParams) => {
      this.forgotPwd = queryParams['action'] === 'forgot-password'
    })
  }

  public onLogin(): void {
    if (this.loginForm.valid && !this.isSubmitting) {
      this.isSubmitting = true // Disable further submissions
      const loginData = this.loginForm.value
      loginData

      // Simulate an API call
      setTimeout(() => {
        this.isSubmitting = false // Re-enable button after process
      }, 3000) // Example delay of 2 seconds
      this.isSubmitting = false
      // Perform actual login action here
    }
  }

  public clearForm(): void {
    return
  }

  public onSubmitUsername(): void {
    this.emailSent = true
    this.forgotPassword = false
    this.termsOfUse = false
    this.resetPassword = false
    this.successResetPassword = false
  }

  public onResetPassword(): void {
    this.resetPassword = true

    const password = this.loginForm.get('password')?.value
    const newPassword = this.loginForm.get('newPassword')?.value
    const retypeNewPassword = this.loginForm.get('retypeNewPassword')?.value

    if (newPassword !== '' || retypeNewPassword !== '' || password !== '')
      this.loginForm.patchValue({
        password: '',
        newPassword: '',
        retypeNewPassword: '',
      })
  }

  public onReset(): void {
    const newPassword = this.loginForm.get('newPassword')?.value
    const retypeNewPassword = this.loginForm.get('retypeNewPassword')?.value

    if (newPassword !== retypeNewPassword) {
      this.passwordChangeStatus = 'Password does not match'
      this.passwordDontMatch = true
    } else {
      this.passwordChangeStatus = 'Password change successful'
      this.passwordDontMatch = false
      this.resetPassword = false
      this.successResetPassword = true
    }
  }

  public onTermsOfUse(): void {
    this.termsOfUse = true
    this.forgotPassword = false
    this.emailSent = false
  }

  public close(): void {
    this.forgotPassword = false
    this.emailSent = false
    this.termsOfUse = false
    this.resetPassword = false
    this.successResetPassword = false
  }

  public openDialog(): void {
    this.forgotPassword = true
  }
}
