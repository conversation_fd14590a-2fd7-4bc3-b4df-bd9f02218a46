import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { DocumentsFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { ConfirmationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-edai-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './edai-container.component.html',
  styleUrl: './edai-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly isDocumentSelected = signal<boolean>(false)

  private dialogRef: DialogRef

  private readonly documentFacade = inject(DocumentsFacade)

  private readonly dialogService = inject(DialogService)

  public ngOnInit(): void {
    this.#selectDocumentIds()
    this.#selectedDocumentEvent()
  }

  #selectDocumentIds(): void {
    combineLatest([
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getIsBatchSelected$,
    ])
      .pipe(
        filter(([response]) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([selectedFileIds, isBatchSelected]) => {
        // Whether the document is selected or is batch selected
        this.isDocumentSelected.set(
          isBatchSelected || (!isBatchSelected && selectedFileIds.length > 0)
        )
      })
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'eDiscovery AI'
    instance.message = `Please select at least one document to review with eDiscovery AI.`
    instance.shouldShowCloseButton.set(false)
  }

  #launchAndSetupConfirmationDialog(): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(confirmationDialogRef.content.instance)

    confirmationDialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.SEND_TO_EDAI),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetMenuLoadingState()
        this.#resetMenuEventState()
        if (!this.isDocumentSelected()) {
          // confirm if the document is selected
          this.#launchAndSetupConfirmationDialog()
          return
        }

        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('../edai-dialog/edai-dialog.component').then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.EdaiDialogComponent)

      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentFacade.resetDocumentState('isDocumentMenuLoading')
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxHeight: '90vh',
      maxWidth: '90vw',
      minHeight: '90vh',
      minWidth: '90vw',
    })
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentFacade.resetDocumentState('menuEventPayload')
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
