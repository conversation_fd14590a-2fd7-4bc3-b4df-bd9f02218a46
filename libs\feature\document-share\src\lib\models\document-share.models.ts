import { SharedDocumentDetailModel } from '@venio/data-access/review'

export interface DocumentShareFormModel {
  shareName: string
  sharedExpiryDate: string
  sendCopyToMe: boolean
  internalUsers: string[]
  externalUsers: string[]
  shareToExternalUsers: boolean
  instruction: string
  allowToTag: boolean
  allowToAddNotes: boolean
  allowToViewAnalyzePage: boolean
  allowRedaction: boolean
  newEmail?: string
}

export interface SharedDocInjectModel {
  isDocShareEdit: boolean
  shareDocData: SharedDocumentDetailModel
}

export interface DocumentShareDialogModel {
  isDocShareEdit: boolean
  projectId: number
}
