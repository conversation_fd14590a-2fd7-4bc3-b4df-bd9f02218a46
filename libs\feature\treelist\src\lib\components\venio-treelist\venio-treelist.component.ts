import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
  ViewChild,
} from '@angular/core'
import {
  ColumnBase,
  RowClassArgs,
  SelectableSettings,
  SelectionChangeEvent,
} from '@progress/kendo-angular-treelist'
import { VenioTreelistColumnComponent } from '../venio-treelist-column/venio-treelist-column.component'
import { TreeListColumnModel } from '../../models/treelist-column.model'

/**
 * Custom Angular component for rendering a tree list with enhanced selection functionality.
 *
 * @example
 * The following example shows how to use the VenioTreelistComponent in a component template:
 * For data in the following format:
 * ```
 * [{
 *  "tagId": 1,
 *  "parentTagId": null,
 *  "tagName": "Tag1",
 *  "selected": true,
 *  },
 * {
 * "tagId": 2,
 * "parentTagId": 1,
 * "tagName": "Tag2",
 * "selected": true,
 *  }]
 * ```
 *
 * ```html
 * <venio-treelist
 *    [data]="tagList"
 *    parentIdField="parentTagId"
 *    idField="tagId"
 *    selectedField="selected"
 *    [sortable]="true"
 *    [height]="250"
 *    (venioTreeViewSelectionChange)="selectionChange($event)">
 *
 *    <venio-treelist-column
 *      field="tagName"
 *      title="Tag Name"
 *      [sortable]="true"
 *      [width]="200">
 *    </venio-treelist-column>
 *  </venio-treelist>
 * ```
 *
 * @export
 * @class VenioTreelistComponent
 * @implements {AfterViewInit}
 * @implements {OnChanges}
 */
@Component({
  selector: 'venio-treelist',
  templateUrl: './venio-treelist.component.html',
  styleUrl: './venio-treelist.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VenioTreelistComponent implements AfterViewInit, OnChanges {
  /**
   * Input data which binds to kendo tree list and uses `idField` and `parentIDField` field to define the hierarchy.
   * Uses kendoTreeListFlatBinding of the kendo tree list.
   *
   * @type {any[]}
   * @memberof VenioTreelistComponent
   */
  @Input() public data: any[]

  /**
   * Enables the [sorting]({% slug sorting_treelist %}) of the TreeList columns that have their `field` option set.
   *
   * @type {boolean}
   * @memberof VenioTreelistComponent
   */
  @Input() public sortable = false

  /**
   * The name of the field which contains the unique identifier of the node.
   *
   * @type {string}
   * @memberof VenioTreelistComponent
   */
  @Input() public idField: string

  /**
   * The name of the field which contains the identifier of the parent node.
   *
   * @type {string}
   * @memberof VenioTreelistComponent
   */
  @Input() public parentIdField: string

  /**
   * Height for kendo tree list.
   *
   * @type {number}
   * @memberof VenioTreelistComponent
   */
  @Input() public height: number

  /**
   * Check/uncheck Select All checkbox on load.
   *
   * @type {boolean}
   * @memberof VenioTreelistComponent
   */
  @Input() public selectAllOnLoad: boolean | null

  /**
   * The name of the field from the data source to be used for checkbox state.
   * The type should be `boolean | null`.
   * Settings null to the field will set the checkbox to indeterminate state.
   *
   * @type {string}
   * @memberof VenioTreelistComponent
   */
  @Input({ required: true }) public selectedField: string

  /**
   * If set to `true`, the user can resize columns by dragging the edges (resize handles) of their header cells
   * ([see example]({% slug resizing_columns_treelist %})).
   *
   * @type {boolean}
   * @memberof VenioTreelistComponent
   */
  @Input() public resizable = true

  /**
   * Event emitter for selection change in the tree view.
   * Emits the data binded to the tree list with selection value set.
   *
   * @type {EventEmitter<any[]>}
   * @memberof VenioTreelistComponent
   */
  @Output() public readonly venioTreeViewSelectionChange = new EventEmitter<
    any[]
  >()

  /**
   * Actual data that is binded to the tree list with selection value set after additional handling in the OnChanges lifecycle hook.
   *
   * @type {any[]}
   * @memberof VenioTreelistComponent
   */
  public selectionData: any[] = []

  /**
   * Used to indicate checked(true)/unchecked(false)/indeterminate(null) state of Select All checkbox in the header of the tree list.
   *
   * @type {(boolean | null)}
   * @memberof VenioTreelistComponent
   */
  public selectAllChecked: boolean | null = null

  /**
   * Array of tree list columns.
   *
   * @type {TreeListColumnModel[]}
   * @memberof VenioTreelistComponent
   */
  public treelistColumns: TreeListColumnModel[] = []

  /**
   * Settings for the selection behavior in the tree list.
   *
   * @type {SelectableSettings}
   * @memberof VenioTreelistComponent
   */
  public selectableSettings: SelectableSettings = {
    enabled: true,
    mode: 'row',
    multiple: true,
    drag: true,
  }

  /**
   * Selects an element from the view and updates the tree list columns.
   *
   * @type {ViewChild('treeListWrapper')}
   * @memberof VenioTreelistComponent
   */
  @ViewChild('treeListWrapper') public treeListWrapperRef

  /**
   * List of VenioTreelistColumnComponent
   *
   * @private
   * @type {QueryList<VenioTreelistColumnComponent>}
   * @memberof VenioTreelistComponent
   */
  @ContentChildren(VenioTreelistColumnComponent)
  private venioTreeListColumnComponents: QueryList<VenioTreelistColumnComponent>

  // flag used to determine if there is parent-child data. if true show the expand button and make checkbox column a bit wider to accommodate child checkbox.
  public hasParentChildHierarchy = false

  /**
   * Creates an instance of VenioTreelistComponent.
   *
   * @param {ChangeDetectorRef} cdr - Reference to the ChangeDetectorRef service.
   * @memberof VenioTreelistComponent
   */
  constructor(private cdr: ChangeDetectorRef) {}

  /**
   * Handling OnChanges to update the data binded to tree list with selection value and to set "Select All" checkbox state based on `selectAllOnLoad` value (default true).
   *
   * @param {SimpleChanges} changes - Object containing current and previous property values.
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public ngOnChanges(changes: SimpleChanges): void {
    // Code to execute after the data is bound
    if (changes.data && changes.data.currentValue) {
      // map bound data to selectable items and set selected flag for all items based on selectAllOnLoad value (default true)
      this.selectionData = this.data.map((item) => ({
        ...item,
        [this.selectedField]:
          this.selectAllOnLoad !== undefined && this.selectAllOnLoad !== null
            ? this.selectAllOnLoad
            : item[this.selectedField],
      }))

      this.hasParentChildHierarchy = this.checkIfParentChildHierarchyIsPresent()

      // set Select All checkbox to checked, unchecked or undeterminate state
      if (this.selectionData.length === 0) {
        this.selectAllChecked = false
      } else if (this.selectionData.every((item) => item[this.selectedField])) {
        this.selectAllChecked = true
      } else if (this.selectionData.some((item) => item[this.selectedField])) {
        this.selectAllChecked = null
      } else {
        this.selectAllChecked = false
      }

      this.emitSelectionChange() // emit selection change event
    }
  }

  /**
   * Checks if parent-child hierarchy is present in the data.
   * @returns {boolean} - Returns true if parent-child hierarchy is present in the data.
   */
  private checkIfParentChildHierarchyIsPresent(): boolean {
    return this.data.some(
      (d) =>
        d[this.parentIdField] === null &&
        this.data.some((c) => c[this.parentIdField] === d[this.idField])
    )
  }

  /**
   * Lifecycle hook that is called after the view has been initialized.
   *
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public ngAfterViewInit(): void {
    this.treelistColumns = this.venioTreeListColumnComponents.map((column) => ({
      field: column.field,
      title: column.title,
      width: column.width,
      resizable: column.resizable,
      reorderable: column.reorderable,
      autoSize: column.autoSize,
      hidden: column.hidden,
      format: column.format,
      sortable: column.sortable,
      cssClass: column.class,
      columnTemplate: column.columnTemplate,
    }))
    this.cdr.markForCheck()
  }

  /**
   * Handles the click event on the Select All checkbox.
   *
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public selectAllClick(): void {
    if (this.selectAllChecked) {
      this.selectionData.forEach((item) => {
        item[this.selectedField] = true
      })
    } else {
      this.selectionData.forEach((item) => {
        item[this.selectedField] = false
      })
    }
    this.emitSelectionChange()
  }

  /**
   * Handles the default selection change event of the kendo tree list (not used currently as it is disabled)
   *
   * @param {SelectionChangeEvent} event - The selection change event.
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public selectionChange(event: SelectionChangeEvent): void {
    const selected = event.action === 'add' || event.action === 'select'
    // if row selection is done then first deselect all items before selecting what is selected by clicking or click-dragging
    if (event.action === 'select') {
      this.selectionData.forEach((item) => {
        item[this.selectedField] = false
      })
    }
    const currentItems = this.selectionData.filter((item) =>
      event.items.some(
        (evt) => evt.dataItem[this.idField] === item[this.idField]
      )
    )
    this.handleSelection(currentItems, selected)
  }

  /**
   * Handles the click event on a tree list item.
   *
   * @param {any} dataItem - The clicked data item.
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public itemSelected(dataItem: any): void {
    dataItem[this.selectedField] = !dataItem[this.selectedField]
    this.handleSelection([dataItem], dataItem[this.selectedField])
  }

  /**
   * Handles the selection logic for items and updates the model.
   *
   * @private
   * @param {any[]} currentItems - The current selected items. It is array just in case we need to handle default selection change event of the kendo tree list.
   * @param {boolean} selected - The selection state.
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  private handleSelection(currentItems: any[], selected: boolean): void {
    // get array of parents of current selected items
    const parents = this.selectionData.filter((selItem) =>
      currentItems.some(
        (item) => item[this.parentIdField] === selItem[this.idField]
      )
    )

    // update current item with checked/unchecked status
    currentItems.forEach((item) => {
      item[this.selectedField] = selected
    })

    if (selected) {
      // select all child items
      this.selectionData.forEach((selItem) => {
        if (
          currentItems.some(
            (item) => item[this.idField] === selItem[this.parentIdField]
          )
        ) {
          selItem[this.selectedField] = true
        }
      })

      // If all children are checked, set the parent checkbox to checked state, and if only some children are checked, set it to indeterminate
      if (parents?.length > 0) {
        this.setParentCheckboxState(parents)
      }
    } else {
      // If all children are unchecked, set the parent checkbox to unchecked state, and if only some children are unchecked, set it to indeterminate
      if (parents?.length > 0) {
        this.setParentCheckboxState(parents)
      }

      // uncheck all child items of current item
      this.selectionData.forEach((selItem) => {
        if (
          currentItems.some(
            (item) => selItem[this.parentIdField] === item[this.idField]
          )
        ) {
          selItem[this.selectedField] = false
        }
      })
    }

    // Update Select All checkbox state with same logic
    if (this.selectionData.every((item) => item[this.selectedField])) {
      this.selectAllChecked = true
    } else if (this.selectionData.every((item) => !item[this.selectedField])) {
      this.selectAllChecked = false
    } else {
      this.selectAllChecked = null
    }

    this.emitSelectionChange()
  }

  /**
   * Sets the checkbox state of parent items based on the selection state of their children.
   * If all children are selected, the parent checkbox is set to checked.
   * If some children are selected, the parent checkbox is set to indeterminate.
   * If no children are selected, the parent checkbox is set to unchecked.
   *
   * @private
   * @param {any[]} parents - The array of parent items to process.
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  private setParentCheckboxState(parents: any[]): void {
    parents.forEach((parent) => {
      // find siblings of the current item
      const childOfCurrentParent = this.selectionData.filter(
        (selItem) => selItem[this.parentIdField] === parent[this.idField]
      )

      // number of selected siblings
      const numberOfSelectedChild = childOfCurrentParent.filter(
        (childType) => childType[this.selectedField]
      )

      // if all siblings are selected, set parent checkbox to checked
      if (numberOfSelectedChild.length === childOfCurrentParent.length) {
        parent[this.selectedField] = true
      }
      // if some siblings are selected, set parent checkbox to indeterminate
      else if (numberOfSelectedChild.length > 0) {
        parent[this.selectedField] = null
      }
      // if no siblings are selected, set parent checkbox to unchecked
      else {
        parent[this.selectedField] = false
      }
    })
  }

  /**
   * Emits the selection change event.
   *
   * @memberof VenioTreelistComponent
   * @returns {void}
   */
  public emitSelectionChange(): void {
    this.venioTreeViewSelectionChange.emit(this.selectionData)
  }

  /**
   * Sets checkbox state to checked/unchecked/indeterminate based on the selection state of the data item.
   * Checked if `selected` is true, unchecked if `selected` is false, indeterminate if `selected` is null.
   *
   * @param {any} dataItem - The data item to check.
   * @param {ColumnBase} [column] - The tree list column.
   * @param {number} [columnIndex] - The index of the column.
   * @returns {(boolean | null)} - The selection state.
   * @memberof VenioTreelistComponent
   */
  public isSelected(
    dataItem: any,
    column?: ColumnBase,
    columnIndex?: number
  ): boolean | null {
    return dataItem[this.selectedField]
  }

  /**
   * Returns the CSS class for a tree list row based on the selection state to highlight it as selected
   *
   * @param {RowClassArgs} rowArg - The row class arguments.
   * @returns {string} - The CSS class.
   * @memberof VenioTreelistComponent
   */
  public rowClassFn = (rowArg: RowClassArgs): string => {
    return rowArg.dataItem[this.selectedField] ? 'k-selected' : ''
  }
}
