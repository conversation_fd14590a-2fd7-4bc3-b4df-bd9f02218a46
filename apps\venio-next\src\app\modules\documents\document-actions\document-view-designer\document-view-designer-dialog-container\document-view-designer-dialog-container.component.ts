import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { TabStripModule } from '@progress/kendo-angular-layout'

import {
  Accessibility,
  FieldFacade,
  ReviewSetStateService,
  SearchFacade,
  ViewFacade,
  ViewModel,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { DocumentViewFacade, UserFacade } from '@venio/data-access/common'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import {
  ConditionGroup,
  ConditionUiType,
  UserModel,
} from '@venio/shared/models/interfaces'
import { ConditionStackManagerWorkerService } from '@venio/util/utilities'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

@Component({
  selector: 'venio-document-view-designer-dialog-container',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    TabStripModule,
    IndicatorsModule,
  ],
  templateUrl: './document-view-designer-dialog-container.component.html',
  styleUrl: './document-view-designer-dialog-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerDialogContainerComponent
  implements OnInit, OnDestroy
{
  private toDestroy$ = new Subject<void>()

  private currentUser = signal<UserModel>({} as UserModel)

  private fieldFacade = inject(FieldFacade)

  private documentViewFacade = inject(DocumentViewFacade)

  private activatedRoute = inject(ActivatedRoute)

  private viewFacade = inject(ViewFacade)

  private searchFacade = inject(SearchFacade)

  private userFacade = inject(UserFacade)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  public reviewSetState = inject(ReviewSetStateService)

  @Optional()
  private dialogRef = inject(DialogRef)

  private notificationService = inject(NotificationService)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public readonly commonActionTypes = CommonActionTypes

  public readonly conditionUiType = ConditionUiType.VIEW_CONDITION

  public viewPayload: ViewModel

  private filteredConditions: ConditionGroup[]

  public get isSaveAs(): boolean {
    return (
      this.viewPayload?.viewId > 0 &&
      this.viewPayload?.createdBy !== this.currentUser()?.userId
    )
  }

  public isAddOrUpdateViewLoading$ =
    this.documentViewFacade.selectIsViewAddOrUpdateLoading$

  public lazySortComponent = import(
    '../document-view-designer-sort/document-view-designer-sort.component'
  ).then(
    ({ DocumentViewDesignerSortComponent }) => DocumentViewDesignerSortComponent
  )

  public lazyInfoFormComponent = import(
    '../document-view-designer-info-form/document-view-designer-info-form.component'
  ).then(
    ({ DocumentViewDesignerInfoFormComponent }) =>
      DocumentViewDesignerInfoFormComponent
  )

  public lazyFieldsComponent = import(
    '../document-view-designer-info-fields/document-view-designer-fields.component'
  ).then(
    ({ DocumentViewDesignerFieldsComponent }) =>
      DocumentViewDesignerFieldsComponent
  )

  public lazyConditionsComponent = import(
    '../document-view-designer-info-conditions/document-view-designer-conditions.component'
  ).then(
    ({ DocumentViewDesignerConditionsComponent }) =>
      DocumentViewDesignerConditionsComponent
  )

  public ngOnInit(): void {
    this.#selectCurrentUser()
    this.#selectViewFormChange()
    this.#fetchFields()
    this.#fetchSearchFields()
    this.#selectAddOrUpdateViewResponses()
  }

  public ngOnDestroy(): void {
    this.#resetDocumentViewStates()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public actionClick(commonActionType: CommonActionTypes): void {
    switch (commonActionType) {
      case CommonActionTypes.SAVE_AS:
      case CommonActionTypes.SAVE:
        this.#saveChangeWithPayload()
        break
      case CommonActionTypes.CANCEL:
        this.#resetDocumentViewStates()
        this.dialogRef.close()
        break
      default:
        break
    }
  }

  #resetBreadcrumbStates(): void {
    this.breadcrumbFacade.resetBreadcrumbCurrentStates()
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
      })
  }

  #resetDocumentViewStates(): void {
    this.documentViewFacade.resetDocumentViewState([
      'selectedDocumentView',
      'currentFormData',
      'addOrUpdateViewErrorResponse',
      'addOrUpdateViewSuccessResponse',
      'searchFields',
      'searchFieldsErrorResponse',
    ])
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #saveChangeWithPayload(): void {
    if (!this.viewPayload?.viewName?.trim()) {
      this.#showMessage('Please enter a name for the view.', {
        style: 'warning',
      })
      return
    }

    const viewFields = this.viewPayload.viewFields
      ?.filter((c) => c.fieldName)
      .map((item, index) => ({ ...item, fieldOrder: index + 1 }))

    if (viewFields.length === 0) {
      this.#showMessage('Please select at least one field to proceed.', {
        style: 'warning',
      })
      return
    }

    const viewExpression =
      this.viewPayload?.viewExpression.trim() || 'INTERNAL_FILE_ID>0'

    const viewProjectIds = this.viewPayload.viewProjectIds?.[0]
      ? this.viewPayload.viewProjectIds
      : [this.projectId]

    const viewExpressionJson = JSON.stringify(this.filteredConditions)

    const viewSortSettings = this.viewPayload.viewSortSettings?.filter(
      (c) => c.fieldName
    )

    const payload = {
      ...this.viewPayload,
      viewId: this.isSaveAs ? 0 : this.viewPayload.viewId,
      viewProjectIds,
      viewExpression,
      viewFields,
      viewExpressionJson,
      viewSortSettings,
    } as ViewModel

    // If the view is created/edited, look for the current user and remove it from the list of users
    const withoutCurrentUser = payload.viewUserIds.filter(
      (id) => id !== this.currentUser().userId
    )
    // If the view is public or the view is private and the current user is the only user,
    // the view should be loaded and applied
    const shouldLoadAndApply =
      payload.accessibility === Accessibility.Public ||
      (payload.viewUserIds.includes(this.currentUser().userId) &&
        withoutCurrentUser.length === 0)

    // Whether the view is created by the current user
    const isViewCreatedByCurrentUser =
      this.currentUser().userId === payload.createdBy

    // Add the current user to the list of users if the view is newly created,
    // Only add the current user if the view is not public, and the current user is not yet in the list
    if (!payload.viewId) {
      payload.viewUserIds = !payload.viewUserIds?.includes(-1)
        ? [...new Set(payload.viewUserIds.concat(this.currentUser().userId))]
        : []
    }

    if (payload.viewId > 0 && isViewCreatedByCurrentUser) {
      payload.viewUserIds = [
        ...new Set(payload.viewUserIds.concat(this.currentUser().userId)),
      ]
    }

    this.documentViewFacade.addOrUpdateView(payload, shouldLoadAndApply)

    // Reset the breadcrumb states if the view is loads and applied
    if (shouldLoadAndApply) {
      this.#resetBreadcrumbStates()
    }
  }

  #resetFormStateAfterDialogDisposal(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetDocumentViewStates()
    })
  }

  #selectAddOrUpdateViewResponses(): void {
    combineLatest([
      this.documentViewFacade.selectAddOrUpdateViewSuccessResponse$,
      this.documentViewFacade.selectAddOrUpdateViewErrorResponse$,
      this.documentViewFacade.selectIsViewAddOrUpdateLoading$,
    ])
      .pipe(
        filter(
          ([success, error, isLoading]) =>
            Boolean(success || error) && !isLoading
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message

        this.#showMessage(message, { style })

        if (isError) {
          return
        }

        this.#resetFormStateAfterDialogDisposal()

        this.dialogRef.close()

        // When the user saves a view, we need to fetch the user default view again and reset the search input controls
        this.viewFacade.isViewManuallyChanged.set(true)
        this.viewFacade.fetchUserDefaultView(this.projectId)
        this.searchFacade.resetSearchInputControls()
      })
  }

  #filterInvalidConditions(): void {
    const managerWorker = new ConditionStackManagerWorkerService()
    managerWorker
      .getFilteredConditions(
        JSON.parse(this.viewPayload.viewExpressionJson || '[]')
      )
      .then((conditions) => {
        this.filteredConditions = conditions
        managerWorker.terminate()
      })
  }

  #selectViewFormChange(): void {
    this.documentViewFacade.selectCurrentFormData$
      .pipe(
        // debounceTime(500),
        filter((view) => !!view),
        takeUntil(this.toDestroy$)
      )
      .subscribe((changedFormData) => {
        this.viewPayload = changedFormData
        this.#filterInvalidConditions()
      })
  }

  #fetchFields(): void {
    this.fieldFacade.fetchAllPermittedFields(this.projectId)
  }

  #fetchSearchFields(): void {
    this.documentViewFacade.fetchSearchFields()
  }
}
