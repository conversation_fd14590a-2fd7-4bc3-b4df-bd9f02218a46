<kendo-grid
  #grid
  venioDynamicHeight
  [isKendoDialog]="true"
  [kendoGridBinding]="bulkCodingData"
  [loading]="isBulkCodingValuesLoading$ | async"
  [resizable]="true"
  [sortable]="true"
  [autoSize]="true"
  [pageable]="{ type: 'numeric', position: 'top' }"
  [pageSize]="pageSize"
  [trackBy]="codingListTrackByFn"
  (dataStateChange)="onDataStateChange()">
  <ng-template kendoGridNoRecordsTemplate>
    <div class="t-flex t-h-min t-w-full">
      <span class="t-text-[#000000BC] t-text-[16px]">No records found</span>
    </div>
  </ng-template>
  <ng-template kendoPagerTemplate>
    <kendo-grid-spacer></kendo-grid-spacer>
    <venio-pagination
      [disabled]="totalHitCount() === 0"
      [totalRecords]="totalHitCount()"
      [pageSize]="pageSize"
      [showPageJumper]="false"
      [showPageSize]="true"
      [showRowNumberInputBox]="true"
      (pageChanged)="pageChanged($event)"
      (pageSizeChanged)="pageSizeChanged($event)"
      class="t-px-5 t-block t-py-2">
    </venio-pagination>
  </ng-template>
  <kendo-grid-column
    *ngFor="let field of headers"
    [field]="field"
    headerClass="t-text-primary">
    <ng-template kendoGridHeaderTemplate let-column>
      <span kendoTooltip [title]="field">{{ field }}</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <span kendoTooltip [title]="dataItem[field]">{{ dataItem[field] }}</span>
    </ng-template>
  </kendo-grid-column>
</kendo-grid>
