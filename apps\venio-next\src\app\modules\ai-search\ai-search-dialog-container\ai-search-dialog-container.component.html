<kendo-dialog-titlebar (close)="actionClicked('close')">
  <div class="t-flex t-w-[65%] t-justify-between">
    <div class="t-block">
      <span
        [tabindex]="-1"
        venioSvgLoader
        svgUrl="assets/svg/icon-ai-smooth-handmade-stars.svg"
        height="30px"
        class="t-w-16 t-h-[51px]"></span>
    </div>
  </div>
</kendo-dialog-titlebar>

<venio-ai-search-message-box />

<div
  *ngIf="hasSummaryList()"
  #containerEl
  class="t-flex t-w-full t-mt-5 t-transition-all t-ease-in t-delay-300 t-gap-[2%] v-hide-scrollbar t-overflow-x-hidden t-relative">
  <div
    class="t-flex t-flex-col t-rounded-md t-border-[1px] t-border-[#cccccc] t-px-3"
    [ngStyle]="{ 'width.px': containerEl.offsetWidth / 2 }"
    #doc_container>
    <kendo-tabstrip (tabSelect)="tabSelect($event)" [keepTabContent]="true">
      <kendo-tabstrip-tab title="Progress" [selected]="true">
        <ng-template kendoTabContent>
          <venio-ai-search-progress />
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="Document Summary">
        <ng-template kendoTabContent>
          <div
            class="t-relative t-overflow-auto"
            [ngStyle]="{ 'height.px': doc_container.offsetHeight - 50 }">
            <venio-ai-search-document-list />
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>
  </div>
  <venio-ai-search-document-summary
    [ngStyle]="{ 'width.px': containerEl.offsetWidth / 2 }" />
</div>

<kendo-dialog-actions
  class="t-transition-all t-ease-in t-delay-300 v-hide-scrollbar"
  [ngClass]="{
    't-h-0 t-opacity-0 t-overflow-y-hidden': !hasSummaryList(),
    't-h-[70.4px] t-opacity-100': hasSummaryList()
  }">
  <div class="t-flex t-gap-4 t-justify-end" *ngIf="hasSummaryList()">
    <button
      kendoButton
      *ngIf="allFileIds()?.[0]"
      (click)="actionClicked('done')"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      DONE
    </button>
  </div>
</kendo-dialog-actions>
