<div class="t-flex t-flex-col t-w-full t-h-full" #container>
  <kendo-grid
    [kendoGridBinding]="isSavedSearch ? savedSearchHistory : searchLogHistory"
    kendoGridSelectBy="id"
    [loading]="isSearchHistoryLoading$ | async"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="true"
    (sortChange)="onSortChange($event)"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    [skip]="skip"
    [height]="
      isUsedInPanel ? container.scrollHeight - 15 : container.clientHeight - 5
    ">
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total">
      <kendo-grid-spacer></kendo-grid-spacer>

      <venio-pagination
        [disabled]="totalPages === 0"
        [totalRecords]="
          isSavedSearch
            ? totalSavedSearchHistoryRecords
            : totalSearchHistoryRecords
        "
        [pageSize]="pageSize"
        [showPageJumper]="false"
        [showPageSize]="true"
        [showRowNumberInputBox]="false"
        (pageChanged)="pageChanged($event)"
        (pageSizeChanged)="pageSizeChanged($event)"
        class="t-px-5 t-block t-py-2">
      </venio-pagination>
    </ng-template>

    <kendo-grid-column
      *ngIf="isSavedSearch"
      headerClass="t-text-primary"
      field="searchName"
      title="Search Name"
      [width]="180">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="searchExpression"
      title="Search Expression"
      [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          kendoTooltip
          class="t-cursor-default"
          [title]="dataItem.searchExpression">
          {{ dataItem.searchExpression }}</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="dynamicFolder"
      title="Dynamic Folder"
      [width]="180">
    </kendo-grid-column>

    <kendo-grid-column
      *ngIf="!isUsedInPanel"
      headerClass="t-text-primary"
      field="searchedBy"
      title="Search By"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false">
    </kendo-grid-column>
    <kendo-grid-column
      *ngIf="!isUsedInPanel"
      headerClass="t-text-primary"
      field="searchedDate"
      title="Search Date"
      [width]="130">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.searchedDate | date : 'MM/dd/yyyy hh:mm a' }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      *ngIf="!isUsedInPanel"
      headerClass="t-text-primary"
      field="includeFamily"
      title="Is included Family"
      [width]="100">
    </kendo-grid-column>

    <kendo-grid-column
      *ngIf="!isUsedInPanel"
      headerClass="t-text-primary"
      field="searchDupOption"
      title="Duplicate Option"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      *ngIf="!isUsedInPanel"
      headerClass="t-text-primary"
      field="totalHits"
      title="Hit Count"
      [width]="120">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="isSqlMode"
      title="Is Query Mode"
      [width]="120">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      title="Action"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex">
          @for (icon of svgIconForGridControls; track icon.actionType) {
          <span
            [title]="
              icon.actionType === 'SEARCH' &&
              isMultilineExpression(dataItem.searchExpression)
                ? 'Multiline search cannot perform in review'
                : ''
            "
            kendoTooltip
            class="t-inline-block">
            <button
              kendoButton
              #actionGrid
              *venioHasUserGroupRights="icon.allowedPermission"
              class="!t-p-[0.3rem] t-w-1/2"
              [disabled]="
                icon.actionType === 'SEARCH' &&
                isMultilineExpression(dataItem.searchExpression)
              "
              (click)="searchHistoryActionClicked(dataItem, icon.actionType)"
              fillMode="clear"
              size="none">
              <span
                [parentElement]="actionGrid.element"
                venioSvgLoader
                hoverColor="#FFBB12"
                color="#979797"
                [svgUrl]="icon.iconPath"
                height="0.9rem"
                width="1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </span>
          }
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
