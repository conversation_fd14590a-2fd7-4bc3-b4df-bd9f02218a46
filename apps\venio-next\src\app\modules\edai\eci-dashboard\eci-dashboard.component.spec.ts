import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EciDashboardComponent } from './eci-dashboard.component'

describe('EciDashboardComponent', () => {
  let component: EciDashboardComponent
  let fixture: ComponentFixture<EciDashboardComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EciDashboardComponent]
    }).compileComponents()

    fixture = TestBed.createComponent(EciDashboardComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
