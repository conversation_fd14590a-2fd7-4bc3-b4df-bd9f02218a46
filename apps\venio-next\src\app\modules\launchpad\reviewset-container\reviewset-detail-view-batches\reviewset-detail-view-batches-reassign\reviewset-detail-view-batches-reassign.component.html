<div class="t-mt-5">
  <div class="t-mt-4 t-flex t-gap-2">
    <div class="t-w-full">
      <kendo-treeview
        [nodes]="loadedReviewSetUserGroup()"
        textField="name"
        class="v-hide-scrollbar"
        kendoTreeViewExpandable
        kendoTreeViewFlatDataBinding
        idField="keyID"
        parentIdField="treeParentId"
        expandBy="keyID"
        [expandedKeys]="expandedKeys()"
        [extraSpacing]="70"
        venioDynamicHeight>
        <ng-template kendoTreeViewNodeTemplate let-dataItem>
          <div class="t-flex t-items-center t-gap-2">
            @if(!dataItem.treeParentId){
            <span class="t-font-semibold">
              {{ dataItem.name }}
            </span>
            } @else {
            <span class="t-inline-flex t-items-center t-gap-2">
              <input
                type="radio"
                size="small"
                kendoRadioButton
                name="treeViewRadio"
                [attr.id]="'radio-' + dataItem.keyId"
                [value]="dataItem.userId"
                [ngModel]="selectedReviewer()"
                (ngModelChange)="selectedReviewer.set($event)" />
            </span>
            <kendo-label
              [for]="'radio-' + dataItem.keyId"
              [text]="dataItem.name"></kendo-label>
            }
          </div>
        </ng-template>
      </kendo-treeview>
    </div>
  </div>

  <!-- footer-->
  <div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
    <button
      kendoButton
      class="v-custom-show-title-disabled-btn v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save"
      (click)="actionButtonClick(commonActionTypes.SAVE)">
      <span kendoTooltip> SAVE </span>
    </button>
    <button
      data-qa="cancel"
      kendoTooltip
      title="cancel"
      kendoButton
      themeColor="dark"
      fillMode="outline"
      (click)="actionButtonClick(commonActionTypes.CANCEL)">
      CANCEL
    </button>
  </div>
</div>
