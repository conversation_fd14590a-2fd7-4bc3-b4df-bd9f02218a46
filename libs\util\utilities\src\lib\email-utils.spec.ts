import { EmailUtil } from './email-util'

describe('Email Validation', () => {
  it('should reject empty email input', () => {
    // GIVEN: An empty email string
    const email = ''

    // WHEN: The validateEmail function is called with the empty email
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })

  it('should reject email missing "@" symbol', () => {
    // GIVEN: An email string without "@"
    const email = 'invalidemail.com'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })

  it('should reject email with invalid TLD', () => {
    // GIVEN: An email with an invalid TLD (e.g., a single character TLD)
    const email = 'user@domain.c'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })

  it('should reject email when local part longer than 64 characters', () => {
    // GIVEN: An email with a local part longer than 64 characters
    const email = 'a'.repeat(65) + '@domain.com'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })
  it('should reject email when domain part longer than 255 characters', () => {
    // GIVEN: An email with a domain part longer than 255 characters
    const email = 'user@' + 'a'.repeat(256) + '.com'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })

  it('should reject email when total length exceeds maximum', () => {
    // GIVEN: An email with a total length exceeding 320 characters
    const email = 'a'.repeat(65) + '@' + 'a'.repeat(256) + '.com'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return false
    expect(result).toBe(false)
  })

  it('should accept standard email format', () => {
    // GIVEN: A valid email
    const email = '<EMAIL>'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return true
    expect(result).toBe(true)
  })

  it('should accept email with subdomains', () => {
    // GIVEN: A valid email with subdomains
    const email = '<EMAIL>'

    // WHEN: The validateEmail function is called
    const result = EmailUtil.validateEmail(email)

    // THEN: It should return true
    expect(result).toBe(true)
  })
})
