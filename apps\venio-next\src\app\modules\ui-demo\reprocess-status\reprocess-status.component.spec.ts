import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReprocessStatusComponent } from './reprocess-status.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReprocessStatusComponent', () => {
  let component: ReprocessStatusComponent
  let fixture: ComponentFixture<ReprocessStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReprocessStatusComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReprocessStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
