import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerFieldsComponent } from './document-view-designer-fields.component'
import { provideMockStore } from '@ngrx/store/testing'
import { FieldFacade } from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentViewDesignerFieldsComponent', () => {
  let component: DocumentViewDesignerFieldsComponent
  let fixture: ComponentFixture<DocumentViewDesignerFieldsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerFieldsComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        FieldFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerFieldsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
