import { TestBed, ComponentFixture } from '@angular/core/testing'
import { DownloadProductionComponent } from './download-production.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { CommonModule } from '@angular/common'
import { ProgressBarModule } from '@progress/kendo-angular-progressbar'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('DownloadProductionComponent', () => {
  let component: DownloadProductionComponent
  let fixture: ComponentFixture<DownloadProductionComponent>

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        DownloadProductionComponent,
        CommonModule,
        HttpClientTestingModule,
        ProgressBarModule,
        ButtonModule,
        IconsModule,
      ],
      providers: [
        {
          provide: NotificationService,
          useValue: {
            show: jest.fn(),
          },
        },
      ],
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(DownloadProductionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create the component and initialize correctly', () => {
    expect(component).toBeTruthy()
    expect(component.downloadProgressInfo()).toEqual({
      progress: 0,
      downloadSpeed: '0',
      remainingTime: '0',
      isDownloadCompleted: false,
      totalFileSize: '0',
    })
    expect(component.isDownloadStarted()).toBe(false)
  })
})
