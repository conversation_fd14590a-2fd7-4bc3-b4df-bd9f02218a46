{"name": "email-thread", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/email-thread/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/email-thread/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/feature/email-thread/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/email-thread/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/email-thread/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}