import { Component, computed, inject, input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { ReviewSetForm } from '@venio/shared/models/interfaces'
import { CheckBoxDirective } from '@progress/kendo-angular-inputs'
import {
  DropDownListComponent,
  ItemTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { LabelComponent } from '@progress/kendo-angular-label'
import { ReviewsetFormService } from '../reviewset-form.service'

@Component({
  selector: 'venio-reviewset-form-document-sort-options',
  standalone: true,
  imports: [
    CommonModule,
    CheckBoxDirective,
    DropDownListComponent,
    LabelComponent,
    ReactiveFormsModule,
    ItemTemplateDirective,
  ],
  templateUrl: './reviewset-form-document-sort-options.component.html',
  styleUrl: './reviewset-form-document-sort-options.component.scss',
})
export class ReviewsetFormDocumentSortOptionsComponent {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  public readonly reviewSetFormService = inject(ReviewsetFormService)

  public readonly isCustomFieldsLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isCustomFieldsLoading
  )
}
