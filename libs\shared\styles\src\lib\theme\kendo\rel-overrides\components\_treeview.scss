@layer {
  // for note thread tree
  kendo-treeview {

    &.custom-treeview-template{
      .k-treeview-lines{
        .k-treeview-item{
          .k-treeview-leaf, .k-treeview-leaf-text {
            @apply t-flex t-flex-1 #{!important};
          }
        }
      }
    }
    // default changes
    .k-treeview-lines{
      .k-treeview-item[aria-checked="true"] {
        .k-treeview-leaf-text {
          @apply t-text-[var(--tb-kendo-primary-100)] t-font-semibold #{!important};
        }
      }
      .k-treeview-item{
        .k-treeview-leaf{
          @apply t-py-1 #{!important};
          &.k-focus{
            @apply t-shadow-none #{!important};
          }
        }
        .k-svg-i-caret-alt-down{
          svg {
            @apply t-hidden #{!important};
          }
          &::before {
            content: ' ';
            width: 20px;
            height: 20px;
            background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
              no-repeat center/16px 16px;
          }
        }
    
        .k-svg-i-caret-alt-right {
          svg {
            @apply t-hidden #{!important};
          }
          &::before {
            content: ' ';
            width: 20px;
            height: 20px;
            background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
              no-repeat center/16px 16px;
            transform: rotate(268deg);
          }
        }
      }
    }
    

    .k-treeview-group{
      .k-treeview-item{
          .k-treelist-toggle{
            .k-svg-i-caret-alt-down{
              svg {
                @apply t-hidden #{!important};
              }
              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                  no-repeat center/16px 16px;
              }
            }

            .k-svg-i-caret-alt-right {
              svg {
                @apply t-hidden #{!important};
              }
              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                  no-repeat center/16px 16px;
                transform: rotate(268deg);
              }
            }
          }
      }
    }
    // For tags custom styles
    &.v-custom-treeview {
      .k-treeview-leaf {
        @apply t-w-full;
        .k-treeview-leaf-text {
          @apply t-w-full;
        }
      }
      .k-treeview-bot {
        @apply t-block;
      }
    }

    &.v-note-thread-tree {
      .k-treeview-leaf {
        @apply t-p-0 t-flex-1 t-w-full t-min-w-[300px];

        .k-treeview-leaf-text {
          @apply t-w-full;
        }

        &:hover {
          @apply t-bg-white #{!important};
        }
      }

      .k-treeview-toggle {
        .k-icon {
          @apply t-text-[#979797];
        }
      }

      .t-text-wrap {
        text-wrap: wrap;
      }
    }

    &.v-custom-listview-transcript {
      .k-listview-item {
        &:hover {
          @apply t-bg-[#cccccc] #{!important};
        }
      }
    }

    &.v-ai-job-tree {
      .k-treeview-lines {
        .k-treeview-group {
          .k-treeview-item {
            @apply t-pl-1 #{!important};
          }
        }        
      }
    }
  }
}
