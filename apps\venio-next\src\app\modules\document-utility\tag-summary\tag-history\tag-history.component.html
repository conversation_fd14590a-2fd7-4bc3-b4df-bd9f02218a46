<div class="t-flex t-mt-2">
  <kendo-grid
    [data]="tagHistory"
    scrollable="virtual"
    [resizable]="true"
    [size]="'small'"
    [loading]="isTagHistoryLoading"
    class="v-tag-history-grid !t-max-h-[calc(100vh_-_19.5rem)] t-min-h-[15rem]"
    [trackBy]="tagHistroryTrackByFn">
    <kendo-grid-column
      headerClass="t-text-primary"
      field="userName"
      title="Author"
      [width]="120">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="loggedDateTime"
      title="Date"
      [width]="125">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="actionType"
      title="Tag Type"
      [width]="100">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          *ngIf="
            dataItem.actionType === 'FILES_ADDED' ||
            dataItem.actionType === 'FILES_REMOVED' ||
            dataItem.actionType === 'FILES_MOVED'
          "
          title="Folder"
          >Folder</span
        >
        <span
          *ngIf="
            dataItem.actionType === 'TAG' || dataItem.actionType === 'UNTAG'
          "
          title="Tag"
          >Tag</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="tagActionDescriptions"
      title="Tag Description">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          *ngIf="
            dataItem.actionType === 'TAG' || dataItem.actionType === 'UNTAG'
          ">
          <div
            *ngFor="let tagAction of dataItem.tagActionDescriptions"
            class="t-flex t-flex-col t-gap-1">
            <div class="t-font-bold">{{ tagAction.groupName }} :</div>
            <div class="t-flex t-flex-col t-pl-4 t-gap-1 t-mb-3">
              <div
                class="t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap"
                *ngFor="let tagName of tagAction.tagNames">
                <span>{{ tagName.name }} :</span>
                <span class="t-pl-3">{{ tagName.actionType }}</span>
              </div>
            </div>
          </div>
        </div>
        <div
          *ngIf="
            dataItem.actionType === 'FILES_ADDED' ||
            dataItem.actionType === 'FILES_REMOVED' ||
            dataItem.actionType === 'FILES_MOVED'
          ">
          <div
            *ngFor="let folderAction of dataItem.folderActionDescriptions"
            class="t-flex t-flex-col t-gap-1">
            <div class="t-flex t-flex-col t-pl-4 t-gap-1 t-mb-3">
              <div
                class="t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap"
                *ngFor="let folder of folderAction.folderNames">
                <span
                  title="Removed from {{ folder.name }}"
                  *ngIf="folder.actionType === 'FILES_REMOVED'"
                  >Removed from {{ folder.name }}</span
                >
                <span
                  title="Moved from {{ folder.name }}"
                  *ngIf="folder.actionType === 'FILES_MOVED'"
                  >Moved from {{ folder.name }}</span
                >
                <span
                  title="Added to {{ folder.name }}"
                  *ngIf="folder.actionType === 'FILES_ADDED'"
                  >Added to {{ folder.name }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="tagCommentDescriptions"
      title="Tags Comment">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          *ngFor="let tagComment of dataItem.tagCommentDescriptions"
          class="t-flex t-flex-col t-gap-1">
          <div class="t-font-bold" *ngIf="tagComment.tagNames.length > 0">
            {{ tagComment.groupName }} :
          </div>
          <div class="t-flex t-flex-col t-pl-4 t-gap-1 t-mb-3">
            <div
              class="t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap"
              *ngFor="let tagName of tagComment.tagNames">
              <span>{{ tagName.name }} :</span>
              <span class="t-pl-3">{{ tagName.comment }}</span>
            </div>
          </div>
        </div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="eventComment"
      title="Event Comment"
      [width]="150">
    </kendo-grid-column>
  </kendo-grid>
</div>
