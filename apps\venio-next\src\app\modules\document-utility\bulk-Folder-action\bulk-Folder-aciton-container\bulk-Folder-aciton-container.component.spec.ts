import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkFolderAcitonContainerComponent } from './bulk-Folder-aciton-container.component'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BulkFolderAcitonContainerComponent', () => {
  let component: BulkFolderAcitonContainerComponent
  let fixture: ComponentFixture<BulkFolderAcitonContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkFolderAcitonContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkFolderAcitonContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
