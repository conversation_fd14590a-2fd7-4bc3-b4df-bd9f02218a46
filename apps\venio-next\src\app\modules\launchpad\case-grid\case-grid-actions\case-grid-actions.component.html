<kendo-buttongroup>
  <button
    [disabled]="isFavoriteProjectToggleLoading()"
    kendoButton
    kendoTooltip
    title="Favorite"
    #favorite
    (click)="actionButtonClick(commonActionTypes.FAVOURITE)"
    class="t-rounded-tr-none t-rounded-br-none t-w-[1.95rem] hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
    size="none"
    [ngClass]="favoriteButtonClass()">
    <kendo-loader
      *ngIf="isFavoriteProjectToggleLoading()"
      size="small"
      themeColor="error" />
    <span
      *ngIf="!isFavoriteProjectToggleLoading()"
      [parentElement]="favorite.element"
      venioSvgLoader
      [hoverColor]="hoverColor()"
      [color]="color()"
      svgUrl="assets/svg/icon-heart-solid-fill.svg"
      height="0.75rem"
      width="0.8rem">
      <kendo-loader size="small" />
    </span>
  </button>

  <button
    kendoTooltip
    title="Review"
    kendoButton
    [disabled]="
      isMediaStatusLoading() && activeAction() === commonActionTypes.REVIEW
    "
    #review
    *ngIf="canViewReviewPage()"
    class="!t-py-[0.38rem] !t-px-[0.5rem] t-h-[26px] t-rounded-tr-none t-rounded-br-none t-w-[1.95rem] hover:t-border-[#9BD2A7] hover:t-bg-[#9BD2A7]"
    (click)="actionButtonClick(commonActionTypes.REVIEW)"
    size="none">
    <kendo-loader
      *ngIf="
        isMediaStatusLoading() && activeAction() === commonActionTypes.REVIEW
      "
      size="small"
      themeColor="error" />
    <span
      *ngIf="
        !isMediaStatusLoading() || activeAction() !== commonActionTypes.REVIEW
      "
      [parentElement]="review.element"
      venioSvgLoader
      applyEffectsTo="fill"
      hoverColor="#FFFFFF"
      color="#979797"
      svgUrl="assets/svg/icon-rate-review-note.svg"
      height="0.75rem"
      width="0.8rem">
      <kendo-loader size="small"></kendo-loader>
    </span>
  </button>

  <button
    kendoTooltip
    title="Analyze"
    kendoButton
    [disabled]="
      isMediaStatusLoading() && activeAction() === commonActionTypes.ANALYZE
    "
    #analyze
    *ngIf="canViewAnalyzePage()"
    class="!t-py-[0.38rem] !t-px-[0.5rem] t-h-[26px] t-rounded-tr-none t-rounded-br-none t-w-[1.95rem] hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
    (click)="actionButtonClick(commonActionTypes.ANALYZE)"
    size="none">
    <kendo-loader
      *ngIf="
        isMediaStatusLoading() && activeAction() === commonActionTypes.ANALYZE
      "
      size="small"
      themeColor="error" />
    <span
      *ngIf="
        !isMediaStatusLoading() || activeAction() !== commonActionTypes.ANALYZE
      "
      [parentElement]="analyze.element"
      venioSvgLoader
      applyEffectsTo="fill"
      hoverColor="#FFFFFF"
      color="#979797"
      svgUrl="assets/svg/icon-table-pie-chart.svg"
      height="0.75rem"
      width="0.8rem">
      <kendo-loader size="small"></kendo-loader>
    </span>
  </button>
</kendo-buttongroup>

@for(button of buttonsConfig(); track button.title; let index = $index) { @if
(getActionDropdownData(index)?.length > 0) {
<kendo-dropdownbutton
  kendoTooltip
  [title]="button.title"
  [data]="getActionDropdownData(index)"
  class="v-custom-dropdown-case-btn t-w-[31.2px] t-max-h-[25.8px] hover:!t-border-error hover:t-text-white"
  [ngClass]="index === 1 ? 'v-custom-dropdown-case-btn2' : ''"
  (itemClick)="dropdownActionItemClick($event)"
  [imageUrl]="getIconPath(rowDataItem().projectId, index)"
  (mouseenter)="setHoverState(index, true)"
  (mouseleave)="setHoverState(index, false)"
  [popupSettings]="{
    popupClass: 'v-custom-dropdown-case-title ' + getDynamicClass(index),
    animate: true
  }">
  <ng-template kendoDropDownButtonItemTemplate let-dropdownItem>
    <span>{{ dropdownItem.text }}</span>
  </ng-template>
</kendo-dropdownbutton>
} }
