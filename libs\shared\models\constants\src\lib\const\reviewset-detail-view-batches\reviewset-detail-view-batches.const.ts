import { CommonActionTypes } from '../../enums/common-action-types'

export const REVIEWSET_BATCH_DIALOG_MESSAGES = {
  [CommonActionTypes.DELETE]: {
    single: (batchName: string): string =>
      `Are you sure you want to delete ${batchName}`,
    multiple: 'Are you sure you want to delete the selected batches',
  },
  [CommonActionTypes.REBATCH]: {
    default:
      'Re-batching will commit the reviewed documents and create a new batch for remaining documents. Do you want to continue?',
  },
}

export const REVIEWSET_BATCH_SUCCESS_MESSAGES = {
  [CommonActionTypes.DELETE]: {
    single: (batchName: string): string =>
      `Batch ${batchName} is deleted successfully.`,
    multiple: 'Selected batches has been deleted successfully',
  },
  [CommonActionTypes.REBATCH]: {
    single: (batchName: string): string =>
      `Batch ${batchName} is re-batched successfully.`,
    multiple: 'Selected batches has been re-batched successfully',
  },
  [CommonActionTypes.REASSIGN]: {
    single: (batchName: string): string =>
      `Batch ${batchName} is reassigned successfully.`,
  },
}

export const REVIEWSET_BATCH_SUCCESS_TITLE = {
  [CommonActionTypes.DELETE]: 'Delete',
  [CommonActionTypes.REBATCH]: 'Re-batch',
  [CommonActionTypes.REASSIGN]: 'Re-assign',
}

export const DAY_ORDINAL_SUFFIX = (day: number): string => {
  if (day % 10 === 1 && day !== 11) return 'st'
  if (day % 10 === 2 && day !== 12) return 'nd'
  if (day % 10 === 3 && day !== 13) return 'rd'
  return 'th'
}

export const ROUND_TO_TWO_DECIMALS = (val: number): number => {
  return Number.isNaN(+val) ? 0 : Math.abs(+val.toFixed(2))
}
