import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewBatchesGridComponent } from './reviewset-detail-view-batches-grid.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { ReviewSetBatchRequestModel } from '@venio/shared/models/interfaces'

describe('ReviewsetDetailViewBatchesGridComponent', () => {
  let component: ReviewsetDetailViewBatchesGridComponent
  let fixture: ComponentFixture<ReviewsetDetailViewBatchesGridComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewBatchesGridComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selecteSelectedBatchDetail$: of([]),
            selectReviewSetBatchDetail$: of([]),
            selectIsReviewSetBatchLoading$: of(false),
            selectReviewSetBatchRequestInfo$: of(
              {} as ReviewSetBatchRequestModel
            ),
            resetProjectState: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewBatchesGridComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
