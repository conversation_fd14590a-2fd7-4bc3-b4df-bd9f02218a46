import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BrowsePanelActionControlsComponent } from './browse-panel-action-controls.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BrowserPanelActionControlsComponent', () => {
  let component: BrowsePanelActionControlsComponent
  let fixture: ComponentFixture<BrowsePanelActionControlsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BrowsePanelActionControlsComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(BrowsePanelActionControlsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
