.dataviz-wrapper {
  padding: 1rem;
  background: var(--viz-bg, #fcfbff);
  border: 1px solid var(--viz-border, #ccc9da);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.row {
  display: flex;
  align-items: center;
}

.column {
  display: flex;
  flex-direction: column;
}

.gap-48 {
  gap: 48px;
}

.docs-counts-wrapper {
  gap: 16px;
}

.no-m {
  margin: 0;
}

.xl-text {
  font-size: 40px;
  font-weight: bold;
  color: var(--viz-text, #0f0f0f);
}

.button-wrapper {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  justify-content: center;
}
