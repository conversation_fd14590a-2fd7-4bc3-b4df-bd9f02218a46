import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormDocumentSortOptionsComponent } from './reviewset-form-document-sort-options.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { createMockReviewSetForm } from '../review-set-form.mock'
import { ReviewSetPayloadService } from '../reviewset-payload.service'

describe('ReviewsetFormDocumentSortOptionsComponent', () => {
  let component: ReviewsetFormDocumentSortOptionsComponent
  let fixture: ComponentFixture<ReviewsetFormDocumentSortOptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormDocumentSortOptionsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideMockStore(),
        ReviewsetFormService,
        ReviewSetPayloadService,
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormDocumentSortOptionsComponent)
    component = fixture.componentInstance
    const formBuilder = TestBed.inject(FormBuilder)
    fixture.componentRef.setInput(
      'reviewSetForm',
      createMockReviewSetForm(formBuilder)
    )
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
