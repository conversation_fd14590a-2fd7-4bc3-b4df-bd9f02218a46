import { Observable, Subject } from 'rxjs'

/**
 * In some scenario, we might need to perform a complex calculation of data processing in
 * the client side where we are going to get unresponsiveness of browser or need to wait until
 * it's done. By using worker API, we can run such action as a background task within the worker thread.
 *
 * We've created an inline worker class to use it in TS with the combination of rxjs so we can write our `fn`
 * and supply to the inline worker class `ctor` to do our job in the background task.
 *
 * @see : https://developer.mozilla.org/en-US/docs/Web/API/Worker
 */
export class InlineWorker {
  private readonly worker: Worker

  private onMessage = new Subject<MessageEvent>()

  private onError = new Subject<ErrorEvent>()

  /**
   * worker constructor to run inline function as a background task.
   * @param func Inline function to run within worker
   */
  constructor(func: any) {
    const WORKER_ENABLED = !!Worker

    if (WORKER_ENABLED) {
      /**
       * converts a function to a string and creates ObjectURL which will be passed to
       *  a worker class through a constructor.
       */
      const functionBody = func
        .toString()
        .replace(/^[^{]*{\s*/, '')
        .replace(/\s*}[^}]*$/, '')
      // passed to a worker class through a constructor.
      this.worker = new Worker(
        URL.createObjectURL(
          new Blob([functionBody], { type: 'text/javascript' })
        )
      )

      this.worker.onmessage = (data): any => {
        this.onMessage.next(data)
      }

      this.worker.onerror = (data): any => {
        this.onError.next(data)
      }
    } else {
      throw new Error('WebWorker is not enabled')
    }
  }

  public postMessage = (data: any): any => this.worker.postMessage(data)

  public onmessage = (): Observable<MessageEvent> =>
    this.onMessage.asObservable()

  /**
   * Fires when an error happens in the worker.
   */
  public onerror = (): Observable<ErrorEvent> => this.onError.asObservable()

  /**
   * Terminates the current instance worker.
   */
  public terminate(): any {
    if (this.worker) {
      this.worker.terminate()
    }
  }
}
