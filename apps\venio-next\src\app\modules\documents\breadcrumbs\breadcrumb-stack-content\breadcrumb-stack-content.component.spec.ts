import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbStackContentComponent } from './breadcrumb-stack-content.component'
import { NO_ERRORS_SCHEMA, PLATFORM_ID } from '@angular/core'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

jest.doMock('@progress/kendo-angular-dialog', () => ({
  DialogService: jest.fn(),
}))

describe('BreadcrumbStackContentComponent', () => {
  let component: BreadcrumbStackContentComponent
  let fixture: ComponentFixture<BreadcrumbStackContentComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbStackContentComponent, DialogsModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        BreadcrumbFacade,
        provideMockStore({}),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbStackContentComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
