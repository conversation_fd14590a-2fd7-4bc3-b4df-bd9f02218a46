import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SummaryChartComponent } from './summary-chart.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('SummaryChartComponent', () => {
  let component: SummaryChartComponent
  let fixture: ComponentFixture<SummaryChartComponent>

  const mockReviewSetFacade = {
    selectIsReviewSetBatchSummaryLoading$: of(false),
    selectReviewSetBatchSummary$: of(undefined),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SummaryChartComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(SummaryChartComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
