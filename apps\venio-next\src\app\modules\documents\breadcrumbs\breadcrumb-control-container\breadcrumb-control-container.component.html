<div
  class="v-custom-condition t-flex t-border t-border-[#cccccc] t-rounded t-overflow-hidden t-relative t-min-h-[4rem]">
  <div class="v-custom-label t-text-white t-w-[25px] t-text-center">
    <kendo-dropdownbutton
      data-qa="not4"
      [data]="groupOperators"
      (itemClick)="changeGroupOperator($event)"
      fillMode="none"
      class="v-custom-operator-menu">
      {{ selectedOperator }}
    </kendo-dropdownbutton>
  </div>
  <div class="t-w-[calc(100%_-_25px)] t-relative">
    <ng-content></ng-content>
  </div>
</div>
