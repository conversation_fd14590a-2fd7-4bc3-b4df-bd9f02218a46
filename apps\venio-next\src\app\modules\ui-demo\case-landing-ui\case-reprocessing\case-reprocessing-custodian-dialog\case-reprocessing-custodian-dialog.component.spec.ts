import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseReprocessingCustodianDialogComponent } from './case-reprocessing-custodian-dialog.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseReprocessingCustodianDialogComponent', () => {
  let component: CaseReprocessingCustodianDialogComponent
  let fixture: ComponentFixture<CaseReprocessingCustodianDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseReprocessingCustodianDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseReprocessingCustodianDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
