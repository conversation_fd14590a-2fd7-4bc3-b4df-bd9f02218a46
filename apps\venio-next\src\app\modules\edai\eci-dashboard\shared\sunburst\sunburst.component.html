<div class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm sunburst-wrapper">
  <app-title-and-download [title]="title"></app-title-and-download>
  <div class="t-w-full t-min-h-[300px] t-relative">
    <app-center-text *ngIf="showLegend"></app-center-text>
    <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"
      (plotlyClick)="onChartClick($event)"></plotly-plot>
  </div>
  <app-vertical-legends *ngIf="showLegend"></app-vertical-legends>
</div>