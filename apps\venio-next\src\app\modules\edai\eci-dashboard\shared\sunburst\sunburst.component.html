<div class="t-bg-white t-border t-border-gray-200 t-rounded-3xl t-p-7 t-flex t-flex-col t-gap-6 sunburst-wrapper">
  <app-title-and-download [title]="title"></app-title-and-download>
  <div class="t-w-full t-min-h-[400px] t-relative">
    <app-center-text *ngIf="showLegend"></app-center-text>
    <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"
      (plotlyClick)="onChartClick($event)"></plotly-plot>
  </div>
  <app-vertical-legends *ngIf="showLegend"></app-vertical-legends>
  <div class="t-mb-2"></div>
</div>