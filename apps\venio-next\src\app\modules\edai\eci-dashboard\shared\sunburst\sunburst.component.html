<div class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm sunburst-wrapper">
  <app-title-and-download [title]="title"></app-title-and-download>
  <div class="t-w-full t-flex t-flex-col t-gap-4">
    <div class="t-w-full t-h-[350px] t-relative t-flex t-justify-center t-items-center">
      <app-center-text *ngIf="showLegend"></app-center-text>
      <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config" (plotlyClick)="onChartClick($event)"
        class="t-w-full t-h-full">
      </plotly-plot>
    </div>
    <div class="t-w-full t-flex t-justify-center">
      <app-vertical-legends *ngIf="showLegend"></app-vertical-legends>
    </div>
  </div>
</div>