<div class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm sunburst-wrapper">
  <app-title-and-download [title]="title"></app-title-and-download>
  <div class="t-w-full t-h-[350px] t-relative t-overflow-hidden">
    <app-center-text *ngIf="showLegend"></app-center-text>
    <div class="t-w-full t-h-full">
      <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config" (plotlyClick)="onChartClick($event)"
        class="t-w-full t-h-full"></plotly-plot>
    </div>
  </div>
  <div class="t-min-h-[80px] t-flex t-items-center t-justify-center">
    <app-vertical-legends *ngIf="showLegend"></app-vertical-legends>
  </div>
</div>