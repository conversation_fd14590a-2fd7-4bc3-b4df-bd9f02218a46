{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "rxjs", "unused-imports", "prettier"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}], "semi": "off", "no-eq-null": "error", "valid-jsdoc": "warn", "no-alert": "warn", "no-console": "warn", "no-debugger": "error", "no-confusing-arrow": "warn", "no-dupe-args": "error", "no-var": "error", "no-caller": "warn", "no-const-assign": "error", "no-duplicate-case": "error", "no-duplicate-imports": "error", "no-else-return": "warn", "eqeqeq": "error", "complexity": ["warn", 35]}}, {"files": ["*.ts", "*.tsx"], "parserOptions": {"project": ["tsconfig.?*.json"]}, "extends": ["plugin:@nx/typescript", "plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "plugin:@typescript-eslint/recommended", "plugin:jest/recommended", "plugin:jest/style", "prettier", "plugin:prettier/recommended", "plugin:rxjs/recommended"], "rules": {"@angular-eslint/prefer-output-readonly": "error", "@angular-eslint/use-lifecycle-interface": "error", "@angular-eslint/contextual-lifecycle": "error", "@angular-eslint/no-lifecycle-call": "error", "@angular-eslint/no-output-native": "error", "@angular-eslint/component-max-inline-declarations": [2, {"animations": 100, "styles": 100, "template": 100}], "@angular-eslint/no-conflicting-lifecycle": "error", "@angular-eslint/no-pipe-impure": "error", "no-useless-escape": ["warn"], "newline-per-chained-call": ["error", {"ignoreChainWithDepth": 3}], "@typescript-eslint/no-var-requires": ["error"], "@typescript-eslint/no-empty-function": ["warn"], "@typescript-eslint/no-explicit-any": "warn", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["error", {"vars": "all", "varsIgnorePattern": "^_", "args": "none", "argsIgnorePattern": "^_"}], "@typescript-eslint/lines-between-class-members": ["warn"], "@typescript-eslint/no-this-alias": ["error", {"allowDestructuring": true, "allowedNames": ["self"]}], "@typescript-eslint/ban-ts-comment": ["error"], "@typescript-eslint/explicit-function-return-type": ["error"], "@typescript-eslint/no-misused-promises": ["error"], "@typescript-eslint/no-misused-new": ["error"], "@typescript-eslint/no-unnecessary-type-assertion": ["error"], "@typescript-eslint/no-unsafe-return": ["warn"], "@typescript-eslint/restrict-plus-operands": "warn", "@typescript-eslint/naming-convention": ["error", {"selector": "variableLike", "format": ["camelCase", "snake_case", "UPPER_CASE", "PascalCase"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "interface", "format": ["PascalCase"], "custom": {"regex": "^I[A-Z]", "match": false}}], "@typescript-eslint/explicit-member-accessibility": ["error", {"accessibility": "explicit", "overrides": {"accessors": "explicit", "constructors": "no-public", "methods": "explicit", "properties": "explicit", "parameterProperties": "explicit"}}], "@typescript-eslint/no-extra-semi": "off", "no-extra-semi": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {"@typescript-eslint/no-extra-semi": "off", "no-extra-semi": "off"}}]}