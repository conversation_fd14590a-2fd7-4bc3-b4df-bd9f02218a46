import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiSearchProgressComponent } from './ai-search-progress.component'
import { provideMockStore } from '@ngrx/store/testing'

describe('AiSearchProgressComponent', () => {
  let component: AiSearchProgressComponent
  let fixture: ComponentFixture<AiSearchProgressComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiSearchProgressComponent],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(AiSearchProgressComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
