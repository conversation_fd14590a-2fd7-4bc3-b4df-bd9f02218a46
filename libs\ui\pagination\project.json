{"name": "ui-pagination", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/ui/pagination/src", "prefix": "venio", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/ui/pagination"], "options": {"tsConfig": "libs/ui/pagination/tsconfig.lib.json", "project": "libs/ui/pagination/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/ui/pagination/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/ui/pagination/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui/pagination/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}