import { Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { KENDO_LAYOUT } from '@progress/kendo-angular-layout'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { DropDownButtonModule } from '@progress/kendo-angular-buttons'
import { filterIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { KENDO_INPUTS } from '@progress/kendo-angular-inputs'
import { KENDO_POPUP } from '@progress/kendo-angular-popup'
import { KENDO_LISTVIEW } from '@progress/kendo-angular-listview'
import { custodians } from './mock-data/custodians'
import { EciSummaryComponent } from './components/summary/summary.component'
import { EciRelevanceComponent } from './components/relevance/relevance.component'
import { EciSunburstComponent } from './components/sunburst/sunburst.component'
import { EciInappropriateContentComponent } from './components/inappropriate-content/inappropriate-content.component'
import { EciWordCloudComponent } from './components/word-cloud/word-cloud.component'
import { EciDataService } from './shared/data.service'

@Component({
  selector: 'venio-eci-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    KENDO_LAYOUT,
    KENDO_BUTTONS,
    KENDO_POPUP,
    KENDO_INPUTS,
    KENDO_LISTVIEW,
    DropDownButtonModule,
    EciSummaryComponent,
    EciRelevanceComponent,
    EciSunburstComponent,
    EciInappropriateContentComponent,
    EciWordCloudComponent
  ],
  templateUrl: './eci-dashboard.component.html',
  styleUrl: './eci-dashboard.component.scss'
})
export class EciDashboardComponent implements OnInit {
  isFocusedSectionOpened = false;

  constructor(private dataService: EciDataService) { }

  public custodians: any[] = custodians;
  public showFocused = true;
  public show = false;
  public showCustodianFilters = false;

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(val => {
      this.isFocusedSectionOpened = val;
      console.log('Focused section opened:', val);
    });
  }

  public onToggle(): void {
    this.show = !this.show;
  }

  public svgFilter: SVGIcon = filterIcon;

  public onCustodianClick(): void {
    this.showCustodianFilters = !this.showCustodianFilters;
  }
}
