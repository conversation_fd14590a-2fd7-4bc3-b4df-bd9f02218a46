import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '@progress/kendo-angular-buttons';
import { PopupComponent } from '@progress/kendo-angular-popup';
import { ListViewComponent, ItemTemplateDirective } from '@progress/kendo-angular-listview';
import { filterIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { custodians } from './mockData';
import { CheckboxListItemComponent } from './shared/checkbox-list-item/checkbox-list-item.component';
import { SummaryComponent } from './shared/summary/summary.component';
import { RelevanceComponent } from './shared/relevance/relevance.component';
import { SunburstComponent } from './shared/sunburst/sunburst.component';
import { InappropriateContentComponent } from './shared/inappropriate-content/inappropriate-content.component';
import { WordCloudComponent } from './shared/word-cloud/word-cloud.component';
import { FocusedSectionComponent } from './shared/focused-section/focused-section.component';
import { DataService } from './shared/data.service';

@Component({
  selector: 'venio-eci-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    PopupComponent,
    ListViewComponent,
    ItemTemplateDirective,
    SummaryComponent,
    CheckboxListItemComponent,
    RelevanceComponent,
    SunburstComponent,
    InappropriateContentComponent,
    WordCloudComponent,
    FocusedSectionComponent
  ],
  providers: [DataService],
  templateUrl: './eci-dashboard.component.html',
  styleUrls: ['./eci-dashboard.component.scss']
})
export class EciDashboardComponent implements OnInit {
  isFocusedSectionOpened = false;
  constructor(private dataService: DataService) { }

  public custodians: any[] = custodians;
  public showFocused = true;
  public show = false;
  public showCustodianFilters = false;

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(val => {
      this.isFocusedSectionOpened = val;
      console.log('Focused section opened:', val);
    });
  }

  public onToggle(): void {
    this.show = !this.show;
  }

  public svgFilter: SVGIcon = filterIcon;

  public onCustodianClick(): void {
    this.showCustodianFilters = !this.showCustodianFilters;
  }
}
