import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  ConditionGroup,
  GroupOperator,
  SearchTriggerSource,
} from '@venio/shared/models/interfaces'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

@Component({
  selector: 'venio-breadcrumb-stack-group-operator',
  standalone: true,
  imports: [CommonModule, DropDownsModule],
  templateUrl: './breadcrumb-stack-group-operator.component.html',
  styleUrl: './breadcrumb-stack-group-operator.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbStackGroupOperatorComponent {
  private readonly breadcrumbFacade = inject(BreadcrumbFacade)

  public readonly defaultGroupOperator = GroupOperator.AND

  public readonly groupOperator = [GroupOperator.AND, GroupOperator.OR]

  @Input()
  public conditionOperatorGroup: ConditionGroup

  public operatorValue<PERSON>hange(operator: GroupOperator): void {
    this.breadcrumbFacade.setLastSearchTriggerSource(
      SearchTriggerSource.AutoRun
    )
    this.breadcrumbFacade.updateBreadcrumb({
      ...this.conditionOperatorGroup,
      operator,
    })
  }
}
