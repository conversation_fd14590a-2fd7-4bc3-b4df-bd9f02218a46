import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'

import { PopoverContainerDirective } from '@progress/kendo-angular-tooltip'
import {
  DocumentsFacade,
  DocumentsService,
  ReviewFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import { Subject, filter, takeUntil, withLatestFrom } from 'rxjs'

@Component({
  selector: 'venio-document-select-header-renderer',
  templateUrl: './document-select-header-renderer.component.html',
  styleUrls: ['./document-select-header-renderer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentSelectHeaderRendererComponent
  implements OnInit, OnDestroy
{
  private allFileIds: number[]

  private unsubscribed$ = new Subject<void>()

  public selectedDocuments: number[]

  @ViewChild('chkSelectAll')
  private chkSelectAll: ElementRef

  @ViewChild('container', { static: false })
  private container: PopoverContainerDirective

  constructor(
    private documentsFacade: DocumentsFacade,
    private searchResultsFacade: SearchResultFacade,
    private documentsService: DocumentsService,
    private reviewFacade: ReviewFacade,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.searchResultsFacade.getSearchResultFileIds
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((fileIds: number[]) => {
        this.allFileIds = fileIds.filter((fileId) => fileId > 0)
      })

    this.documentsFacade.getSelectedDocuments$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((selectedDocuments: number[]) => {
        this.selectedDocuments = selectedDocuments
      })

    this.#deselectDocumentsOnReviewViewChange()
  }

  #deselectDocumentsOnReviewViewChange(): void {
    this.reviewFacade.getReviewViewType$
      .pipe(
        withLatestFrom(this.reviewFacade.getIsSwitchingView$),
        filter(([viewType, isSwitchingView]) => isSwitchingView),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([viewType, isSwitchingView]) => {
        this.deselectAllFromAllPages()
      })
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public onSelectOptionClicked(option: string): void {
    switch (option) {
      case 'SELECT-CURRENT':
        this.selectAllFromCurrentPage()
        break
      case 'DESELECT-CURRENT':
        this.deselectAllFromCurrentPage()
        break
      case 'SELECT_ALL':
        this.selectAllFromAllPages()
        break
      case 'DESELECT-ALL':
        this.deselectAllFromAllPages()
        break
    }
    this.container.hide()
  }

  private selectAllFromAllPages(): void {
    //select all documents in the batch
    this.documentsFacade.setSelectedDocuments(this.allFileIds)

    //deselect all documents
    this.documentsFacade.setUnSelectedDocuments([])

    //set batch selection true. This means all documents in all pages are selected.
    this.documentsFacade.setIsBatchSelection(true)

    // this.store.dispatch(
    //   fromDocumentsActions.setCurrentDocumentAuto({
    //     payload: { documentId: null, resetSelection: false }
    //   })
    // )
    this.documentsService.updateDocumentSelection$.next()
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
  }

  private deselectAllFromAllPages(): void {
    //deselect all files.
    this.documentsFacade.setSelectedDocuments([])

    //remove all documents in batch from unselected list if any
    this.documentsFacade.setUnSelectedDocuments(this.allFileIds)

    //reset the isBatchselection
    this.documentsFacade.setIsBatchSelection(false)

    // this.store.dispatch(fromDocumentsActions.unsetCurrentDocument())
    this.documentsService.updateDocumentSelection$.next()
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
  }

  private selectAllFromCurrentPage(): void {
    this.documentsFacade.addToSelectedDocuments(this.allFileIds)

    // this.store.dispatch(
    //   fromDocumentsActions.addToSelectedDocuments({
    //     payload: { documentIds: this.allFileIds }
    //   })
    // )
    this.documentsService.updateDocumentSelection$.next()
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
  }

  private deselectAllFromCurrentPage(): void {
    this.documentsFacade.removeFromSelectedDocuments(this.allFileIds)
    this.documentsService.updateDocumentSelection$.next()
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
  }

  public onSelectAllChanged(e): void {
    this.cdr.markForCheck()
    this.chkSelectAll.nativeElement.checked = false
  }
}
