import { Injectable } from '@angular/core'
import {
  PropagationRules,
  ReviewSetModel,
  TagsModel,
} from '@venio/shared/models/interfaces'
import dayjs from 'dayjs'

export enum ReviewType {
  REGULAR = 0,
  VAR_TRAINING = 1,
  VAR_TRAINING_QC = 2,
  VAR_PREDICTION_QC = 3,
  CAL_CONTROLSET = 4,
  CAL_TRAINING = 5,
}
@Injectable()
export class ReviewSetPayloadService {
  private readonly DEFAULTS = {
    batch: {
      prefix: 'Default',
      start: 1,
      padding: 8,
      size: 100,
    },
    sort: {
      order: 'asc',
      custodianOrder: 'asc',
    },
    selection: {
      tagOption: 'OR',
      folderOption: 'OR',
    },
    cal: {
      trainingRecall: 70,
      categoryTraining: 70,
      predictionAccuracy: 70,
      richnessThreshold: 10,
      relevanceThreshold: 80,
      confidenceLevel: 95,
      confidenceInterval: 5,
      controlSetPercent: 5,
      minControlDocs: 5,
      maxControlDocs: 76,
      minTrainingDocs: 5,
      maxTrainingDocs: 3000,
    },
  }

  public transform<T>(
    uiData: ReviewSetModel,
    projectSettings: {
      enableNativeAutoPrefetch: boolean
      isImageEnabled: boolean
      allViewableTags?: TagsModel[]
    }
  ): T {
    const basePayload = {
      reviewSetId: uiData.reviewSetId || 0,
      name: uiData.name,
      batchPrefix: uiData.batchPrefix || this.DEFAULTS.batch.prefix,
      batchStartNumber: uiData.batchStartNumber || this.DEFAULTS.batch.start,
      batchPaddingLength:
        uiData.batchPaddingLength || this.DEFAULTS.batch.padding,
      batchSize: uiData.batchSize || this.DEFAULTS.batch.size,
      purpose: uiData.purpose || '',
      reviewSource: uiData.reviewSource,
      tagID: this.formatIds(uiData['tagId']),
      tagSelectionOption:
        uiData.tagSelectionOption || this.DEFAULTS.selection.tagOption,
      folderID: this.formatIds(uiData['folderId']),
      folderSelectionOption:
        uiData.folderSelectionOption || this.DEFAULTS.selection.folderOption,
      savedSearchID: uiData['savedSearchId'] || null,
      orderByField: this.resolveOrderByField(uiData),
      sortOrder: uiData.sortOrder || this.DEFAULTS.sort.order,
      sortByCustodian: this.resolveBoolean(uiData.sortByCustodian, true),
      custodianSortOrder:
        uiData.custodianSortOrder || this.DEFAULTS.sort.custodianOrder,
      displayTag: this.generateHierarchicalDisplayTagXml(
        projectSettings.allViewableTags,
        (uiData.displayTag as unknown as TagsModel[]).map((c) => c.id)
      ),
      enableAutoCollect: this.resolveBoolean(uiData.enableAutoCollect),
      autoCollectFrequency: uiData.autoCollectFrequency || 24,
      autoCollectExpiresOn: uiData.enableAutoCollect
        ? dayjs(uiData.autoCollectExpiresOn).format('YYYY-MM-DD')
        : '',
      autoCollectMinThresholdValue: uiData.autoCollectMinThresholdValue || 500,
      autoCollectionSelectionCriteria: this.resolveBoolean(
        uiData.autoCollectionSelectionCriteria
      ),
      autoCollectReviewset: uiData.autoCollectReviewset || 0,
      selectedUserGroups: this.formatUserGroups(uiData.selectedUserGroups),
      layout: uiData.layout || 1,
      highlightGroup: uiData.highlightGroup || 1,
      parentChildIncluded: this.resolveBoolean(uiData.parentChildIncluded),
      msgThreadIncluded: this.resolveBoolean(uiData.msgThreadIncluded),
      excludePrevReviewSetDoc: this.resolveBoolean(
        uiData.excludePrevReviewSetDoc
      ),
      excludeNonInclusiveEmails: this.resolveBoolean(
        uiData.excludeNonInclusiveEmails
      ),
      propagateTagPCSet: this.resolveBoolean(uiData.propagateTagPCSet),
      tagPropagationRule: this.resolvePropagationRule(
        uiData.tagPropagationRule as unknown as boolean
      ),
      propagateTagEmailThread: this.resolveBoolean(
        uiData.propagateTagEmailThread
      ),
      propagateReviewPCSet: this.resolveBoolean(uiData.propagateReviewPCSet),
      reviewDuplicatePropagationRule: this.resolvePropagationRule(
        uiData.reviewDuplicatePropagationRule as unknown as boolean
      ),
      propagateReviewEmailThread: this.resolveBoolean(
        uiData.propagateReviewEmailThread
      ),
      useCALProfileForReviewSet: this.resolveBoolean(
        uiData.useCALProfileForReviewSet,
        true
      ),
      autoQueueForHtmlConversion: projectSettings.enableNativeAutoPrefetch
        ? this.resolveBoolean(uiData.autoQueueForHtmlConversion, true)
        : false,
      autoQueueForTiff: projectSettings.isImageEnabled
        ? this.resolveBoolean(uiData.autoQueueForTiff)
        : false,
      markTaggedDocsAsReviewed: this.resolveBoolean(
        uiData.markTaggedDocsAsReviewed
      ),
      // Not sure if it is even required. The OLD is using it so it is here
      createdBy: 1,
      allowReviewAfterCALThreshold: this.resolveBoolean(
        uiData.allowReviewAfterCALThreshold,
        true
      ),
      ...this.systemIdentifiers(uiData.useCALProfileForReviewSet),
      ...this.calProperties(uiData),
    }

    // CAL Validation
    if (basePayload.useCALProfileForReviewSet) {
      basePayload.calControlSetDocCount = this.handleControlSetDocCount(
        uiData.calControlSetDocCount
      )
      basePayload.calControlSetMaxDocCount = this.handleControlSetDocCount(
        uiData.calControlSetMaxDocCount
      )
    }

    return basePayload
  }

  private handleControlSetDocCount(value: number): number {
    // Special case handling for negative values
    if (value && value < 0) return value
    return value || this.DEFAULTS.cal.maxControlDocs
  }

  private calProperties(uiData: ReviewSetModel): any {
    return {
      trainingRecallThreshold:
        uiData.trainingRecallThreshold || this.DEFAULTS.cal.trainingRecall,
      categoryTrainingThreshold:
        uiData.categoryTrainingThreshold || this.DEFAULTS.cal.categoryTraining,
      predictionAccuracyThreshold:
        uiData.predictionAccuracyThreshold ||
        this.DEFAULTS.cal.predictionAccuracy,
      batchRichnessThreshold:
        uiData.batchRichnessThreshold || this.DEFAULTS.cal.richnessThreshold,
      reviewRelevanceThreshold:
        uiData.reviewRelevanceThreshold || this.DEFAULTS.cal.relevanceThreshold,
      allowReviewAfterCALThreshold: this.resolveBoolean(
        uiData.allowReviewAfterCALThreshold,
        true
      ),
      controlSetSizeDerivedBy: uiData.controlSetSizeDerivedBy || 0,
      percentageOfPopulation: uiData.percentageOfPopulation || 10,
      numberOfDocuments: uiData.numberOfDocuments || 1000,
      isDynamicControlSet: this.resolveBoolean(
        uiData.isDynamicControlSet,
        true
      ),
      calControlSetDocCount:
        uiData.calControlSetDocCount || this.DEFAULTS.cal.maxControlDocs,
      confidenceLevel:
        uiData.confidenceLevel || this.DEFAULTS.cal.confidenceLevel,
      confidenceInterval:
        uiData.confidenceInterval || this.DEFAULTS.cal.confidenceInterval,
      controlSetPercentFromTrainingBatch:
        uiData.controlSetPercentFromTrainingBatch ||
        this.DEFAULTS.cal.controlSetPercent,
      calControlSetMinDocCount:
        uiData.calControlSetMinDocCount || this.DEFAULTS.cal.minControlDocs,
      calControlSetMaxDocCount:
        uiData.calControlSetMaxDocCount || this.DEFAULTS.cal.maxControlDocs,
      calTrainingSetMinDocCount:
        uiData.calTrainingSetMinDocCount || this.DEFAULTS.cal.minTrainingDocs,
      calTrainingSetMaxDocCount:
        uiData.calTrainingSetMaxDocCount || this.DEFAULTS.cal.maxTrainingDocs,
      calProfileInfo: uiData.useCALProfileForReviewSet
        ? this.buildCalProfile(uiData)
        : null,
    }
  }

  private buildCalProfile(data: ReviewSetModel): any {
    const name = data.name
    return {
      profileName: name,
      actualProfileName: `VAR_${name}`,
      autoCatTag: `Auto_${name}`,
      // Copy values from parent instead of using defaults
      batchRichnessThreshold:
        data.batchRichnessThreshold || this.DEFAULTS.cal.richnessThreshold,
      calControlSetDocCount:
        data.calControlSetDocCount || this.DEFAULTS.cal.maxControlDocs,
      calControlSetMaxDocCount:
        data.calControlSetMaxDocCount || this.DEFAULTS.cal.maxControlDocs,
      calControlSetMinDocCount:
        data.calControlSetMinDocCount || this.DEFAULTS.cal.minControlDocs,
      calPrimaryCategoryName: `${name}_Responsive`,
      calTrainingSetMaxDocCount:
        data.calTrainingSetMaxDocCount || this.DEFAULTS.cal.maxTrainingDocs,
      calTrainingSetMinDocCount:
        data.calTrainingSetMinDocCount || this.DEFAULTS.cal.minTrainingDocs,
      // Copy all the properties that exist in Object 1 but are missing here
      categoryName: '',
      categoryDesc: '',
      categoryTrainingThreshold:
        data.categoryTrainingThreshold || this.DEFAULTS.cal.categoryTraining,
      confidenceInterval:
        data.confidenceInterval || this.DEFAULTS.cal.confidenceInterval,
      confidenceLevel:
        data.confidenceLevel || this.DEFAULTS.cal.confidenceLevel,
      controlSetPercentFromTrainingBatch:
        data.controlSetPercentFromTrainingBatch ||
        this.DEFAULTS.cal.controlSetPercent,
      controlSetSizeDerivedBy: data.controlSetSizeDerivedBy || 0,
      createdBy: 0,
      excludeTrainingDocsOnPrediction: false,
      isCalProfile: true,
      isDiscontinueDynamicControlSet: false,
      isDynamicControlSet: data.isDynamicControlSet !== false,
      manualCatTag: `${name}_`,
      numberOfDocuments: data.numberOfDocuments || 1000,
      percentageOfPopulation: data.percentageOfPopulation || 10,
      predictionAccuracyThreshold:
        data.predictionAccuracyThreshold ||
        this.DEFAULTS.cal.predictionAccuracy,
      profileSettingTemplateId: null,
      purpose: '',
      reviewRelevanceThreshold:
        data.reviewRelevanceThreshold || this.DEFAULTS.cal.relevanceThreshold,
      allowReviewAfterCalThreshold: data.allowReviewAfterCALThreshold !== false, // Note case difference
      showReviewStatistics: false,
      trainingRecallThreshold:
        data.trainingRecallThreshold || this.DEFAULTS.cal.trainingRecall,
      // Rest of object stays the same
      categoryInfos: [
        this.createCategory(name, 'Responsive'),
        this.createCategory(name, 'Non-Responsive'),
      ],
      options: {
        balanceSeedMode: 'AllCategoryBalanced',
        englishStemmerFalback: true,
        skipStopWords: true,
        stemWords: true,
        skipNumbers: true,
        skipNumberPerfix: true,
        skipStartNumber: true,
        maxC: 3,
        minG: 0,
        cStep: 2,
        gStep: 1,
        isBalanceSeed: true,
        noOfConsecutiveBatches: 3,
        skipWordsWithDocumentFrequencyLessThan: 10,
        skipWordsWithDocumentFrequencyMoreThan: 90,
        skipWordsWithLessThan: 3,
        skipWordsWithMoreThan: 20,
        balanceTrainingSeedWithSelectedAlgorithm: null,
        excludeDocumentTextSizeThresholdInKB: null,
        skipWordLessChBx: false,
        skipWordMoreChBx: false,
        skipWordLessThanDocFreq: true,
        skipWordMoreThanDocFreq: true,
      },
    }
  }

  private createCategory(name: string, type: string): any {
    return {
      categoryId: -1,
      categoryName: `${name}_${type}`,
      categoryDesc: '',
      tagManualCategorization: `Manual_${name}_${type}`,
      tagAutoCategorization: `Auto_${name}_${type}`,
    }
  }

  private systemIdentifiers(isCal: boolean): any {
    return {
      selectedField: 'INTERNAL_FILE_ID',
      sendEmailAfterAutoCollection: false,
      sendEmailAfterReviewSetCreation: false,
      enforceTagForReview: false,
      showHideReviewedFiles: false,
      sampleSelectionOption: 'OR',
      showPurposeInUI: true,
      nearDupsIncluded: false,
      profileID: -1,
      reviewTagID: -1,
      sourceReviewSetId: -1,
      reviewSetTemplateId: -1,
      sampleID: '-1',
      predictionId: '-1',
      redactionSetIds: '',
      coddingField: '',
      emailUserInfos: [],
      emailUserIds: '',
      visibleReviewPanel: [],
      predictionScoreClause: '',
      tagGroupIds: '<TagGroups/>',
      tagRuleIds: '<TagRule/>',
      reviewType: isCal ? ReviewType.CAL_TRAINING : ReviewType.REGULAR,
    }
  }

  private formatIds(ids?: number[]): string | null {
    return ids?.filter((id) => Number.isInteger(id) && id > 0).join(',') || null
  }

  private formatUserGroups(groups?: any[]): any[] {
    if (!groups) return []
    return groups.map((g) => ({
      userGroupId: g.type === 'GROUP' ? g.groupId : g.userId,
      type: g.type === 'USER' ? 'USER' : 'GROUP',
    }))
  }

  private generateHierarchicalDisplayTagXml(
    tags?: Array<TagsModel>,
    selectedTagIds?: number[]
  ): string {
    if (!tags?.length || !selectedTagIds?.length) return ''

    // 1. Mark all ancestors of selected tags
    const selectedSet = new Set(selectedTagIds)
    const includedSet = new Set(selectedTagIds)

    // Find all ancestor tags that need to be included
    let changed = true
    while (changed) {
      changed = false
      tags.forEach((tag) => {
        if (
          includedSet.has(tag.id) &&
          tag.parentId &&
          !includedSet.has(tag.parentId)
        ) {
          includedSet.add(tag.parentId)
          changed = true
        }
      })
    }

    // 2. Build hierarchical structure
    const tagMap = new Map()
    tags.forEach((tag) => tagMap.set(tag.id, { ...tag, children: [] }))

    const rootNodes = []
    tags.forEach((tag) => {
      const node = tagMap.get(tag.id)
      if (tag.parentId === null) {
        rootNodes.push(node)
      } else if (tagMap.has(tag.parentId)) {
        tagMap.get(tag.parentId).children.push(node)
      } else {
        rootNodes.push(node)
      }
    })

    // 3. Generate XML
    let orderCounter = 0

    const buildXml = (nodes): string => {
      let result = ''

      nodes.forEach((node) => {
        // Only include nodes that are in the ancestor path of selected nodes
        if (includedSet.has(node.id)) {
          const isGroup = node.tagGroupId === -1
          const tagOrGroup = isGroup ? 'Group' : 'Tag'
          const id = isGroup ? node.treeKeyId : node.tagId

          // Only add XML if this node is actually selected
          if (selectedSet.has(node.id)) {
            result += `
                    <TagSortOrder>
                      <TagOrGroup>${tagOrGroup}</TagOrGroup>
                        <ID>${id}</ID>
                        <Order>${orderCounter++}</Order>
                      <WritePermission>1</WritePermission>
                    </TagSortOrder>`
          }

          // Process children
          if (node.children.length) {
            result += buildXml(node.children)
          }
        }
      })

      return result
    }

    return buildXml(rootNodes)
  }

  private resolveOrderByField(uiData: ReviewSetModel): string {
    return uiData.useCALProfileForReviewSet
      ? 'CAL'
      : uiData.orderByField || 'INGESTION_ORDER'
  }

  private resolveBoolean(value?: boolean, defaultValue = false): boolean {
    return typeof value === 'boolean' ? value : defaultValue
  }

  private resolvePropagationRule(value?: boolean): PropagationRules {
    return value
      ? PropagationRules.PROPAGATE_TO_DUPLICATES
      : PropagationRules.DO_NOT_PROPAGATE_TO_DUPLICATES
  }
}
