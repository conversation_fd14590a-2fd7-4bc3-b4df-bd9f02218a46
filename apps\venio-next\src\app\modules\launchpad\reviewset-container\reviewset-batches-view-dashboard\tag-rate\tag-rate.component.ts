import {
  ChangeDetectionStrategy,
  Component,
  signal,
  computed,
  inject,
  effect,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { TagRateWorkerService } from '@venio/util/utilities'
import { TagRateUserData } from '@venio/shared/models/interfaces'
import { uniq } from 'lodash'

@Component({
  selector: 'venio-tag-rate',
  standalone: true,
  imports: [CommonModule, GridModule],
  templateUrl: './tag-rate.component.html',
  styleUrl: './tag-rate.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagRateComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  public readonly tagRate = signal<TagRateUserData[]>([])

  public readonly columns = computed(() => {
    const reviewSetTagRate = this.loadedReviewSetTagRate()
    const columnUser = uniq(reviewSetTagRate?.map((item) => item.userName))
    return columnUser || []
  })

  /** Signal for the review set tag rate loading state */
  public isReviewSetTagRateLoading = toSignal(
    this.reviewSetFacade.isReviewSetTagRateLoading$,
    { initialValue: true }
  )

  /** Signal for the review set tag rate detail */
  private readonly reviewSetTagRate = toSignal(
    this.reviewSetFacade.selectReviewSetTagRateSuccess$
  )

  /** Computed property for the review set tag rate detail */
  public readonly loadedReviewSetTagRate = computed(() => {
    return this.reviewSetTagRate() || []
  })

  constructor() {
    effect(() => {
      if (this.loadedReviewSetTagRate()) {
        untracked(() => this.prepareTagRateData())
      }
    })
  }

  public prepareTagRateData(): void {
    const tagRateWorkerService = new TagRateWorkerService()
    tagRateWorkerService
      .tagRateUserDataTransform(this.loadedReviewSetTagRate())
      .then((tagRate: TagRateUserData[]) => {
        this.tagRate.set(tagRate)
        tagRateWorkerService.terminate()
      })
  }
}
