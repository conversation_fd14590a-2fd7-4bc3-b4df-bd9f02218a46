<kendo-dialog-titlebar (close)="close()">
  <div class="t-flex t-w-[65%] t-justify-between">
    <div class="t-block">{{ dialogTitle }}</div>
    <div class="t-block">
      <div
        #appendNotification
        class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-8 t-w-[420px]"></div>
    </div>
  </div>
</kendo-dialog-titlebar>

<kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
  <kendo-tabstrip-tab
    title="Redact"
    *ngIf="showBulkRedactTab"
    [selected]="isSelected(0)">
    <ng-template kendoTabContent>
      <div class="t-flex t-gap-3">
        <div
          class="t-flex t-flex-col t-gap-3 t-pt-4 t-w-[300px] t-border-r-[1px] t-border-r-[#ccc] t-pr-4">
          <div class="t-flex t-uppercase t-font-semibold t-text-[#1EBADC]">
            Redact for
          </div>
          <div class="t-block">
            <kendo-textbox
              #termInput
              placeholder="Terms"
              [(ngModel)]="searchTerm"
              [disabled]="isSearchingForBulkRedaction | async"
              (keydown)="onEnter($event)">
            </kendo-textbox>
          </div>
        </div>

        <div class="t-flex t-flex-col t-gap-4 t-flex-1">
          <div class="t-flex t-justify-between t-mb-4 t-items-center t-mt-4">
            <div
              class="t-flex t-uppercase t-text-xs t-font-semibold t-text-primary">
              Redact terms
            </div>
            <div class="t-flex">
              <button
                kendoButton
                class="v-custom-secondary-button"
                themeColor="secondary"
                data-qa="redact-button"
                fillMode="outline"
                [disabled]="
                  disabledRedactButton ||
                  (isQueuingFilesForBulkRedaction | async)
                "
                (click)="QueueBulkRedaction()">
                REDACT
              </button>
            </div>
          </div>

          <kendo-grid
            class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
            [kendoGridBinding]="termGridData"
            venioDynamicHeight
            [isKendoDialog]="true"
            [loading]="isSearchingForBulkRedaction | async"
            kendoGridSelectBy="hashNum"
            [sortable]="true"
            [groupable]="false"
            [reorderable]="true"
            [resizable]="true">
            <kendo-grid-column title="#" [width]="50">
              <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
                {{ rowIndex + 1 }}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="term"
              title="Term"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Term">
                  Term
                </span>
              </ng-template>
              <ng-template
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex">
                <span>
                  {{ dataItem.term }}
                </span>
                <span
                  class="t-text-[#979797] t-text-xs hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-w-full t-pl-4"
                  (click)="onEditTerm(dataItem, rowIndex)">
                  <kendo-svg-icon [icon]="icons.pencilIcon"></kendo-svg-icon>
                </span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column
              field="hits"
              title="Hits"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Hits">
                  Hits
                </span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="numOfDoc"
              [width]="230"
              [minResizableWidth]="100"
              title="Number of Documents"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Number of Documents">
                  Number of Documents
                </span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="action"
              title="Action"
              headerClass="t-text-primary"
              [width]="100"
              [minResizableWidth]="100">
              <ng-template
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex">
                <span
                  class="hover:t-cursor-pointer t-text-error t-flex t-w-full t-mr-7"
                  (click)="onDelete(dataItem, rowIndex)">
                  <kendo-svg-icon
                    [icon]="
                      dataItem.action ? icons.closeIcon : null
                    "></kendo-svg-icon>
                </span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="terms"
              title="Terms"
              headerClass="t-text-primary"
              [hidden]="true">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Hits"
                  >Terms</span
                >
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
        </div>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>
  <kendo-tabstrip-tab title="History" [selected]="isSelected(1)">
    <ng-template kendoTabContent>
      <venio-bulk-redaction-grid
        (viewFailedDocuments)="onViewFailedDocumentsClick($event)"
        (downloadDocuments)="downloadBulkRedactedFileDetails($event)">
      </venio-bulk-redaction-grid>
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="close()"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CLOSE
    </button>
  </div>
</kendo-dialog-actions>
