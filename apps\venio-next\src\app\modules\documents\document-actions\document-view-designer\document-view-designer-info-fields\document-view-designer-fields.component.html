<div class="t-flex t-gap-2 t-w-full">
  <venio-document-view-designer-field-selection
    [fields]="leftFields"
    [clearSelection]="!selectedLeftFields().length"
    title="UNSELECTED"
    (fieldSelected)="selectionItemChanged($event)" />
  <ng-container
    *ngTemplateOutlet="
      iconButtonTpl;
      context: { buttonIcons: middleActionButtonSvg }
    " />
  <venio-document-view-designer-field-selection
    [clearSelection]="!selectedRightFields.length"
    [fields]="rightFields"
    title="SELECTED"
    (fieldSelected)="selectionItemChanged($event, true)" />
  <ng-container
    *ngTemplateOutlet="
      iconButtonTpl;
      context: { buttonIcons: rightSideActions }
    " />
</div>

<ng-template #iconButtonTpl let-icons="buttonIcons">
  <div class="t-flex t-w-[8%] t-flex-col t-justify-center t-items-center">
    <ul
      class="t-flex t-flex-col t-gap-3 t-justify-center t-mt-20"
      [showAfter]="700"
      kendoTooltip>
      <li *ngFor="let icon of icons" [ngClass]="icon['extraClasses']">
        <button
          kendoButton
          [disabled]="icon.isDisabled()"
          [title]="icon.title"
          fillMode="outline"
          size="none"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [attr.data-qa]="icon.dataQa"
          (click)="icon.clickAction()"
          #iconRef>
          <span
            venioSvgLoader
            [parentElement]="iconRef.element"
            [svgUrl]="icon.svgUrl"
            [hoverColor]="icon.hoverColor"
            height=".95rem"
            width=".95rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </li>
    </ul>
  </div>
</ng-template>
