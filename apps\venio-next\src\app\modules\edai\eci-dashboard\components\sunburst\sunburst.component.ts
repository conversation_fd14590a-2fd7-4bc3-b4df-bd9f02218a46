import { Component, OnInit, Input } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { calculateSunburstData } from './helpers';
import { mockDocumentTypes } from '../../mock-data/document-types';
import { NgIf } from '@angular/common';
import { EciDataService } from '../../shared/data.service';

@Component({
  selector: 'venio-eci-sunburst',
  standalone: true,
  imports: [
    PlotlyModule,
    NgIf
  ],
  templateUrl: './sunburst.component.html',
  styleUrl: './sunburst.component.scss'
})
export class EciSunburstComponent implements OnInit {
  @Input() showLegend = true;
  @Input() title = 'Document Categories';
  
  constructor(private dataService: EciDataService) {}
  
  public sortedDocuTypes = mockDocumentTypes.sort((a, b) => b.count - a.count);
  public parentLabels: any;
  public childrenLabels: any;
  public chartOneTotal!: number;
  public chartOneSubTotal!: number[];
  public chartOnePercents: number[] | undefined;
  public chartOneChildPercents: number[][] | undefined;
  public allCountsOne!: number[];
  public allValsOne!: number[];
  public formattedCounts!: string[];
  public config: any;
  public graph: any;
  public isParentData: boolean = true;
  public labels: string[] = [];
  public parents: string[] = ['Total'];

  ngOnInit() {
    const {
      parentLabels,
      childrenLabels,
      chartOneTotal,
      chartOneSubTotal,
      chartOnePercents,
      chartOneChildPercents,
      allCountsOne,
      allValsOne,
      formattedCounts
    } = calculateSunburstData(this.sortedDocuTypes);

    this.parentLabels = parentLabels;
    this.childrenLabels = childrenLabels;
    this.chartOneTotal = chartOneTotal;
    this.chartOneSubTotal = chartOneSubTotal;
    this.chartOnePercents = chartOnePercents;
    this.chartOneChildPercents = chartOneChildPercents;
    this.allCountsOne = allCountsOne;
    this.allValsOne = allValsOne;
    this.formattedCounts = formattedCounts;

    this.dataService.isParentData$.subscribe(val => {
      this.isParentData = val;
    });

    this.labels = ['Total'];
    this.parents = [''];

    const parentsLen = this.parentLabels.length;
    this.labels.push(...this.parentLabels);
    this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'));

    for (let i = 0; i < this.childrenLabels.length; i++) {
      this.labels.push(...this.childrenLabels[i]);
      const len = this.childrenLabels[i].length;
      this.parents.push(...Array.from({ length: len }, () => this.parentLabels[i]));
    }

    const updatedVals = [...this.allValsOne];
    this.parents.forEach((parent, idx) => {
      if (parent === 'Total' || idx === 0) {
        updatedVals[idx] = 0;
      }
    });
    
    this.graph = {
      data: [
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: 0.5,
          textinfo: 'label'
        }
      ],
      layout: {
        autosize: true,
        automargin: true,
        title: this.title,
        branchvalues: 'remainder',
        margin: { t: 40, r: 0, b: 0, l: 0 }
      }
    };
    
    this.config = {
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      modeBarButtonsToRemove: [
        'toImage',
        'sendDataToCloud',
        'editInChartStudio',
        'zoom2d',
        'select2d',
        'pan2d',
        'lasso2d',
        'autoScale2d',
        'resetScale2d'
      ]
    };
  }
  
  onChartClick(event: any) {
    console.log('Sunburst chart clicked:', event);
    if (this.isParentData && event.points[0].parent === 'Total') {
      const childLabel = event.points[0].label;
      const childData = this.sortedDocuTypes.find(docuType => docuType.category === childLabel);
      console.log('Child data:', childData);
      if (!childData) return;
      
      this.dataService.setIsParentData(false);
      this.dataService.setShowDetails(true);
    } else if (!this.isParentData && event.points[0].parent === 'Total') {
      console.log('Clicked on parent -Total');
      this.dataService.setIsParentData(true);
      this.dataService.setShowDetails(false);
    } else {
      console.log('grandchild clicked', event.points[0]);
      this.dataService.setShowDetails(true);
    }
  }
}
