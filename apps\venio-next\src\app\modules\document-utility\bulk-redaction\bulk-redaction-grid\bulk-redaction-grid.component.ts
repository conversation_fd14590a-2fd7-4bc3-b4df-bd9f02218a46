import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  BulkRedactFacade,
  BulkRedactionJobDetailModel,
  BulkRedactionJobDetailRequestModel,
  ViewerService,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { filter, Subject, switchMap, takeUntil } from 'rxjs'
import { ResponseModel, UserModel } from '@venio/shared/models/interfaces'
import { UserFacade } from '@venio/data-access/common'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { chevronDownIcon, eyeIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { FormsModule } from '@angular/forms'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { UiDateTimePickerModule } from '@venio/ui/date-time-picker'
import { SelectionRange } from '@progress/kendo-angular-dateinputs'
import dayjs from 'dayjs'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-bulk-redaction-grid',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    GridModule,
    IconsModule,
    UiPaginationModule,
    DropDownsModule,
    SvgLoaderDirective,
    FormsModule,
    UiDateTimePickerModule,
    DialogsModule,
    DynamicHeightDirective,
  ],
  templateUrl: './bulk-redaction-grid.component.html',
  styleUrl: './bulk-redaction-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkRedactionGridComponent implements OnInit, OnDestroy {
  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private readonly toDestroy$ = new Subject<void>()

  private refreshClick$ = new Subject<void>() // To handle the refresh clicks

  public isGettingJobDetailsOfBulkRedaction =
    this.bulkRedactFacade.getIsGettingJobDetailsOfBulkRedaction$

  public selectedUsers: number[]

  public selectedStatuses: string[]

  public statusList: Array<string> = [
    'Not Started',
    'In Progress',
    'Cancelled',
    'Failed',
    'Successful',
  ]

  public currentPage = 1

  public pageSize = 10

  public jobDetails: BulkRedactionJobDetailModel[]

  public totalRecords: number

  public users: UserModel[]

  private startDate: Date

  private endDate: Date

  public downIcon: SVGIcon = chevronDownIcon

  @Input() public showFilter = true

  @Input() public showProgressDetail = false

  @Input() public hideColumn = false

  @Input() public hideDownloadButton = false

  @Input() public successfulRedacted = 0

  @Input() public failedRedacted = 0

  @Output()
  public readonly viewFailedDocuments: EventEmitter<string> =
    new EventEmitter<string>()

  @Output()
  public readonly downloadDocuments: EventEmitter<BulkRedactionJobDetailModel> =
    new EventEmitter<BulkRedactionJobDetailModel>()

  public icons = {
    eyeIcon: eyeIcon,
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private bulkRedactFacade: BulkRedactFacade,
    private userFacade: UserFacade,
    private cdr: ChangeDetectorRef,
    private viewerService: ViewerService,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#getBulkRedactionJobDetails()
    this.#populateJobDetailsOfBulkRedaction()
    this.#handleSuccessResponse()
    this.#fetchUsers()
    this.#handleRefreshClick()
  }

  #getBulkRedactionJobDetails(): void {
    const bulkRedactionJobDetailRequestModel: BulkRedactionJobDetailRequestModel =
      {
        userIds: this.selectedUsers === null ? null : this.selectedUsers,
        startDate: this.startDate,
        endDate: this.endDate,
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        statuses: this.selectedStatuses === null ? null : this.selectedStatuses,
      }

    this.bulkRedactFacade.getJobDetailsOfBulkRedaction(
      bulkRedactionJobDetailRequestModel,
      this.projectId
    )
  }

  #populateJobDetailsOfBulkRedaction(): void {
    this.bulkRedactFacade.getJobDetailsOfBulkRedactionSuccessResponse$
      .pipe(
        filter((response) => !!response),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.jobDetails = response.data.bulkRedactionJobDetailModel
        this.totalRecords = response.data.totalHitCount
      })
  }

  #fetchUsers(): void {
    this.userFacade.fetchUserList()
  }

  #handleRefreshClick(): void {
    this.refreshClick$
      .pipe(
        switchMap(() => this.viewerService.redactionStatus(this.projectId)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.#updateRedactionStatus(response)
      })
  }

  #updateRedactionStatus(response: ResponseModel): void {
    this.successfulRedacted = response?.data?.completedCount
    this.failedRedacted = response?.data?.inProgressFailureCount
  }

  #handleSuccessResponse(): void {
    this.userFacade.selectUserListSuccessResponse$
      .pipe(
        filter((response) => Boolean(response?.data)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.users = response.data
      })
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.currentPage = args.pageNumber

      this.#getBulkRedactionJobDetails()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    this.pageSize = args.pageSize
    this.currentPage = args.pageNumber

    this.#getBulkRedactionJobDetails()
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public onFilterClick(): void {
    this.currentPage = 1
    this.#getBulkRedactionJobDetails()
  }

  public onViewFailedDocumentsClick(dataItem: any): void {
    const searchExpression = this.GetJobSearchExpression(dataItem)
    this.viewFailedDocuments.emit(searchExpression)
  }

  private GetJobSearchExpression(
    dataItem: BulkRedactionJobDetailModel
  ): string {
    return (
      '((JobSearch("BulkPdfRedaction","' +
      dataItem.statusName +
      '","Any")) AND JobID in (' +
      dataItem.jobId +
      '))'
    )
  }

  public onDownloadDocumentsClick(dataItem: any): void {
    this.downloadDocuments.emit(dataItem)
  }

  public onDateRangeChange(dateRange: SelectionRange): void {
    this.startDate = dateRange?.start
      ? dayjs(dateRange?.start).startOf('day').toDate()
      : null
    this.endDate = dateRange?.end
      ? dayjs(dateRange?.end).endOf('day').toDate()
      : null
  }

  #resetBulkRedactState(): void {
    this.bulkRedactFacade.resetBulkRedactState([
      'getJobDetailsOfBulkRedactionSuccessResponse',
      'getJobDetailsOfBulkRedactionFailureResponse',
    ])
  }

  public onRefreshClick(): void {
    this.#getBulkRedactionJobDetails()

    if (!this.showProgressDetail) return
    this.refreshClick$.next()
  }

  public close(): void {
    this.dialogRef.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetBulkRedactState()
  }
}
