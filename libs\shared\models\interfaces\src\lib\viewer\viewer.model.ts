export interface NativePrefetchDocumentModel {
  thresholdFileSize: number
  invalidFileExtensions: string[]
  documentPrefetchCount: number
  currentDocumentId: number
}
export interface DocumentPrefetchWorkerModel {
  fileIds: Array<number>
  projectId: number
  accessToken: string
  serviceUrl: string
}

export interface ChunkDocumentPartModel {
  ProjectId?: number
  PartId: number
  FileId: number
  PartIndex: number
  Content: string
  Size: number
  TotalParts: number
  IsForwardedText: boolean
  ChunkPartsLoadedCount: number
  SnippetText: string
  UpdatedDate: number
  HtmlConvertedDateValue: string
}

export interface FlattenedTranscriptData {
  pageNumber: number
  lineNumber: number
  text: string
}
export interface Line {
  [lineNumber: number]: string
}

export interface Page {
  [pageNumber: number]: Line[]
}

export interface LinkedDocument {
  fileId: number
  fileName: string
  index: string
  id: string
}

export interface TranscriptViewModel {
  linkedDocuments: LinkedDocument[]
  flattenedTranscriptData: FlattenedTranscriptData[]
}

export interface SocialMediaParticipant {
  emailaddress: string
  name: string
  id: string
  avatarUrl: string
}

export interface SocialMediaMessage {
  author: SocialMediaParticipant
  text: string
  timestamp: Date
}

export interface TranscriptReportData {
  action: string
  rangeNumber: string
  lineNumber: string
  description: string
  reportType: string
  reportId: number
  notes?: string
  fileId?: number
  fileName?: string
  rowId: number
}

export interface TranscriptAnnotations {
  index: string
  selectedText: string
  annotationType: string
  reportId: number
  notes?: string
  fileId?: number
  fileName?: string
}

export enum TranscriptAnnotationType {
  ALL,
  HIGHLIGHT,
  NOTES,
  DOCUMENTLINK,
}
