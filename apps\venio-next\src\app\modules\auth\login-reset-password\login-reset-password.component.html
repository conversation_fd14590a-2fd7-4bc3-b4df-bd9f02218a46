<form
  autocomplete="off"
  [formGroup]="passwordResetFormGroup"
  (keydown.enter)="resetClick()">
  <kendo-dialog-titlebar>
    <button class="t-flex t-items-center" (click)="closeDialog()">
      <kendo-svgicon
        [icon]="iconLeft"
        class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
      <kendo-label class="t-text-xs t-font-black t-text-[#000000]"
        >LOGIN
      </kendo-label>
    </button>
  </kendo-dialog-titlebar>
  @if(passwordResetResponse()?.type === 'success') {
  <p
    *ngIf="passwordResetResponse()?.type === 'success'"
    class="t-py-3 t-text-center t-text-[#030303] t-text-lg/5 t-font-normal t-flex t-flex-col t-gap-2">
    <span> Your password has been successfully reset. </span>
    <span>Please log in with your new password.</span>
  </p>
  } @else {
  <div class="t-flex t-flex-col t-items-center t-justify-center t-p-4">
    <div class="t-flex t-w-full t-flex-col">
      <div class="t-flex t-flex-row t-items-start">
        <div
          class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
          <img
            ngSrc="assets/svg/icon-reset-password.svg"
            height="24"
            width="24"
            alt="Reset Password Icon" />
        </div>
        <div class="t-flex t-flex-col t-items-start t-justify-center">
          <p class="t-text-[#030303] t-text-[1.65rem] t-font-black t-mb-2">
            {{ formControls?.isPasswordExpired.value ? 'CHANGE' : 'RESET' }}
            PASSWORD
          </p>
          <p
            class="t-text-[#FFBB12] t-text-lg/5 t-font-black t-mb-6 t-align-left t-text-left t-mr-4">
            Please enter new password.
          </p>
        </div>
      </div>
    </div>
    <kendo-formfield
      [ngClass]="{ 't-hidden': formControls?.isPasswordExpired.value }"
      class="t-w-full t-align-left t-mb-2">
      <kendo-textbox
        #currentUsername
        formControlName="userName"
        class="t-w-full t-align-left v-input-shadow"
        placeholder="Username"
        id="username">
      </kendo-textbox>
      <div
        *ngIf="
          (formControls?.userName?.invalid &&
            formControls?.userName?.dirty &&
            !formControls?.userName?.untouched) ||
          (formControls?.userName?.dirty &&
            !formControls?.userName?.untouched &&
            formControls?.userName?.value.trim() === '')
        "
        class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
        {{
          formControls?.oldPassword?.hasError('required')
            ? 'Username is required'
            : 'Please enter a valid username'
        }}
      </div>
    </kendo-formfield>
    <kendo-formfield
      [ngClass]="{ 't-hidden': formControls?.isPasswordExpired.value }"
      class="t-w-full t-align-left t-mb-2">
      <kendo-textbox
        [type]="isCurrentPasswordVisible() ? 'text' : 'password'"
        formControlName="oldPassword"
        venioCopyPrevention
        class="t-w-full t-align-left v-input-shadow"
        placeholder="Current Password"
        id="currentPassword">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            [tabIndex]="-1"
            themeColor="none"
            fillMode="clear"
            look="clear"
            class="t-pr-2"
            (click)="isCurrentPasswordVisible.set(!isCurrentPasswordVisible())">
            <kendo-svgicon
              [icon]="isCurrentPasswordVisible() ? iconEye : iconSlashEye">
            </kendo-svgicon>
          </button>
        </ng-template>
      </kendo-textbox>
      <div
        *ngIf="
          (formControls?.oldPassword?.invalid &&
            formControls?.oldPassword?.dirty &&
            !formControls?.oldPassword?.untouched) ||
          (formControls?.oldPassword?.dirty &&
            !formControls?.oldPassword?.untouched &&
            formControls?.oldPassword?.value.trim() === '')
        "
        class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
        {{
          formControls?.oldPassword?.hasError('required')
            ? 'Current password is required'
            : 'Please enter a valid current password'
        }}
      </div>
    </kendo-formfield>
    <kendo-formfield class="t-w-full t-align-left t-mb-2">
      <kendo-textbox
        (focus)="setCurrentActiveControl('newPassword')"
        (valueChange)="setCurrentActiveControl('newPassword')"
        (blur)="setCurrentActiveControl('')"
        venioCopyPrevention
        [popover]="newPassword"
        #newPasswordControl
        #newPasswordPopoverAnchor="kendoPopoverAnchor"
        kendoPopoverAnchor
        [type]="isNewPasswordVisible() ? 'text' : 'password'"
        formControlName="newPassword"
        class="t-w-full t-align-left v-input-shadow"
        placeholder="New Password"
        id="newPassword">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            [tabIndex]="-1"
            themeColor="none"
            fillMode="clear"
            look="clear"
            class="t-pr-2"
            (click)="isNewPasswordVisible.set(!isNewPasswordVisible())">
            <kendo-svgicon
              [icon]="isNewPasswordVisible() ? iconEye : iconSlashEye">
            </kendo-svgicon>
          </button>
        </ng-template>
      </kendo-textbox>
      <kendo-popover #newPassword position="right" [width]="280">
        <ng-template kendoPopoverBodyTemplate>
          <div class="v-error-popover t-opacity-90 v-error-popover">
            {{ newPasswordStrengthMessage }}
          </div>
        </ng-template>
      </kendo-popover>
      <div
        *ngIf="
          (formControls?.newPassword?.invalid &&
            formControls?.newPassword?.dirty &&
            !formControls?.newPassword?.untouched) ||
          (formControls?.newPassword?.dirty &&
            !formControls?.newPassword?.untouched &&
            formControls?.newPassword?.value.trim() === '')
        "
        class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
        {{
          formControls?.newPassword?.hasError('required')
            ? 'New password is required'
            : ''
        }}
      </div>
    </kendo-formfield>
    <kendo-formfield class="t-w-full t-align-left t-mb-2">
      <kendo-textbox
        (focus)="setCurrentActiveControl('confirmPassword')"
        (valueChange)="setCurrentActiveControl('confirmPassword')"
        (blur)="setCurrentActiveControl('')"
        venioCopyPrevention
        [popover]="confirmNewPassword"
        #confirmPasswordPopoverAnchor="kendoPopoverAnchor"
        kendoPopoverAnchor
        [type]="isConfirmPasswordVisible() ? 'text' : 'password'"
        formControlName="confirmPassword"
        class="t-w-full t-align-left v-input-shadow"
        placeholder="Confirm New Password"
        id="confirmPassword">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            [tabIndex]="-1"
            kendoButton
            themeColor="none"
            fillMode="clear"
            look="clear"
            class="t-pr-2"
            (click)="isConfirmPasswordVisible.set(!isConfirmPasswordVisible())">
            <kendo-svgicon
              [icon]="isConfirmPasswordVisible() ? iconEye : iconSlashEye">
            </kendo-svgicon>
          </button>
        </ng-template>
        <kendo-popover #confirmNewPassword position="right" [width]="280">
          <ng-template kendoPopoverBodyTemplate>
            <div class="v-error-popover t-opacity-90 v-error-popover">
              {{ confirmPasswordStrengthMessage }}
            </div>
          </ng-template>
        </kendo-popover>
      </kendo-textbox>
      @if((formControls?.confirmPassword?.dirty &&
      !formControls?.confirmPassword?.untouched &&
      formControls?.confirmPassword?.invalid) ||
      (formControls?.confirmPassword?.dirty &&
      !formControls?.confirmPassword?.untouched &&
      formControls?.confirmPassword?.value.trim() === '')){
      @if(formControls?.confirmPassword?.hasError('mismatch')){
      <div class="t-text-error t-mb-1">
        Passwords do not match. Please try again.
      </div>
      } @else{
      <div class="t-accent-error t-text-error t-m-1 t-text-[11.23px]">
        {{
          formControls?.confirmPassword?.hasError('required')
            ? 'Confirm new password is required'
            : ''
        }}
      </div>
      } }
    </kendo-formfield>
    <p
      *ngIf="passwordResetResponse()?.type === 'error'"
      class="t-text-error t-text-base t-py-3">
      {{ passwordResetResponse().message }}
    </p>
    <p
      *ngIf="formControls?.isPasswordExpired"
      class="t-text-error t-text-base t-py-3">
      {{ formControls?.remarkMessage.value }}
    </p>
    <button
      (click)="resetClick()"
      kendoButton
      [disabled]="isPasswordResetLoading()"
      class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-mt-2 t-h-[2.5rem] t-rounded-xl t-drop-shadow-md t-font-sans t-text-sm t-border-none">
      <kendo-loader
        *ngIf="isPasswordResetLoading()"
        type="pulsing"
        themeColor="success" />
      Reset
    </button>
  </div>
  }
</form>
