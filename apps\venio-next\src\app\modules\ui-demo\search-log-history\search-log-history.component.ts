import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { IconsModule, SVGIcon } from '@progress/kendo-angular-icons'
import {
  ButtonsModule,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { DataBindingDirective, GridModule } from '@progress/kendo-angular-grid'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { searchIcon } from '@progress/kendo-svg-icons'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { employees } from '../launchpad-caseui/employees'

@Component({
  selector: 'venio-search-log-history',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    InputsModule,
    IconsModule,
    ButtonsModule,
    DropDownButtonModule,
    DropDownsModule,
    LabelModule,
    SvgLoaderDirective,
  ],
  templateUrl: './search-log-history.component.html',
  styleUrls: ['./search-log-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchLogHistoryComponent implements OnInit {
  public svgIconForPageControls = [
    {
      actionType: 'FIRST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: 'PREV_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: 'NEXT_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: 'LAST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  public svgIconForGridControls = [
    {
      actionType: 'ACTION_SEARCH',
      iconPath: 'assets/svg/icon-search.svg',
    },
    {
      actionType: 'ACTION_UPLOAD',
      iconPath: 'assets/svg/icon-tagedit-delete.svg',
    },
  ]

  public searchSvg: SVGIcon = searchIcon

  public pageSize = 20

  public buttonCount = 2

  public sizes = [5, 10, 20, 50]

  public skip = 0

  @ViewChild(DataBindingDirective) public dataBinding: DataBindingDirective

  public gridData: unknown[] = employees

  public gridView: unknown[]

  public currentPage: number

  public ngOnInit(): void {
    this.gridView = this.gridData
  }

  public browseActionClicked(actionType: any): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }
}
