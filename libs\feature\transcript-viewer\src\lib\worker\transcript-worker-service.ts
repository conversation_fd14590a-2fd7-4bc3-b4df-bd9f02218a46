import { Page } from '@venio/shared/models/interfaces'
import { TranscriptDataWorkerType } from '@venio/util/utilities'
import { wrap } from 'comlink'

/**
 * This class handles the management of prepare the document similar data. It utilizes a web worker
 * to process and generate document similar data asynchronously.
 */
export class TranscriptWorkerService {
  private readonly worker: Worker

  /**
   * Initializes the DocumentSimilarService and creates a new web worker
   * for handling generate document similar data.
   */
  constructor() {
    this.worker = new Worker(
      new URL('./transcript-data-transform.worker', import.meta.url),
      { type: 'module' }
    )
  }

  public getTranscriptFlattenedData(
    data: Page,
    documentArray: any
  ): Promise<any> {
    const proxy = wrap<TranscriptDataWorkerType>(this.worker)
    return proxy.flattenTranscriptData(data, documentArray)
  }

  /**
   * Generates a transcript report data based on the provided collection
   *
   * @param {any} transcriptAnnotationData - The transcipt annotation data.
   * @param {string} action - User selected report.
   * @returns {string} A promise that resolves to the generated transcript report data.
   */
  public generateTranscriptReport(transcriptAnnotationData: any): Promise<any> {
    const proxy = wrap<TranscriptDataWorkerType>(this.worker)
    return proxy.transcriptReport(transcriptAnnotationData)
  }
}
