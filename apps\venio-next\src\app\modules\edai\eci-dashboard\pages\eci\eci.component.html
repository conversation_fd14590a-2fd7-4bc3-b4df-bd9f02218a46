<!doctype html>
<div class="page-wrapper">
  <div class="filters-wrapper">
    <button kendoButton [svgIcon]="svgFilter" #anchor (click)="onToggle()">Filters</button>
    @if (show) {
      <kendo-popup [anchor]="anchor.element">
        @if (showCustodianFilters) {
          <kendo-listview [data]="custodians">
            <ng-template kendoListViewItemTemplate let-dataItem="dataItem" let-isFirst="isFirst">
              <app-checkbox-list-item [custodian]="dataItem"></app-checkbox-list-item>
            </ng-template>
          </kendo-listview>
        } @else {
          <div style="padding: 30px; background-color: #fcf7f8">
            <button kendoButton (click)="onCustodianClick()">Custodians</button>
          </div>
        }
      </kendo-popup>
    }
  </div>
  <app-summary></app-summary>
  @if (isFocusedSectionOpened) {
    <app-focused-section></app-focused-section>
  }

  <div class="flex flex-col gap-6">
    <div class="w-full grid grid-cols-1 lg:grid-cols-3 lg:gap-6">
      <div class="col-span-1"><app-relevance></app-relevance></div>

      <div class="col-span-2"><app-word-cloud></app-word-cloud></div>
    </div>
    <div class="w-full grid grid-cols-1 lg:grid-cols-3 lg:gap-6 min-h-[400px]">
      <div class="col-span-1"><app-sunburst [showLegend]="true"></app-sunburst></div>
      <div class="col-span-1"><app-sunburst [showLegend]="true"></app-sunburst></div>
      <div class="col-span-1"><app-sunburst [showLegend]="true"></app-sunburst></div>
    </div>
    <div class="w-full grid grid-cols-1 min-h-[400px]">
      <div class="col-span-1"><app-inappropriate-content></app-inappropriate-content></div>
    </div>
  </div>
</div>
