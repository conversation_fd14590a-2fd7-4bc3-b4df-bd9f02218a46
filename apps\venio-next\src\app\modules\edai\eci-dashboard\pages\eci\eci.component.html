<div class="page-wrapper">
  <div class="t-flex t-justify-between t-items-center t-mb-4">
    <div></div>
    <div class="filters-wrapper">
      <button kendoButton [svgIcon]="svgFilter" #anchor (click)="onToggle()" fillMode="outline" size="medium">
        Filters
      </button>
      @if (show) {
      <kendo-popup [anchor]="anchor.element">
        @if (showCustodianFilters) {
        <kendo-listview [data]="custodians">
          <ng-template kendoListViewItemTemplate let-dataItem="dataItem" let-isFirst="isFirst">
            <app-checkbox-list-item [custodian]="dataItem"></app-checkbox-list-item>
          </ng-template>
        </kendo-listview>
        } @else {
        <div style="padding: 30px; background-color: #fcf7f8">
          <button kendoButton (click)="onCustodianClick()">Custodians</button>
        </div>
        }
      </kendo-popup>
      }
    </div>
  </div>
  <app-summary></app-summary>
  @if (isFocusedSectionOpened) {
  <app-focused-section></app-focused-section>
  }

  <div class="t-flex t-flex-col t-gap-6">
    <div class="t-w-full t-grid t-grid-cols-1 lg:t-grid-cols-3 lg:t-gap-6">
      <div class="t-col-span-1"><app-relevance></app-relevance></div>

      <div class="t-col-span-2"><app-word-cloud></app-word-cloud></div>
    </div>
    <div class="t-w-full t-grid t-grid-cols-1 lg:t-grid-cols-3 lg:t-gap-6 t-min-h-[400px]">
      <div class="t-col-span-1">
        <app-sunburst [showLegend]="true" [chartType]="'document-types'" [title]="'Document Types'">
        </app-sunburst>
      </div>
      <div class="t-col-span-1">
        <app-sunburst [showLegend]="true" [chartType]="'relevant-documents'" [title]="'Relevant Documents'">
        </app-sunburst>
      </div>
      <div class="t-col-span-1">
        <app-sunburst [showLegend]="true" [chartType]="'not-relevant-documents'" [title]="'Not Relevant Documents'">
        </app-sunburst>
      </div>
    </div>
    <div class="t-w-full t-grid t-grid-cols-1 t-min-h-[400px]">
      <div class="t-col-span-1"><app-inappropriate-content></app-inappropriate-content></div>
    </div>
  </div>
</div>