import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentCodingListComponent } from './document-coding-list.component'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { ActivatedRoute } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { of } from 'rxjs'
import { AnimationBuilder } from '@angular/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentCodingListComponent', () => {
  let component: DocumentCodingListComponent
  let fixture: ComponentFixture<DocumentCodingListComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DocumentCodingListComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        AnimationBuilder,
        DocumentsFacade,
        DocumentCodingFacade,
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentCodingListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
