import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerFieldSelectionComponent } from './document-view-designer-field-selection.component'

describe('DocumentViewDesignerFieldSelectionComponent', () => {
  let component: DocumentViewDesignerFieldSelectionComponent
  let fixture: ComponentFixture<DocumentViewDesignerFieldSelectionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerFieldSelectionComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(
      DocumentViewDesignerFieldSelectionComponent
    )
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
