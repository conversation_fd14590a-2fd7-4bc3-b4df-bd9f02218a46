import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'

@Component({
  selector: 'venio-review-create-case-general',
  standalone: true,
  imports: [CommonModule, InputsModule],
  templateUrl: './review-create-case-general.component.html',
  styleUrl: './review-create-case-general.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseGeneralComponent {}
