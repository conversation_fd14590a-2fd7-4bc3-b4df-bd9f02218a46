import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ListViewModule } from '@progress/kendo-angular-listview'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

// Define the interface for a timeline entry
interface TimelineEntry {
  date: string
  user: string
  action: string
  description: string
}

// Define the interface for a grouped timeline
interface GroupedTimeline {
  year: string
  entries: TimelineEntry[]
}

@Component({
  selector: 'venio-document-history-ui',
  standalone: true,
  imports: [CommonModule, ListViewModule, IndicatorsModule],
  templateUrl: './document-history-ui.component.html',
  styleUrl: './document-history-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentHistoryUiComponent {
  public groupedTimelineData: GroupedTimeline[] = [
    {
      year: '2024',
      entries: [
        {
          date: '14 Mar',
          user: '<PERSON>',
          action: 'Tagged',
          description: '',
        },
        {
          date: '14 Mar',
          user: '<PERSON>',
          action: 'Removed',
          description: 'Gloss',
        },
        {
          date: '14 Mar',
          user: '<PERSON>',
          action: 'Removed',
          description: 'Gloss',
        },
        {
          date: '13 Mar',
          user: 'Melissa',
          action: 'Redaction',
          description: 'REDACTED',
        },
        // ... other entries for 2024
      ],
    },
    {
      year: '2023',
      entries: [
        {
          date: '13 Mar',
          user: 'John Doe',
          action: 'Viewed',
          description: 'Document',
        },
        {
          date: '13 Mar',
          user: 'Melissa',
          action: 'Redaction',
          description: '',
        },
        {
          date: '13 Mar',
          user: 'John Doe',
          action: 'Viewed',
          description: 'Document',
        },
        // ... other entries for 2023
      ],
    },
    // ... other years
  ]

  public showSpinner = false

  constructor(private cdr: ChangeDetectorRef) {}

  // Function to load more entries when the user scrolls to the bottom
  public loadMore(year: string): void {
    this.showSpinner = true // enable the loader

    // Simulate an API call with a delay NOTE: This will be removed when we have a real API
    setTimeout(() => {
      this.loadMoreEntries(year)
      this.showSpinner = false // disable the loader
    }, 1000) // Delay of 1 second
  }

  // Helper function to load more entries
  private loadMoreEntries(year: string): void {
    const newEntries: TimelineEntry[] = this.createDummyEntries(year, 5)

    const group = this.groupedTimelineData.find((g) => g.year === year)
    if (group) {
      group.entries = [...group.entries, ...newEntries]
      this.cdr.markForCheck() // Tell Angular to update the list view
    }
  }

  // Helper function to create dummy entries
  private createDummyEntries(year: string, count: number): TimelineEntry[] {
    const newEntries: TimelineEntry[] = []
    const baseDate = new Date(`${year}-01-01T00:00:00`) // Set base date to January 1st of the given year

    for (let i = 0; i < count; i++) {
      // Create a new date for each entry, spaced one day apart
      const entryDate = new Date(baseDate.setDate(baseDate.getDate() + 1))
      const dateString = `${entryDate.getDate()} ${entryDate.toLocaleString(
        'default',
        { month: 'short' }
      )}`

      newEntries.push({
        date: dateString,
        user: `User ${i}`,
        action: `Action ${i}`,
        description: `Description for action ${i}`,
      })
    }

    return newEntries
  }
}
