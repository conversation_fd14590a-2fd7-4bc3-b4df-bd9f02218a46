<div class="t-flex t-flex-wrap t-gap-x-6 t-gap-y-2 t-justify-center t-items-center t-w-full">
  <ng-container *ngFor="let legend of legends; let i = index">
    <div class="t-flex t-items-center t-flex-row t-gap-2 t-min-w-0">
      <div class="t-h-3 t-w-3 t-rounded-sm t-flex-shrink-0" [ngStyle]="{ background: chartColors[i + 1] }">
      </div>
      <p class="t-font-medium t-text-xs t-text-gray-700 t-leading-tight t-text-center t-whitespace-nowrap">
        {{ legend }}
      </p>
    </div>
  </ng-container>
</div>