import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { TextTruncateDirective } from '@venio/feature/shared/directives'
import { filter, map, Subject, takeUntil } from 'rxjs'
import { AiFacade, AiSearchResult } from '@venio/data-access/ai'

@Component({
  selector: 'venio-ai-search-document-list',
  standalone: true,
  imports: [CommonModule, TooltipsModule, TextTruncateDirective],
  templateUrl: './ai-search-document-list.component.html',
  styleUrl: './ai-search-document-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiSearchDocumentListComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  /**
   * Summary list of AI search results
   * Key: AI search UUID
   * Value: AI search result of type AiSearchResult
   */
  public readonly summaryList = signal<
    Array<{ key: string; value: AiSearchResult }>
  >([])

  public ngOnInit(): void {
    this.#selectResetTrigger()
    this.#selectSummary()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public trackBySummary(
    _: number,
    item: { key: string; value: AiSearchResult }
  ): unknown {
    return item.value.content
  }

  #selectSummary(): void {
    this.aiFacade.selectAiSearchAiSummaryList$
      .pipe(
        filter((summary) => typeof summary !== 'undefined'),
        map((sumObject) =>
          Object.keys(sumObject)
            .map((key) => ({ key, value: sumObject[key] }))
            .reverse()
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((summary) => {
        this.summaryList.set(summary)
      })
  }

  #selectResetTrigger(): void {
    this.aiFacade.selectIsResetTriggered$
      .pipe(
        filter((isReset) => isReset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.summaryList.set([])
        this.aiFacade.resetAiState('isResetTriggered')
      })
  }
}
