import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingContainerComponent } from './tag-coding-container.component'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { NO_ERRORS_SCHEMA, PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagCodingContainerComponent', () => {
  let component: TagCodingContainerComponent
  let fixture: ComponentFixture<TagCodingContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TagCodingContainerComponent],
      imports: [
        IframeMessengerModule.forRoot({}),
        EffectsModule.forRoot({}),
        StoreModule.forRoot({}),
      ],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        SearchResultFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagCodingContainerComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
