import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseReprocessingUpdateComponent } from './case-reprocessing-update.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseReprocessingUpdateComponent', () => {
  let component: CaseReprocessingUpdateComponent
  let fixture: ComponentFixture<CaseReprocessingUpdateComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseReprocessingUpdateComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseReprocessingUpdateComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
