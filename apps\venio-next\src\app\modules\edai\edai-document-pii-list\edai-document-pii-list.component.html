<div class="t-inline-block t-py-3 t-pl-3 t-pr-3 t-w-full">
  @if(!eDaiDocumentPII()?.length && !isPIIDocumentLoading()){
  <div class="t-block t-p-4 t-bg-error t-text-white t-mt-2">
    No records found.
  </div>
  } @else { @if(isPIIDocumentLoading()){
  <kendo-skeleton height="30px" width="100%" class="t-mb-2" />
  <kendo-skeleton height="30px" width="100%" class="t-mb-2" />
  } @else {
  <div
    class="t-mb-5 t-rounded-md t-border-2 t-border-[#F3F2F3] t-border-dashed">
    <div class="t-px-2 t-py-3">
      <div class="t-font-bold t-text-sm t-mb-2">PII Type</div>
      <div class="t-flex t-flex-col t-text-[10px] t-gap-1">
        @for(pii of eDaiDocumentPII(); track pii.piiTypeName){
        <div class="t-font-medium">{{ pii.piiTypeName }}</div>
        <div class="t-text-[#707070] t-lowercase">
          <span class="t-capitalize">{{ pii.value }}</span>
        </div>
        }
      </div>
    </div>
  </div>
  } }
</div>
