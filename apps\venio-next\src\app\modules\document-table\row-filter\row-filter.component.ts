import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { TextBoxModule } from '@progress/kendo-angular-inputs'
import {
  FieldFacade,
  FilterParameterModel,
  SearchFacade,
  equalsOperator,
  STRING_OPERATORS,
  DB_OPERATORS,
  NULL_OPERATORS,
  OperatorModel,
  SearchService,
  ReviewBreadcrumbType,
} from '@venio/data-access/review'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { firstValueFrom } from 'rxjs'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { UuidGenerator } from '@venio/util/uuid'

@Component({
  selector: 'venio-row-filter',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    TextBoxModule,
    DropDownListModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './row-filter.component.html',
  styleUrls: ['./row-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RowFilterComponent implements OnInit {
  @Input()
  public displayFieldName: string

  public userValue = new FormControl('')

  public operators: OperatorModel[] = []

  public selectedOperator: OperatorModel

  constructor(
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private searchService: SearchService
  ) {}

  public ngOnInit(): void {
    this.loadOperators()
  }

  private loadOperators(): void {
    firstValueFrom(
      this.fieldFacade.getFieldInfoByDisplayFieldName$(this.displayFieldName)
    ).then((fieldInfo) => {
      const { searchDataType, allowNullSearch } = fieldInfo
      this.operators = [...this.operators, equalsOperator]
      if (searchDataType === 'STRING' || searchDataType === 'DBSTRING') {
        if (searchDataType === 'DBSTRING')
          this.operators = [...this.operators, ...DB_OPERATORS]
        if (fieldInfo.internalFieldName !== 'FILE_PASSWORD')
          this.operators = [...this.operators, ...STRING_OPERATORS]
      } else if (
        searchDataType === 'NUMBER' ||
        searchDataType === 'NUMBER_SIZE' ||
        searchDataType === 'DATE'
      )
        this.operators = [...this.operators, ...DB_OPERATORS]

      if (allowNullSearch)
        this.operators = [...this.operators, ...NULL_OPERATORS]
    })
  }

  public applyFilter(): void {
    const filterParam: FilterParameterModel = {
      field: this.displayFieldName,
      value: this.userValue.value,
      operator: this.selectedOperator.value ?? 'CONTAINS',
    }

    this.searchService.constructFilterQuery(filterParam).then((expression) => {
      this.searchFacade.addBreadcrumb(
        {
          id: UuidGenerator.uuid,
          expression: expression,
          isLogicalGroup: false,
          operator: 'AND',
          children: [],
        },
        ReviewBreadcrumbType.Filter
      )
    })

    // console.log(filterParam)
    // this.searchFacade.filterSubject.next([filterParam])
    // console.log('filter applied')
  }
}
