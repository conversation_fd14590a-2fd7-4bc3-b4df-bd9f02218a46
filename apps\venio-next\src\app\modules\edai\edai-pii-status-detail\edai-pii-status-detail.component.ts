import {
  ChangeDetectionStrategy,
  Component,
  computed,
  HostListener,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  ColumnComponent,
  GridComponent,
} from '@progress/kendo-angular-grid'
import {
  catchError,
  combineLatest,
  filter,
  map,
  of,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs'
import {
  AiFacade,
  AIJobType,
  AiSearchService,
  EDaiStatusModel,
  PIITypes,
} from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'

import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'
import { WINDOW } from '@venio/data-access/iframe-messenger'

@Component({
  selector: 'venio-edai-pii-status-detail',
  standalone: true,
  imports: [
    CommonModule,
    ColumnComponent,
    GridComponent,
    CellTemplateDirective,
  ],
  templateUrl: './edai-pii-status-detail.component.html',
  styleUrl: './edai-pii-status-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiPiiStatusDetailComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private readonly windowRef = inject(WINDOW)

  private readonly aiSearchService = inject(AiSearchService)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly notificationService = inject(NotificationService)

  public readonly jobType = AIJobType

  public readonly selectedJobItem = input<EDaiStatusModel>()

  public readonly statusDetailData = signal([])

  public readonly isPiiOptionsVisible = signal(false)

  /**
   * We'll store container's position (top/left in px) as an object:
   * e.g., { top: '100px', left: '200px' }
   */
  public readonly popoverPosition = signal<{ top: string; left: string }>({
    top: '0px',
    left: '0px',
  })

  public readonly dynamicPrivilegeColumns = signal<string[]>([])

  /**
   * Signal that provides a list of PII (Personally Identifiable Information) types
   * based on the selected job item. The signal is derived from an observable that:
   * - Filters out job items with a non-positive `jobId`.
   * - Fetches job status details for valid `jobId` values and a specified `projectId`.
   * - Ensures that only responses with defined `data` are processed.
   * - Maps the response data to an array of `PIITypes`.
   * - Handles errors by returning an empty array.
   *
   * The signal is initialized with an empty array as the default value.
   *
   */
  private readonly piiTypes = toSignal(
    toObservable(this.selectedJobItem).pipe(
      filter((item) => item.jobId > 0),
      switchMap((item) =>
        this.aiSearchService.fetchJobStatusDetails(
          +this.activatedRoute.snapshot.queryParams['projectId'],
          item?.jobId,
          AIJobType.PIIType
        )
      ),
      filter((r) => typeof r.data !== 'undefined'),
      map((r) => r.data as PIITypes[]),
      catchError(() => of([] as PIITypes[]))
    ),
    {
      initialValue: [],
    }
  )

  /**
   * Computed property to filter and return default PII (Personally Identifiable Information) types.
   * Default types are those where `isCustomType` is `false`.
   *
   */
  public readonly defaultPIITypes = computed(() =>
    this.piiTypes().filter((t) => !t.isCustomType)
  )

  /**
   * Computed property to filter and return custom PII (Personally Identifiable Information) types.
   * Custom types are those where `isCustomType` is `true`.
   *
   */
  public readonly customPIITypes = computed(() =>
    this.piiTypes().filter((t) => t.isCustomType)
  )

  public ngOnInit(): void {
    this.#selectStatusJobDetail()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Handles click events on the document to close the popover when clicked outside its container.
   *
   * - If the popover is not visible, the method returns immediately.
   * - Checks whether the click target is outside the `.pii-popover-container` element.
   * - If the click is outside, the popover visibility is set to `false`.
   *
   * @param {MouseEvent} event - The click event captured from the document.
   * @returns {void}
   */
  @HostListener('document:click', ['$event'])
  public closePopover(event: MouseEvent): void {
    // If popover is not visible, no need to do anything
    if (!this.isPiiOptionsVisible()) return

    // Check if clicked element is outside our popover container
    const target = event.target as HTMLElement
    if (!target.closest('.pii-popover-container')) {
      this.isPiiOptionsVisible.set(false)
    }
  }

  /**
   * Show or hide the popover for selecting options. If the popover is being opened, its position
   * is computed to appear on the right side of the anchor element, with the arrow pointing left.
   *
   * Positioning details:
   * - The popover is aligned to the right of the anchor element with a horizontal gap of 20px.
   * - The top of the popover is aligned with the anchor element's top, offset by 8px.
   * - Positions are adjusted for page scroll using `scrollTop` and `scrollLeft`.
   *
   * @param {MouseEvent} event - The click event that triggers the action.
   * @returns {void}
   */
  public openSelectedOptions(event: MouseEvent): void {
    event.stopPropagation()

    const wasVisible = this.isPiiOptionsVisible()
    this.isPiiOptionsVisible.set(!wasVisible)

    if (!wasVisible) {
      const anchor = event.currentTarget as HTMLElement
      const anchorRect = anchor.getBoundingClientRect()

      const scrollTop = this.windowRef.document.documentElement.scrollTop
      const scrollLeft = this.windowRef.document.documentElement.scrollLeft

      const left = anchorRect.right + scrollLeft + 20

      const top = anchorRect.top + scrollTop - 8

      this.popoverPosition.set({
        top: `${top}px`,
        left: `${left}px`,
      })
    }
  }

  /**
   * Displays a notification message with the specified content and type.
   *
   * - The notification is animated with a fade effect lasting 300ms.
   * - It automatically hides after 10 seconds.
   * - Users can dismiss the notification by clicking on it.
   *
   * @param {string} content - The message content to display. If empty or whitespace, no action is taken.
   * @param {Type} type - The type of the notification (e.g., success, error).
   * @returns {void}
   */
  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  /**
   * Handles the selection of job status details by combining success and error streams.
   *
   * - Listens to job status success and error streams from the facade.
   * - Processes the result or error to construct detailed data and dynamically generated columns.
   * - Updates `statusDetailData` and `dynamicPrivilegeColumns` with processed values.
   * - Displays an error message notification if the response contains an error.
   *
   * @returns {void}
   */
  #selectStatusJobDetail(): void {
    combineLatest([
      this.aiFacade.selectEdaiJobStatusDetailsSuccess$,
      this.aiFacade.selectEdaiJobStatusDetailsError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const results = success?.data
        const style = success?.message ? 'success' : 'error'
        const privTypeDocCount = results?.privilegeTypeDocCount || {}
        const message = success?.message || error?.message

        const privTypeDocCountCols = Object.keys(privTypeDocCount).map((key) =>
          key.replace(/[-|_]/g, ' ')
        )

        const merged = {
          ...results,
          ...Object.keys(privTypeDocCount).reduce((o, originalKey, index) => {
            o[privTypeDocCountCols[index]] = privTypeDocCount[originalKey]
            return o
          }, {}),
        }

        this.statusDetailData.set([merged])

        this.dynamicPrivilegeColumns.set(privTypeDocCountCols)

        // Only display the message if it is an error
        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
