import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewContainerComponent } from './reviewset-detail-view-container.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'

describe('ReviewsetDetailViewContainerComponent', () => {
  let component: ReviewsetDetailViewContainerComponent
  let fixture: ComponentFixture<ReviewsetDetailViewContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewContainerComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
