import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  Signal,
  signal,
  untracked,
  viewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DialogActionsComponent,
  DialogRef,
  DialogTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import {
  SelectEvent,
  TabContentDirective,
  TabStripComponent,
  TabStripTabComponent,
} from '@progress/kendo-angular-layout'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import {
  LoaderComponent,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import {
  AiFacade,
  AIJobType,
  BasicJobForm,
  GeneralForm,
  JobForm,
  JobRequestModel,
  PIIJobForm,
  PrivilegeJobForm,
  PrivilegeTypeInfoForm,
  PrivilegeTypes,
  RelevanceJobForm,
} from '@venio/data-access/ai'
import {
  combineLatest,
  filter,
  map,
  Subject,
  switchMap,
  takeUntil,
  zip,
} from 'rxjs'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { EciDashboardComponent } from '../eci-dashboard/eci-dashboard.component'

import { UiPaginationModule } from '@venio/ui/pagination'
import { EdaiStatusComponent } from '../edai-status/edai-status.component'
import { EdaiFormContainerComponent } from '../edai-form-container/edai-form-container.component'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'

import {
  WindowMessageType,
  WindowMessengerService,
} from '../../../services/window.messenger.service'
import { UtilityPanelType } from '@venio/shared/models/constants'
import { AppIdentitiesTypes } from '@venio/data-access/iframe-messenger'
import { WindowManagerService } from '../../../services/window.manager.service'

@Component({
  selector: 'venio-edai-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogTitleBarComponent,
    TabStripComponent,
    DialogActionsComponent,
    TabStripTabComponent,
    TabContentDirective,
    ButtonComponent,
    ReactiveFormsModule,
    LoaderComponent,
    UiPaginationModule,
    EdaiStatusComponent,
    EdaiFormContainerComponent,
    SkeletonComponent,
    SvgLoaderDirective,
    EciDashboardComponent,
  ],
  templateUrl: './edai-dialog.component.html',
  styleUrl: './edai-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDialogComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly injector = inject(Injector)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly dialogRef = inject(DialogRef)

  private readonly formBuilder = inject(FormBuilder)

  private readonly aiFacade = inject(AiFacade)

  private readonly documentFacade = inject(DocumentsFacade)

  private readonly searchFacade = inject(SearchFacade)

  private readonly notificationService = inject(NotificationService)

  private messengerService = inject(WindowMessengerService)

  private windowManager = inject(WindowManagerService)

  public readonly isCreateJobEdaiLoading = toSignal(
    this.aiFacade.selectIsCreateJobEdaiLoading$,
    {
      initialValue: false,
    }
  )

  public readonly piiTemplateData = toSignal(
    this.aiFacade.selectEdaiPIIEntities$.pipe(
      filter((e) => typeof e !== 'undefined'),
      map((e) => e.data.piiTemplateData)
    ),
    {
      initialValue: undefined,
    }
  )

  public readonly isStatusTab = signal(false)

  public readonly isEciDashboardTab = signal(false)

  public readonly isDocumentSelected = signal(false)

  public edaiFormGroup: FormGroup<JobForm> = null

  public readonly tabStrip: Signal<TabStripComponent> =
    viewChild(TabStripComponent)

  private get jobFormControls(): JobForm {
    return this.edaiFormGroup?.controls
  }

  private readonly selectedJobType = toSignal(
    toObservable(signal(undefined)).pipe(
      switchMap(() => this.jobFormControls?.jobType?.valueChanges)
    )
  )

  public readonly isJobFormInvalid = toSignal(
    toObservable(signal(false)).pipe(
      switchMap(() =>
        zip([
          this.edaiFormGroup?.statusChanges,
          this.edaiFormGroup?.valueChanges,
        ])
      ),
      map(() => this.edaiFormGroup?.invalid)
    ),
    {
      initialValue: false,
    }
  )

  #patchTemplateValues(): void {
    effect(
      () => {
        const templateData = this.piiTemplateData()
        if (!templateData) {
          return
        }
        untracked(() => {
          // First, we need to clear the existing values
          if (templateData.piiTypesDefault.length > 0) {
            this.jobFormControls.piiJobModel.controls.defaultTypes.clear()
          }

          templateData.piiTypesDefault.forEach((element) => {
            this.jobFormControls.piiJobModel.controls.defaultTypes.push(
              this.formBuilder.group<GeneralForm>({
                id: new FormControl(element.id),
                name: new FormControl(element.piiTypeName),
              })
            )
          })
          this.jobFormControls.piiJobModel.controls.defaultTypes.updateValueAndValidity()
          if (templateData.piiTypesCustom?.length > 0) {
            this.jobFormControls.piiJobModel.controls.customTypes.clear()
            templateData.piiTypesCustom.forEach((element) => {
              this.jobFormControls.piiJobModel.controls.customTypes.push(
                this.formBuilder.group<GeneralForm>({
                  name: new FormControl(element.piiTypeName),
                  description: new FormControl(element.piiTypeDescription),
                })
              )
            })
          }
        })
      },
      { injector: this.injector }
    )
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#initForm()
    this.#selectDocumentIds()
    this.#selectJobResponse()
    this.#formValidations()
    this.#patchTemplateValues()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public tabSelectionChange(se: SelectEvent): void {
    this.isStatusTab.set(se.index === 1)
    this.isEciDashboardTab.set(se.index === 2)

    // Only reset form data for AI Job and Status tabs, not for ECI Dashboard
    if (se.index !== 2) {
      this.#resetToDefault()
    }

    // If the selected type is PII, we need to fetch the latest data from template
    if (this.selectedJobType() === AIJobType.PII && !this.isStatusTab() && se.index !== 2) {
      this.#fetchPiiEntities()
    }
  }

  public closeDialog(): void {
    this.dialogRef.close()
  }

  public submit(): void {
    if (!this.isDocumentSelected()) {
      this.#showMessage(
        ' No document selected. Please select document(s) to proceed.',
        {
          style: 'error',
        }
      )
      return
    }

    const formValue = this.edaiFormGroup.getRawValue()

    const finalPayload = this.#buildPayload(
      formValue as unknown as JobRequestModel
    ) as JobRequestModel

    if (!finalPayload) return

    this.aiFacade.createJobEdai(this.projectId, finalPayload)
  }

  #buildPayload(formValue: Partial<JobRequestModel>): Partial<JobRequestModel> {
    const {
      jobType,
      basicJobModel,
      relevanceJobModel,
      privilegeJobModel,
      piiJobModel,
    } = formValue

    const payloadBase: Partial<JobRequestModel> = {
      jobType,
      basicJobModel,
      // more in the future
    }

    // Now keep only the relevant sub-model, based on jobType
    switch (jobType) {
      case AIJobType.Relevance:
        return {
          ...payloadBase,
          relevanceJobModel,
        }

      case AIJobType.Privilege:
        return {
          ...payloadBase,
          privilegeJobModel: {
            attorneyList: privilegeJobModel.attorneyList,
            domains: privilegeJobModel.domains,
            privilegeTypes: privilegeJobModel.privilegeTypes
              .filter(
                (p) =>
                  (typeof p.name === 'boolean' && p.name) ||
                  (typeof p.name === 'string' && p.name.trim())
              )
              .map((priv) => {
                switch (priv.privilegeType) {
                  case PrivilegeTypes.AttorneyClient:
                  case PrivilegeTypes.WorkProduct:
                    return {
                      // Backend requires name value to be exact as assigned below
                      name:
                        priv.privilegeType === PrivilegeTypes.AttorneyClient
                          ? 'Attorney-Client'
                          : 'Work Product',
                      description: priv.description,
                      // Exact value needs to be passed to backend
                      privType: priv.privilegeType,
                    }
                  case PrivilegeTypes.CustomType1:
                  case PrivilegeTypes.CustomType2:
                    return {
                      name: priv.name,
                      description: priv.description,
                      // Exact value needs to be passed to backend
                      privType: priv.privilegeType,
                    }
                }
              }),
          },
        }

      case AIJobType.PII: {
        const defaultTypes = piiJobModel.defaultTypes.filter((type) =>
          type.name?.trim()
        )
        const customTypes = piiJobModel.customTypes.filter((type) =>
          type.name?.trim()
        )
        const filteredPiiJobModel = {
          ...piiJobModel,
          defaultTypes: defaultTypes.length > 0 ? defaultTypes : null,
          customTypes: customTypes.length > 0 ? customTypes : null,
        }

        return {
          ...payloadBase,
          piiJobModel: filteredPiiJobModel,
        }
      }
    }
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #resetToDefault(): void {
    // Grab the current job type so we can preserve it
    const currentJobType = this.edaiFormGroup.get('jobType')?.value

    // Grab default values of basicJobModel
    const basicJobModel = this.edaiFormGroup.get('basicJobModel').getRawValue()

    // Now reset the entire FormGroup, but give "jobType" the existing value
    this.edaiFormGroup.reset({
      jobType: currentJobType,
      basicJobModel: {
        ...basicJobModel,
        jobName: '',
      },
      relevanceJobModel: {
        issues: [{ name: '01 Issue', description: '' }],
      },
      privilegeJobModel: {
        attorneyList: '',
        domains: '',
        privilegeTypes: [
          {
            privilegeType: PrivilegeTypes.AttorneyClient,
            name: false,
            description: '',
          },
          {
            privilegeType: PrivilegeTypes.WorkProduct,
            name: false,
            description: '',
          },
          {
            privilegeType: PrivilegeTypes.CustomType1,
            name: '',
            description: '',
          },
          {
            privilegeType: PrivilegeTypes.CustomType2,
            name: '',
            description: '',
          },
        ],
      },
      piiJobModel: {
        defaultTypes: [],
        customTypes: [{ name: '', description: '' }],
        isPIIExtractJob: false,
      },
    })

    this.edaiFormGroup.updateValueAndValidity()

    // (Optional) Mark the form as pristine or untouched if needed
    this.edaiFormGroup.markAsPristine()
    this.edaiFormGroup.markAsUntouched()
  }

  #selectJobResponse(): void {
    combineLatest([
      this.aiFacade.selectCreateJobEdaiSuccess$,
      this.aiFacade.selectCreateJobEdaiError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : isError ? 'error' : 'info'
        const message = success?.message || error?.message

        this.#showMessage(message, { style })

        this.aiFacade.resetAiState([
          'createJobEdaiSuccess',
          'createJobEdaiError',
        ])

        if (!isError && success?.message) {
          // Once the job is created, switch to the status tab
          this.tabStrip().selectTab(1)
          this.isStatusTab.set(true)

          // Reset the form to default
          this.#resetToDefault()
          this.#notifyTagCodingDataUpdateToPopupWindow()
        }
      })
  }

  #selectDocumentIds(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.documentFacade.getIsBatchSelected$,
    ])
      .pipe(
        filter(([response]) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([
          searchResponse,
          selectedFileIds,
          unSelectedFileIds,
          isBatchSelected,
        ]) => {
          // Set required values to the form for payload
          const {
            tempTables: { searchResultTempTable },
          } = searchResponse
          this.edaiFormGroup.patchValue({
            basicJobModel: {
              searchTempTable: searchResultTempTable,
              selectedFileIds,
              unSelectedFileIds,
              isBatchSelected,
            },
          })

          // Whether the document is selected or is batch selected
          this.isDocumentSelected.set(
            isBatchSelected || (!isBatchSelected && selectedFileIds.length > 0)
          )
        }
      )
  }

  #createGeneralFormGroup(
    controlNames: Partial<Record<keyof GeneralForm, string>>[]
  ): FormGroup<GeneralForm>[] {
    return controlNames.map((record) =>
      this.formBuilder.group<GeneralForm>({
        ...Object.entries(record).reduce(
          (acc, [key, value]) => ({
            ...acc,
            [key]: this.formBuilder.control(value),
          }),
          {} as Record<keyof GeneralForm, FormControl<string & number & null>>
        ),
      })
    )
  }

  #createPrivilegeTypeGroup(
    name: PrivilegeTypes,
    formState: string | boolean
  ): FormGroup<PrivilegeTypeInfoForm> {
    return this.formBuilder.group<PrivilegeTypeInfoForm>({
      privilegeType: this.formBuilder.control(name),
      name: this.formBuilder.control(formState),
      description: this.formBuilder.control(''),
    })
  }

  #atLeastOnePrivilegeTypeValidator(
    control: FormArray<FormGroup<PrivilegeTypeInfoForm>>
  ): ValidationErrors | null {
    const jobType: AIJobType = control?.parent?.parent?.get('jobType').value
    if (jobType !== AIJobType.Privilege) return null

    const privilegeTypes = control.value

    // Check if at least one of AttorneyClient or WorkProduct has name = true
    const hasRequiredType = privilegeTypes.some(
      (type) =>
        (type.privilegeType === PrivilegeTypes.AttorneyClient ||
          type.privilegeType === PrivilegeTypes.WorkProduct) &&
        type.name
    )

    // If no required type is selected, return an error
    if (!hasRequiredType) {
      return { atLeastOnePrivilegeTypeRequired: true }
    }

    // If validation passes, return null
    return null
  }

  #initForm(): void {
    this.edaiFormGroup = this.formBuilder.group<JobForm>({
      jobType: this.formBuilder.control(AIJobType.Relevance, {
        validators: Validators.required,
        nonNullable: true,
      }),
      basicJobModel: this.formBuilder.group<BasicJobForm>({
        jobName: this.formBuilder.control('', Validators.required),
        searchTempTable: this.formBuilder.control('', { nonNullable: true }),
        selectedFileIds: this.formBuilder.control([], { nonNullable: true }),
        unSelectedFileIds: this.formBuilder.control([], { nonNullable: true }),
        isBatchSelected: this.formBuilder.control(false, { nonNullable: true }),
      }),
      relevanceJobModel: this.formBuilder.group<RelevanceJobForm>({
        issues: this.formBuilder.array<FormGroup<GeneralForm>>(
          // When creating the issue form, the name is required with sequential numbering e.g. 01 Issue, 02 Issue, etc.
          this.#createGeneralFormGroup([{ name: '01 Issue', description: '' }])
        ),
      }),
      privilegeJobModel: this.formBuilder.group<PrivilegeJobForm>({
        attorneyList: this.formBuilder.control('', [
          Validators.maxLength(2000),
        ]),
        domains: this.formBuilder.control(''),
        privilegeTypes: this.formBuilder.array<
          FormGroup<PrivilegeTypeInfoForm>
        >(
          [
            this.#createPrivilegeTypeGroup(
              PrivilegeTypes.AttorneyClient,
              false
            ),
            this.#createPrivilegeTypeGroup(PrivilegeTypes.WorkProduct, false),
            this.#createPrivilegeTypeGroup(PrivilegeTypes.CustomType1, ''),
            this.#createPrivilegeTypeGroup(PrivilegeTypes.CustomType2, ''),
          ],
          { validators: this.#atLeastOnePrivilegeTypeValidator }
        ),
      }),
      piiJobModel: this.formBuilder.group<PIIJobForm>(
        {
          // To patch values as selected PII types,
          // [name: 'Full Name', description: ''] etc. which the name becomes selected type value of entities;
          defaultTypes: this.formBuilder.array<FormGroup<GeneralForm>>(
            this.#createGeneralFormGroup([])
          ),
          customTypes: this.formBuilder.array<FormGroup<GeneralForm>>(
            this.#createGeneralFormGroup([{ name: '', description: '' }])
          ),
          // Later, it'll be used whether the job is PII extraction or not
          isPIIExtractJob: this.formBuilder.control(false, {
            nonNullable: true,
          }),
        },
        { validators: this.#customPIIValidator }
      ),
    })
  }

  /**
   * Validates the PII job form to ensure it meets the required criteria.
   * - At least one valid PII option (default or custom) must be present.
   * - The total number of PII options (default + custom) must not exceed 15.
   * - Custom types with a `name` must have a `description`.
   * - Validation only applies when the selected job type is PII.
   *
   * @param {FormGroup<PIIJobForm>} formGroup - The form group containing defaultTypes and customTypes controls.
   * @returns {ValidationErrors | null} - Returns an error object if validation fails, otherwise null.
   */
  #customPIIValidator(
    formGroup: FormGroup<PIIJobForm>
  ): ValidationErrors | null {
    const selectedType = formGroup.parent?.get('jobType')?.value
    if (!selectedType || selectedType !== AIJobType.PII) return null

    const defaultTypes = formGroup.controls.defaultTypes
    const customTypes = formGroup.controls.customTypes

    const totalPII = defaultTypes.length + customTypes.length
    if (totalPII > 15) {
      return { maxPiiOptionsExceeded: true }
    }

    const hasValidDefaultTypes = defaultTypes.length > 0
    const hasValidCustomTypes = customTypes.controls.some((customType) =>
      customType.controls.name?.value?.trim()
    )

    if (!hasValidDefaultTypes && !hasValidCustomTypes) {
      return { atLeastOnePIIRequired: true }
    }

    for (const customType of customTypes.controls) {
      const name = customType.controls.name?.value?.trim()
      const description = customType.controls.description?.value?.trim()

      if (name && !description) {
        return { descriptionRequired: true }
      }
    }

    return null
  }

  #formValidations(): void {
    effect(
      () => {
        // Decide which validations to enable based on jobType
        const jobType = this.selectedJobType()
        switch (jobType) {
          case AIJobType.Relevance:
            this.#enableRelevanceValidations()
            this.#clearPrivilegeValidations()
            break

          case AIJobType.Privilege:
            this.#enablePrivilegeValidations()
            this.#clearRelevanceValidations()
            break

          case AIJobType.PII:
            this.#clearRelevanceValidations()
            this.#clearPrivilegeValidations()
            // If this method triggers any signal writes, it'll throw an error so use untracked
            // to avoid that while still allowing to use signals i.e. toSignal, signal, etc.
            // This is because the signal writes are not allowed inside the effect.
            // So we tell effect to un-tract the dependencies and not to throw an error.
            // Not recommended to overuse untracked, only use when you know what you are doing.
            untracked(() => this.#fetchPiiEntities())
            break
        }

        // Finally, re-validate the top-level form so .valid / .invalid are correct
        this.edaiFormGroup.updateValueAndValidity({ emitEvent: false })
      },
      { injector: this.injector }
    )
  }

  #fetchPiiEntities(): void {
    this.aiFacade.fetchPIIEntities(this.projectId)
  }

  #enableRelevanceValidations(): void {
    const issuesArray = this.jobFormControls.relevanceJobModel.controls.issues
    issuesArray.controls.forEach((issueControl) => {
      const descCtrl = issueControl.controls.description
      descCtrl.setValidators([Validators.required])
      descCtrl.updateValueAndValidity({ emitEvent: false })
    })
  }

  #clearRelevanceValidations(): void {
    const issuesArray = this.jobFormControls.relevanceJobModel.controls.issues
    issuesArray.controls.forEach((issueControl) => {
      const descCtrl = issueControl.controls.description
      descCtrl.setValidators([])
      descCtrl.updateValueAndValidity({ emitEvent: false })
      descCtrl.reset('')
    })
  }

  #enablePrivilegeValidations(): void {
    const privilegeGroup = this.jobFormControls.privilegeJobModel

    const attorneyList = privilegeGroup.controls.attorneyList
    const domains = privilegeGroup.controls.domains
    attorneyList.setValidators([
      Validators.required,
      Validators.maxLength(2000),
    ])
    domains.setValidators([Validators.required])
    attorneyList.updateValueAndValidity({ emitEvent: false })
    domains.updateValueAndValidity({ emitEvent: false })

    const privilegeArray = privilegeGroup.controls.privilegeTypes
    privilegeArray.controls.forEach((ctrl: AbstractControl) => {
      const group = ctrl as FormGroup<PrivilegeTypeInfoForm>
      const privType = group.controls.privilegeType.value
      const nameCtrl = group.controls.name as AbstractControl<string>
      const descCtrl = group.controls.description

      if (
        privType === PrivilegeTypes.CustomType1 ||
        privType === PrivilegeTypes.CustomType2
      ) {
        if (nameCtrl.value?.trim().length) {
          descCtrl.setValidators([Validators.required])
        } else {
          descCtrl.setValidators([])
        }
        descCtrl.updateValueAndValidity({ emitEvent: false })
      }
    })
  }

  #clearPrivilegeValidations(): void {
    const privilegeGroup = this.jobFormControls.privilegeJobModel
    const attorneyList = privilegeGroup.controls.attorneyList
    const domains = privilegeGroup.controls.domains

    attorneyList.reset('')
    domains.reset('')

    attorneyList.setValidators([])
    domains.setValidators([])
    attorneyList.updateValueAndValidity({ emitEvent: false })
    domains.updateValueAndValidity({ emitEvent: false })

    const privilegeArray = privilegeGroup.controls.privilegeTypes
    privilegeArray.controls.forEach((ctrl: AbstractControl) => {
      const group = ctrl as FormGroup<PrivilegeTypeInfoForm>
      group.controls.name.setValidators([])
      group.controls.description.setValidators([])
      group.controls.name.updateValueAndValidity({ emitEvent: false })
      group.controls.description.updateValueAndValidity({ emitEvent: false })
      group.controls.name.reset()
      group.controls.description.reset('')
    })
  }

  #notifyTagCodingDataUpdateToPopupWindow(): void {
    const popoutWindow: Window = this.windowManager.getWindow(
      AppIdentitiesTypes.REVIEW_PANEL
    )
    if (popoutWindow) {
      this.messengerService.sendMessage(
        {
          payload: {
            type: WindowMessageType.DATA_UPDATE,
            content: {
              componentId: UtilityPanelType.TAG_CODING,
              projectId: this.projectId,
              isTagupdate: true,
            },
          },
        },
        popoutWindow
      )
    }
  }
}
