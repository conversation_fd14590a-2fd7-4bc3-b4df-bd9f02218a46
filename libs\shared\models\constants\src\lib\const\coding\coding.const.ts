export const CodingErrorMessages = {
  MissingDelimiter: {
    key: 'missingDelimiter',
    message: 'Multiple values must be separated by the specified delimiter',
  },
  InvalidDate: {
    key: 'invalidDate',
    message: 'Please enter a valid date',
  },
  InvalidDateTime: {
    key: 'invalidDateTime',
    message: 'Please enter a valid datetime',
  },
  InvalidDateFormat: {
    key: 'invalidDateFormat',
    message: 'Please use the correct date format',
  },
  InvalidDateTimeFormat: {
    key: 'invalidDateTimeFormat',
    message: 'Please use the correct date and time format',
  },
  InvalidInteger: {
    key: 'invalidInteger',
    message: 'Please enter a valid integer number',
  },
  InvalidIntegerLength: (
    length: number
  ): {
    key: string
    message: string
  } => ({
    key: 'invalidIntegerLength',
    message: `Maximum ${length} digits allowed`,
  }),
  InvalidNumeric: {
    key: 'invalidNumeric',
    message: 'Please enter a valid number',
  },
  InvalidNumericScale: (
    scale: number
  ): {
    key: string
    message: string
  } => ({
    key: 'invalidNumericScale',
    message: `Maximum ${scale} decimal places allowed`,
  }),
  InvalidNumericLength: (
    length: number
  ): {
    key: string
    message: string
  } => ({
    key: 'invalidNumericScale',
    message: `Maximum ${length} digits allowed before decimal point`,
  }),
  TextTooLong: {
    key: 'textTooLong',
    message: 'Text exceeds maximum length',
  },
  ParagraphTooLong: {
    key: 'paragraphTooLong',
    message: 'Paragraph exceeds maximum length',
  },
  InvalidBoolean: {
    key: 'invalidBoolean',
    message: 'Please enter true/false or 1/0',
  },
}
