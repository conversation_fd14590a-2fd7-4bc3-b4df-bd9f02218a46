import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  output,
  signal,
  TrackByFunction,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  CheckboxColumnComponent as GridCheckboxColumn,
  ColumnComponent as GridColumnComponent,
  GridComponent,
  GridDataResult,
  GridItem,
  HeaderTemplateDirective,
  NoRecordsTemplateDirective,
  PageChangeEvent,
  SelectionDirective,
} from '@progress/kendo-angular-grid'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ReviewsetGridActionsComponent } from '../reviewset-grid-actions/reviewset-grid-actions.component'
import {
  CaseDetailModel,
  LaunchpadAction,
  ReviewSetEntry,
} from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import {
  CompositeFilterDescriptor,
  SortDescriptor,
} from '@progress/kendo-data-query'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { DebounceTimer } from '@venio/util/utilities'
import { cloneDeep } from 'lodash'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ProgressBarComponent } from '@progress/kendo-angular-progressbar'
import { combineLatest, filter } from 'rxjs'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import {
  WindowComponent,
  WindowTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { ReviewsetDetailViewHeaderComponent } from '../../reviewset-detail-view-header/reviewset-detail-view-header.component'
import { ReviewsetDetailViewDialogContainerComponent } from '../../reviewset-detail-view-dialog-container/reviewset-detail-view-dialog-container.component'
import { StartupsFacade, UserRights } from '@venio/data-access/review'
import { ReviewsetBatchesDashboardHeaderComponent } from '../../reviewset-batches-view-dashboard/reviewset-batches-dashboard-header/reviewset-batches-dashboard-header.component'
import { ReviewsetBatchesViewDashboardComponent } from '../../reviewset-batches-view-dashboard/reviewset-batches-view-dashboard.component'

@Component({
  selector: 'venio-reviewset-grid',
  standalone: true,
  imports: [
    CommonModule,
    GridComponent,
    SelectionDirective,
    GridColumnComponent,
    HeaderTemplateDirective,
    TooltipDirective,
    CellTemplateDirective,
    ReviewsetGridActionsComponent,
    NoRecordsTemplateDirective,
    GridCheckboxColumn,
    SkeletonComponent,
    DynamicHeightDirective,
    ProgressBarComponent,
    SvgLoaderDirective,
    WindowTitleBarComponent,
    ReviewsetDetailViewHeaderComponent,
    ReviewsetDetailViewDialogContainerComponent,
    ReviewsetBatchesDashboardHeaderComponent,
    ReviewsetBatchesViewDashboardComponent,
    WindowComponent,
  ],
  templateUrl: './reviewset-grid.component.html',
  styleUrl: './reviewset-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetGridComponent implements OnDestroy, OnInit {
  private readonly projectFacade = inject(ProjectFacade)

  private readonly permissionFacade = inject(StartupsFacade)

  private readonly notificationService = inject(NotificationService)

  private readonly injector = inject(Injector)

  public readonly reviewSetDetailViewDialogVisibility = signal(false)

  public readonly reviewerDashboardDialogVisibility = signal(false)

  public readonly selectedReviewSetEntry = signal<ReviewSetEntry>(undefined)

  /** Output event for the action invoked.
   * When the actions of grid e.g., produce, upload, etc. are clicked, this event is emitted with type `LaunchpadAction`
   */
  public readonly actionInvoked = output<LaunchpadAction>()

  /** An instance for grid [data] object including data and total */
  public gridView = signal<GridDataResult>(undefined)

  public sort: SortDescriptor[] = []

  /** This page size is only for the client side grid rendering optimization, not for serve.
   * This way, the UI is not overwhelmed with too many records at once making the grid unresponsive.
   *
   * When a user scrolls up or down, the grid will add or remove records based on the pageSize and skip
   * while the actual count of data is fetched from the server.
   */
  public readonly pageSize = 45

  /** The number of records to skip when virtualization is enabled */
  public skip = 0

  /** Static common action types */
  public readonly commonActionTypes = CommonActionTypes

  /**
   * User rights for the current project.
   */
  private readonly projectRights = toSignal(
    this.projectFacade.selectProjectIdsToRights$
  )

  /**
   * Weather a user can manage the review set.
   * Checks if the user has the right to manage the review set and is not a reviewer.
   * @param {ReviewSetEntry} reviewSet - The review set entry.
   * @returns {boolean} - The boolean value.
   */
  public canManageReviewSet(reviewSet: ReviewSetEntry): boolean {
    const { projectId } = reviewSet
    const rights = this.projectRights()?.[projectId]
    return rights?.includes(UserRights.ALLOW_TO_MANAGE_REVIEW_SET)
  }

  /** Signal for the selected projectIds */
  public readonly selectedProjectIds = toSignal(
    this.projectFacade.selectSelectedCaseDetail$.pipe(
      map(
        (selected: CaseDetailModel[]) =>
          selected?.map((item) => item.projectId) || []
      )
    )
  )

  /** Signal for the review set detail loading state */
  public isReviewSetSummaryDetailLoading = toSignal(
    this.projectFacade.selectIsReviewSetSummaryDetailLoading$,
    { initialValue: true }
  )

  /** Signal for the review set detail list */
  private readonly reviewSetSummaryDetail = toSignal(
    this.projectFacade.selectReviewSetSummaryDetail$
  )

  private readonly reviewSetDeleteResponse = toSignal(
    combineLatest([
      this.projectFacade.selectReviewSetDeleteSuccess$,
      this.projectFacade.selectReviewSetDeleteError$,
    ]).pipe(
      filter(
        ([success, error]) =>
          typeof success !== 'undefined' || typeof error !== 'undefined'
      )
    )
  )

  /** Signal for the review set detail list */
  private readonly loadedReviewSets = computed<ReviewSetEntry[]>(
    () => this.reviewSetSummaryDetail()?.reviewSetEntries || []
  )

  /** Signal for the total review set count */
  private readonly totalReviewSets = computed<number>(
    () => this.reviewSetSummaryDetail()?.totalReviewSetCount || 0
  )

  constructor() {
    // Effects are handle in the constructor.
    // if we need to handle outside the constructor, we need to use
    // injector and use it in lifecycle hooks.
    effect(() => {
      // If the review set detail is loading, exit early;
      if (this.isReviewSetSummaryDetailLoading()) return

      untracked(() => this.loadGridData())
    })
  }

  public ngOnInit(): void {
    this.#reviewSetActionResponse()
  }

  public ngOnDestroy(): void {
    this.#resetReviewSetStates()
  }

  /** Handles the paging event for the virtual scroll to avoid loading all the data at once
   * stressing the UI performance. Instead, we load the data in chunks.
   *
   * @see loadGridData
   * @see pageSize
   * @see skip
   *
   * @param {PageChangeEvent} event - The paging event.
   * @returns {void}
   */
  public handlePagingForVirtualScroll(event: PageChangeEvent): void {
    this.skip = event.skip
    this.loadGridData()
  }

  /** Loads the grid data based on the skip and pageSize
   * @see skip
   * @see pageSize
   * @see loadedReviewSets
   * @returns {void}
   */
  private loadGridData(): void {
    // this.changeDetectorRef.markForCheck()
    const allReviewSets = cloneDeep(this.loadedReviewSets())
    this.gridView.set({
      data: allReviewSets.slice(this.skip, this.skip + this.pageSize),
      total: this.totalReviewSets(),
    })

    // if the review set batch file is deleted then we need to update the count in the header
    // we'll check if the review set detail view dialog is open and then update the selected review set entry
    if (this.reviewSetDetailViewDialogVisibility()) {
      const selected = allReviewSets.find(
        (item) =>
          item.reviewSetId === this.selectedReviewSetEntry()?.reviewSetId &&
          item.projectId === this.selectedReviewSetEntry()?.projectId
      )
      this.selectedReviewSetEntry.set(selected)
    }
  }

  /** Stores the selected review set in the store.
   * @param {string[]} event - The selected review set unique Id
   * @returns {void}
   * */
  public selectReviewSet(event: string[]): void {
    const selectedEntities = this.loadedReviewSets().filter((item) =>
      event.includes(item.uuid)
    )
    this.projectFacade.storeSelectedReviewSet(selectedEntities)
  }

  /** Handles the review set filter change event.
   * @param {CompositeFilterDescriptor} value - The filter descriptor.
   * @returns {void}
   **/
  public reviewSetFilterChange(value: CompositeFilterDescriptor): void {
    // Whenever the filter changes, we need to fetch the review set detail, again
    // The filter payload is store in the review set request info and is used to fetch the review set detail
    this.projectFacade.fetchReviewSetSummaryDetail()
  }

  /** Handles the review set sort order change event. It updates the sort field and sort order in the review set request info.
   * @param {SortDescriptor[]} sort - The sort descriptor.
   * @returns {void}
   */
  public caseSortOrderChange(sort: SortDescriptor[]): void {
    const dir = sort[0]?.dir
    this.projectFacade.updateReviewSetDetailRequestInfo({
      sortField: sort[0]?.field,
      sortOrder: dir === 'asc' ? 'asc' : dir === 'desc' ? 'desc' : '',
    })

    this.sort = [
      {
        field: sort[0]?.field,
        dir: dir === 'asc' ? 'asc' : dir === 'desc' ? 'desc' : undefined,
      },
    ]

    // Whenever the sort order changes, we need to fetch the review set detail, again
    this.projectFacade.fetchReviewSetSummaryDetail()
  }

  /**
   * When column action controls are clicked, this method is called which then
   * emits another event to the parent component with selected review set details and action type.
   * @param {CommonActionTypes} actionType - The action type that is clicked.
   * @param {CaseDetailModel} content - The selected review set details.
   * @returns {void}
   */
  @DebounceTimer(200)
  public forwardActionControlClick(
    actionType: CommonActionTypes,
    content: CaseDetailModel
  ): void {
    this.actionInvoked.emit({
      actionType,
      content,
    })
  }

  /**
   * The grid data is rendered as virtual scroll, so when the user scrolls up or down,
   * the data are added or removed based on the pageSize and skip.
   *
   * To reflect actual changes in the UI, we need to track the data by the projectId.
   * @param {number} _ - The index of the item.
   * @param {GridItem} item - The grid item.
   * @returns {TrackByFunction<GridItem>} - The track by function.
   */
  public caseTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    JSON.stringify(item.data) as unknown as TrackByFunction<GridItem>

  public openReviewSetViewDetailDialog(selected: ReviewSetEntry): void {
    this.selectedReviewSetEntry.set(selected)
    this.reviewSetDetailViewDialogVisibility.set(true)
  }

  public closeReviewSetViewDetailDialog(): void {
    this.reviewSetDetailViewDialogVisibility.set(false)
    this.selectedReviewSetEntry.set(undefined)
  }

  public openReviewerDashboardDialog(selected: ReviewSetEntry): void {
    const { projectId } = selected
    const rights = this.projectRights()?.[projectId]
    const shouldShowReviewDashboard = rights.includes(
      UserRights.ALLOW_TO_VIEW_REVIEW_DASHBOARD
    )
    if (shouldShowReviewDashboard) {
      this.selectedReviewSetEntry.set(selected)
      this.reviewerDashboardDialogVisibility.set(true)
    }
  }

  public closeReviewerDashboardDialog(): void {
    this.reviewerDashboardDialogVisibility.set(false)
    this.selectedReviewSetEntry.set(undefined)
  }

  #resetReviewSetStates(): void {
    this.projectFacade.resetProjectState([
      'reviewSetSummaryDetailSuccess',
      'reviewSetSummaryDetailError',
      'isReviewSetSummaryDetailLoading',
      'reviewSetSummaryDetailRequestInfo',
      'selectedReviewSetSummaryDetail',
    ])
  }

  #reviewSetActionResponse(): void {
    effect(
      () => {
        const response = this.reviewSetDeleteResponse()
        if (!response) return

        const [success, error] = response

        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : isError ? 'error' : 'info'
        const message = success?.message || error?.message
        // Since we are using signal to load response,
        // by doing reset from effect method is also signal writes so
        // we need to tell angular not to track this avoiding signal write error
        untracked(() => {
          this.projectFacade.resetProjectState([
            'reviewSetDeleteSuccess',
            'reviewSetDeleteError',
            'isReviewSetDeleteLoading',
          ])
        })

        if (!message) return

        this.#showMessage(message, { style })
      },
      { injector: this.injector }
    )
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }
}
