import { DocumentCodingControlActionType } from '@venio/shared/models/constants'

export interface DocumentCodingModel {
  customFieldInfoId: number
  fieldName: string
  description: string
  displayName: string
  uiInputType: string
  createdOn: string
  createdBy: number
  allowMultipleCodingValues: boolean
  delimiterForCodingValues: string
  allowPredefinedCodingValuesOnly: boolean
  detailDataCount: number
  scale: number
  length: number
  multiValuedCodingOptions: number
  fieldCodingValues: string[]
  updatedFieldValue: string
  currentFieldValue: string
  dateFormat: string
}

export interface CodingSaveModel {
  fieldCodingModel: DocumentCodingModel[]
  state: CodingState
}

export interface CodingState {
  globalTempTable: string
  codingParameterValue: string
  searchId: number
  userId: number
  codingInfo: string
  isBulkCoding: boolean
}

export interface CodingFieldSavedModel {
  fileId: number
  fileName: string
  codingFieldSavedChange: CodingSummary[]
}

export interface CodingSummary {
  fieldName: string
  fieldDisplayName: string
  oldValue: string
  newValue: string
}

export interface CurrentFieldInfo {
  length: number | undefined
  scale: number | undefined
  delimiter: string | undefined
  description: string | undefined
  uiInputType: string | undefined
}

export interface DocumentCodingResponseType {
  fieldCodingModel: DocumentCodingModel[]
}

export interface DocumentCodingViewModel {
  customFieldInfoId: number
  fieldName: string
  description: string
  displayName: string
  uiInputType: string
  createdOn: string
  createdBy: number
  allowMultipleCodingValues: boolean
  delimiterForCodingValues: string
  allowPredefinedCodingValuesOnly: boolean
  detailDataCount: number
  scale: number
  length: number
  multiValuedCodingOptions: number
  fieldCodingValues: string[]
  updatedFieldValue: string
  currentFieldValue: string
  dateFormat: string
  checkForDateType: boolean
  shouldShowPredefinedCodingField: boolean
  shouldShowDelimiter: boolean
  shouldShowTextField: boolean
  shouldShowMultipleCodingValuesField: boolean
  extractedDelimiter: string
  shouldShowField: boolean
}

export interface SelectedMultiCodingValueModel {
  selectedField: DocumentCodingModel
  selectedValues?: string
  selectedOperationType?: number
  selectedNewValues?: string
  eventType: DocumentCodingControlActionType
  isBulkDocument: boolean
}

export interface CodingFieldPayloadModel {
  selectedDocuments: number[]
  projectId: number
  type: string
}

export interface BulkCodingValuesPayloadModel {
  pageNumber: number
  pageSize: number
  fileIds: number[]
  UnselectedFileIds: number[]
  SearchResultTempTable: string
  IsBatchSelected: boolean
}

export interface ValidationError {
  key: string
  message: string
}
