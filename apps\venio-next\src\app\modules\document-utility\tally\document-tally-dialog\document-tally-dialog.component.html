<kendo-dialog-titlebar (close)="close()">
  <div class="t-flex t-flex-row t-gap-2">
    <div
      class="t-bg-[#F2F2F2] t-w-10 t-h-10 t-flex t-items-center t-justify-center t-rounded-full">
      <span
        venioSvgLoader
        color="#B8B8B8"
        svgUrl="assets/svg/icon-percentage.svg"
        width="1.2rem"
        height="1.2rem">
      </span>
    </div>
    <div
      class="t-flex t-text-[#2F3080] t-opacity-87 t-text-base t-font-medium t-relative t-top-2.5 t-ml-2">
      {{ dialogTitle }}
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="t-block t-w-full t-h-full">
  <kendo-grid
    venioDynamicHeight
    [isKendoDialog]="true"
    [kendoGridBinding]="tallyResponseData?.TallyDataSource"
    kendoGridSelectBy="FieldValue"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="{ allowUnsort: false }"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    [selectable]="
      allowToFilterDocuments ? { mode: 'multiple', drag: true } : false
    "
    [loading]="isLoading"
    [(selectedKeys)]="selectedKeys"
    (sortChange)="sortChange($event)">
    <ng-template kendoPagerTemplate>
      <div>Total Tally Count : {{ tallyResponseData?.TotalTallyCount }}</div>
      <kendo-grid-spacer></kendo-grid-spacer>
      <venio-pagination
        [disabled]="tallyResponseData?.Count === 0"
        [totalRecords]="tallyResponseData?.Count"
        [pageSize]="pageSize"
        [showPageJumper]="false"
        [showPageSize]="true"
        [showRowNumberInputBox]="false"
        (pageChanged)="pageChanged($event)"
        (pageSizeChanged)="pageSizeChanged($event)"
        class="t-px-5 t-block t-py-2">
      </venio-pagination>
    </ng-template>

    <ng-template kendoGridToolbarTemplate>
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[25rem]"
        placeholder="Search the Value"
        [clearButton]="true"
        [(ngModel)]="searchText"
        (valueChange)="onSearchTextValueChange($event)">
        <ng-template kendoTextBoxSuffixTemplate>
          <kendo-textbox-separator></kendo-textbox-separator>
          <button kendoButton fillMode="clear" (click)="onSearchClick()">
            <span
              venioSvgLoader
              svgUrl="assets/svg/icon-search.svg"
              height="1rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </ng-template>
      </kendo-textbox>

      <kendo-dropdownlist
        [data]="fields"
        textField="displayFieldName"
        class="!t-w-52"
        valueField="internalFieldName"
        [(value)]="selectedField"
        [filterable]="true"
        (valueChange)="onFieldChange($event)"
        (filterChange)="handleFilterForFields($event)">
      </kendo-dropdownlist>

      <kendo-grid-spacer></kendo-grid-spacer>
      <button
        kendoButton
        fillMode="outline"
        class="t-capitalize !t-border-[#ccc]"
        (click)="onExportToCSVClick()">
        Export to CSV
      </button>
    </ng-template>

    <ng-container *ngIf="allowToFilterDocuments">
      <kendo-grid-checkbox-column
        [showSelectAll]="true"
        [width]="50"
        class="!t-py-[0.6rem]"></kendo-grid-checkbox-column>
    </ng-container>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="FieldValue"
      [title]="selectedFieldName">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="Count"
      title="Count"
      [width]="150">
    </kendo-grid-column>
  </kendo-grid>
</div>

<kendo-dialog-actions>
  <div class="col-md-9">
    <span
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-2"
      *ngIf="formMsg"
      >{{ formMsg }}</span
    >
  </div>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      *ngIf="allowToFilterDocuments"
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      (click)="applyFilter()">
      Apply Filter
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
