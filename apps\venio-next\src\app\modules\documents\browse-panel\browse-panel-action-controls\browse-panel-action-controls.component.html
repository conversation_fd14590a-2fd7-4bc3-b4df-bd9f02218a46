<button
  kendoButton
  #el
  *ngFor="let icon of svgIconForControls"
  kendoTooltip
  class="!t-p-[0.3rem]"
  (click)="browseActionClicked(icon.actionType)"
  fillMode="clear"
  [title]="
    icon.actionType.charAt(0).toUpperCase() +
    icon.actionType.slice(1) +
    ' Folder'
  "
  themeColor="none"
  size="none">
  <span
    [parentElement]="el.element"
    venioSvgLoader
    [isSelectedState]="icon.actionType === selectedAction"
    hoverColor="#FFBB12"
    [applyEffectsTo]="icon.applyEffectTo"
    [color]="icon.actionType === selectedAction ? '#FFBB12' : '#2f3080'"
    [svgUrl]="icon.iconPath"
    height="1.1rem"
    width="1.1rem">
    <kendo-loader size="small"></kendo-loader>
  </span>
</button>
