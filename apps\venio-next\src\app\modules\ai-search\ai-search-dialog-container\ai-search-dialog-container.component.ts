import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
  Injector,
  On<PERSON><PERSON>roy,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule, DOCUMENT } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  DialogModule,
  DialogRef,
  WindowModule,
} from '@progress/kendo-angular-dialog'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  SelectEvent,
  TabStripComponent,
  TabStripModule,
} from '@progress/kendo-angular-layout'
import { TextAreaModule } from '@progress/kendo-angular-inputs'
import { AiSearchMessageBoxComponent } from '../ai-search-message-box/ai-search-message-box.component'
import { AiSearchProgressComponent } from '../ai-search-progress/ai-search-progress.component'
import { AiSearchDocumentListComponent } from '../ai-search-document-list/ai-search-document-list.component'
import { AiSearchDocumentSummaryComponent } from '../ai-search-document-summary/ai-search-document-summary.component'
import { ProjectFacade } from '@venio/data-access/common'
import { filter, map, Subject, takeUntil } from 'rxjs'
import { AiFacade, AiSearchUiTypes } from '@venio/data-access/ai'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'venio-ai-search-dialog-container',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    DialogModule,
    PopoverModule,
    SvgLoaderDirective,
    TabStripModule,
    TextAreaModule,
    WindowModule,
    AiSearchMessageBoxComponent,
    AiSearchProgressComponent,
    AiSearchDocumentListComponent,
    AiSearchDocumentSummaryComponent,
  ],
  templateUrl: './ai-search-dialog-container.component.html',
  styleUrl: './ai-search-dialog-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiSearchDialogContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild(TabStripComponent)
  private tabStrip: TabStripComponent

  private readonly toDestroy$ = new Subject<void>()

  private dialogRef = inject(DialogRef)

  private document = inject(DOCUMENT)

  private activatedRoute = inject(ActivatedRoute)

  private projectFacade = inject(ProjectFacade)

  private changeDetectorRef = inject(ChangeDetectorRef)

  private aiFacade = inject(AiFacade)

  private injector = inject(Injector)

  public isSearchInProgress = signal<boolean>(false)

  public hasSummaryList = signal<boolean>(false)

  public allFileIds = signal<number[]>([])

  private get projectId(): number {
    return Number(this.activatedRoute.snapshot.queryParams['projectId'])
  }

  public ngOnInit(): void {
    this.#selectSelectedTab()
    this.#fetchProjectInfo()
    this.#selectSummaryList()
    this.#selectAllFileIds()
    this.#selectResetTrigger()
  }

  public ngAfterViewInit(): void {
    this.#selectIsSearchInProgress()
    this.#toggleDialogPositionCssClass()
    this.#selectSummaryTab()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public actionClicked(type: 'done' | 'close' | 'reset'): void {
    if (type === 'reset') {
      this.aiFacade.resetAiState([
        'searchSummaryList',
        'selectedSearchTab',
        'activeUuid',
        'selectedDocumentSummary',
        'allFileIds',
      ])
      this.aiFacade.triggerReset(true)
      return
    }
    this.dialogRef.close(type === 'done' ? this.allFileIds() : undefined)
  }

  public tabSelect(tab: SelectEvent): void {
    const { index } = tab
    const selected =
      index === 0
        ? AiSearchUiTypes.Progress
        : index === 1
        ? AiSearchUiTypes.DocSummary
        : undefined

    this.aiFacade.updateSelectedSearchTab(selected)
  }

  #selectSummaryTab(): void {
    this.changeDetectorRef.markForCheck()
    const hasSummaryList = this.hasSummaryList()
    if (hasSummaryList) {
      this.tabStrip.selectTab(1)
      this.changeDetectorRef.detectChanges()
    }
  }

  #fetchProjectInfo(): void {
    this.projectFacade.fetchProjectInfo(this.projectId)
  }

  #selectSelectedTab(): void {
    this.aiFacade.selectSelectedSearchTab$
      .pipe(
        filter(
          (selected) => typeof selected === 'string' && Boolean(this.tabStrip)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((selectedTab) => {
        const index =
          selectedTab === AiSearchUiTypes.Progress
            ? 0
            : selectedTab === AiSearchUiTypes.DocSummary
            ? 1
            : 0
        this.tabStrip.selectTab(index)
      })
  }

  #selectIsSearchInProgress(): void {
    this.aiFacade.selectIsAiSearchLoading$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isSearchInProgress) => {
        this.isSearchInProgress.set(isSearchInProgress)
      })
  }

  #selectSummaryList(): void {
    this.aiFacade.selectAiSearchAiSummaryList$
      .pipe(
        filter((summaryList) => typeof summaryList !== 'undefined'),
        map((summaryList) =>
          Object.values(summaryList).some((s) =>
            Boolean(s.original_query.trim())
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((hasSummaryList) => {
        this.hasSummaryList.set(hasSummaryList)
      })
  }

  #toggleDialogPositionCssClass(): void {
    runInInjectionContext(this.injector, () =>
      effect(() => {
        const customCssClass = 'v-custom-dialog-pos'
        const hasSummaryList = this.hasSummaryList()
        const dialogElement = this.dialogRef.dialog.location.nativeElement
        if (
          hasSummaryList &&
          dialogElement.classList.contains(customCssClass)
        ) {
          this.document.body.style.overflow = 'auto'
          dialogElement.classList.remove(customCssClass)
        }
      })
    )
  }

  #selectAllFileIds(): void {
    this.aiFacade.selectAllFileId$
      .pipe(
        filter((ids) => typeof ids !== 'undefined'),
        takeUntil(this.toDestroy$)
      )
      .subscribe((allFileIds) => {
        this.allFileIds.set(allFileIds)
      })
  }

  #repositionDialogCss(): void {
    const customCssClass = 'v-custom-dialog-pos'
    const dialogElement = this.dialogRef.dialog.location
      .nativeElement as HTMLElement
    dialogElement.classList.add(customCssClass)
    this.document.body.style.overflow = 'hidden'
  }

  #selectResetTrigger(): void {
    this.aiFacade.selectIsResetTriggered$
      .pipe(
        filter((isReset) => isReset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.hasSummaryList.set(false)
        this.allFileIds.set(undefined)
        this.aiFacade.resetAiState('isResetTriggered')
        this.#repositionDialogCss()
      })
  }
}
