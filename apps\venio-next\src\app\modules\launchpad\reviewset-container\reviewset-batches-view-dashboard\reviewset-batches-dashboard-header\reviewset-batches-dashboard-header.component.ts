import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
  OnInit,
  output,
  signal,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { WindowCloseActionDirective } from '@progress/kendo-angular-dialog'
import { ReviewSetEntry } from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  chevronDownIcon,
  chevronLeftIcon,
  eyeIcon,
} from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { FormsModule } from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ReportDatePickerComponent } from '../../../../reports/tabular/report-date-picker/report-date-picker.component'
import { ReportsFacade } from '@venio/data-access/reports'
import { filter } from 'rxjs'
import dayjs from 'dayjs'

@Component({
  selector: 'venio-reviewset-batches-dashboard-header',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    InputsModule,
    IconsModule,
    FormsModule,
    LabelModule,
    ButtonComponent,
    ReportDatePickerComponent,
    WindowCloseActionDirective,
  ],
  templateUrl: './reviewset-batches-dashboard-header.component.html',
  styleUrl: './reviewset-batches-dashboard-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetBatchesDashboardHeaderComponent implements OnInit {
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly dialogCloseAction = output()

  private readonly reviewSetFacade = inject(ReviewSetFacade)

  private readonly reportFacade = inject(ReportsFacade)

  public icons = {
    chevronDownIcon: chevronDownIcon,
    chevronLeftIcon: chevronLeftIcon,
    eyeIcon: eyeIcon,
  }

  public isAllReviewerSelected = signal(false)

  public selectedReviewer = signal<number[]>([])

  /** Signal for the review set reviewers */
  private readonly reviewSetReviewersDetail = toSignal(
    this.reviewSetFacade.selectReviewSetReviewersSuccess$
  )

  /** Computed property for the review set reviewers list */
  public readonly loadedReviewSetReviewers = computed(() => {
    return this.reviewSetReviewersDetail() || []
  })

  /** Signal for the dashboard date range */
  private readonly getSelectedDateRange = toSignal(
    this.reportFacade.selectSelectedDateRange$.pipe(
      filter((range) => typeof range !== 'undefined')
    )
  )

  /** Computed property for the review set reviewers list */
  public readonly selectedDateRange = computed(() => {
    return this.getSelectedDateRange()
  })

  constructor() {
    effect(() => {
      if (this.selectedDateRange()) {
        untracked(() => this.#updateReportDate())
      }
    })
  }

  public ngOnInit(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.fetchReviewSetReviewers(projectId, reviewSetId)
    this.#updateDashboardData()
  }

  /** Fetch review set reviewers */
  public fetchReviewSetReviewers(projectId: number, reviewSetId: number): void {
    this.reviewSetFacade.fetchReviewSetReviewers(projectId, reviewSetId)
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public onSelectAllChanged(): void {
    this.isAllReviewerSelected.set(!this.isAllReviewerSelected())
    if (this.isAllReviewerSelected())
      this.selectedReviewer.set(
        this.loadedReviewSetReviewers().map((reviewer) => reviewer.userId)
      )
    else this.selectedReviewer.set([])
    this.#updateReviewers()
  }

  public onValueChange(event: number[]): void {
    if (event.length === 0) {
      this.isAllReviewerSelected.set(false)
    } else {
      this.isAllReviewerSelected.set(
        event.length === this.loadedReviewSetReviewers().length
      )
    }
    this.selectedReviewer.set(event)
    this.#updateReviewers()
  }

  #updateReportDate(): void {
    const { start, end } = this.selectedDateRange()
    if (!start) return
    this.reviewSetFacade.updateReviewSetDashboardRequestInfo({
      startDate: dayjs(start).startOf('day').format('MM/DD/YYYY HH:mm:ss'),
      endDate: dayjs(end).endOf('day').format('MM/DD/YYYY HH:mm:ss'),
    })
    this.#updateDashboardData()
  }

  #updateReviewers(): void {
    const reviewers = this.selectedReviewer()
    this.reviewSetFacade.updateReviewSetDashboardRequestInfo({
      reviewers,
    })
    this.#updateDashboardData()
  }

  #updateDashboardData(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.reviewSetFacade.fetchReviewerDashboardData(projectId, reviewSetId)
  }
}
