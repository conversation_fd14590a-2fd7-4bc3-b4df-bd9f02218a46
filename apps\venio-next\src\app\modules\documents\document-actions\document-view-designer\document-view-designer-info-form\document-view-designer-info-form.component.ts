import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { LabelModule } from '@progress/kendo-angular-label'
import { TabStripModule } from '@progress/kendo-angular-layout'
import { TextBoxModule } from '@progress/kendo-angular-inputs'
import { DocumentViewFacade, UserFacade } from '@venio/data-access/common'
import {
  combineLatest,
  filter,
  from,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { UserModel } from '@venio/shared/models/interfaces'
import { Accessibility, ViewModel, ViewType } from '@venio/data-access/review'
import { CaseConvertorService } from '@venio/util/utilities'
import { map } from 'rxjs/operators'

@Component({
  selector: 'venio-document-view-designer-info-form',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    DropDownListModule,
    LabelModule,
    TabStripModule,
    TextBoxModule,
    ReactiveFormsModule,
  ],
  templateUrl: './document-view-designer-info-form.component.html',
  styleUrl: './document-view-designer-info-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerInfoFormComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private toDestroy$ = new Subject<void>()

  private currentUser = signal<Partial<UserModel>>({})

  private selectedDocumentView = signal<Partial<ViewModel>>({})

  private get userIdControl(): AbstractControl {
    return this.viewInfoFormGroup?.get('viewUserIds')
  }

  public get getPublicUser(): Partial<UserModel> {
    return { userName: 'public', userId: -1 }
  }

  public get isMeSelected(): boolean {
    return this.userIdControl?.value === this.currentUser()?.userId
  }

  public isUserListLoading = signal<boolean>(false)

  public userList = signal([])

  public viewInfoFormGroup: FormGroup

  constructor(
    private documentViewFacade: DocumentViewFacade,
    private userFacade: UserFacade,
    private notificationService: NotificationService,
    private formBuilder: FormBuilder
  ) {}

  public ngOnInit(): void {
    this.#initViewInfoFormGroup()
    this.#selectCurrentUser()
    this.#fetchUserList()
    this.#storeFormChange()
  }

  public ngAfterViewInit(): void {
    this.#selectSelectedDocumentView()
    this.#selectIsUserListLoading()
    this.#selectUserList()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public selectCurrentUser(): void {
    const userId = this.currentUser()?.userId
    this.userIdControl.setValue(userId)
  }

  #initViewInfoFormGroup(): void {
    this.viewInfoFormGroup = this.formBuilder.group({
      viewName: ['', Validators.required],
      viewUserIds: [this.getPublicUser.userId, Validators.required],
    })
  }

  #getSelectedViewUserId(): number {
    const { viewUserIds, createdBy, accessibility } =
      this.selectedDocumentView() || {}
    const viewUserId = []
      .concat(viewUserIds)
      .map(Number)
      .filter((n) => !isNaN(n))

    const isPublic = accessibility === Accessibility.Public
    let userId: number
    const withoutCreatedBy = viewUserId.filter((id) => id !== createdBy)
    if (isPublic) {
      userId = -1
    } else if (viewUserId.length === 1 && createdBy === viewUserId[0]) {
      userId = createdBy
    } else if (viewUserId.length > 0 && withoutCreatedBy.length > 0) {
      userId = withoutCreatedBy[0]
    } else {
      userId = -1
    }

    return userId
  }

  #patchViewInfoFormGroup(): void {
    const { viewId, viewName, createdBy } = this.selectedDocumentView() || {}
    const { userId } = this.currentUser() || {}

    // Currently logged-in user
    const currentLoggedInUserId = userId

    // Determines the ID to be selected in the dropdown
    const uId = this.#getSelectedViewUserId()
    const isCreatedByMe = createdBy === currentLoggedInUserId
    const newViewName = viewId > 0 && isCreatedByMe ? viewName : ''

    this.viewInfoFormGroup.patchValue({
      viewName: newViewName,
      viewUserIds: uId,
    })

    this.#setPublicUser()
  }

  #fetchUserList(): void {
    this.userFacade.fetchUserList()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectSelectedDocumentView(): void {
    this.documentViewFacade.selectSelectedDocumentView$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedDocumentView) => {
        // If the view is not created by the current user and there is view ID, we need to reset to null
        this.selectedDocumentView.set(selectedDocumentView || {})
        this.#patchViewInfoFormGroup()
      })
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
      })
  }

  #selectIsUserListLoading(): void {
    this.userFacade.selectIsUserListLoading$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isLoading) => {
        this.isUserListLoading.set(isLoading)
      })
  }

  /**
   * Only set the public user when the form is new or there are no user ids.
   * (default view has no user ids)
   * For update, the values are coming from the selected document view.
   * @returns {void}
   */

  #setPublicUser(): void {
    const hasUserId = this.selectedDocumentView()?.viewUserIds?.[0] > 0

    if (hasUserId || !this.userIdControl) return

    this.userIdControl.setValue(this.getPublicUser.userId)
  }

  #selectUserList(): void {
    combineLatest([
      this.userFacade.selectUserListSuccessResponse$,
      this.userFacade.selectUserListErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        switchMap(([success, error]) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData = camelCaseConvertorService.convertToCase<any>(
            success,
            'camelCase'
          )
          return from(convertedData).pipe(map((data) => ({ data, error })))
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(({ data, error }) => {
        const users = [this.getPublicUser].concat(data?.data || [])
        this.userList.set(users)

        this.#setPublicUser()

        if (error) this.#showMessage(error?.message, { style: 'error' })
      })
  }

  #storeFormChange(): void {
    this.viewInfoFormGroup.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((form) => {
        // Whether user ids is an array or a single value
        const isUsers = Array.isArray(form.viewUserIds)

        // Either the user ids is an array or a single value, it should be greater than 0
        const hasUserId =
          (isUsers && form.viewUserIds?.[0] > 0) || form.viewUserIds > 0

        // Based on the user ids, the accessibility should be set
        const accessibility = hasUserId
          ? Accessibility.Private
          : Accessibility.Public

        // If the user ids is an array, it should be used as is, otherwise, it should be wrapped in an array
        const viewUserIds = isUsers
          ? form.viewUserIds
          : [form.viewUserIds] || []

        // Store the current form data
        this.documentViewFacade.storeCurrentFormData({
          viewName: form.viewName,
          viewType: ViewType.Document,
          isDefault: false,
          viewUserIds,
          accessibility,
        })
      })
  }
}
