import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { Subject, filter, takeUntil } from 'rxjs'
import { DocumentsFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-document-tally-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './document-tally-container.component.html',
  styleUrls: ['./document-tally-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentTallyContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  @ViewChild('dialogContent', { static: true })
  private readonly dialogContent: TemplateRef<any>

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxHeight: '90vh',
      maxWidth: '90vw',
      minHeight: '90vh',
      minWidth: '90vw',
    })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('../document-tally-dialog/document-tally-dialog.component').then(
      (d) => {
        // reset the loading indicator
        this.#resetMenuLoadingState()

        // launch the dialog
        this.#launchDialogContent(d.DocumentTallyDialogComponent)

        // once the dialogRef instance is created
        this.#handleEditDialogCloseEvent()
      }
    )
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.TALLY),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
