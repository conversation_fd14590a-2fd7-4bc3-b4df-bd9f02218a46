import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  computed,
  effect,
  untracked,
  Injector,
  Signal,
  viewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { combineLatest, filter, Subject, take, takeUntil } from 'rxjs'
import { ProjectFacade } from '@venio/data-access/common'
import { PageArgs } from '@venio/ui/pagination'
import {
  CommonActionTypes,
  REVIEWSET_BATCH_DIALOG_MESSAGES,
  REVIEWSET_BATCH_SUCCESS_MESSAGES,
  REVIEWSET_BATCH_SUCCESS_TITLE,
} from '@venio/shared/models/constants'
import {
  ReviewSetBatchAction,
  ReviewSetBatchBulkAction,
  ReviewSetReassignAction,
  ReviewSetBatchModel,
  SelectedReviewSetBatchModel,
  ReviewSetEntry,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { cloneDeep } from 'lodash'
import { chevronDownIcon, SVGIcon, xIcon } from '@progress/kendo-svg-icons'
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { ReviewsetDetailViewBatchesGridComponent } from './reviewset-detail-view-batches-grid/reviewset-detail-view-batches-grid/reviewset-detail-view-batches-grid.component'
import { ReviewsetDetailViewBatchesReassignComponent } from './reviewset-detail-view-batches-reassign/reviewset-detail-view-batches-reassign.component'
import { ReviewsetDetailViewBatchesToolbarComponent } from './reviewset-detail-view-batches-toolbar/reviewset-detail-view-batches-toolbar.component'
import { toSignal } from '@angular/core/rxjs-interop'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import {
  ConfirmationDialogComponent,
  NotificationDialogComponent,
} from '@venio/feature/notification'
import { DialogService } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-reviewset-detail-view-batches',
  standalone: true,
  imports: [
    CommonModule,
    SkeletonModule,
    TooltipModule,
    IconsModule,
    SVGIconModule,
    ButtonsModule,
    ReviewsetDetailViewBatchesGridComponent,
    ReviewsetDetailViewBatchesToolbarComponent,
    ReviewsetDetailViewBatchesReassignComponent,
  ],
  templateUrl: './reviewset-detail-view-batches.component.html',
  styleUrl: './reviewset-detail-view-batches.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewBatchesComponent implements OnInit, OnDestroy {
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly appendTo = viewChild('appendNotification', {
    read: ViewContainerRef,
  })

  private readonly projectFacade = inject(ProjectFacade)

  private readonly injector = inject(Injector)

  private readonly notificationService = inject(NotificationService)

  private readonly dialogService = inject(DialogService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  public icons = {
    closeIcon: xIcon,
    custodianIcon: 'assets/svg/icon-shuffle-communicate.svg',
  }

  public downIcon: SVGIcon = chevronDownIcon

  public readonly isRessaginWindowActive = signal(false)

  public readonly isBulkBatchOperation = signal(false)

  private readonly selectedBatchIds = signal<number[]>(undefined)

  private readonly selectedBatch = signal<ReviewSetBatchModel>(undefined)

  public readonly selectedBatchName = computed(() => {
    return this.selectedBatch()?.name || ''
  })

  public selectedReviewSetBatch = computed<SelectedReviewSetBatchModel>(() => {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    return { projectId, reviewSetId }
  })

  private readonly reviewSetBatchRebatchResponse = toSignal(
    combineLatest([
      this.projectFacade.selectReviewSetRebatchSuccess$,
      this.projectFacade.selectReviewSetReBatchError$,
    ]).pipe(
      filter(
        ([success, error]) =>
          typeof success !== 'undefined' || typeof error !== 'undefined'
      )
    )
  )

  private readonly reviewSetBatchDeleteResponse = toSignal(
    combineLatest([
      this.projectFacade.selectReviewSetDeleteBatchSuccess$,
      this.projectFacade.selectReviewSetDeleteBatchError$,
    ]).pipe(
      filter(
        ([success, error]) =>
          typeof success !== 'undefined' || typeof error !== 'undefined'
      )
    )
  )

  private readonly reviewSetBatchReassignResponse = toSignal(
    combineLatest([
      this.projectFacade.selectReviewSetReassignSuccess$,
      this.projectFacade.selectReviewSetReassignError$,
    ]).pipe(
      filter(
        ([success, error]) =>
          typeof success !== 'undefined' || typeof error !== 'undefined'
      )
    )
  )

  private readonly toDestroy$ = new Subject<void>()

  public ngOnInit(): void {
    this.#reviewSetBatchRebatchActionResponse()
    this.#reviewSetBatchDeleteActionResponse()
    this.#reviewSetBatchReassignActinResponse()
    this.#fetchReviewSetBatch()
  }

  #fetchReviewSetBatch(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.projectFacade.fetchReviewSetBatch(projectId, reviewSetId)
  }

  #reviewSetBatchRebatchActionResponse(): void {
    const response = this.reviewSetBatchRebatchResponse
    this.#handleBatchActionResponse(response, CommonActionTypes.REBATCH)
  }

  #reviewSetBatchDeleteActionResponse(): void {
    const response = this.reviewSetBatchDeleteResponse
    this.#handleBatchActionResponse(response, CommonActionTypes.DELETE)
  }

  #reviewSetBatchReassignActinResponse(): void {
    const response = this.reviewSetBatchReassignResponse
    this.#handleBatchActionResponse(response, CommonActionTypes.REASSIGN)
  }

  /**
   * Handles the paging control change.
   * @param {PageArgs} arg - Generic paging info.
   * @returns {void}
   */
  public pagingChanged(arg: PageArgs): void {
    this.#updatePagingInfo(arg)
    // Once the paging info is updated, we need to fetch the review set detail.
    this.#fetchReviewSetBatch()
  }

  #updatePagingInfo(arg: PageArgs): void {
    const { pageNumber, pageSize } = arg
    this.projectFacade.updateReviewSetBatchRequestInfo({
      pageNumber,
      pageSize,
    })
  }

  /**
   * Handles the review set grid action click.
   * @param {ReviewSetBatchAction} event - The reviewset batch action event.
   * @returns {void}
   */
  public reviewSetBatchActionClick(event: ReviewSetBatchAction): void {
    const { actionType, content } = event
    const selectedReviewSet = cloneDeep(content)
    this.selectedBatch.set(selectedReviewSet)
    this.isBulkBatchOperation.set(false)

    switch (actionType) {
      case CommonActionTypes.REASSIGN:
        if (selectedReviewSet?.batchId) {
          const batchId = [selectedReviewSet.batchId]
          this.selectedBatchIds.set(batchId)
          this.#openReassignWindow()
        }
        break
      case CommonActionTypes.REBATCH:
      case CommonActionTypes.DELETE:
        if (selectedReviewSet?.batchId) {
          const batchId = [selectedReviewSet.batchId]
          this.selectedBatchIds.set(batchId)
          this.#launchAndSetupConfirmationDialog(actionType)
        }
        break
    }
  }

  /**
   * Handles the review set grid action click.
   * @param {ReviewSetBatchAction} event - The reviewset batch action event.
   * @returns {void}
   */
  public reviewSetBatchBulkActionClick(event: ReviewSetBatchBulkAction): void {
    const { actionType, batchIds } = event
    this.selectedBatchIds.set(batchIds)
    this.isBulkBatchOperation.set(true)
    this.#launchAndSetupConfirmationDialog(actionType)
  }

  public reviewSetReassignActionClick(event: ReviewSetReassignAction): void {
    const { actionType, reviewerId, reviewerName } = event
    this.isBulkBatchOperation.set(false)
    switch (actionType) {
      case CommonActionTypes.SAVE:
        if (this.#validateReassignUser(reviewerName))
          this.#reAssignBatch(reviewerId)
        break
      case CommonActionTypes.CANCEL:
        this.isRessaginWindowActive.set(false)
        this.#clearSelectedBatchData()
        break
    }
  }

  #validateReassignUser(reviewerName: string): boolean {
    let isValidateRessignUser = true
    if (!reviewerName) {
      this.#showValidationMessage('Please select a user')
      isValidateRessignUser = false
    } else if (reviewerName === this.selectedBatch().reviewer) {
      this.#showValidationMessage(
        'Selected user is already assigned to the batch.'
      )
      isValidateRessignUser = false
    }
    return isValidateRessignUser
  }

  #reAssignBatch(reviewerId: number): void {
    if (reviewerId) {
      this.isRessaginWindowActive.set(false)
      const { projectId, reviewSetId } = this.selectedReviewSetEntry()
      this.projectFacade.reassignReviewSetBatch(
        projectId,
        reviewSetId,
        this.selectedBatch().batchId,
        reviewerId
      )
    }
  }

  #reBatch(batchIds: number[]): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.projectFacade.rebatchReviewSetBatch(projectId, reviewSetId, batchIds)
  }

  #deleteBatch(batchIds: number[]): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.projectFacade.deleteReviewSetBatch(projectId, reviewSetId, batchIds)
  }

  #openReassignWindow(): void {
    this.isRessaginWindowActive.set(true)
  }

  public closeReassignWindow(): void {
    this.isRessaginWindowActive.set(false)
  }

  #handleBatchActionResponse(
    responseSignal: Signal<
      [ResponseModel | undefined, ResponseModel | undefined]
    >,
    actionType: CommonActionTypes
  ): void {
    effect(
      () => {
        const response = responseSignal()
        if (!response) return

        const [success, error] = response
        const isError = Boolean(error?.status)
        const style: 'success' | 'error' | 'info' = success?.message
          ? 'success'
          : isError
          ? 'error'
          : 'info'
        const message: string | undefined = success?.message || error?.message

        untracked(() => {
          if (message) {
            if (style === 'success') this.#showNotificationMessage(actionType)
            else this.#showMessage(message, { style })
          }
          this.#resetReviewSetBatchResponseStates()
          this.#clearSelectedBatchData()
        })
      },
      { injector: this.injector }
    )
  }

  #clearSelectedBatchData(): void {
    this.selectedBatchIds.set(undefined)
    this.selectedBatch.set(undefined)
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #setDialogInput(
    instance: ConfirmationDialogComponent,
    actionType: CommonActionTypes
  ): void {
    const batchName = this.selectedBatch()?.name || ''
    const actionMessage = REVIEWSET_BATCH_DIALOG_MESSAGES[actionType]
    const message = this.isBulkBatchOperation()
      ? actionMessage?.multiple ?? actionMessage?.default
      : actionType === CommonActionTypes.REBATCH
      ? actionMessage?.default
      : actionMessage?.single(batchName)
    instance.title = REVIEWSET_BATCH_SUCCESS_TITLE[actionType]
    instance.message = message
  }

  #launchAndSetupConfirmationDialog(actionType: CommonActionTypes): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: `v-confirmation-dialog v-dialog-${actionType.toLowerCase()}`,
      width: '25rem',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(confirmationDialogRef.content.instance, actionType)

    confirmationDialogRef.result
      .pipe(
        filter((action) => typeof action === 'boolean' && action),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        const batchIds = this.selectedBatchIds()
        switch (actionType) {
          case CommonActionTypes.DELETE:
            this.#deleteBatch(batchIds)
            break
          case CommonActionTypes.REBATCH:
            this.#reBatch(batchIds)
            break
        }
      })
  }

  #showValidationMessage(content: string): void {
    this.notificationService.show({
      appendTo: this.appendTo(),
      content: content,
      animation: { type: 'fade', duration: 300 },
      type: {
        style: 'error',
      },
      cssClass: 'v-custom-save-notification v-custom-notification-multiline',
      position: { horizontal: 'center', vertical: 'top' },
      hideAfter: 6000,
      closable: false,
    })
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {void}
   */
  #showNotificationMessage(actionType: CommonActionTypes): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: `v-confirmation-dialog v-dialog-${actionType.toLowerCase()}`,
      width: '25rem',
      appendTo: this.viewContainerRef,
    })

    // Set the dialog input
    this.#setNotificationDialogInput(
      notificationDialogRef.content.instance,
      actionType
    )
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the NotificationDialogComponent.
   * @param content The content of the notification message.
   */

  #setNotificationDialogInput(
    instance: NotificationDialogComponent,
    actionType: CommonActionTypes
  ): void {
    const batchName = this.selectedBatch()?.name || ''
    const actionMessage = REVIEWSET_BATCH_SUCCESS_MESSAGES[actionType]

    const message = this.isBulkBatchOperation()
      ? actionMessage.multiple
      : actionMessage.single(batchName)

    instance.title = REVIEWSET_BATCH_SUCCESS_TITLE[actionType]
    instance.message = message
  }

  #resetReviewSetBatchResponseStates(): void {
    this.projectFacade.resetProjectState([
      'reviewSetDeleteBatchSuccess',
      'reviewSetDeleteBatchError',
      'reviewSetRebatchSuccess',
      'reviewSetReBatchError',
      'reviewSetReassignSuccess',
      'reviewSetReassignError',
      'isReviewSetBatchLoading',
    ])
  }

  #resetReviewSetBatchStates(): void {
    this.projectFacade.resetProjectState([
      'reviewSetBatchSuccess',
      'reviewSetBatchError',
      'isReviewSetBatchLoading',
      'reviewSetBatchRequestInfo',
      'selectedBatchDetail',
    ])
  }

  public ngOnDestroy(): void {
    this.#resetReviewSetBatchStates()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
