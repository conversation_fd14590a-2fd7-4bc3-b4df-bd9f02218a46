import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  output,
  signal,
  OnInit,
  effect,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  UserGroupTree,
  SelectedReviewSetBatchModel,
  ReviewSetReassignAction,
} from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { FormsModule } from '@angular/forms'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-reviewset-detail-view-batches-reassign',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    LabelModule,
    InputsModule,
    FormsModule,
    IconsModule,
    ButtonsModule,
    LoaderModule,
    TooltipDirective,
    DynamicHeightDirective,
  ],
  templateUrl: './reviewset-detail-view-batches-reassign.component.html',
  styleUrl: './reviewset-detail-view-batches-reassign.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewBatchesReassignComponent implements OnInit {
  public selectedReviewSetBatch = input.required<SelectedReviewSetBatchModel>()

  public isRessaginWindowActive = input.required<boolean>()

  private readonly projectFacade = inject(ProjectFacade)

  public readonly selectedReviewer = signal<number | undefined>(undefined)

  /** Output event for the action invoked.
   * When the actions of grid e.g., reassign, rebatch and delete. are clicked, this event is emitted with type `ReviewSetBatchAction`
   */
  public readonly actionInvoked = output<ReviewSetReassignAction>()

  /**
   * Common action types reference.
   */
  public readonly commonActionTypes = CommonActionTypes

  /** Signal for the review set user group loading state */
  public isReviewSetBatchLoading = toSignal(
    this.projectFacade.selectIsReviewSetUserGroupLoading$,
    { initialValue: true }
  )

  /** Signal for the review set user group */
  private readonly reviewSetUserGroupDetail = toSignal(
    this.projectFacade.selectReviewSetUserGroupDetail$
  )

  /** Signal for the review set user group list */
  public readonly loadedReviewSetUserGroup = computed<UserGroupTree[]>(() => {
    const userGroup: UserGroupTree[] = this.reviewSetUserGroupDetail() || []
    return userGroup.map(
      (item: UserGroupTree): UserGroupTree => ({
        ...item,
        treeParentId: item.parentID === -1 ? null : item.parentID,
      })
    )
  })

  public readonly expandedKeys = computed<number[]>(() => {
    return this.loadedReviewSetUserGroup()
      .filter((t) => !t.treeParentId)
      .map((item) => item.keyID)
  })

  public readonly selectedReviewerName = computed(() => {
    const selectedReviewer = this.loadedReviewSetUserGroup().find(
      (t) => t.userId === this.selectedReviewer()
    )

    return selectedReviewer ? selectedReviewer.name : undefined
  })

  public readonly saveTitle = computed(() => {
    return this.selectedReviewer() ? 'Save' : 'Please select the user'
  })

  constructor() {
    effect(() => {
      if (this.isRessaginWindowActive()) {
        untracked(() => this.selectedReviewer.set(undefined))
      }
    })
  }

  public ngOnInit(): void {
    this.#fetchReviewSetBatchUserGroup()
  }

  #fetchReviewSetBatchUserGroup(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetBatch()
    this.selectedReviewer.set(undefined)
    this.projectFacade.fetchReviewSetUserGroups(projectId, reviewSetId)
  }

  /**
   * Emits an action when action icon buttons are clicked.
   * @param {CommonActionTypes} actionType - The action type.
   * @returns {void}
   */
  public actionButtonClick(actionType: CommonActionTypes): void {
    const reassignAction = {
      actionType: actionType,
      reviewerId: this.selectedReviewer(),
      reviewerName: this.selectedReviewerName(),
    }
    this.actionInvoked.emit(reassignAction)
  }
}
