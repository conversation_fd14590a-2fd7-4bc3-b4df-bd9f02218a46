import {
  Component,
  computed,
  effect,
  inject,
  Injector,
  input,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup } from '@angular/forms'
import { ReviewSetForm } from '@venio/shared/models/interfaces'
import {
  CheckDirective,
  ExpandDirective,
  FlatDataBindingDirective,
  TreeViewComponent,
} from '@progress/kendo-angular-treeview'
import { ReviewsetFormService } from '../reviewset-form.service'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { combineLatest, switchMap } from 'rxjs'
import { map } from 'rxjs/operators'

@Component({
  selector: 'venio-reviewset-form-reviewers',
  standalone: true,
  imports: [
    CommonModule,
    CheckDirective,
    ExpandDirective,
    TreeViewComponent,
    FlatDataBindingDirective,
    SkeletonComponent,
  ],
  templateUrl: './reviewset-form-reviewers.component.html',
  styleUrl: './reviewset-form-reviewers.component.scss',
})
export class ReviewsetFormReviewersComponent implements OnInit {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  public readonly reviewSetFormService = inject(ReviewsetFormService)

  private readonly injector = inject(Injector)

  public readonly isReviewersLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isProjectUserLoading
  )

  public readonly isSelectedUserGroupInvalid = toSignal(
    toObservable(signal(false)).pipe(
      switchMap(() =>
        combineLatest([
          this.reviewSetForm().controls.selectedUserGroups.valueChanges,
          this.reviewSetForm().controls.selectedUserGroups.statusChanges,
        ])
      ),
      map((x) => {
        const control = this.reviewSetForm().controls.selectedUserGroups
        const error = control.errors
        return (error?.['required'] as boolean) || control.value.length <= 0
      })
    )
  )

  public selectedKeys: number[] = []

  public selectionChange(): void {
    const allUsers = this.reviewSetFormService.allProjectUsers()
    const selectedUsers = allUsers.filter((user) =>
      this.selectedKeys.includes(user.keyId)
    )
    this.reviewSetForm().controls.selectedUserGroups.setValue(selectedUsers)
    this.reviewSetForm().controls.selectedUserGroups.updateValueAndValidity()
  }

  public ngOnInit(): void {
    effect(
      () => {
        const isReviewersLoading = this.isReviewersLoading()
        if (isReviewersLoading) return
        const _ = this.isSelectedUserGroupInvalid()
        this.selectedKeys =
          this.reviewSetForm().controls.selectedUserGroups.value.map(
            (user) => user.keyId
          )
      },
      { injector: this.injector }
    )
  }
}
