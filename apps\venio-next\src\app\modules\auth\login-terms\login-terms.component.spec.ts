import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginTermsComponent } from './login-terms.component'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { PLATFORM_ID } from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LoginTermsComponent', () => {
  let component: LoginTermsComponent
  let fixture: ComponentFixture<LoginTermsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginTermsComponent,
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginTermsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
