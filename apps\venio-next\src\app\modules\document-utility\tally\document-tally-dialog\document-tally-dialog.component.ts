import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  DefaultFieldModel,
  Field,
  FieldFacade,
  FilterParams,
  ReviewSetStateService,
  SearchFacade,
  SearchInputParams,
  SearchRequestModel,
  StartupsFacade,
  TallyFacade,
  TallyModel,
  TallyRequestModel,
  TallyResponseModel,
  UserRights,
  ViewFacade,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import {
  Subject,
  combineLatest,
  filter,
  switchMap,
  take,
  takeUntil,
} from 'rxjs'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import {
  CsvBuilder,
  PagingUtil,
  TallyDataTransformService,
} from '@venio/util/utilities'
import { SortDescriptor } from '@progress/kendo-data-query'
import { orderBy } from 'lodash'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-document-tally-dialog',
  standalone: true,
  templateUrl: './document-tally-dialog.component.html',
  styleUrls: ['./document-tally-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    GridModule,
    SvgLoaderDirective,
    InputsModule,
    FormsModule,
    DropDownListModule,
    IconsModule,
    UiPaginationModule,
    LoaderModule,
    DynamicHeightDirective,
  ],
})
export class DocumentTallyDialogComponent implements OnInit, OnDestroy {
  private unsubscribed$: Subject<void> = new Subject<void>()

  public dialogTitle = 'Tally'

  public tallyOperation: TallyOperation = TallyOperation.LOAD

  public sourceFields: Field[]

  public viewFields: DefaultFieldModel[]

  public fieldName: string

  public fields: Field[]

  public selectedField: Field

  public selectedFieldName: string

  public isLoading = false

  public selectedKeys: string[]

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public searchText = ''

  public queryOperatorLimit = 100 //To Do: get control setting key MAX_SEARCH_OPERATORS value

  /**
   * Validation message to show
   */
  public formMsg = ''

  /**
   * Page size to maintain in grid for pagination
   */
  public pageSize = 100

  public currentPage = 1

  public tallyResponseData: TallyResponseModel

  public exportTallyData: TallyModel[]

  public sort: SortDescriptor[] = [
    {
      field: 'Count',
      dir: 'desc',
    },
  ]

  private searchParams: Partial<SearchRequestModel>

  /**
   * User right to filter the documents
   */
  public allowToFilterDocuments: boolean

  constructor(
    private fieldFacade: FieldFacade,
    private viewFacade: ViewFacade,
    private cdr: ChangeDetectorRef,
    private activatedRoute: ActivatedRoute,
    private tallyFacade: TallyFacade,
    private searchFacade: SearchFacade,
    private tallyDataTransformService: TallyDataTransformService,
    private breadcrumbFacade: BreadcrumbFacade,
    private startupsFacade: StartupsFacade,
    private reviewSetState: ReviewSetStateService,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public close(): void {
    this.dialogRef.close()
  }

  public ngOnInit(): void {
    this.#selectFields()
    this.#selectSearchParams()
    this.#selectTallyDatasource()
    this.#selectViewFilterReviewDocumentRights()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  #selectViewFilterReviewDocumentRights(): void {
    this.startupsFacade
      .hasGroupRight$(UserRights.ALLOW_TO_FILTER_REVIEW_DOCUMENTS)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((result: boolean) => {
        this.allowToFilterDocuments = result
      })
  }

  #selectFields(): void {
    combineLatest([
      this.fieldFacade.getAllFields$,
      this.viewFacade.getSelectedViewFields$,
    ])
      .pipe(
        filter(
          ([allFields, viewFields]) =>
            !!allFields &&
            allFields.length > 0 &&
            !!viewFields &&
            viewFields.length > 0
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([allFields, viewFields]) => {
        this.cdr.markForCheck()
        this.sourceFields = orderBy(
          allFields.filter((allField) =>
            viewFields.some(
              (viewField) =>
                viewField.venioFieldId === allField.venioFieldId &&
                viewField.isCustomField === allField.isCustomField
            )
          ),
          'displayFieldName'
        )
        this.fields = this.sourceFields.slice() // Shallow copy as sourceFields is used in field filter
        //Select the field if sent while opening the dialog otherwise the first field selected
        this.selectedField = this.fieldName
          ? this.fields.find((item) => item.displayFieldName === this.fieldName)
          : this.fields[0]

        this.tallyOperation = TallyOperation.LOAD
        this.#displayTallyOfSelectedField()
      })
  }

  #selectSearchParams(): void {
    this.startupsFacade.getSearchParams$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((searchParams) => {
        this.searchParams = searchParams
      })
  }

  #displayTallyOfSelectedField(): void {
    //For Title of table
    this.selectedFieldName = this.selectedField?.displayFieldName

    this.searchFacade.getSearchTempTables$
      .pipe(
        filter((searchTempTable) => !!searchTempTable),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((searchTempTable) => {
        this.cdr.markForCheck()
        this.isLoading = true
        const tallyRequestModel: TallyRequestModel = {
          currentPage:
            this.tallyOperation === TallyOperation.LOAD ? this.currentPage : 1,
          pageSize:
            this.tallyOperation === TallyOperation.LOAD
              ? this.pageSize
              : this.tallyResponseData.Count,
          fieldName: this.selectedField.internalFieldName,
          searchText: this.searchText,
          globalTempTable: searchTempTable.searchResultTempTable,
          sortFieldName:
            this.sort[0].field === 'FieldValue'
              ? this.selectedField.internalFieldName
              : 'Count',
          sortOrder: this.sort[0].dir,
          reviewSetId: this.reviewSetState.reviewSetId(),
        }
        this.tallyFacade.fetchTallyData(this.projectId, tallyRequestModel)
      })
    if (
      this.selectedField.fieldDataType?.toUpperCase() === 'DATETIME' &&
      this.selectedField.isCustomField
    ) {
      this.formMsg = `Only date value will be used for tally. Time value will be ignored.`
    } else {
      this.formMsg = ''
    }
  }

  #selectTallyDatasource(): void {
    combineLatest([
      this.tallyFacade.getTallyDataSource$,
      this.fieldFacade.getAllFields$,
    ])
      .pipe(
        filter(
          ([tallyResponseModel, allFields]) =>
            !!tallyResponseModel && !!allFields
        ),
        switchMap(async ([tallyResponseModel, allFields]) => {
          const clonedTallyResponseModel =
            await this.tallyDataTransformService.transformData(
              tallyResponseModel,
              allFields,
              this.selectedField
            )

          return clonedTallyResponseModel
        }),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tallyResponseModel: TallyResponseModel) => {
        this.cdr.markForCheck()
        if (this.tallyOperation === TallyOperation.EXPORT) {
          this.exportTallyData = tallyResponseModel.TallyDataSource
          const fileName = `Tally of ` + this.selectedField.internalFieldName
          const headers = [this.selectedField.internalFieldName, 'Count']
          const rows = this.exportTallyData.map((r) => ({
            [this.selectedFieldName]: r.FieldValue,
            ['Count']: r.Count,
          }))

          new CsvBuilder(fileName).createCsvAndDownload(headers, rows)
        } else {
          this.tallyResponseData = tallyResponseModel
        }
        this.isLoading = false
      })
  }

  public onSearchClick(): void {
    this.tallyOperation = TallyOperation.LOAD
    this.currentPage = 1
    this.#displayTallyOfSelectedField()
  }

  public onSearchTextValueChange(value: string): void {
    //handle clear search text
    if (!value.trim()) {
      this.tallyOperation = TallyOperation.LOAD
      this.currentPage = 1
      this.#displayTallyOfSelectedField()
    }
  }

  public onFieldChange(value: string): void {
    // Perform actions when the value changes
    this.selectedKeys = []
    this.tallyOperation = TallyOperation.LOAD
    this.currentPage = 1
    this.#displayTallyOfSelectedField()
  }

  public handleFilterForFields(value: string): void {
    this.fields = this.sourceFields.filter((field) =>
      field.displayFieldName.toLowerCase().includes(value.toLowerCase())
    )
  }

  public onExportToCSVClick(): void {
    this.tallyOperation = TallyOperation.EXPORT
    this.#displayTallyOfSelectedField()
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.tallyOperation = TallyOperation.LOAD
      this.currentPage = args.pageNumber
      this.#displayTallyOfSelectedField()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    //Calculate current page when page size changed
    this.currentPage = PagingUtil.getNewPageNumber(
      this.currentPage,
      this.pageSize,
      args.pageSize
    )

    this.pageSize = args.pageSize
    this.tallyOperation = TallyOperation.LOAD
    this.#displayTallyOfSelectedField()
  }

  public sortChange(sort: SortDescriptor[]): void {
    this.sort = sort
    this.#displayTallyOfSelectedField()
  }

  public applyFilter(): void {
    if (!this.#isFormValid()) return
    const filterParams: FilterParams = {
      fieldName: this.selectedField.internalFieldName,
      fieldValues: this.fieldFacade.mapSelectedMetadataValue(
        this.selectedField,
        this.selectedKeys
      ),
      filterType: 'include',
    }
    const query = this.#constructSearchQuery(filterParams)

    this.addBreadcrumb(query.query)

    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(take(1), takeUntil(this.unsubscribed$))
      .subscribe((syntax) => {
        const payload: SearchInputParams = {
          searchExpression: syntax,
          isResetBaseGuid: false,
          searchDuplicateOption: this.searchParams.searchDuplicateOption,
          reviewSetId: this.reviewSetState.reviewSetId(),
          isFilterSearch: true,
          isForwardFilter: true,
        }
        this.searchFacade.search(payload)
      })
    this.dialogRef.close()
  }

  private addBreadcrumb(expression): void {
    const payload: ConditionGroup = {
      id: UuidGenerator.uuid,
      conditions: [{ conditionSyntax: expression }] as ConditionElement[],
      conditionType: ConditionType.Group,
      groupStackType: GroupStackType.TALLY,
      checked: true,
    }
    this.breadcrumbFacade.storeBreadcrumbs([payload])
  }

  #constructSearchQuery(filterParameters: FilterParams): any {
    let tempQuery = ''
    let filterText = ''

    const queryObj = this.#performGenericFilter(filterParameters)
    tempQuery = queryObj.query
    filterText = queryObj.filterText

    tempQuery =
      '( ' + tempQuery.substring(0, tempQuery.length - 3).trim() + ' )'

    return { query: tempQuery, filterText: filterText }
  }

  #performGenericFilter(filterParameters: FilterParams): any {
    // pasa
    let tempQuery = ''
    let filterText = ''
    filterParameters.fieldValues.forEach((value) => {
      if (value === '') {
        /** if value is empty on column CONTROL_NUMBER OR BATES NUMBER then empty string must be replaced
         * with IS NULL*/

        tempQuery += filterParameters.fieldName + ' IS NULL ' + ' OR '
      } else {
        // if (filterParameters.fieldName === '__isReviewed') {
        //   if (value)
        //     tempQuery =
        //       tempQuery +
        //       ' ReviewSet=' +
        //       '"' +
        //       this.selectedReviewSetInfo.name +
        //       '"' +
        //       ' AND REVIEWSET_BATCH_ID = ' +
        //       this.searchResultParameter.batchId +
        //       ' AND TAGS(' +
        //       '"' +
        //       this.reviewTagInfo?.tagName +
        //       '"' +
        //       ')' +
        //       ' OR '
        //   else
        //     tempQuery =
        //       tempQuery +
        //       ' ReviewSet=' +
        //       '"' +
        //       this.selectedReviewSetInfo.name +
        //       '"' +
        //       ' AND REVIEWSET_BATCH_ID = ' +
        //       this.searchResultParameter.batchId +
        //       ' AND NOT TAGS(' +
        //       '"' +
        //       this.reviewTagInfo?.tagName +
        //       '"' +
        //       ')' +
        //       ' OR '
        // }else
        if (filterParameters.fieldName === 'FAMILY_DATE_TIME') {
          /**
           * Here GroupDate is hard coded since the search id done from tbl_ex_FileInfo.[GroupDate] table
           * and the column there is GroupDate instead of FAMILY_DATE_TIME
           */
          tempQuery += 'GroupDate' + '=' + '"' + value + '"' + ' OR '
        } else
          tempQuery =
            tempQuery +
            filterParameters.fieldName +
            '=' +
            '"' +
            value.toString().split('"').join('\\"') +
            '"' +
            ' OR '
      }

      if (filterText.length > 0) {
        filterText = filterText + ', ' + value.toString()
      } else {
        filterText = value.toString()
      }
    })
    return { query: tempQuery, filterText: filterText }
  }

  #isFormValid(): boolean {
    //TO DO : Check user right ALLOW_TO_FILTER_REVIEW_DOCUMENTS
    //this.isDisabledApplyFilter = true
    // if (!this.allowToFilterDocuments) {
    //   this.formMsg = ''
    //   this.isDisabledApplyFilter = false
    //   return false
    // }

    if (!this.selectedField.isSearchableField) {
      this.formMsg = `'${this.selectedFieldName}' is not a searchable field.`
      return false
    }

    if (!this.selectedKeys || this.selectedKeys.length <= 0) {
      this.formMsg = 'At least one values must be selected.'
      return false
    } else if (this.selectedKeys.length > this.queryOperatorLimit) {
      this.formMsg = `The selected items exceed the configured limit of ${this.queryOperatorLimit} items for filtering`
      return false
    } else if (
      !this.selectedField.allowNullSearch &&
      this.selectedKeys.indexOf('') > -1
    ) {
      this.formMsg =
        'The field does not support null search, please uncheck empty value'
      return false
    }

    this.formMsg = ''
    return true
  }
}
export enum TallyOperation {
  LOAD = 'custom',
  SELECT = 'system',
  EXPORT = 'auto',
}
