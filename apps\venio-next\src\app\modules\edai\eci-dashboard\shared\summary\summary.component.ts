import { Component, OnInit } from '@angular/core';
import { KENDO_BUTTONS } from "@progress/kendo-angular-buttons";
import { KENDO_DIALOG } from "@progress/kendo-angular-dialog";
import { DataService } from '../data.service';

@Component({
  selector: 'app-summary',
  standalone: true,
  imports: [KENDO_BUTTONS, KENDO_DIALOG],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class SummaryComponent implements OnInit {
  public selectedDocuments: string = '0'; // Will be '13,479' when filters are applied
  public showCaseInfoDialog: boolean = false;
  public showNarrativeDialog: boolean = false;

  constructor(private dataService: DataService) { }

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(isOpened => {
      this.selectedDocuments = isOpened ? '13,479' : '0';
    });
  }

  public onCaseButtonClick(): void {
    console.log('Case Info button clicked!');
    this.showCaseInfoDialog = true;
  }

  public onNarrativeButtonClick(): void {
    console.log('Narrative button clicked!');
    this.showNarrativeDialog = true;
  }

  public onFiltersClick(): void {
    console.log('Filters button clicked!');
    // TODO: Toggle filters panel
  }

  public closeCaseInfoDialog(): void {
    this.showCaseInfoDialog = false;
  }

  public closeNarrativeDialog(): void {
    this.showNarrativeDialog = false;
  }
}
