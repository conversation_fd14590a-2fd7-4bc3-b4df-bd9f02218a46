import { Component } from '@angular/core';
import { ButtonComponent } from '@progress/kendo-angular-buttons';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'venio-eci-summary',
  standalone: true,
  imports: [ButtonComponent, CommonModule],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class EciSummaryComponent {
  public onCaseButtonClick(): void {
    console.log('Case Info button clicked!');
  }
  
  public onNarrativeButtonClick(): void {
    console.log('Narrative button clicked!');
  }
}
