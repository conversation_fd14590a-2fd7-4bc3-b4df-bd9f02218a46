<div class="t-mt-14 t-relative">
  <kendo-loader
    *ngIf="showSpinner"
    size="medium"
    type="pulsing"
    class="t-absolute t-top-0 t-grid t-place-content-center t-w-full t-h-full t-bg-white/75 t-z-[3]"></kendo-loader>
  <div
    *ngFor="let group of groupedTimelineData"
    class="v-custom-timeline-block t-relative">
    <h2
      class="t-absolute t-left-[73px] t-top-[10px] t-text-xs t-bg-white t-z-[2] t-border t-p-2 t-tracking-wider t-rounded-full">
      {{ group.year }}
    </h2>
    <div class="v-custom-timeline-block__wrap t-relative t-pt-14">
      <div
        class="v-custom-timeline-block__wrap__line t-absolute t-bottom-0 t-top-0 t-left-[95px] t-w-[2px] t-bg-[#718792] t-z-[1]"></div>

      <kendo-listview
        [data]="group.entries"
        class="v-custom-timeline-block__wrap__listview !t-border-0"
        [height]="170"
        (scrollBottom)="loadMore(group.year)">
        <ng-template kendoListViewItemTemplate let-dataItem>
          <!-- Don't change the PX values to REM as the UI has been set from the Left axis to place the bullet ICON on the line, there is no issue with the responsiveness with this change. -->
          <div
            class="v-custom-timeline-block__wrap__listview__item t-flex t-gap-[10px] t-items-center t-p-2 t-relative">
            <div class="timeline-date t-w-[85px]">
              <div class="t-flex t-flex-col t-tracking-wider t-uppercase">
                <div class="t-w-full t-flex t-justify-center">
                  {{ dataItem.date }}
                </div>
                <div class="t-w-full t-flex t-justify-center">13:00</div>
              </div>
            </div>
            <div class="t-flex t-flex-col t-ml-2.5">
              <div class="timeline-user">
                <span class="t-font-semibold">User - </span>{{ dataItem.user }}
              </div>
              <!-- if activity is related only tagged NOTE: background color is inlined, in dev the color code will be replaced with the dynamic data color code-->
              <div *ngIf="!dataItem.description" class="timeline-action">
                <span class="t-font-semibold">Tagged, </span>
                <span class="t-text-[#979797]"
                  >Flyer<span
                    class="t-w-[10px] t-inline-block t-h-[10px] t-mx-1 t-rounded-full"
                    style="background-color: red">
                  </span>
                  {{ dataItem.action }}</span
                >
              </div>
              <!-- if activity is related other than tagged -->
              <div *ngIf="dataItem.description" class="timeline-action">
                <span class="t-font-semibold">Print, </span>
                <span class="t-text-[#979797]"></span>{{ dataItem.description }}
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-listview>
    </div>
  </div>
</div>
