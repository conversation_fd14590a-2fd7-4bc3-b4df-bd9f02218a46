<div class="t-flex t-flex-col t-flex-1 t-p-5" [formGroup]="settingsForm">
  <p
    class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px]">
    Select a Case & Service
  </p>

  <div class="t-flex t-flex-row t-gap-3 t-w-[60%] t-mt-2 t-mb-1 t-items-start">
    @if(!isServiceTypeRequestLoading()) {
    <div class="t-min-w-[16rem] t-p-0">
      <div class="t-flex">
        <kendo-dropdownlist
          id="service-type"
          formControlName="ServiceRequestType"
          [data]="serviceTypeList"
          textField="serviceTypeDisplayName"
          valueField="serviceTypeId"
          [defaultItem]="defaultItemServiceOption"
          (valueChange)="onServiceRequestSelection($event)"
          [valuePrimitive]="true"
          placeholder="Select Service Type">
        </kendo-dropdownlist>
        <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
      </div>
      <div
        *ngIf="isServiceTypeRequestValid"
        class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
        Please select a service type.
      </div>
    </div>
    } @else {
    <kendo-skeleton
      shape="rectangle"
      animation="pulse"
      class="t-min-w-[16rem] t-h-[2.1rem]"></kendo-skeleton>
    } @if(!existingCase && showCaseNameInput){
    <div class="t-min-w-[16rem] t-p-0">
      <div class="t-flex">
        <kendo-textbox
          id="case-name"
          formControlName="caseName"
          placeholder="Type Case Name"
          [maxlength]="60"
          (blur)="settingsForm.get('caseName').updateValueAndValidity()"
          [class.is-invalid]="
            settingsForm.get('caseName').invalid &&
            settingsForm.get('caseName').touched
          "
          class="t-rounded v-input-shadow">
        </kendo-textbox>
        <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
      </div>
      <div
        *ngIf="
          settingsForm.get('caseName').touched &&
          settingsForm.get('caseName').hasError('required')
        "
        class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
        Case Name is required.
      </div>
      <div
        *ngIf="
          settingsForm.get('caseName').errors?.notUniqueName &&
          settingsForm.get('caseName').touched
        "
        class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
        Case Name must be unique.
      </div>
    </div>
    } @if(existingCase || !showCaseNameInput){ @if(!isCaseDetailLoading()){
    <div class="t-min-w-[16rem] t-p-0">
      <div class="t-flex">
        <kendo-dropdownlist
          id="case-name"
          formControlName="selectedCase"
          [data]="caseList"
          textField="projectName"
          valueField="projectId"
          (valueChange)="selectedCaseChanged($event)"
          [defaultItem]="defaultItemCaseOption"
          [valuePrimitive]="true"
          placeholder="Select Case"
          [class.is-invalid]="
            settingsForm.get('selectedCase').invalid &&
            settingsForm.get('selectedCase').touched
          ">
        </kendo-dropdownlist>
        <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
      </div>
      <div
        *ngIf="
          settingsForm.get('selectedCase').touched &&
          (settingsForm.get('selectedCase').hasError('required') ||
            settingsForm.get('selectedCase').hasError('invalidValue'))
        "
        class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
        Please select a case.
      </div>
    </div>
    }@else{
    <kendo-skeleton
      shape="rectangle"
      animation="pulse"
      class="t-min-w-[16rem] t-h-[2.1rem]"></kendo-skeleton>
    } } @if(existingCase){
    <label class="t-flex t-min-w-[17rem] t-p-2">
      <input
        [ngModel]="overrideSettings"
        [ngModelOptions]="{ standalone: true }"
        (change)="updateOverrideSettingsOption($event)"
        [disabled]="!isValidFormFilled()"
        id="override-settings"
        type="checkbox"
        kendoCheckBox
        rounded="small"
        size="small" />
      <span
        class="t-text-[#5E6366] t-font-normal t-text-[14px] t-tracking-[0.42px] t-leading-[19px] t-ml-2">
        Override Service Request Settings</span
      >
    </label>
    }
  </div>

  @if(isValidFormFilled()){
  <div class="t-max-w-[80rem]">
    <kendo-expansionpanel
      id="general-setting"
      data-qa="general-setting"
      [expanded]="currentExpandedPanel() === 'general-setting'"
      (expandedChange)="onPanelToggle('general-setting', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="General Setting">
      <div class="t-flex t-flex-row t-gap-[30px] t-p-5">
        <div class="t-flex t-flex-col t-gap-2" formGroupName="generalSettings">
          <kendo-dropdownlist
            id="deduplication-option"
            formControlName="deduplicationOption"
            [data]="deduplicationOptions"
            textField="displayName"
            valueField="value"
            [valuePrimitive]="true"
            class="t-w-[16rem]">
          </kendo-dropdownlist>

          <kendo-dropdownlist
            id="timezone-select"
            formControlName="timeZone"
            [data]="timeZones$"
            textField="displayName"
            valueField="tzTimeZone"
            [valuePrimitive]="true"
            [popupSettings]="{ width: 'auto' }"
            class="t-w-[16rem]">
          </kendo-dropdownlist>

          <kendo-dropdownlist
            id="csv-excel-handling"
            formControlName="csvExcelHandling"
            [data]="csvExcelOptions"
            textField="displayName"
            valueField="value"
            [valuePrimitive]="true"
            class="t-w-[16rem]">
          </kendo-dropdownlist>

          <kendo-dropdownlist
            id="discovery-exception-handling"
            formControlName="discoveryExceptionHandling"
            [data]="exceptionHandlingOptions"
            textField="displayName"
            valueField="value"
            [valuePrimitive]="true"
            class="t-w-[16rem]">
          </kendo-dropdownlist>
        </div>

        <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

        <div
          class="t-flex t-flex-col t-flex-1"
          formGroupName="imageConversionSettings">
          <p
            class="t-text-[#263238] t-font-normal t-text-[14px] t-tracking-[0.01px] t-leading-[20px]">
            Passwords
          </p>
          <p
            class="t-text-[#263238] t-font-normal t-text-[11px] t-tracking-[0.01px] t-leading-[20px]">
            Please list passwords below, list each password on a separate line
          </p>
          <kendo-textarea
            id="passwords"
            formControlName="passwordList"
            class="t-mt-2 t-border-[#BEBEBE] t-rounded-[4px]"
            [rows]="7"
            resizable="none">
          </kendo-textarea>
        </div>
      </div>
    </kendo-expansionpanel>

    <kendo-expansionpanel
      id="image-conversion"
      data-qa="image-conversion-options"
      [expanded]="currentExpandedPanel() === 'image-conversion'"
      (expandedChange)="onPanelToggle('image-conversion', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="Image Conversion Options">
      <div class="!t-flex !t-flex-row t-gap-5 t-w-full">
        <div
          class="t-flex t-flex-col t-p-5"
          formGroupName="imageConversionSettings">
          <p
            class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-3">
            Color Conversion
          </p>

          <div class="t-flex t-flex-row t-gap-[20px]">
            <!-- Create image section -->
            <div
              class="t-flex t-flex-col t-gap-2"
              *ngIf="isConcordanceOrSummationService() || isPrintService()">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-2">
                Create Images?
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="image-type-pdf"
                  size="small"
                  type="radio"
                  [value]="2"
                  kendoRadioButton
                  formControlName="imageType" />
                <kendo-label text="PDF"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="image-type-tiff"
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  formControlName="imageType" />
                <kendo-label text="TIFF"></kendo-label>
              </div>

              <div
                class="t-flex t-flex-row t-gap-2"
                *ngIf="isConcordanceOrSummationService()">
                <input
                  id="image-type-none"
                  size="small"
                  type="radio"
                  [value]="0"
                  kendoRadioButton
                  formControlName="imageType" />
                <kendo-label text="No Image"></kendo-label>
              </div>
            </div>

            <div
              class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto t-mt-[15px]"
              *ngIf="
                showColorConvertionOptions() &&
                isConcordanceOrSummationService()
              "></div>
            <!-- End of Create image section -->

            <div
              class="t-flex t-flex-col t-gap-2"
              formGroupName="imageColorConversion"
              *ngIf="showColorConvertionOptions()">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-2">
                Image File Type
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="image-file-type-bw"
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  name="imageFileType"
                  formControlName="imageFileType"
                  checked />
                <kendo-label text="Black & White"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="image-file-type-color"
                  size="small"
                  type="radio"
                  [value]="2"
                  kendoRadioButton
                  name="imageFileType"
                  formControlName="imageFileType" />
                <kendo-label text="Color"></kendo-label>
              </div>
            </div>

            <div
              class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto t-mt-[15px]"
              *ngIf="showColorConvertionOptions()"></div>

            <div
              class="t-flex t-flex-col t-gap-2"
              formGroupName="imageColorConversion"
              *ngIf="showColorConvertionOptions()">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-2">
                PDF Files
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="pdf-files-bw"
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  name="pdfFiles"
                  formControlName="pdfFiles"
                  checked />
                <kendo-label text="Black & White"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="pdf-files-color"
                  size="small"
                  type="radio"
                  [value]="2"
                  kendoRadioButton
                  name="pdfFiles"
                  formControlName="pdfFiles" />
                <kendo-label text="Color"></kendo-label>
              </div>
            </div>

            <div
              class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto t-mt-[15px]"
              *ngIf="showColorConvertionOptions()"></div>
            <div
              class="t-flex t-flex-col t-gap-2"
              formGroupName="imageColorConversion"
              *ngIf="showColorConvertionOptions()">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-2">
                Powerpoint
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="power-point-bw"
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  name="powerpoint"
                  formControlName="powerpoint"
                  checked />
                <kendo-label text="Black & White"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="power-point-color"
                  size="small"
                  type="radio"
                  [value]="2"
                  kendoRadioButton
                  name="powerpoint"
                  formControlName="powerpoint" />
                <kendo-label text="Color"></kendo-label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </kendo-expansionpanel>

    <kendo-expansionpanel
      id="control-number-endorsement"
      data-qa="control-number-endorsement-option"
      [expanded]="currentExpandedPanel() === 'control-number-endorsement'"
      (expandedChange)="onPanelToggle('control-number-endorsement', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="Control Number & Endorsement">
      <form formGroupName="controlNumberAndEndorsementSettings">
        <div class="t-flex t-flex-row t-p-5 t-w-full">
          <div class="t-flex- t-flex-col t-flex-1 t-mr-3 t-gap-2 t-w-[20%]">
            <kendo-dropdownlist
              id="sort-order-select"
              formControlName="sortOrder"
              [data]="sortOrderOptions"
              [valuePrimitive]="true"
              textField="displayName"
              valueField="value"
              class="t-flex t-flex-1">
            </kendo-dropdownlist>
          </div>
          <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

          <div
            class="t-flex t-flex-col t-flex-1 t-ml-3 t-mr-1 t-gap-2 t-w-[20%]"
            formGroupName="ControlNumberSetting">
            <div clss="t-w-[30%] t-p-0">
              <kendo-textbox
                id="prefix"
                placeholder="Prefix"
                formControlName="controlNumberPrefix"
                [class.is-invalid]="
                  settingsForm.get(
                    'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberPrefix'
                  )?.invalid
                "
                class="t-flex !t-w-[calc(100%_-_0.7rem)]">
              </kendo-textbox>
              <div
                *ngIf="
                  settingsForm
                    .get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberPrefix'
                    )
                    .hasError('invalidPrefix')
                "
                class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
                Prefix shouldn't contain \/:*\"?<>| characters
              </div>
            </div>

            <kendo-dropdownlist
              id="prefix-delimiter"
              formControlName="controlNumberDelimiter"
              [data]="prefixDelimiterOptions"
              [valuePrimitive]="true"
              textField="displayName"
              valueField="value"
              class="t-flex !t-w-[calc(100%_-_0.7rem)]">
            </kendo-dropdownlist>

            <div>
              <div class="t-flex t-flex-col">
                <div class="t-flex t-flex-row">
                  <kendo-textbox
                    id="starting-number"
                    [maxlength]="18"
                    placeholder="Starting Number"
                    formControlName="controlNumberStartingNumber"
                    class="t-flex">
                  </kendo-textbox>
                  <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
                </div>
              </div>
              <div
                *ngIf="
                  settingsForm
                    .get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberStartingNumber'
                    )
                    ?.hasError('requiredStartingNumber')
                "
                class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
                Starting Number is required.
              </div>
              <div
                *ngIf="
                  settingsForm
                    .get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberStartingNumber'
                    )
                    ?.hasError('invalidStartingNumber')
                "
                class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
                Starting Number should be a positive number.
              </div>
            </div>
          </div>

          <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

          <div
            class="t-flex t-flex-col t-flex-1 t-mx-3 t-gap-2 t-w-[20%]"
            formGroupName="ControlNumberSetting">
            <label class="t-flex t-mb-1">
              <input
                type="checkbox"
                kendoCheckBox
                id="endorse-control-number-checkbox"
                formControlName="endorseControlNumber"
                rounded="small"
                size="small" />
              <span
                class="t-text-[#5E6366] t-font-normal t-text-[14px] t-tracking-[0.42px] t-leading-[19px] t-ml-2">
                Endorse Control Number</span
              >
            </label>
            <kendo-dropdownlist
              [disabled]="!endorseControlNumberIsChecked"
              id="controlNumberLocation"
              [data]="controlNumberLocationOptions"
              formControlName="controlNumberLocation"
              [valuePrimitive]="true"
              textField="displayName"
              valueField="value"
              class="t-flex">
            </kendo-dropdownlist>

            <label class="t-flex t-flex-1 t-my-1">
              <input
                type="checkbox"
                kendoCheckBox
                id="endorse-optional-message"
                formControlName="endorseOptionalMessage"
                #endorseOptionalMessage
                rounded="small"
                size="small" />
              <span
                class="t-text-[#5E6366] t-font-normal t-text-[14px] t-tracking-[0.42px] t-leading-[19px] t-ml-2">
                Endorse Optional Message</span
              >
            </label>
            <kendo-textarea
              [disabled]="!endorseOptionalMessageIsChecked"
              id="endorse-message-text"
              placeholder="Message"
              formControlName="messageText"
              class="t-flex"
              [rows]="2"
              resizable="none">
            </kendo-textarea>

            <kendo-dropdownlist
              [disabled]="!endorseOptionalMessageIsChecked"
              id="messageTextLocation"
              [data]="controlNumberLocationOptions"
              formControlName="messageTextLocation"
              [valuePrimitive]="true"
              textField="displayName"
              valueField="value"
              class="t-flex">
            </kendo-dropdownlist>
          </div>
          <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

          <div class="t-flex t-flex-col t-ml-3">
            <div
              class="t-flex t-flex-row t-items-start t-w-full"
              formGroupName="ControlNumberSetting">
              <kendo-textbox
                id="volume-id"
                formControlName="volumeId"
                placeholder="Volume ID"
                class="t-flex"
                [ngClass]="{
                  '!t-border !t-border-[#ED7425] !t-border-1':
                    settingsForm
                      .get(
                        'controlNumberAndEndorsementSettings.ControlNumberSetting.volumeId'
                      )
                      ?.hasError('required') &&
                    (settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.volumeId'
                    )?.touched ||
                      settingsForm.get(
                        'controlNumberAndEndorsementSettings.ControlNumberSetting.volumeId'
                      )?.dirty)
                }">
              </kendo-textbox>
              <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
            </div>
            <div
              *ngIf="isVolumeIdValid"
              class="!t-text-[#ED7425] t-text-[10px] t-mt-1 t-pl-2">
              Volume ID is required.
            </div>
          </div>
        </div>
      </form>
    </kendo-expansionpanel>

    <kendo-expansionpanel
      id="production-options"
      data-qa="production-options"
      [expanded]="currentExpandedPanel() === 'production-options'"
      (expandedChange)="onPanelToggle('production-options', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="Production Options">
      <div
        class="t-flex t-flex-row t-gap-[20px] t-p-5 t-w-[25rem]"
        formGroupName="productionSettings">
        <kendo-dropdownlist
          id="field-template"
          formControlName="fieldTemplateId"
          [data]="exportTemplates"
          [valuePrimitive]="true"
          [popupSettings]="{ width: 'auto' }"
          textField="Name"
          valueField="Id"
          class="t-flex t-flex-1">
        </kendo-dropdownlist>
      </div>
    </kendo-expansionpanel>

    @if(pdfServiceVisible()){
    <kendo-expansionpanel
      id="pdf-services"
      data-qa="pdf-services"
      [expanded]="currentExpandedPanel() === 'pdf-services'"
      (expandedChange)="onPanelToggle('pdf-services', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="PDF Services">
      <div class="t-flex t-flex-col t-p-5" formGroupName="pdfServiceSettings">
        <div class="t-flex t-flex-row t-gap-[30px]">
          <div class="t-flex t-flex-col t-gap-5 t-w-[50%]">
            <div class="t-flex t-flex-col t-gap-2 t-flex-1">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                PDF Type
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="pdf-type-searchable"
                  size="small"
                  type="radio"
                  [value]="0"
                  kendoRadioButton
                  name="pdfType"
                  formControlName="pdfType"
                  checked />
                <kendo-label text="Searchable"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  id="pdf-type-image"
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  name="pdfType"
                  formControlName="pdfType" />
                <kendo-label text="Image Only"></kendo-label>
              </div>
            </div>

            <div class="t-flex t-flex-col t-gap-2 t-flex-1">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                PDF File Naming
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="0"
                  kendoRadioButton
                  name="pdfFileNamingConvention"
                  id="pdf-naming-after-cn"
                  formControlName="pdfFileNamingConvention" />
                <kendo-label text="Name After Control Number"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  kendoRadioButton
                  size="small"
                  type="radio"
                  name="pdfFileNamingConvention"
                  id="pdf-naming-maintain"
                  [value]="1"
                  formControlName="pdfFileNamingConvention" />
                <kendo-label
                  text="Maintain Original File Name and Folder Structure"></kendo-label>
              </div>
            </div>
          </div>

          <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

          <div class="t-flex t-flex-col t-gap-2 t-w-[40%]">
            <p
              class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
              Family File Handling
            </p>

            <div class="t-flex t-flex-row t-gap-2">
              <input
                size="small"
                type="radio"
                name="pdfFamilyFileHandling"
                id="family-file-handling-together"
                [value]="0"
                kendoRadioButton
                formControlName="pdfFamilyFileHandling"
                checked />
              <kendo-label text="Parent/child as 1 doc"></kendo-label>
            </div>
            <div class="t-flex t-flex-row t-gap-2">
              <input
                size="small"
                type="radio"
                [value]="1"
                kendoRadioButton
                name="pdfFamilyFileHandling"
                id="family-file-handling-separate"
                formControlName="pdfFamilyFileHandling" />
              <kendo-label text="Parent & child separate"></kendo-label>
            </div>
          </div>
        </div>
      </div>
    </kendo-expansionpanel>
    } @if(isPrintService()){
    <kendo-expansionpanel
      id="print-services"
      data-qa="print-services"
      [expanded]="currentExpandedPanel() === 'print-services'"
      (expandedChange)="onPanelToggle('print-services', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="Print Services">
      <div class="t-flex t-flex-col t-p-5" formGroupName="printServiceSettings">
        <div class="t-flex t-flex-row t-gap-3">
          <div
            class="t-flex t-flex-row t-gap-5 t-max-w-lg"
            [ngClass]="{
              't-min-w-[18rem]': threeRingBinder.checked,
              't-min-w-[8.5rem]': !threeRingBinder.checked
            }">
            <div class="t-flex t-flex-col t-gap-2 t-flex-1">
              <p
                class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                Binding
              </p>
              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="0"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-none"
                  checked />
                <kendo-label text="None"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="2"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-file-folders" />
                <kendo-label text="File Folders"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="3"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-redwelds" />
                <kendo-label text="Redwelds"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="4"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-velo" />
                <kendo-label text="Velo"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  size="small"
                  type="radio"
                  [value]="5"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-gbc" />
                <kendo-label text="GBC"></kendo-label>
              </div>

              <div class="t-flex t-flex-row t-gap-2">
                <input
                  #threeRingBinder
                  size="small"
                  type="radio"
                  [value]="1"
                  kendoRadioButton
                  formControlName="binding"
                  name="binding"
                  id="binding-3-ring-binders" />
                <kendo-label text="3-Ring Binders"></kendo-label>
              </div>
            </div>

            @if(threeRingBinder.checked){
            <div class="t-flex t-flex-col t-gap-2 t-flex-1">
              <div class="t-flex t-flex-col t-gap-2 t-flex-1">
                <p
                  class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                  3-Ring Binder Size
                </p>

                <kendo-dropdownlist
                  id="3-ring-binder-size"
                  formControlName="threeRingBinderSize"
                  name="threeRingBinderSize"
                  [data]="binderSizes()"
                  [valuePrimitive]="true"
                  textField="label"
                  valueField="value"
                  class="t-w-full">
                </kendo-dropdownlist>
              </div>

              <div class="t-flex t-flex-col t-gap-2 t-flex-1">
                <p
                  class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                  3-Ring Binder Color
                </p>

                <kendo-dropdownlist
                  formControlName="threeRingBinderColor"
                  name="threeRingBinderColor"
                  id="3-ring-binder-color"
                  [data]="binderColors()"
                  [valuePrimitive]="true"
                  textField="label"
                  valueField="value"
                  class="t-w-full">
                </kendo-dropdownlist>
              </div>

              <div class="t-flex t-flex-col t-gap-2 t-flex-1"></div>
            </div>
            }
          </div>

          <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

          <div class="t-flex t-flex-col t-gap-5">
            <div class="t-flex t-flex-row t-gap-3">
              <div>
                <div class="t-flex t-flex-col t-gap-2">
                  <p
                    class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                    Print Set
                  </p>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="0"
                      kendoRadioButton
                      formControlName="printSet"
                      name="printSet"
                      id="print-set-single"
                      checked />
                    <kendo-label text="Single Copy Set"></kendo-label>
                  </div>
                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      #printSet2
                      size="small"
                      type="radio"
                      [value]="1"
                      kendoRadioButton
                      formControlName="printSet"
                      name="printSet"
                      id="print-set-multiple" />
                    <kendo-label text="# of Sets"></kendo-label>
                  </div>

                  @if(printSet2.checked){
                  <div class="t-flex t-flex-row t-gap-2">
                    <kendo-textbox
                      formControlName="numberOfSets"
                      id="number-of-sets"
                      [disabled]="!printSet2.checked"
                      placeholder="Number of Sets"
                      class="t-w-full">
                    </kendo-textbox>
                  </div>
                  }
                </div>

                <div class="t-flex t-flex-col t-gap-2 t-pt-6">
                  <p
                    class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                    Document Seperator
                  </p>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      formControlName="documentSeparator"
                      name="documentSeparator"
                      id="document-separator-blue-slipsheet"
                      [value]="0"
                      kendoRadioButton
                      checked />
                    <kendo-label text="Blank Blue Slipsheet"></kendo-label>
                  </div>
                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="1"
                      kendoRadioButton
                      formControlName="documentSeparator"
                      name="documentSeparator"
                      id="document-separator-metadata-slipsheet" />
                    <kendo-label
                      text="Metadata Slipsheet - Original Filename and Original Path"></kendo-label>
                  </div>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="2"
                      kendoRadioButton
                      formControlName="documentSeparator"
                      name="documentSeparator"
                      id="document-separator-staple" />
                    <kendo-label text="Staple/Clip"></kendo-label>
                  </div>
                </div>
              </div>

              <div
                class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

              <div class="t-w-72">
                <div class="t-flex t-flex-col t-gap-2">
                  <p
                    class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                    Family File Handling
                  </p>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      name="printFamilyFileHandling"
                      id="ffhp-together"
                      [value]="0"
                      kendoRadioButton
                      formControlName="printFamilyFileHandling"
                      checked />
                    <kendo-label text="Parent/child as 1 doc"></kendo-label>
                  </div>
                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="1"
                      kendoRadioButton
                      name="printFamilyFileHandling"
                      id="ffhp-separate"
                      formControlName="printFamilyFileHandling" />
                    <kendo-label text="Parent & child separate"></kendo-label>
                  </div>
                </div>

                <div class="t-flex t-flex-col t-gap-2 t-pt-6">
                  <p
                    class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                    Paper Type
                  </p>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="0"
                      kendoRadioButton
                      formControlName="paperType"
                      name="paperType"
                      id="paper-type-regular"
                      checked />
                    <kendo-label text="Regular"></kendo-label>
                  </div>
                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="1"
                      kendoRadioButton
                      formControlName="paperType"
                      name="paperType"
                      id="paper-type-3" />
                    <kendo-label text="3-hole drill (side)"></kendo-label>
                  </div>

                  <div class="t-flex t-flex-row t-gap-2">
                    <input
                      size="small"
                      type="radio"
                      [value]="2"
                      kendoRadioButton
                      formControlName="paperType"
                      name="paperType"
                      id="paper-type-2" />
                    <kendo-label text="2-hole drill (top)"></kendo-label>
                  </div>
                </div>
              </div>

              <div
                class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

              <div class="t-flex t-flex-col t-gap-2 t-w-[40%]">
                <p
                  class="t-text-[#263238] t-font-bold t-text-[14px] t-tracking-[0.01px] t-leading-[20px] t-mb-1">
                  Paper Side
                </p>

                <div class="t-flex t-flex-row t-gap-2">
                  <input
                    size="small"
                    type="radio"
                    [value]="0"
                    kendoRadioButton
                    formControlName="paperSide"
                    name="paperSide"
                    id="paper-side-single"
                    checked />
                  <kendo-label text="Single Sided"></kendo-label>
                </div>
                <div class="t-flex t-flex-row t-gap-2">
                  <input
                    size="small"
                    type="radio"
                    [value]="1"
                    kendoRadioButton
                    formControlName="paperSide"
                    name="paperSide"
                    id="paper-side-double" />
                  <kendo-label text="Double Sided"></kendo-label>
                </div>
              </div>
            </div>

            <div class="t-flex t-flex-row t-gap-[30px] t-flex-1">
              <div
                class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>
            </div>
          </div>
        </div>
      </div>
    </kendo-expansionpanel>
    } @if(relativityServiceVisible()){
    <kendo-expansionpanel
      id="relativity-services"
      data-qa="relativity-services"
      [expanded]="currentExpandedPanel() === 'relativity-services'"
      (expandedChange)="onPanelToggle('relativity-services', $event)"
      class="v-custom-expansion-panel t-my-2"
      [animation]="true"
      subtitle="Relativity Services">
      <div class="t-flex t-flex-row t-p-5" formGroupName="productionSettings">
        <div class="t-flex t-flex-col t-gap-2" formGroupName="connector">
          <p class="t-font-medium t-text-[#263238] t-text-[14px]">
            Option selection
          </p>
          <!-- Environment Dropdown -->
          <div class="t-flex t-flex-row t-items-start t-w-96">
            <kendo-dropdownlist
              formControlName="id"
              name="id"
              [data]="connectorEnvironmentData()"
              id="environment"
              textField="environmentName"
              valueField="id"
              [valuePrimitive]="true"
              [defaultItem]="defaultItemEnvironmentSelectionOption"
              class="t-w-full"
              (valueChange)="onEnvironmentSelection($event)">
            </kendo-dropdownlist>
            <span class="!t-text-[#ED7425] t-font-bold t-pl-1"> * </span>
          </div>
          <div
            *ngIf="
              settingsForm.get('productionSettings.connector.id').invalid &&
              settingsForm.get('productionSettings.connector.id').touched
            "
            class="!t-text-[#ED7425] t-text-[10px] t-pl-2">
            Environment selection is required.
          </div>

          <!-- Workspace Dropdown -->

          @if(!isWorkSpaceLoading()){
          <div class="t-flex t-flex-row t-items-start t-w-96">
            <kendo-dropdownlist
              formControlName="workspaceId"
              name="workspaceId"
              [data]="workSpaceData()"
              id="work-space"
              textField="name"
              valueField="id"
              [valuePrimitive]="true"
              class="t-w-full"
              [disabled]="
                settingsForm.get('productionSettings.connector.id').value ===
                null
              "
              (valueChange)="onWorkspaceSelection($event)">
            </kendo-dropdownlist>
            <span class="!t-text-[#ED7425] t-font-bold t-pl-1"> * </span>
          </div>
          <div
            *ngIf="
              settingsForm.get('productionSettings.connector.workspaceId')
                .invalid &&
              settingsForm.get('productionSettings.connector.workspaceId')
                .touched
            "
            class="!t-text-[#ED7425] t-text-[10px] t-pl-2">
            Workspace selection is required.
          </div>
          }@else{
          <kendo-skeleton
            shape="rectangle"
            animation="pulse"
            class="t-w-[24rem] t-h-[2.1rem]"></kendo-skeleton>
          }

          <!-- Conditional Workspace Fileshare Dropdown -->
          @if(!isWorkspaceFileShareDataLoading()){
          <div
            class="t-flex t-flex-row t-items-start t-w-96"
            *ngIf="isRelativityOne">
            <kendo-dropdownlist
              formControlName="connectorFileSharePath"
              name="connectorFileSharePath"
              [data]="workSpaceFileshareData()"
              id="work-space-file-share"
              textField="name"
              valueField="name"
              [valuePrimitive]="true"
              [popupSettings]="{ width: 'auto' }"
              [disabled]="
                settingsForm.get('productionSettings.connector.workspaceId')
                  .value === null
              "
              class="t-w-full">
            </kendo-dropdownlist>
            <span class="!t-text-[#ED7425] t-font-bold t-pl-1"> * </span>
          </div>
          <div
            *ngIf="
              settingsForm.get(
                'productionSettings.connector.connectorFileSharePath'
              )?.invalid &&
              settingsForm.get(
                'productionSettings.connector.connectorFileSharePath'
              )?.touched
            "
            class="!t-text-[#ED7425] t-text-[10px] t-pl-2">
            Workspace file share is required.
          </div>
          }@else{
          <kendo-skeleton
            shape="rectangle"
            animation="pulse"
            class="t-w-[24rem] t-h-[2.1rem]"></kendo-skeleton>
          }

          <!-- File Share Path Textbox -->
          <div
            class="t-flex t-flex-row t-items-start t-gap-2 !t-w-[calc(100%_-_0.7rem)]">
            <kendo-textbox
              formControlName="baseAPIUrl"
              name="baseAPIUrl"
              id="base-api-url"
              [disabled]="true"
              placeholder="Base API URL"
              [maxlength]="60"
              class="t-w-full">
            </kendo-textbox>
          </div>
        </div>

        <div
          class="v-dashed-sperator t-opacity-100 t-mx-3 t-w-[1px] t-h-auto"></div>

        <div class="t-flex t-flex-col t-justify-between">
          <div class="t-flex t-flex-col t-flex-1 t-w-96">
            <p class="t-font-medium t-text-[#263238] t-text-[14px] t-mb-2">
              Mapping Template
            </p>

            <!-- Mapping Template -->
            <div class="t-flex t-flex-row t-items-start t-w-70">
              <kendo-dropdownlist
                formControlName="relativityFieldMappingTemplateId"
                [data]="relativityTemplatesData"
                id="mapping-template"
                textField="name"
                valueField="id"
                [valuePrimitive]="true"
                class="t-w-full">
              </kendo-dropdownlist>
              <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
            </div>
            <div
              *ngIf="
                settingsForm.get(
                  'productionSettings.relativityFieldMappingTemplateId'
                )?.invalid &&
                settingsForm.get(
                  'productionSettings.relativityFieldMappingTemplateId'
                )?.touched
              "
              class="!t-text-[#ED7425] t-text-[10px] t-pl-2">
              Mapping template selection is required.
            </div>
          </div>

          @if(settingsForm.get('productionSettings.connector.id')?.value > 0 &&
          selectedEnvironmentData?.userEnvironment?.id > 0){
          <div class="t-flex t-flex-col">
            <p class="t-text-[12px]">
              Accessing workspace using
              {{ isRelativityOne ? 'client id ' : '' }}
              <span class="t-text-[#1DBADC]">{{
                isRelativityOne ? relativityOneClientId : relativityUsername
              }}</span>
            </p>
            <p class="t-text-[12px]">
              <span
                class="t-text-[#1DBADC] t-cursor-pointer"
                (click)="
                  openRelativityLoginDialog(relativityLoginDialogPayload)
                "
                >Click Here</span
              >
              to change the login details.
            </p>
          </div>
          }
        </div>
      </div>
    </kendo-expansionpanel>
    }
  </div>
  }
</div>
