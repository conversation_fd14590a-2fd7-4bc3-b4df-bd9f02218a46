import { FormControl } from '@angular/forms'

export interface UsersListModel {
  userId?: number
  userName: string
  email: string
  groupName?: string
}

export interface InviteUserModel {
  senderUserId: number
  recipientUserIds: number[]
  uploadExpirationPeriod: number
  projectId: number
  instruction: string
  invitedExtUserInfo: string[]
  invitedIntUserInfo: string[]
}

export interface InviteUploadForm {
  shareToExternalUsers: FormControl<boolean>
  instruction: FormControl<string>
  newEmail: FormControl<string>
  validity: FormControl<number>
}

export interface InviteUploadUserGridModel {
  field: string
  title: string
  width: number
}

export interface InviteUploadFormModel {
  shareToExternalUsers: boolean
  instruction: string
  newEmail: string
  validity: number
}

export interface UserSelectionModel {
  selectedItems: UsersListModel[]
  deselectedItems: UsersListModel[]
}
