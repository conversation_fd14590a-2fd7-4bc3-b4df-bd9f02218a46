import { Component } from '@angular/core';
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons';
import { DataService } from '../data.service';

@Component({
  selector: 'app-center-text',
  standalone: true,
  imports: [KENDO_BUTTONS],
  templateUrl: './center-text.component.html',
  styleUrl: './center-text.component.scss'
})
export class CenterTextComponent {
  constructor(private dataService: DataService) {}

  public svgOpenNew: SVGIcon = hyperlinkOpenSmIcon;
  openFocusedSection() {
    this.dataService.setIsFocusedSectionOpened(true);
  }
}
