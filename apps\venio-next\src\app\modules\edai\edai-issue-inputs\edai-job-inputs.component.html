<div class="t-flex t-gap-3 t-items-center" [formGroup]="edaiFormGroup()">
  <ng-container formGroupName="basicJobModel">
    <kendo-textbox
      formControlName="jobName"
      placeholder="Job Name"
      class="t-w-56" />
  </ng-container>
  <kendo-dropdownlist
    formControlName="jobType"
    [data]="jobTypes"
    textField="text"
    valueField="value"
    [valuePrimitive]="true"
    [listHeight]="500"
    class="!t-w-56">
    <ng-template kendoSuffixTemplate>
      <button
        kendoButton
        [svgIcon]="downIcon"
        fillMode="link"
        class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
    </ng-template>
  </kendo-dropdownlist>

  @if(isPiiType()) {
  <p class="t-text-[#232323] t-font-semibold t-text-xs t-ml-4">
    <span class="t-text-error mr-2">NOTE</span> Only 15 Options can be selected
    including custom PII type
  </p>
  }
</div>
