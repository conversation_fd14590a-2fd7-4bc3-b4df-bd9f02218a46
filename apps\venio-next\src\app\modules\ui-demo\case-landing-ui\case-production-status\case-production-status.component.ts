import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { CaseProductionStatusGraphComponent } from './case-production-status-graph/case-production-status-graph.component'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CaseProductionStatusShareComponent } from './case-production-status-share/case-production-status-share.component'

@Component({
  selector: 'venio-case-production-status',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    DropDownsModule,
    IndicatorsModule,
    CaseProductionStatusGraphComponent,
    SvgLoaderDirective,
    CaseProductionStatusShareComponent,
  ],
  templateUrl: './case-production-status.component.html',
  styleUrl: './case-production-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseProductionStatusComponent {
  public dialogTitle = 'Production Status'

  public ifProductionShare: boolean

  public defaultItemExternal: { text: string; value: number } = {
    text: 'All',
    value: 1,
  }

  public externalUsers: Array<{ text: string; value: number }> = [
    { text: 'Inprogress', value: 2 },
    { text: 'Failed', value: 3 },
    { text: 'Not Started', value: 4 },
  ]

  public chartData1: any[] = [
    { value: 1, color: '#718792' },
    { value: 3, color: '#FFB300' },
    { value: 2, color: '#9BD2A7' },
    { value: 2, color: '#ED7425' },
  ]

  public chartData2: any[] = [
    { value: 1, color: '#718792' },
    { value: 5, color: '#FFB300' },
    { value: 7, color: '#9BD2A7' },
    { value: 2, color: '#ED7425' },
  ]

  public chartData3: any[] = [
    { value: 1, color: '#718792' },
    { value: 5, color: '#FFB300' },
    { value: 2, color: '#9BD2A7' },
    { value: 2, color: '#ED7425' },
  ]

  public chartData4: any[] = [
    { value: 6, color: '#718792' },
    { value: 5, color: '#FFB300' },
    { value: 2, color: '#9BD2A7' },
    { value: 2, color: '#ED7425' },
  ]

  public status = 'INPROGRESS'

  public svgIconForGridControls = [
    {
      actionType: 'Setting',
      iconPath: 'assets/svg/icon-measurement-setting.svg',
      hoverColor: '#1EBADC',
      applyFill: 'fill' as any,
    },
    {
      actionType: 'Download',
      iconPath: 'assets/svg/icon-production-setting-download.svg',
      hoverColor: '#FFBB10',
      applyFill: 'stroke' as any,
    },
    {
      actionType: 'Share',
      iconPath: 'assets/svg/icon-review-share.svg',
      hoverColor: '#2F3080',
      applyFill: 'both' as any,
    },
    {
      actionType: 'Note',
      iconPath: 'assets/svg/icon-rate-review-note.svg',
      hoverColor: '#9AD3A6',
      applyFill: 'fill' as any,
    },
    {
      actionType: 'Delete',
      iconPath: 'assets/svg/icon-note-ui-delete.svg',
      hoverColor: '#ED7425',
      applyFill: 'fill' as any,
    },
  ]

  public badgeClassMap: { [key: string]: string } = {
    FAILED: 't-bg-[#E55353]', // Red
    COMPLETED: 't-bg-[#FEB43C]', // Green
    INPROGRESS: 't-bg-[#FEB43C]', // Orange
    'NOT STARTED': 't-bg-[#718792]', // Gray
  }

  constructor(public dialogRef: DialogRef) {}

  public close(status: string): void {
    this.dialogRef.close()
  }

  public changeDialogWidth(dialogRef: DialogRef, width: string): void {
    dialogRef.dialog.instance.width = '80%'
  }

  public getBadgeClass(status: string): string {
    const badgeColors: { [key: string]: string } = {
      FAILED: 't-bg-[#E55353]', // Red
      COMPLETED: '!t-bg-[#7ACB8F]', // Green
      INPROGRESS: 't-bg-[#FEB43C]', // Orange
      'NOT STARTED': 't-bg-[#718792]', // Gray
    }

    return badgeColors[status] || 't-bg-gray-300' // Default color if status is unknown
  }

  public browseActionClicked(actionType: any, dataItem: string): void {
    switch (actionType) {
      case 'Setting':
        // Invoke methods
        break
      case 'Download':
        // Invoke methods
        break
      case 'Share':
        this.ifProductionShare = true
        this.changeDialogWidth(this.dialogRef, '80%')
        break
      case 'Note':
        // Invoke methods
        break
    }
  }
}
