import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  QueryList,
  TemplateRef,
  Type,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TranscriptFacade,
  TranscriptState,
  TranscriptModel,
  NoteObject,
  HighlightObject,
  DocumentObject,
  AnnotationSavePayload,
  TempTableResponseModel,
  DocumentsFacade,
  SearchFacade,
  LinkedDocumentModel,
  AnnotationType,
  ANNOTATION_SYMBOLS,
  TranscriptNote,
  TranscriptNoteBoxPosition,
  TranscriptReportType,
  TRANSCRIPT_REPORT_MENU,
} from '@venio/data-access/review'
import {
  ReviewPanelFacade,
  ReviewPanelSelectedDocumentModel,
} from '@venio/data-access/document-utility'
import {
  EMPTY,
  Observable,
  Subject,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  of,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import {
  Page,
  ResponseModel,
  TranscriptViewModel,
} from '@venio/shared/models/interfaces'
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling'
import {
  ExpansionPanelComponent,
  LayoutModule,
} from '@progress/kendo-angular-layout'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  DialogRef,
  DialogService,
  DialogSettings,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { ListViewModule } from '@progress/kendo-angular-listview'
import { MenusModule } from '@progress/kendo-angular-menu'
import { chevronRightIcon } from '@progress/kendo-svg-icons'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { TextSelectEventDirective } from '../directives/text-select-event.directive'
import { VenioNotificationService } from '@venio/feature/notification'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { HttpErrorResponse } from '@angular/common/http'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { HighlightDirective } from '../directives/highlight.directive'
import { TranscriptWorkerService } from '../worker/transcript-worker-service'
import { TooltipModule } from '@progress/kendo-angular-tooltip'

import { LocalStorage } from '@venio/shared/storage'
import { isEqual } from 'lodash'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { PageControlActionType } from '@venio/shared/models/constants'
import { UserFacade } from '@venio/data-access/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-feature-transcript-viewer',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    LayoutModule,
    ButtonsModule,
    IconsModule,
    ListViewModule,
    InputsModule,
    MenusModule,
    DialogsModule,
    DynamicHeightDirective,
    TextSelectEventDirective,
    SvgLoaderDirective,
    FormsModule,
    ReactiveFormsModule,
    LoaderModule,
    HighlightDirective,
    TooltipModule,
    DropDownsModule,
  ],
  templateUrl: './feature-transcript-viewer.component.html',

  styleUrl: './feature-transcript-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeatureTranscriptViewerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private toDestroy$: Subject<void> = new Subject<void>()

  private annotationFetchedDateTime: string

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public get isViewerPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isViewerPanelPopout')
  }

  public transcriptItems: Array<TranscriptModel>

  public tempTables: TempTableResponseModel

  public selectedDocuments: number[]

  public currentDocument: number

  public currentDocumentName: string

  protected selectedTranscriptId = 0

  public dataArray = []

  public menuState = false

  public boxPosition: TranscriptNoteBoxPosition

  @ViewChildren(ExpansionPanelComponent)
  public readonlypanels: QueryList<ExpansionPanelComponent>

  @ViewChild('transcriptContent', { static: true })
  private transcriptContent: TemplateRef<any>

  @ViewChild('addNote', { static: true })
  private addNotes: TemplateRef<any>

  @ViewChild('linkedDocuments', { static: true })
  private linkedDocuments: TemplateRef<any>

  @ViewChild('wordWheel', { static: true })
  private wordWheel: TemplateRef<any>

  @ViewChild('highlightDocs', { static: true })
  private highlightDocs: TemplateRef<any>

  public reportItems = TRANSCRIPT_REPORT_MENU

  public items = [
    { text: 'Transcript', expanded: false, templateName: 'transcriptContent' },
    { text: 'Highlights', expanded: false, templateName: 'highlightDocs' },
    { text: 'Notes', expanded: false, templateName: 'addNotes' },
    {
      text: 'Linked Documents',
      expanded: false,
      templateName: 'linkedDocuments',
    },
    { text: 'WordWheel', expanded: false, templateName: 'wordWheel' },
  ]

  public dialogTitle = 'Notes'

  public noteDialogOpenStatus = false

  public isAddNewNote = true

  public contextMenuItems: any = [
    {
      text: 'Highlight',
      // icon: borderColorIcon,
      items: [],
      action: (): void => this.removeSelectedHighlight(),
      disabled: false,
    },
    {
      text: 'Add note',
      // icon: fileTxtIcon,
      action: (): void => this.handleNote(),
      disabled: false,
    },
    {
      text: 'Link document',
      // icon: linkIcon,
      action: (): void => this.addDocumentLink(),
      disabled: false,
    },
  ]

  private highlightItems = [
    {
      text: 'Default',
      name: 'default',
      action: (args): void => this.addHighlight(args, '#e4dd4e'),
    },
    {
      text: 'Green',
      name: 'green',
      action: (args): void => this.addHighlight(args, 'green'),
    },
    {
      text: 'Gray',
      name: 'gray',
      action: (args): void => this.addHighlight(args, 'gray'),
    },
  ]

  public noteItems = []

  public notesViewerComponent: Promise<Type<unknown>>

  //generate 10 wheel items

  public wheelItems: any = []

  public icons = { chevronRightIcon: chevronRightIcon }

  private documentArray: DocumentObject[] = []

  private noteArray: NoteObject[] = []

  public fontStyle = {
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    fontSize: '14px',
    fontWeight: '400',
  }

  @ViewChild('cdkviewport')
  public cdkviewPort: CdkVirtualScrollViewport

  @ViewChild(HighlightDirective)
  private highlighter: HighlightDirective

  public isLoading = true

  public wordWheelText: FormControl = new FormControl('')

  private dialogRef: DialogRef

  constructor(
    private searchFacade: SearchFacade,
    private documentsFacade: DocumentsFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private transcriptFacade: TranscriptFacade,
    private userFacade: UserFacade,
    public transcriptState: TranscriptState,
    private activatedRoute: ActivatedRoute,
    private notification: VenioNotificationService,
    private dialogService: DialogService,
    private vcr: ViewContainerRef,
    private iframeMessengerService: IframeMessengerService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.#fetchAndHandleTranscripts()
    this.#getTranscriptFileDetails()
    this.#getwordWheel()
    this.#getTranscriptNote()
    this.#selectCurrentUserDetails()
  }

  #getTranscriptFileDetails(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(
        filter(([selectedDocuments]) =>
          Boolean(
            selectedDocuments?.length > 0 &&
              !isEqual(selectedDocuments, this.selectedDocuments)
          )
        ),
        tap(([selectedDocuments, tempTables]) => {
          this.tempTables = tempTables
          this.selectedDocuments = selectedDocuments
          this.currentDocument = selectedDocuments[0]
        }),
        switchMap(() => this.#fetchTranscriptFileDetails$()),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.#updateTranscriptFileDetails(response)
      })
  }

  #fetchAndHandleTranscripts(): void {
    this.isLoading = true
    this.transcriptFacade
      .fetchTranscript(this.projectId)
      .pipe(
        tap((result: ResponseModel) => {
          if (result?.data?.length === 0) {
            this.cdr.markForCheck()
            this.isLoading = false
          }
        }),
        filter((result: ResponseModel) => result?.data?.length > 0),
        tap((result: ResponseModel) => {
          this.transcriptItems = result.data
          this.selectedTranscriptId = this.transcriptItems[0].Id
        }),
        switchMap(() => this.#fetchTranscriptById$(this.selectedTranscriptId)),
        catchError((error: unknown) => {
          this.isLoading = false
          const response = (error as HttpErrorResponse).error as ResponseModel
          this.notification.showError(response?.message)
          return EMPTY
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.isLoading = false
        this.#handleTranscriptViewerData(response)
      })
  }

  #selectCurrentUserDetails(): void {
    this.userFacade.selectCurrentUserDetails$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((currentUserDetails) => {
        this.transcriptState.currentUserDetails.set(currentUserDetails)
      })
  }

  public ngAfterViewInit(): void {
    this.cdkviewPort.scrolledIndexChange
      .pipe(
        filter(() => !this.isLoading),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.loadAnnotations()
        this.highlighter.textChanged$.next()
        //this.setDocumentLinkPageChange()
        //this.setHighlightAfterPageChange()
      })
  }

  #fetchTranscriptById$(id: number): Observable<any> {
    return this.transcriptFacade.fetchTranscriptById(this.projectId, id)
  }

  #fetchTranscriptFileDetails$(): Observable<any> {
    return this.transcriptFacade.fetchTranscriptFileDetails(
      this.projectId,
      this.selectedTranscriptId,
      this.tempTables?.searchResultTempTable,
      this.currentDocument
    )
  }

  #updateTranscriptFileDetails(response: ResponseModel): void {
    this.transcriptState.fileIdDetails.set(response.data?.FileIdDetails)
    this.currentDocumentName = response.data?.FileName
  }

  #getwordWheel(): void {
    this.wordWheelText.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        switchMap((word: string) => {
          return this.transcriptFacade.getWordWheel(
            this.projectId,
            this.selectedTranscriptId,
            word
          )
        }),
        catchError((error: unknown) => {
          const response = (error as HttpErrorResponse).error as ResponseModel
          this.notification.showError(response?.message)
          return EMPTY
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        if (Array.isArray(response?.data)) {
          this.wheelItems = response?.data?.map((item) => ({
            label: item.Id,
            id: item.Id,
            words: item.Words,
            action: (id): void => this.scrollIntoView(id),
          }))
        } else this.wheelItems = []
      })
  }

  public getTemplate(templateName: string): TemplateRef<any> {
    return this[templateName]
  }

  public onAction(index: number): void {
    this.items[index].expanded = !this.items[index].expanded
  }

  public onTranscriptItemClick(transcriptItem: TranscriptModel): void {
    this.selectedTranscriptId = transcriptItem.Id
    this.#fetchTranscriptById$(transcriptItem.Id)
      .pipe(
        tap(() => (this.isLoading = true)),
        catchError((error: unknown) => {
          this.isLoading = false
          const response = (error as HttpErrorResponse).error as ResponseModel
          this.notification.showError(response?.message)
          return EMPTY
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.isLoading = false
        this.selectedTranscriptId = transcriptItem.Id
        this.#handleTranscriptViewerData(response)
      })
    this.#loadTranscriptFileDetails()
  }

  #loadTranscriptFileDetails(): void {
    this.#fetchTranscriptFileDetails$()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        this.#updateTranscriptFileDetails(response)
      })
  }

  #getAnnotationLastSavedDatetime(): void {
    this.transcriptFacade
      .getAnnotationLastSavedTime(this.projectId, this.selectedTranscriptId)
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        this.annotationFetchedDateTime = response.data
      })
  }

  #handleTranscriptViewerData(response: ResponseModel): void {
    this.#getAnnotationLastSavedDatetime()
    this.transcriptState.setAnnotations(response.data?.Annotation)
    const data = response.data?.FileJson as Page
    const documentArray = this.transcriptState.documentArray()
    const serviceworker = new TranscriptWorkerService()
    serviceworker
      .getTranscriptFlattenedData(data, documentArray)
      .then((transcriptViewModel: TranscriptViewModel) => {
        this.cdr.markForCheck()
        this.dataArray = transcriptViewModel.flattenedTranscriptData
        this.transcriptState.setLinkedDocuments(
          transcriptViewModel.linkedDocuments
        )
        setTimeout(() => this.#updateTranscriptViewer(), 10)
      })
    this.wheelItems = []
    this.wordWheelText.reset()
    this.scrollIntoView('1:1')
  }

  #updateTranscriptViewer(): void {
    this.loadAnnotations()
    //this.setDocumentLinkPageChange()
    //this.setHighlightAfterPageChange()
    const scrollToPage = this.transcriptState.selectedDocumentPageId()
    if (scrollToPage) {
      this.scrollIntoView(scrollToPage)
      this.#clearSelectedDocumentPageId()
    }
  }

  public trackByViewerData(i: number): (index: number, subItem: any) => string {
    return (index: number, subItem: any) => {
      return `${i}-${index}-${subItem[index + 1]}`
    }
  }

  public onContextMenuSelect(event: any): void {
    this.#getBoxPosition(event)
    const selectedItem = event.item
    if (selectedItem && selectedItem.action) {
      selectedItem.action()
    }
  }

  public onActionPerformed(
    action: string,
    dataItem: HighlightObject | NoteObject
  ): void {
    switch (action) {
      case 'SCROLL':
        if ('id' in dataItem) {
          this.scrollIntoView(dataItem?.id)
        } else {
          const index = dataItem?.index?.replace(/[a-zA-Z]+/g, '')
          this.scrollIntoView(index)
        }
        break
      case 'DELETE_HIGHLIGHT':
        this.deleteHighlight(dataItem as HighlightObject)
        break
    }
  }

  private scrollIntoView(dataItemId: string): void {
    const indices = dataItemId.split(':')
    const index = this.dataArray.findIndex(
      (d) => d.pageNumber === +indices[0] && d.lineNumber === +indices[1]
    )
    this.cdkviewPort.scrollToIndex(index)
  }

  private openNote(id: string): void {
    const note = this.transcriptState
      .notes()
      .find((hl) => hl.index?.replace(/[a-zA-Z]+/g, '') === id)
    if (note) {
      this.isAddNewNote = false
      this.transcriptState.selectedNote.set(note)
      this.noteDialogOpenStatus = true
      this.#handleLazyLoadedDialog()
      this.cdr.markForCheck()
    }
  }

  #handleTranscriptReportDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetSelectedReport()
    })
  }

  #handleTranscriptNoteDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetSelectedNote()
    })
  }

  #launchDialogContent(
    dialogContent: Type<unknown>,
    dialogOptions: DialogSettings
  ): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      appendTo: this.vcr,
      ...dialogOptions,
    })
  }

  #getNoteDialogOptions(): DialogSettings {
    const defaultOptions: DialogSettings = {
      maxWidth: '550',
      minWidth: '35%',
      height: '90vh',
      width: '55%',
    }
    return defaultOptions
  }

  #getReportDialogOptions(): DialogSettings {
    const defaultOptions: DialogSettings = {
      maxWidth: '1600',
      minWidth: '250',
      height: '80vh',
      width: '85%',
    }
    return defaultOptions
  }

  #handleLazyLoadedDialog(): void {
    const dialogOptions = this.#getNoteDialogOptions()
    import(
      '../feature-transcript-notes/feature-transcript-notes.component'
    ).then((d) => {
      // launch the dialog
      this.#launchDialogContent(
        d.FeatureTranscriptNotesComponent,
        dialogOptions
      )
      // once the dialogRef instance is created
      this.#handleTranscriptNoteDialogCloseEvent()
    })
  }

  #hanldeLazyLoadReportComponent(): void {
    const dialogOptions = this.#getReportDialogOptions()
    import(
      '../feature-transcript-report/feature-transcript-report.component'
    ).then((d) => {
      // launch the dialog
      this.#launchDialogContent(
        d.FeatureTranscriptReportComponent,
        dialogOptions
      )
      // once the dialogRef instance is created
      this.#handleTranscriptReportDialogCloseEvent()
    })
  }

  public handleNote(): void {
    this.transcriptState.selectedTextArray().forEach((el) => {
      // if window is already open, no need to open it again for the multiple line select for the same note
      if (this.noteDialogOpenStatus) return
      this.isAddNewNote = true
      this.noteDialogOpenStatus = true
      this.#setCurrentNote(el.id)
      this.#handleLazyLoadedDialog()
      this.cdr.markForCheck()
    })
  }

  public close(status: string): void {
    this.noteDialogOpenStatus = false
  }

  public toggleMenuState(): void {
    this.menuState = !this.menuState
  }

  public trackById(index: number, item: any): number {
    return item.id
  }

  public renderRectangles(event): void {
    const selection = document.getSelection()
    const isValid = this.validateSelection(event)
    if (!isValid) {
      this.notification.showWarning('Text selection invalid')
      selection.empty()
    }
    this.transcriptState.selectedTextArray.set(event)
  }

  private validateSelection(eventData): boolean {
    //const argument =eventData[0]
    let isvalid = true
    const combinedAnnotations = this.transcriptState.combinedAnnotations()
    eventData.forEach((argument) => {
      if (argument.startOffset > argument.endOffset) {
        isvalid = false
        return
      }
      const hasAnnotationOnSpecificLine = Object.keys(combinedAnnotations).some(
        (key) => key === argument.id
      )
      if (hasAnnotationOnSpecificLine) {
        const annotations = combinedAnnotations[argument.id]
        for (const annotation of annotations) {
          if (
            (argument.startOffset > Number(annotation.startOffset) &&
              argument.startOffset < Number(annotation.endOffset)) ||
            (argument.endOffset > Number(annotation.startOffset) &&
              argument.endOffset < Number(annotation.endOffset))
          ) {
            isvalid = false
            break
          }
        }
      }
    })
    return isvalid
  }

  #validateDocumentLink(): boolean {
    let isValid = true
    if (this.currentDocument < 0) {
      this.notification.showError('No document selected')
      isValid = false
    }
    return isValid
  }

  /**
   * Document linkevent
   * @constructor
   * * @param  event - input on selection event
   */
  public addDocumentLink(): void {
    if (!this.#validateDocumentLink()) return
    this.transcriptState.selectedTextArray().forEach((element) => {
      if (!element.id) return
      let indexs = []
      if (element.id.includes('Note')) {
        indexs = this.noteArray.filter((el) => el.index === element.id)
      } else if (element.id.includes('highlight')) {
        indexs = this.noteArray.filter((el) => el.index === element.id)
      }
      const id = element.id.split(
        element.id.includes('highlight')
          ? 'highlight'
          : element.id.includes('Note')
          ? 'Note'
          : null
      )[0]
      const startOffset =
        indexs.length && indexs[0].selectedText === element.text
          ? indexs[0].startOffset
          : element.startOffset
      const endOffset =
        indexs.length && indexs[0].selectedText === element.text
          ? indexs[0].endOffset
          : element.endOffset

      const selectedDocument = {
        fileId: this.currentDocument,
        fileName: this.currentDocumentName,
      }
      this.transcriptState.linkedDocuments.set([
        ...this.transcriptState.linkedDocuments(),
        {
          fileId: this.currentDocument,
          fileName: this.currentDocumentName,
          index: `${element.id}`,
          id: id,
        },
      ])
      this.transcriptState.documentArray.set([
        ...this.transcriptState.documentArray(),
        {
          selectedText: element.text,
          index: `${element.id}`,
          id: id,
          startOffset: startOffset,
          endOffset: endOffset,
          linkedFile: selectedDocument,
        },
      ])
      this.transcriptState.updateAnnotations()
      this.#loadAnnotationByElement(
        element.id,
        this.#getAnnotationsByElementId(element.id)
      )
    })
    this.saveAnnotation()
    window.getSelection().removeAllRanges()
  }

  public addHighlight(item, color): void {
    item = { name: 'default', color: color }
    this.transcriptState.selectedTextArray().forEach((element) => {
      const highlightBoolean = this.transcriptState
        .highlights()
        .some((el) => el.index === element.id)
      const noteBoolean = this.transcriptState
        .notes()
        .some((el) => el.index.split('Note')[0] === element.id)

      if (highlightBoolean || noteBoolean) {
        this.notification.showError('Restricted')
        return
      }
      if (!element.id.includes('highlight') && !element.id.includes('Note')) {
        let indexs = []
        if (element.id.includes('Document')) {
          indexs = this.documentArray.filter(
            (element) => element.id === element.id.split('Document')[0]
          )
        }
        this.transcriptState.highlights.set([
          ...this.transcriptState.highlights(),
          {
            selectedText: element.text,
            id: element.id,
            startOffset:
              indexs.length && indexs[0].selectedText === element.text
                ? indexs[0].startOffset
                : element.startOffset,
            endOffset:
              indexs.length && indexs[0].selectedText === element.text
                ? indexs[0].endOffset
                : element.endOffset,
            index: element.id,
            color: item.color,
            highlightId: element.id + 'highlight',
          },
        ])
        this.transcriptState.updateAnnotations()
        this.#loadAnnotationByElement(
          element.id,
          this.#getAnnotationsByElementId(element.id)
        )
      }
    })
    this.saveAnnotation()
    //this.transcriptState.selectedTextArray.set([])
    //this.showNoteBox = false
  }

  public removeSelectedHighlight(): void {
    if (this.contextMenuItems[0].text !== 'Remove Highlight') return
    this.transcriptState.selectedTextArray().forEach((el) => {
      const highlight = this.transcriptState
        .highlights()
        .find((hl) => hl.id === el.id)
      if (highlight) {
        this.deleteHighlight(highlight)
      }
    })
    this.saveAnnotation()
  }

  public deleteHighlight(item: HighlightObject): void {
    this.#removeHighlight(item)
    const newHighlights = this.transcriptState
      .highlights()
      .filter((h) => h.id !== item.id)
    this.transcriptState.highlights.set(newHighlights)
  }

  #removeHighlight(item): void {
    const elMain: HTMLElement = document.getElementById(`${item.id}`)
    if (elMain) {
      const elHighlight: HTMLElement | null = document.getElementById(
        `${item.highlightId}`
      )

      if (elHighlight) {
        // Extract the inner text from the div
        const divText = elHighlight.innerText

        // Create a text node with the div's text
        const textNode = document.createTextNode(divText)

        // Insert the text node before the div
        elMain.insertBefore(textNode, elHighlight)

        // Remove the div
        elMain.removeChild(elHighlight)
      }
    }
  }

  /**
   * Save annotaions.
   * @constructor
   */
  private saveAnnotation(): void {
    this.transcriptFacade
      .getAnnotationLastSavedTime(this.projectId, this.selectedTranscriptId)
      .pipe(
        switchMap((response: ResponseModel) => {
          if (this.annotationFetchedDateTime !== response.data) {
            this.notification.showWarning(
              'The annotation has been saved by another user after you have loaded annotations. So, your changes are not saved. Please reload annotations.'
            )
            return EMPTY
          }
          return of(response)
        }),
        switchMap(() => {
          const payload: AnnotationSavePayload =
            this.#getAnnotationSavePayload()
          return this.transcriptFacade.saveAnnotation(payload)
        }),
        //catchError()
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.notification.showSuccess('Annotation saved successfully')
        this.#getAnnotationLastSavedDatetime()
        this.transcriptState.updateAnnotations()
      })
  }

  #getAnnotationSavePayload(): AnnotationSavePayload {
    const payload: AnnotationSavePayload = {
      projectInfo: {
        projectId: this.projectId,
        fileId: this.selectedTranscriptId,
      },
      annotationData: {
        highlightArray:
          this.transcriptState.highlights() ??
          [].filter((item) => item && item.selectedText),
        noteArray:
          this.transcriptState.notes() ??
          [].filter((item) => item && item.selectedText),
        documentArray:
          this.transcriptState.documentArray() ??
          [].filter((item) => item && item.selectedText),
      },
    }
    return payload
  }

  public expandLinkedDocuments(): void {
    this.menuState = true
    this.items.forEach((item) => {
      if (item.templateName === 'linkedDocuments') {
        item.expanded = true
      }
    })
    this.cdr.markForCheck()
  }

  #clearSelectedDocumentPageId(): void {
    this.transcriptState.setSelectedDocumentPageId(null)
  }

  public highlightContextMenuText(): string {
    let isSelectedTextHighlighted = false
    for (const el of this.transcriptState.selectedTextArray()) {
      if (this.transcriptState.highlights())
        isSelectedTextHighlighted = this.transcriptState
          .highlights()
          .some((hl) => hl.id === el.id)
      if (isSelectedTextHighlighted) break
    }
    if (isSelectedTextHighlighted) {
      this.contextMenuItems[0].text = 'Remove Highlight'
      this.contextMenuItems[0].items = []
    } else {
      this.contextMenuItems[0].text = 'Add Highlight'
      this.contextMenuItems[0].items = this.highlightItems
    }
    return isSelectedTextHighlighted ? 'Remove Highlight' : 'Add Highlight'
  }

  public linkDocumentContextMenu(): string {
    let id: string
    let isSelectedTextLinked = false
    for (const el of this.transcriptState.selectedTextArray()) {
      if (this.transcriptState.linkedDocuments()) {
        id = el.id?.replace(/[a-zA-Z]+/g, '')
        isSelectedTextLinked = this.transcriptState
          .linkedDocuments()
          .some((hl) => hl.id === id)
        if (isSelectedTextLinked) break
      }
    }

    const linkText = isSelectedTextLinked
      ? 'Remove Link Document'
      : 'Link Document'
    const linkAction: () => void = isSelectedTextLinked
      ? (): void => this.removeDocumentLink()
      : (): void => this.addDocumentLink()

    this.contextMenuItems[2] = {
      text: linkText,
      action: linkAction,
    }

    return linkText
  }

  public addNoteContextMenu(): string {
    let index: string
    let isSelectedNote = false
    let isSelectedHighlight = false
    for (const el of this.transcriptState.selectedTextArray()) {
      const id = el.id?.replace(/[a-zA-Z]+/g, '')
      if (this.transcriptState.highlights()) {
        isSelectedHighlight = this.transcriptState
          .highlights()
          .some((hl) => hl.id === id)
        if (isSelectedHighlight) break
      }
      if (this.transcriptState.notes() && !isSelectedHighlight) {
        index = `${id}Note`
        isSelectedNote = this.transcriptState
          .notes()
          .some((hl) => hl.index === index)
        if (isSelectedNote) break
      }
    }

    const noteText = isSelectedNote ? 'Remove Note' : 'Add Note'
    const noteAction: () => void = isSelectedNote
      ? (): void => this.removeNote()
      : (): void => this.handleNote()

    this.contextMenuItems[1] = {
      text: noteText,
      action: noteAction,
      disabled: isSelectedHighlight,
    }

    return noteText
  }

  #removeDocument(id: string): void {
    const indexs = this.transcriptState
      .documentArray()
      .filter((element) => element.index === id)

    if (indexs.length !== 0) {
      indexs.forEach((element) => {
        const mainEl: any = document.getElementById(id)
        const docEl: any = document.getElementById(element.id + 'Document')

        // This will remove the underline from the text if it is currently visible in the viewer.
        // If the text is not visible, the underline will be removed when the page is scrolled.
        if (mainEl && docEl) {
          const text = docEl.innerText
          const textNode = document.createTextNode(text)
          mainEl.insertBefore(textNode, docEl)
          mainEl.removeChild(docEl)
        }
      })
    }
  }

  // This method is used to remove the document link if it is already linked.
  public removeDocumentLink(): void {
    this.transcriptState.selectedTextArray().forEach((el) => {
      const documentLink = this.transcriptState
        .linkedDocuments()
        .find((doc) => {
          return doc.id === el.id
        })
      if (documentLink) this.deleteDocument(documentLink, false)
    })
    this.saveAnnotation()
  }

  public deleteDocument(dataItem, canSaveAnnotation = true): void {
    let item: DocumentObject

    const noOfDocuments = this.transcriptState
      .linkedDocuments()
      .filter((doc) => doc.index === dataItem.index).length
    //remove only if there is only one document linked to the document
    if (noOfDocuments === 1) {
      this.#removeDocument(dataItem.index)
    }
    this.transcriptState.documentArray().forEach((element) => {
      dataItem.id === element.index && (item = element)
    })

    // Retrieve the document array for saving the data and the linked document array for UI updates
    const documentArray = this.#getRemoveDocumentPayload(item, dataItem)
    const linkedDocumentArray = this.transcriptState
      .linkedDocuments()
      .filter(
        (item) => !(item.id === dataItem.id && item.fileId === dataItem.fileId)
      )
    //this.#removeDocument(dataItem.index)
    //const linkedDocumentArray =this.transcriptState.linkedDocuments().filter((item)=>)
    this.transcriptState.linkedDocuments.set(linkedDocumentArray)
    this.transcriptState.documentArray.set(documentArray)
    if (canSaveAnnotation) this.saveAnnotation()
  }

  #getRemoveDocumentPayload(
    item: DocumentObject,
    dataItem: LinkedDocumentModel
  ): DocumentObject[] {
    let documentArray: DocumentObject[]

    // remove the document from the linked document
    if (!Array.isArray(item?.linkedFile) || item?.linkedFile.length === 1) {
      documentArray = this.transcriptState
        .documentArray()
        .filter((h) => h.id !== dataItem.id)
    } else {
      // remove the linked document from the linked documents array
      documentArray = this.transcriptState.documentArray().map((item) => {
        if (item.id === dataItem.id) {
          return {
            ...item,
            linkedFile: item.linkedFile.filter(
              (file) => file.fileId !== dataItem.fileId
            ),
          }
        }
        return item
      })
    }
    return documentArray
  }

  public openLinkDocument(dataItem): void {
    const fileId = dataItem?.fileId
    //If the document is already open, scroll to the document.
    if (this.currentDocument === +fileId) {
      this.scrollIntoView(dataItem.id)
      return
    }
    this.transcriptState.setSelectedDocumentPageId(dataItem.id)
    const isFileExistsSearchScope = this.transcriptState
      .fileIdDetails()
      .find((x) => x.FileId === +fileId)?.IsFileExistsSearchScope
    const selectReviewPanelDocument: ReviewPanelSelectedDocumentModel = {
      currentfileId: this.currentDocument,
      documentNo: fileId,
      isDocumentExistsInSearchScope: isFileExistsSearchScope,
    }
    this.reviewPanelFacade.setSelectedReviewPanelDocument(
      selectReviewPanelDocument
    )
    // Send an action event from the popout window to update data in the parent window.
    if (this.isViewerPanelPopout) {
      this.#sendLinkDocumentActionEvent(selectReviewPanelDocument)
      return
    }
  }

  /**
   * Sends a Link document action event to the parent window.
   * @param {ReviewPanelSelectedDocumentModel} selectReviewPanelDocument - The selected document to include in the event payload.
   * @returns {void} This method does not return anything.
   */
  #sendLinkDocumentActionEvent(
    selectReviewPanelDocument: ReviewPanelSelectedDocumentModel
  ): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      payload: {
        type: MessageType.WINDOW_CHANGE,
        content: {
          selectReviewPanelDocument: selectReviewPanelDocument,
          pageControlActionType: PageControlActionType.LINK_DOCUMENT,
        },
      },
      eventTriggeredBy: AppIdentitiesTypes.VIEWER_PANEL,
      iframeIdentity: AppIdentitiesTypes.VIEWER_PANEL,
      eventTriggeredFor: 'ALL_WINDOW',
    })
  }

  #getAnnotationsByElementId(elementId: string): Array<HighlightObject> {
    const combinedAnnotations = this.transcriptState.combinedAnnotations()
    if (elementId in combinedAnnotations) return combinedAnnotations[elementId]
    return []
  }

  public loadAnnotations(): void {
    for (const [index, annotations] of Object.entries(
      this.transcriptState.combinedAnnotations()
    )) {
      this.#loadAnnotationByElement(index, annotations)
    }
  }

  #loadAnnotationByElement(elementId, annotations): void {
    const id = elementId.replace(/[A-Za-z]/g, '')
    const mainElement = document.getElementById(id)
    if (!mainElement) return
    let text = mainElement.textContent
    const addedAnnotationTypes: AnnotationType[] = []

    const highlightSymbols = ANNOTATION_SYMBOLS[AnnotationType.Highlight]
    const documentLinkSymbols = ANNOTATION_SYMBOLS[AnnotationType.DocumentLink]
    const noteSymbols = ANNOTATION_SYMBOLS[AnnotationType.Note]

    // Sort ranges by startOffset
    annotations.sort((a, b) => Number(a.startOffset) - Number(b.startOffset))

    const finalText = ''
    let startOffset, endOffset
    let color = '#e4dd4e'
    annotations.forEach((ann, index) => {
      const annotationType: AnnotationType = this.#getAnnotationType(ann)
      const { start: startSymbol, end: endSymbol } =
        ANNOTATION_SYMBOLS[annotationType]
      addedAnnotationTypes.push(annotationType)

      if (index === 0) {
        startOffset = Number(ann.startOffset)
        endOffset = Number(ann.endOffset)
      } else {
        startOffset = Number(ann.startOffset) + startSymbol.length * 2
        endOffset = Number(ann.endOffset) + endSymbol.length * 2
      }

      if (annotationType === AnnotationType.Highlight && ann?.color)
        color = ann?.color
      const beforeSelectedText = text.slice(0, startOffset)
      const selectedText = text.slice(startOffset, endOffset)
      const afterSelectedText = text.slice(endOffset)

      text = finalText.concat(
        beforeSelectedText,
        startSymbol,
        selectedText,
        endSymbol,
        afterSelectedText
      )
    })

    mainElement.innerHTML = text
      .replaceAll(
        highlightSymbols.start,
        this.#getHighlightMarkup(elementId, color)
      )
      .replaceAll(highlightSymbols.end, '</span>')
      .replace(
        documentLinkSymbols.start,
        this.#getDocumentLinkMarkup(elementId)
      )
      .replace(documentLinkSymbols.end, '</u>')
      .replace(noteSymbols.start, this.#getNoteMarkup(elementId))
      .replace(noteSymbols.end, '</span>')
    this.#handleClickEvent(id, addedAnnotationTypes)
  }

  #handleClickEvent(
    elementId: string,
    annotationTypes: AnnotationType[]
  ): void {
    const clickHandlers: Record<AnnotationType, () => void> = {
      [AnnotationType.DocumentLink]: () => this.expandLinkedDocuments(),
      [AnnotationType.Note]: () => this.openNote(elementId),
      [AnnotationType.Highlight]: () => {}, // No action for Highlight
    }

    annotationTypes.forEach((annotationType) => {
      if (annotationType === AnnotationType.Highlight) return

      const { name: annotationName } = ANNOTATION_SYMBOLS[annotationType]
      const element = document.getElementById(elementId + annotationName)

      if (element) {
        element.onclick = (event: MouseEvent): void => {
          if (event.button === 0 && !window.getSelection()?.toString()) {
            event.preventDefault()
            clickHandlers[annotationType]()
          }
        }
      }
    })
  }

  #getAnnotationType(annotation): AnnotationType {
    if ('linkedFile' in annotation) return AnnotationType.DocumentLink
    if ('note' in annotation) return AnnotationType.Note
    return AnnotationType.Highlight
  }

  #getHighlightMarkup(elementId: string, color: string): string {
    return `<span id="${elementId}highlight" style="display:inline;background-color:${color}">`
  }

  #getDocumentLinkMarkup(elementId: string): string {
    return `<u id="${elementId}Document" style="display:inline;">`
  }

  #getNoteMarkup(elementId: string): string {
    return `<span id="${elementId}Note" class="t-cursor-pointer t-bg-[#DDFFDD] t-border-l-[6px] t-border-l-[6px] t-border-l-[#4CAF50] t-px-[4px] t-py-[2px]">`
  }

  /* Manage document tag navigation. */
  #getTranscriptNote(): void {
    this.transcriptFacade.getTranscriptNote
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((note: TranscriptNote) => {
        if (this.isAddNewNote) {
          this.#addNewNote(note)
        } else {
          this.#addNote(note)
        }
      })
  }

  #addNewNote(note: TranscriptNote): void {
    let canSave = true
    this.transcriptState.selectedTextArray().forEach((element) => {
      if (!element.id) return
      const highlightBoolean = this.transcriptState
        .highlights()
        .some((el) => el.index === element.id)
      const noteBoolean = this.transcriptState
        .notes()
        .some((el) => el.index.split('Note')[0] === element.id)
      if (highlightBoolean || noteBoolean) {
        this.notification.showError('Restricted')
        canSave = false
        return
      }

      if (
        !element.id.includes('highlight') &&
        !element.id.includes('Note') &&
        element.text !== ''
      ) {
        let indexs = []
        if (element.id.includes('Document')) {
          indexs = this.documentArray.filter(
            (element) => element.id === element.id.split('Document')[0]
          )
        }
        const boxPosition = {
          top: this.boxPosition.top,
          left: '0px',
        }
        const index = element.id.split('Document')[0]
        this.transcriptState.notes.set([
          ...this.transcriptState.notes(),
          {
            selectedText: element.text,
            index: `${index}Note`,
            note: [note, null],
            startOffset:
              indexs.length && indexs[0].selectedText === element.text
                ? indexs[0].startOffset
                : element.startOffset,
            endOffset:
              indexs.length && indexs[0].selectedText === element.text
                ? indexs[0].endOffset
                : element.endOffset,
            boxPosition: boxPosition,
          },
        ])
        this.#setCurrentNote(index)
        this.transcriptState.updateAnnotations()
        this.#loadAnnotationByElement(
          element.id,
          this.#getAnnotationsByElementId(element.id)
        )
        this.transcriptFacade.setNoteUpdateStatus = true
        this.isAddNewNote = false
      }
    })
    if (canSave) this.saveAnnotation()
  }

  #addNote(note: TranscriptNote): void {
    const index = this.transcriptState.selectedNote().index
    this.transcriptState.notes.set(
      this.transcriptState.notes().map((item) => {
        if (item.index === index) {
          return {
            ...item,
            note: [...(item.note || []), note],
          }
        }
        return item
      })
    )
    this.#setCurrentNote(index)
    this.transcriptState.updateAnnotations()
    this.transcriptFacade.setNoteUpdateStatus = true
    this.saveAnnotation()
  }

  // This method is used to remove the note if it is already linked.
  public removeNote(): void {
    this.transcriptState.selectedTextArray().forEach((el) => {
      const id = el.id?.replace(/[a-zA-Z]+/g, '')
      const index = `${id}Note`
      const note = this.transcriptState.notes().find((doc) => {
        return doc.index === index
      })
      if (note) this.deleteNote(note, false)
    })
    this.saveAnnotation()
  }

  public deleteNote(dataItem, canSaveAnnotation = true): void {
    const index = dataItem.index
    this.#removeNoteElement(index)
    this.transcriptState.notes.set(
      this.transcriptState.notes().filter((h) => h.index !== dataItem.index)
    )
    this.transcriptState.updateAnnotations()
    if (canSaveAnnotation) this.saveAnnotation()
  }

  #removeNoteElement(index): void {
    const indexs = this.transcriptState
      .notes()
      .filter((element) => element.index === index)
    if (indexs.length !== 0) {
      indexs.forEach((element) => {
        const id = index?.replace(/[a-zA-Z]+/g, '')
        const mainEl: any = document.getElementById(id)
        if (mainEl) {
          const noteEl: any = document.getElementById(element.index)
          const innerHTMLHighLight = noteEl?.innerHTML
          noteEl?.remove()
          const innerHTMLMain = mainEl?.innerHTML
          const output =
            innerHTMLMain?.substring(0, element.startOffset) +
            innerHTMLHighLight +
            innerHTMLMain?.substring(element.startOffset)
          mainEl.innerHTML = output
        }
      })
    }
  }

  #setCurrentNote(id: string): void {
    const index = id?.replace(/[a-zA-Z]+/g, '')
    const note = this.transcriptState
      .notes()
      .find((hl) => hl.index?.replace(/[a-zA-Z]+/g, '') === index)
    if (note) {
      this.transcriptState.selectedNote.set(note)
    }
  }

  #getBoxPosition(event: any): void {
    if (event?.originalEvent) {
      this.boxPosition = {
        top: event.originalEvent.clientY,
        left: event.originalEvent.clientX,
      }
    }
  }

  public reportTypeChange(event: object): void {
    this.transcriptState.selectedReportType.set(
      event['id'] as TranscriptReportType
    )
    this.#hanldeLazyLoadReportComponent()
    this.cdr.markForCheck()
  }

  #resetSelectedNote(): void {
    this.transcriptState.selectedNote.set(null)
    this.isAddNewNote = true
    this.noteDialogOpenStatus = false
    window.getSelection().removeAllRanges()
  }

  #resetSelectedReport(): void {
    this.transcriptState.selectedReportType.set(TranscriptReportType.ALL)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
