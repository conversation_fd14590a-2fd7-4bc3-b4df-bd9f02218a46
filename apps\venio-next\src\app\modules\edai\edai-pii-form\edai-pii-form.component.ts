import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { AiFacade, GeneralForm, JobForm, PIITypes } from '@venio/data-access/ai'
import {
  CheckBoxDirective,
  TextAreaComponent,
  TextBoxComponent,
} from '@progress/kendo-angular-inputs'
import { EditorModule } from '@progress/kendo-angular-editor'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { minusIcon, plusIcon } from '@progress/kendo-svg-icons'
import { toSignal } from '@angular/core/rxjs-interop'
import { filter, map } from 'rxjs'
import { sortBy } from 'lodash'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-edai-pii-form',
  standalone: true,
  imports: [
    CommonModule,
    CheckBoxDirective,
    EditorModule,
    ReactiveFormsModule,
    TextBoxComponent,
    TextAreaComponent,
    SVGIconComponent,
    TooltipDirective,
    SkeletonComponent,
  ],
  templateUrl: './edai-pii-form.component.html',
  styleUrl: './edai-pii-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiPiiFormComponent {
  private readonly formBuilder = inject(FormBuilder)

  private readonly aiFacade = inject(AiFacade)

  public readonly addIcon = plusIcon

  public readonly removeIcon = minusIcon

  public edaiFormGroup = input.required<FormGroup<JobForm>>()

  public readonly piiFormGroup = computed(
    () => this.edaiFormGroup().controls.piiJobModel
  )

  private readonly selectedDefaultTypes = computed(
    () => this.piiFormGroup().controls.defaultTypes
  )

  public readonly customTypeControls = computed(
    () => this.piiFormGroup().controls.customTypes
  )

  public readonly totalPIIControls = signal(0)

  public readonly isPIIEntityLoading = toSignal(
    this.aiFacade.selectIsEdaiPIIEntitiesLoading$,
    {
      initialValue: true,
    }
  )

  public readonly piiDefaultEntities = toSignal(
    this.aiFacade.selectEdaiPIIEntities$.pipe(
      filter((e) => typeof e !== 'undefined'),
      map((e) =>
        sortBy<PIITypes>(e.data?.piiEntities?.['piiTypes'] || [], [
          'piiTypeName',
        ])
      )
    ),
    {
      initialValue: [],
    }
  )

  public getValidation(index: number): boolean {
    const customTypesArray = this.customTypeControls()
    const formGroup = customTypesArray.at(index)
    const descriptionControl = formGroup.controls.description
    const hasName = formGroup.controls.name.value?.trim()
    if (hasName) {
      descriptionControl.markAsTouched()
      descriptionControl.markAsDirty()
      descriptionControl.addValidators([Validators.required])
    } else {
      descriptionControl.clearValidators()
    }
    descriptionControl.updateValueAndValidity()
    return (
      descriptionControl.dirty &&
      descriptionControl.touched &&
      descriptionControl.invalid
    )
  }

  public checkboxChange(checked: boolean, piiType: PIITypes): void {
    const defaultTypesArray = this.selectedDefaultTypes()
    const { id, piiTypeName } = piiType
    if (checked) {
      defaultTypesArray.push(
        this.formBuilder.group<GeneralForm>({
          id: new FormControl(id),
          name: new FormControl(piiTypeName),
        })
      )
    } else {
      const index = defaultTypesArray.controls.findIndex(
        (control) => control.controls.name?.value === piiTypeName
      )
      if (index > -1) {
        defaultTypesArray.removeAt(index)
      }
    }

    this.totalPIIControls.set(
      defaultTypesArray.length + this.customTypeControls().length
    )
  }

  public isTypeSelected(piiType: string): boolean {
    const defaultTypesArray = this.selectedDefaultTypes()
    const index = defaultTypesArray.controls.findIndex(
      (control) => control.controls.name?.value === piiType
    )
    return index > -1
  }

  public addCustomType(): void {
    const customTypesArray = this.customTypeControls()
    customTypesArray.push(
      this.formBuilder.group<GeneralForm>({
        name: new FormControl(''),
        description: new FormControl(''),
      })
    )
    customTypesArray.updateValueAndValidity()
    this.totalPIIControls.set(
      this.selectedDefaultTypes().length + this.customTypeControls().length
    )
  }

  public removeCustomType(index: number): void {
    const customTypesArray = this.customTypeControls()
    if (customTypesArray.length > 1) {
      customTypesArray.removeAt(index)
      customTypesArray.updateValueAndValidity()
      this.totalPIIControls.set(
        this.selectedDefaultTypes().length + this.customTypeControls().length
      )
    }
  }
}
