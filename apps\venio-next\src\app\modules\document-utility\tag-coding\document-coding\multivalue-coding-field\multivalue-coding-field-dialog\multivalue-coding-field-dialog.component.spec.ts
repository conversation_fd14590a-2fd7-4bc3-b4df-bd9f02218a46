import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MultivalueCodingFieldDialogComponent } from './multivalue-coding-field-dialog.component'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('MultivalueCodingFieldDialogComponent', () => {
  let component: MultivalueCodingFieldDialogComponent
  let fixture: ComponentFixture<MultivalueCodingFieldDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, MultivalueCodingFieldDialogComponent],
      providers: [DocumentCodingFacade, provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(MultivalueCodingFieldDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
