export interface SearchFieldModel {
  searchFields: SearchField[]
  custodianNames: string[]
  exportNames: string[]
  imageSetNames: string[]
  languages: string[]
  redactionSets: string[]
  redactionReason: string[]
  samplingNames: string[]
  fileTypeGroups: string[]
  timeZones: string[]
  tiffTypes: string[]
  imagingEngineConfigured: string[]
  volumeNames: string[]
  mediaNames: string[]
}

export interface SearchField {
  fieldName: string
  searchDataType: SearchDataType
  allowNullSearch: boolean
  displayName: string
  isCustomField: boolean
  displayOrder: number
  venioFieldID: number
}

export enum SearchDataType {
  None = 'NONE',
  Boolean = 'BOOLEAN',
  Date = 'DATE',
  Dbstring = 'DBSTRING',
  MultiSelect = 'MULTI_SELECT',
  Number = 'NUMBER',
  NumberSize = 'NUMBER_SIZE',
  SingleSelect = 'SINGLE_SELECT',
  String = 'STRING',
}

export enum SearchTriggerSource {
  AutoRun = 'AutoRun',
  Manual = 'Manual',
}
