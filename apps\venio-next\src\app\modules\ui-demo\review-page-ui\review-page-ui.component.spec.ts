import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewPageUiComponent } from './review-page-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ReviewPageUiComponent', () => {
  let component: ReviewPageUiComponent
  let fixture: ComponentFixture<ReviewPageUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewPageUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewPageUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
