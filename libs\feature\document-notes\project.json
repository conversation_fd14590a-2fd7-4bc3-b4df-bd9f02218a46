{"name": "document-notes", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/document-notes/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/document-notes/ng-package.json", "tailwindConfig": "libs/feature/document-notes/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/document-notes/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/document-notes/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/document-notes/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}