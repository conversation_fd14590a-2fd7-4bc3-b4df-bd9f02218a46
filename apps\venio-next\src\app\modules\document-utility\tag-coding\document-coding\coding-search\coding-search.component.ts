import { Component, On<PERSON><PERSON>roy } from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  AfterValueChangedDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import { Subject } from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-coding-search',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    AfterValueChangedDirective,
    ButtonsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './coding-search.component.html',
  styleUrl: './coding-search.component.scss',
})
export class CodingSearchComponent implements OnDestroy {
  constructor(private documentCodingFacade: DocumentCodingFacade) {}

  private unsubscribed$: Subject<void> = new Subject<void>()

  public onFilter(value): void {
    const searchValue: string = value
    this.documentCodingFacade.searchDocumentCoding(searchValue)
  }

  /**
   * Reset the search text
   * @returns {void}
   */
  #resetSearchTextState(): void {
    this.documentCodingFacade.resetDocumentCodingState(['searchDocumentCoding'])
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.#resetSearchTextState()
  }
}
