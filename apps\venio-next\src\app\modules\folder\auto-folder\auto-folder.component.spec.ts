import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AutoFolderComponent } from './auto-folder.component'
import { AnimationBuilder } from '@angular/animations'
import {
  FieldFacade,
  FolderFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('AutoFolderComponent', () => {
  let component: AutoFolderComponent
  let fixture: ComponentFixture<AutoFolderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AutoFolderComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        AnimationBuilder,
        FolderFacade,
        SearchFacade,
        FieldFacade,
        NotificationService,
        StartupsFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AutoFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
