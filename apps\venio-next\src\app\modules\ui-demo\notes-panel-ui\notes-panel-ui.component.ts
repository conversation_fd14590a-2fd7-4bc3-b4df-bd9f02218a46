import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TreeViewComponent,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { Observable, of } from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { SVGIcon, checkIcon, xIcon } from '@progress/kendo-svg-icons'

interface ChatMessageNode {
  id: number // Ensure there's an ID for each message for reference
  text: string
  author: string
  timestamp: Date
  items?: ChatMessageNode[] // Nested replies
}

@Component({
  selector: 'venio-notes-panel-ui',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    ButtonsModule,
    InputsModule,
    SvgLoaderDirective,
    TooltipsModule,
    DropDownsModule,
  ],
  templateUrl: './notes-panel-ui.component.html',
  styleUrl: './notes-panel-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotesPanelUiComponent {
  // Sample data structure
  // public chatMessages: ChatMessageNode[] = [
  //   {
  //     author: 'Super',
  //     timestamp: new Date('2023-02-13T21:36:13'),
  //     text: 'Lorem Ipsum Lorem Ipsum Lorem Ipsum...',
  //     items: [
  //       {
  //         author: 'Super',
  //         timestamp: new Date('2023-02-13T21:36:13'),
  //         text: 'All permissions are granted',
  //       },
  //       // ... More nested replies if any
  //     ],
  //   },
  //   // ... More top-level chat messages
  // ];

  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public commonActionTypes = CommonActionTypes

  public checkIcon: SVGIcon = checkIcon

  public xIcon: SVGIcon = xIcon

  public tagGroupSvgIcons = [
    {
      actionType: CommonActionTypes.NEW_NOTE,
      iconPath: 'assets/svg/icon-new-note-ui.svg',
    },
    {
      actionType: CommonActionTypes.EDIT,
      iconPath: 'assets/svg/icon-action-grid-pencil.svg',
    },
    {
      actionType: CommonActionTypes.DELETE,
      iconPath: 'assets/svg/icon-note-ui-delete.svg',
    },
  ]

  public chatMessages: ChatMessageNode[] = []

  @ViewChild(TreeViewComponent, { static: false })
  public treeView: TreeViewComponent

  // Show and hide the new note panel
  public showNewNotePanel = false

  // Show and hide the edit note panel
  public showEditNotePanel = false

  constructor() {
    // Generate sample chat messages
    this.chatMessages = this.generateChatMessages(0, 5, 3) // Start at 0, generate 5 levels deep, with 3 replies each
  }

  public hasChildren = (item: ChatMessageNode): boolean =>
    !!item.items && item.items.length > 0

  public getChildren = (
    node: ChatMessageNode
  ): Observable<ChatMessageNode[]> => {
    return of(node.items || [])
  }

  // Generate sample note messages
  private generateChatMessages(
    currentDepth: number,
    maxDepth: number,
    repliesCount: number,
    parentId = 0
  ): ChatMessageNode[] {
    if (currentDepth >= maxDepth) {
      return []
    }

    const messages: ChatMessageNode[] = []
    for (let i = 1; i <= repliesCount; i++) {
      const id = parentId * 10 + i // Generate a unique ID
      let items = []

      // Randomly decide if this node should have children
      if (Math.random() < 0.5) {
        items = this.generateChatMessages(
          currentDepth + 1,
          maxDepth,
          repliesCount,
          id
        )
      }

      messages.push({
        id: id,
        author: `Author ${id}`,
        timestamp: new Date(),
        text: `Message text for note will be shown here and you can add more  ${id}`,
        items: items.length > 0 ? items : undefined,
      })
    }

    return messages
  }

  // on button group action
  public onTagGroupAction(actionType: CommonActionTypes): void {
    // Handle the action here
    this.showNewNotePanel = true
    // show and hide the dropdown panel if action is edit
    if (actionType === CommonActionTypes.EDIT) {
      this.showEditNotePanel = true
    } else {
      this.showEditNotePanel = false
    }
  }

  // Scroll to a specific node for better user experience
  public handleScroll(event: any): void {
    // Finding the node index or using a unique identifier
    const lookupKey = event.index // This could be another identifier depending on your data structure
    const nodeElement = this.treeView.element.nativeElement.querySelector(
      `[data-treeindex="${lookupKey}"]`
    )
    if (nodeElement) {
      nodeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      })
    }
  }
}
