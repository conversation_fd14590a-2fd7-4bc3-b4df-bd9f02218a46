<div class="t-flex t-flex-row t-gap-4">
  <div class="t-flex-1 t-flex t-flex-col t-gap-1">
    <kendo-label
      class="t-font-semibold t-text-[#263238] t-mb-2"
      text="Include Options"></kendo-label>
    <ng-container *ngFor="let option of incOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input type="checkbox" kendoCheckBox [id]="option.id" />
        <kendo-label
          class="k-checkbox-label"
          [for]="option.id"
          [text]="option.label"></kendo-label>
      </div>
    </ng-container>

    <kendo-label
      class="t-font-semibold t-mt-4 t-text-[#263238] t-mb-2"
      text="Exclude Options"></kendo-label>
    <ng-container *ngFor="let option of excOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input type="checkbox" kendoCheckBox [id]="option.id" />
        <kendo-label
          class="k-checkbox-label"
          [for]="option.id"
          [text]="option.label"></kendo-label>
      </div>
    </ng-container>

    <kendo-label
      class="t-font-semibold t-mt-4 t-text-[#263238] t-mb-2"
      text="Review set Propagation Options"></kendo-label>
    <ng-container *ngFor="let option of propOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input type="checkbox" kendoCheckBox [id]="option.id" />
        <kendo-label
          class="k-checkbox-label"
          [for]="option.id"
          [text]="option.label"></kendo-label>
      </div>
    </ng-container>

    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="HTML Auto Job Options"></kendo-label>

      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input type="checkbox" kendoCheckBox #nativeDoc />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="nativeDoc"
          text="Convert the native documents to HTML"></kendo-label>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Image Auto Job Options"></kendo-label>

      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input type="checkbox" kendoCheckBox #nativeImage />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="nativeImage"
          text="Convert the native documents to Image"></kendo-label>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Review set Options"></kendo-label>

      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input type="checkbox" kendoCheckBox #multipleDoc />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="multipleDoc"
          text="When multiple documents are selected and applied tag, also mark all the document as reviewed"></kendo-label>
      </div>
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-flex t-basis-2/5 t-flex-col t-gap-3">
    <div class="t-w-full t-flex t-flex-col t-gap-1">
      <div class="t-flex t-items-center t-mb-1">
        <kendo-label
          class="t-font-semibold t-text-[#263238]"
          text="Tag Propagation Options"></kendo-label>
      </div>

      <div class="t-flex t-flex-col t-gap-2 t-w-full">
        <ng-container *ngFor="let option of tagOptions">
          <div class="t-flex t-items-center t-gap-1">
            <input type="checkbox" kendoCheckBox [id]="option.id" />
            <kendo-label
              class="k-checkbox-label"
              [for]="option.id"
              [text]="option.label"></kendo-label>
          </div>
        </ng-container>
      </div>
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Continuous Active Learning Options (CAL)"></kendo-label>

      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input
          type="checkbox"
          kendoCheckBox
          (change)="useCal = !useCal"
          #useCalElement />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="useCalElement"
          text="Use review set for CAL"></kendo-label>
      </div>

      <ng-container *ngIf="useCal">
        <kendo-label
          class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
          text="Options"></kendo-label>

        <div class="t-flex t-flex-col t-gap-3">
          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Prediction confidence threshold</p>

            <kendo-textbox [maxlength]="2" type="text" class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-textbox>
          </div>

          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Batch richness threshold</p>

            <kendo-textbox [maxlength]="2" type="text" class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-textbox>
          </div>

          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Review relevance threshold</p>

            <kendo-textbox [maxlength]="2" type="text" class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-textbox>
          </div>
        </div>

        <div class="t-flex t-gap-1 t-w-full t-mt-2">
          <kendo-checkbox #allowUsers class="t-block"></kendo-checkbox
          ><kendo-label
            class="k-checkbox-label t-items-center"
            [for]="allowUsers"
            text="Allow users to review even if batch richness & relevance threshold are met"></kendo-label>
        </div>

        <div class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSetting"
            text="Control Set Settings"></kendo-label>
          <kendo-dropdownlist
            class="t-w-[210px]"
            #controlSetting
            [(ngModel)]="selectedControlSet"
            [data]="controlSet"></kendo-dropdownlist>
        </div>

        <ng-container *ngIf="selectedControlSet === 'Percentage of population'">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Percentage</p>

              <kendo-textbox
                [maxlength]="2"
                value="90"
                type="text"
                class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-textbox>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="selectedControlSet === 'Normal distribution'">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Confidence level</p>

              <kendo-textbox [maxlength]="2" type="text" class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-textbox>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Confidence interval</p>

              <kendo-textbox [maxlength]="2" type="text" class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-textbox>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="selectedControlSet === 'Number of documents'">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Number</p>

              <kendo-numerictextbox
                format="#"
                [value]="1000"
                class="t-w-[90px]"
                [step]="1"></kendo-numerictextbox>
            </div>

            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>

              <div class="t-inline-block t-w-[90px] t-text-left">0</div>
            </div>
          </div>
        </ng-container>

        <div class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSettingFor"
            text="Control Set Fromat"></kendo-label>
          <kendo-dropdownlist
            class="t-w-[210px]"
            #controlSettingFor
            [(ngModel)]="selectedControlSetFormat"
            [data]="controlSetFormatData"></kendo-dropdownlist>
        </div>

        <div
          class="t-flex t-flex-col t-gap-2 t-mt-3"
          *ngIf="selectedControlSetFormat === 'Fixed'">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            text="Min & Max Training Set Primary Doc Count"></kendo-label>
          <div class="t-flex t-gap-3">
            <kendo-numerictextbox
              format="#"
              class="t-w-[90px]"
              [value]="5"
              [step]="1"></kendo-numerictextbox>

            <kendo-numerictextbox
              format="#"
              [value]="3000"
              class="t-w-[90px]"
              [step]="1"></kendo-numerictextbox>
          </div>
        </div>

        <div
          class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4"
          *ngIf="selectedControlSetFormat === 'Dynamic'">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSettingFor"
            text="Control Set Quota"></kendo-label>

          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>% from each Training Batch</p>

            <kendo-textbox
              [maxlength]="2"
              type="text"
              value="5"
              class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-textbox>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-mt-3">
            <kendo-label
              class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
              text="Min & Max Control Set Primary Doc Count"></kendo-label>
            <div class="t-flex t-gap-3">
              <kendo-numerictextbox
                format="#"
                class="t-w-[90px]"
                [value]="5"
                [step]="1"></kendo-numerictextbox>

              <kendo-numerictextbox
                format="#"
                [value]="0"
                class="t-w-[90px]"
                [step]="1"></kendo-numerictextbox>
            </div>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-mt-3">
            <kendo-label
              class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
              text="Min & Max Training Set Primary Doc Count"></kendo-label>
            <div class="t-flex t-gap-3">
              <kendo-numerictextbox
                format="#"
                class="t-w-[90px]"
                [value]="5"
                [step]="1"></kendo-numerictextbox>

              <kendo-numerictextbox
                format="#"
                [value]="3000"
                class="t-w-[90px]"
                [step]="1"></kendo-numerictextbox>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>
