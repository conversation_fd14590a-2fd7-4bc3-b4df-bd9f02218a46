export interface CommonEntries {
  userId: number
  userName: string
}
export interface LoginLogoutEntry extends CommonEntries {
  details: string
  ipAddress: string
  loginDate: string
  loginTime: string
  logoutDate: string
  logoutTime: string
}

export interface LockedUserEntry extends CommonEntries {
  fullName?: string
  globalRoleName?: string
  lockedDate?: string
  lockedTime?: string
}

export interface LoginLogoutReport {
  reportEntries: LoginLogoutEntry[] | LockedUserEntry[]
  totalHitCount: number
}
