<div class="t-flex t-flex-row t-gap-4">
  <div class="t-basis-1/2">
    <div class="t-flex t-flex-col t-gap-2">
      <div class="t-flex">
        <kendo-dropdownlist
          class="t-w-full"
          defaultItem="Tags"
          [data]="listItems"></kendo-dropdownlist
        ><span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>

      <div class="t-flex">
        <kendo-multiselecttree
          #multiselecttree
          [kendoMultiSelectTreeHierarchyBinding]="tagDropdowndata"
          [filterable]="true"
          [tagMapper]="tagMapper"
          kendoMultiSelectTreeExpandable
          [expandOnFilter]="filterExpandSettings"
          [checkAll]="true"
          childrenField="items"
          textField="text"
          valueField="text"
          class="!t-w-full"
          placeholder="Select Tags"
          [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
          <ng-template kendoSuffixTemplate>
            <button
              kendoButton
              [svgIcon]="downIcon"
              fillMode="link"
              class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
          </ng-template>
        </kendo-multiselecttree>

        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>

      <div class="t-flex t-flex-col t-gap-2">
        <div class="t-flex">
          Combining Operator
          <span class="t-text-error t-align-super t-ml-1"> *</span>
        </div>

        <div class="t-flex t-gap-4 t-items-center">
          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #Append
              value="Append"
              kendoRadioButton
              name="appendOrReplace"
              size="small"
              checked />
            <kendo-label [for]="Append" text="OR"></kendo-label>
          </div>

          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #replace
              value="replace"
              kendoRadioButton
              size="small"
              name="appendOrReplace" />
            <kendo-label [for]="replace" text="AND"></kendo-label>
          </div>
        </div>
      </div>
      <div class="t-flex">
        <kendo-multiselecttree
          #multiselecttree
          [kendoMultiSelectTreeHierarchyBinding]="tagDropdowndata"
          [filterable]="true"
          [tagMapper]="tagMapper"
          kendoMultiSelectTreeExpandable
          [expandOnFilter]="filterExpandSettings"
          [checkAll]="true"
          childrenField="items"
          textField="text"
          valueField="text"
          class="!t-w-full"
          placeholder="Tags Viewable"
          [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
          <ng-template kendoSuffixTemplate>
            <button
              kendoButton
              [svgIcon]="downIcon"
              fillMode="link"
              class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
          </ng-template>
        </kendo-multiselecttree>

        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-basis-2/3 t-flex t-flex-col t-gap-3">
    <div class="t-w-full t-flex t-flex-col t-gap-2">
      <div class="t-flex t-items-center t-mb-1">
        <kendo-checkbox
          #indentState
          class="t-block"
          (change)="isDisabled = !isDisabled"></kendo-checkbox
        ><kendo-label
          class="k-checkbox-label"
          [for]="indentState"
          text="Endorse Auto Collection"></kendo-label>
      </div>

      <div
        class="t-flex t-flex-col t-gap-2 t-w-[70%]"
        [ngClass]="{ 't-opacity-60': isDisabled }">
        <div class="t-flex t-items-center">
          <kendo-numerictextbox
            id="frequency"
            placeholder="Frequency"
            [disabled]="isDisabled"
            [format]="'n0'"
            [min]="0"
            [step]="1"
            suffix="Hrs"
            class="t-w-full t-rounded-r-none t-border-r-none">
          </kendo-numerictextbox>
          <kendo-textbox
            class="v-input-l-none t-w-[60px] t-m-0"
            [readonly]="true"
            [disabled]="isDisabled"
            placeholder="Hrs"></kendo-textbox>
        </div>

        <div class="t-flex t-items-center t-space-x-2 t-w-full">
          <kendo-datepicker
            id="datePicker"
            [disabled]="isDisabled"
            format="dd MM yyyy"
            class="t-w-full"
            formatPlaceholder="formatPattern">
          </kendo-datepicker>
        </div>

        <div class="t-flex t-items-center">
          <kendo-numerictextbox
            id="minThreshold"
            placeholder="Minimum Threshold"
            [disabled]="isDisabled"
            [format]="'n0'"
            [min]="0"
            [step]="1"
            suffix="Docs"
            class="t-w-full t-rounded-r-none t-border-r-none">
          </kendo-numerictextbox>
          <kendo-textbox
            class="v-input-l-none t-w-[60px] t-m-0"
            [readonly]="true"
            [disabled]="isDisabled"
            placeholder="Docs"></kendo-textbox>
        </div>
      </div>

      <div class="t-space-y-2">
        <div class="t-flex t-gap-2 t-items-center t-flex-wrap t-flex-row">
          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #collect
              value="Append"
              kendoRadioButton
              name="collect"
              size="small"
              checked />
            <kendo-label
              [for]="collect"
              text="Collected from matched source"></kendo-label>
          </div>

          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #collectMatch
              value="replace"
              kendoRadioButton
              size="small"
              name="appendOrReplace" />
            <kendo-label
              [for]="collectMatch"
              text="Collected from matched source and only if reviewed in batch"></kendo-label>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
