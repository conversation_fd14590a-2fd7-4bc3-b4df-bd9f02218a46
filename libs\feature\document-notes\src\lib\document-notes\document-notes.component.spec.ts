import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentNotesComponent } from './document-notes.component'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentsFacade } from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
describe('DocumentNotesComponent', () => {
  let component: DocumentNotesComponent
  let fixture: ComponentFixture<DocumentNotesComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentNotesComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        VenioNotificationService,
        NotificationService,
        DocumentsFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentNotesComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
