export enum PageControlActionType {
  FIRST_PAGE = 'FIRST_PAGE',
  NEXT_PAGE = 'NEXT_PAGE',
  PREV_PAGE = 'PREV_PAGE',
  LAST_PAGE = 'LAST_PAGE',
  FIRST_PAGE_TEXT = 'First Page',
  NEXT_PAGE_TEXT = 'Next Page',
  PREV_PAGE_TEXT = 'Previous Page',
  LAST_PAGE_TEXT = 'Last Page',
  FIRST_DOCUMENT_TEXT = 'First Document',
  NEXT_DOCUMENT_TEXT = 'Next Document',
  PREV_DOCUMENT_TEXT = 'Previous Document',
  LAST_DOCUMENT_TEXT = 'Last Document',
  VIEW_TEXT = 'View',
  SAVE_TEXT = 'Save',
  ADD_TEXT = 'Add',
  VIEW = 'VIEW',
  SAVE = 'SAVE',
  ADD = 'ADD',
  PARENT_CHILD_DOCUMENT = 'PARENT_CHILD_DOCUMENT',
  DUPLICATE_DOCUMENT = 'DUPLICATE_DOCUMENT',
  SIMILAR_DOCUMENT = 'SIMILAR_DOCUMENT',
  EMAIL_THREAD = 'EMAIL_THREAD',
  LINK_DOCUMENT = 'LINK_DOCUMENT',
  NEAR_DUPLICATE = 'NEAR_DUPLICATE',
}

export enum DocuemntDetailToolBarActionType {
  REVIEW_TAG = 'REVIEW_TAG',
  NOTES = 'NOTES',
  PRINT = 'PRINT',
  SHARE = 'SHARE',
}

export enum DocumentCodingControlActionType {
  MULTIVALUE = 'MULTIVALUE',
  MULTIDOCUMENT = 'MULTIDOCUMENT',
}

export enum DocumentActionTypeTitle {
  MOVE_PREVIOUS_DOCUMENT_TEXT = 'Save Tag & Move Previous',
  MOVE_NEXT_DOCUMENT_TEXT = 'Save Tag & Move Next',
  VIEW_TEXT = 'View',
  SAVE_TEXT = 'Save',
  ADD_TEXT = 'Add',
  REVIEW_MOVE_PREVIOUS_DOCUMENT_TEXT = 'Review, Save Tag & Move Previous',
  REVIEW_MOVE_NEXT_DOCUMENT_TEXT = 'Review, Save Tag & Move Next',
}

export enum DocumentTagTreeListToggle {
  EXPAND_ALL = 'Expand All',
  COLLAPSE_ALL = 'Collapse All',
}

export enum TagGroupActionType {
  USER_SYSTEM = 'User & System',
  RELEVANCE = 'AI Tag Group',
  PRIVILEDE = 'AI Privilege Tag Group',
  PII = 'AI PII Tag Group',
}

export enum TagGroupActionTitle {
  USER_SYSTEM = 'User & System Tags',
  RELEVANCE = 'Relevance Tags',
  PRIVILEDE = 'Privilege Tags',
  PII = 'PII Tags',
}
