<div class="t-p-5">
  <button
    kendoButton
    fillMode="clear"
    (click)="this.opened = true"
    size="none"
    class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-leading-none t-overflow-hidden t-group">
    <span
      class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>
    <kendo-svgicon
      class="t-text-[#6C6C6C] group-hover:t-text-[#1EBADC]"
      [icon]="icons.eyeIcon"></kendo-svgicon>
  </button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="650"
  [maxWidth]="1100"
  [height]="'90vh'"
  [minWidth]="250"
  [width]="'60%'">
  <!-- For dialog Component Instance Copy from here -->
  <kendo-dialog-titlebar (close)="close('yes')">
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <!-- Content Body -->

  <div class="t-flex t-flex-col t-gap-2 t-w-full">
    <kendo-textbox
      class="!t-w-[20rem] !t-mt-2"
      placeholder="Metadata"
      [clearButton]="true">
      <ng-template kendoTextBoxSuffixTemplate>
        <button class="t-flex t-items-center" (click)="close('yes')">
          <kendo-svgicon
            [icon]="icons.search"
            themeColor="info"
            size="large"
            class="t-text-[#9BD2A7] t-w-4 t-h-3.5 t-mx-4"></kendo-svgicon>
        </button>
      </ng-template>
    </kendo-textbox>

    <div class="t-flex t-flex-col t-w-full">
      <!-- Select All Checkbox -->
      <div class="t-mt-2 t-pb-1 t-bg-white">
        <input
          type="checkbox"
          kendoCheckBox
          [(ngModel)]="selectAllChecked"
          (change)="toggleSelectAll()" />
        <kendo-label
          class="k-checkbox-label t-text-sm t-font-medium"
          text="Select All"></kendo-label>
      </div>

      <div
        class="t-w-full v-hide-scrollbar"
        venioDynamicHeight
        [isKendoDialog]="true">
        <div class="t-mt-2" *ngFor="let checkbox of checkboxes">
          <input
            type="checkbox"
            #checked
            kendoCheckBox
            [(ngModel)]="checkbox.checked"
            (change)="updateSelectAllState()" />
          <kendo-label
            class="k-checkbox-label t-text-sm t-font-medium"
            [for]="checked"
            [text]="checkbox.label"></kendo-label>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        APPLY
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>

  <!-- For dialog Component Instance Copy till here -->
</kendo-dialog>
