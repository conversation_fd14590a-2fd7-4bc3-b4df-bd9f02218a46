@if(isReviewSetDocumentViewLoading()){ @for(n of [1,2,3,4]; track n){
<div class="t-flex t-flex-row t-gap-2 t-pl-6 t-mb-4">
  <kendo-skeleton shape="rectangle" width="17px" height="17px" />
  <kendo-skeleton shape="rectangle" width="200px" height="17px" />
</div>
} } @else { @if(!isReviewSetDocumentViewLoading() &&
reviewSetDocumentView().length === 0){
<div class="t-flex t-py-6 t-items-center">
  <div class="t-text-center">
    <div class="t-text-[#656565] t-text-sm t-font-normal">No records found</div>
  </div>
</div>
} @else{
<div class="t-mt-4 t-flex t-gap-2">
  <div class="t-w-full">
    <div
      class="t-flex t-items-center t-gap-2 t-justify-between t-w-64 t-pl-[26px]">
      <span class="t-inline-flex t-items-center t-gap-2">
        <input
          type="checkbox"
          #selectAll
          (change)="toggleSelectAll($event.target['checked'])"
          kendoCheckBox
          [indeterminate]="isIntermediateSelected()"
          [checked]="isAllSelected()" />
        <kendo-label
          class="k-radio-label"
          [for]="selectAll"
          text="Select All"></kendo-label>
      </span>
      @if(selectedBatchIds().length) {
      <span>
        <button
          [disabled]="isBatchesDeleting()"
          kendoButton
          #deleteBtn
          kendoTooltip
          title="Delete"
          fillMode="clear"
          size="medium"
          (click)="confirmBatchDeletion()">
          @if(isBatchesDeleting()){
          <kendo-skeleton shape="circle" height="0.8rem" width="0.8rem" />
          } @else {
          <span
            [parentElement]="deleteBtn.element"
            venioSvgLoader
            applyEffectsTo="fill"
            hoverColor="#ED7425"
            color="#979797"
            svgUrl="assets/svg/Icon-material-delete.svg"
            height="0.75rem"
            width="0.8rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
          }
        </button>
      </span>
      }
    </div>

    <kendo-treeview
      [(expandedKeys)]="expandedRowKeys"
      [isDisabled]="handleDisable"
      [nodes]="reviewSetDocumentView()"
      (checkedChange)="selectionChange()"
      (selectionChange)="selectionChange()"
      [(checkedKeys)]="selectedRowKeys"
      textField="batchName"
      kendoTreeViewFlatDataBinding
      idField="id"
      parentIdField="parentId"
      kendoTreeViewExpandable
      [kendoTreeViewCheckable]="{ checkOnClick: true }"
      class="v-hide-scrollbar"
      [navigable]="false"
      checkBy="id"
      [extraSpacing]="70"
      venioDynamicHeight>
      <ng-template kendoTreeViewNodeTemplate let-dataItem>
        <div
          class="t-flex t-items-center t-gap-2"
          [tabIndex]="0"
          aria-hidden="undefined">
          @if(!dataItem.fileName){
          <span class="t-font-semibold">
            {{ dataItem.batchName }}
          </span>
          <span>
            <div
              class="t-font-medium t-text-xs"
              [ngClass]="{
        't-text-success': dataItem.batchStatus === 'COMPLETED',
        't-text-error': dataItem.batchStatus === 'FAILED',
        't-text-[#FFBB12]': dataItem.batchStatus === 'IN PROGRESS',
        't-text-[#718792]': dataItem.batchStatus === 'NOT STARTED' || dataItem.batchStatus === 'NOT CHECKED OUT',
      }">
              {{ dataItem.batchStatus }}
            </div>
          </span>

          <span class="t-text-[#656565] t-text-xs t-font-normal">{{
            dataItem.reviewer
          }}</span>
          } @else {
          <span class="t-inline-block">
            {{ dataItem.fileName }}
            <span class="t-text-[#656565] t-text-xs t-font-normal">{{
              dataItem.fileType
            }}</span>
          </span>
          }
        </div>
      </ng-template>
    </kendo-treeview>
  </div>
</div>
} }
