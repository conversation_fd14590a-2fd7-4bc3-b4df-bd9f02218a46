import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentDuplicateOptionsComponent } from './document-duplicate-options.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { FormControl, FormGroup } from '@angular/forms'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentDuplicateOptionsComponent', () => {
  let component: DocumentDuplicateOptionsComponent
  let fixture: ComponentFixture<DocumentDuplicateOptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentDuplicateOptionsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentDuplicateOptionsComponent)
    component = fixture.componentInstance
    component.searchFormGroup = new FormGroup({
      searchDuplicateOption: new FormControl(),
    })
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
