import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiSearchDocumentSummaryComponent } from './ai-search-document-summary.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'

describe('AiSearchDocumentSummaryComponent', () => {
  let component: AiSearchDocumentSummaryComponent
  let fixture: ComponentFixture<AiSearchDocumentSummaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiSearchDocumentSummaryComponent],
      providers: [provideMockStore({}), provideHttpClient()],
    }).compileComponents()

    fixture = TestBed.createComponent(AiSearchDocumentSummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
