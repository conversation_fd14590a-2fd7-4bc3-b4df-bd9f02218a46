import {
  AxisTicks,
  Border,
  ChartArea,
  CategoryAxisLabels,
  GridLines,
  SeriesLabels,
  AxisLabelContentArgs,
  SeriesLabelsContentArgs,
  LegendLabels,
  LegendMarkers,
} from '@progress/kendo-angular-charts'

export const COMMON_CHART_OPTIONS: {
  chartArea: ChartArea
  gridLines: GridLines
  lineOptions: { visible: boolean }
  categoryLabels: CategoryAxisLabels
  valueAxisLabels: CategoryAxisLabels
  seriesLabels: SeriesLabels
  border: Border
  majorTicks: AxisTicks
  minorTicks: AxisTicks
} = {
  chartArea: {
    background: 'transparent',
    border: { width: 0 },
  },
  gridLines: { visible: false },
  lineOptions: { visible: true },
  categoryLabels: { visible: true },
  valueAxisLabels: {
    visible: false,
    step: 1,
    format: '{0}',
    color: '#6b7280',
  },
  seriesLabels: {
    visible: true,
    position: 'left',
    format: '{0}',
    background: 'transparent',
    font: 'bold 8px Arial, sans-serif',
    color: '#FFFFFF',
    margin: -15,
  },
  border: {
    width: 0,
    color: '#fff',
    dashType: 'solid',
  },
  majorTicks: { visible: false },
  minorTicks: { visible: false },
}

export const CATEGORY_AXIS_LABELS: CategoryAxisLabels = {
  visible: true,
  rotation: 0,
  color: '#000000',
  font: 'bold 10px Arial',
  content: (e: AxisLabelContentArgs) =>
    e.value >= 1000 ? `${e.value / 1000}K` : `${e.value}`,
}

export const VALUE_AXIS_LABELS: CategoryAxisLabels = {
  format: 'n0',
  visible: true,
  color: '#000000',
  font: 'bold 10px Arial',
  content: (e: AxisLabelContentArgs) =>
    e.value >= 1000 ? `${e.value / 1000}K` : `${e.value}`,
}

export const SUMMARY_CHART_OPTIONS: {
  labels: SeriesLabels
  legendLabels: LegendLabels
  legendMarkers: LegendMarkers
} = {
  labels: {
    visible: true,
    content: (args: SeriesLabelsContentArgs) => `${args.dataItem.count}`,
    position: 'center',
    background: 'transparent',
  },

  legendLabels: {
    color: '#000',
    font: '14px Arial',
  },

  legendMarkers: {
    type: 'square',
  },
}
