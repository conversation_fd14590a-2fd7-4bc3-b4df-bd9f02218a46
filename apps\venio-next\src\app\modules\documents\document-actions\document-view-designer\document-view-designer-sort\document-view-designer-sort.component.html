<kendo-sortable
  (dragOver)="onDragOver($event)"
  (navigate)="onNavigate($event)"
  [navigable]="true"
  (dragStart)="onDragStart($event)"
  [data]="staticRows"
  [animation]="true">
  <ng-template let-item="item">
    <div class="t-flex t-flex-row t-gap-2 t-mb-2" [formGroup]="item.formGroup">
      <button
        (mousedown)="setDragInitiated(true)"
        (touchstart)="setDragInitiated(true)"
        data-qa="sort"
        kendoButton
        fillMode="flat"
        size="none"
        class="t-p-1 t-cursor-move">
        <span
          venioSvgLoader
          svgUrl="assets/svg/icon-drag-drop.svg"
          height="1.5rem"
          width="1rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </button>
      <div class="t-flex t-flex-row t-grow t-gap-2">
        <kendo-combobox
          formControlName="displayFieldName"
          data-qa="field-name"
          class="t-basis-2/3"
          [clearButton]="true"
          placeholder="Search..."
          [loading]="isPermittedFieldLoading()"
          [filterable]="true"
          [virtual]="{ itemHeight: 28 }"
          [kendoDropDownFilter]="{
            caseSensitive: false,
            operator: 'contains'
          }"
          [data]="item.availableFields()"
          [valuePrimitive]="true"
          textField="displayFieldName"
          valueField="displayFieldName">
        </kendo-combobox>
        <kendo-dropdownlist
          formControlName="fieldSortType"
          data-qa="sort-type"
          class="t-basis-1/3"
          [data]="staticSortOrderData"
          [valuePrimitive]="true"
          textField="text"
          valueField="value">
        </kendo-dropdownlist>
      </div>
    </div>
  </ng-template>
</kendo-sortable>
