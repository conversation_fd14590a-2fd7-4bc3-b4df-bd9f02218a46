<kendo-card
  class="t-w-full t-shadow-xl t-rounded-md t-overflow-hidden t-max-h-24 t-mt-3"
  width="100%">
  <kendo-card-header class="t-pb-2 t-flex t-items-center">
    <div class="t-flex t-justify-between t-items-center t-w-full">
      <h4 class="t-font-bold t-text-sm t-tracking-widest">
        {{ breadcrumb?.groupStackType }}
      </h4>
      <div class="t-flex t-items-center t-gap-1.5">
        <input
          type="checkbox"
          #notification
          kendoCheckBox
          [formControl]="conditionCheckboxControl" />
        <button
          (click)="deleteBreadcrumb()"
          kendoButton
          [svgIcon]="xCircleIcon"
          [size]="'large'"
          fillMode="clear"
          class="t-p-0 t-text-[#ED7425]"></button>
      </div>
    </div>
  </kendo-card-header>

  <kendo-card-body class="t-pt-2">
    <div
      venioAutoScrollOrFocus
      [shouldFocusOrScroll]="syntax"
      #syntaxContainerElement
      kendoTooltip
      [showAfter]="800"
      [tooltipWidth]="syntaxContainerElement.clientWidth"
      class="t-flex t-flex-grow t-text-[#979797] t-tracking-wider t-relative t-w-full t-h-full"
      venioTextTruncate
      (isTextTruncated)="isTextTruncated($event)"
      [lineCount]="2"
      [title]="hasTruncatedText() ? syntax : ''">
      {{ syntax }}
    </div>
  </kendo-card-body>
</kendo-card>
