/**
 * Payload type to filter out the needed data when there are lot of events are triggered.
 */
export enum IframePayloadType {
  /**
   * Indicates the payload contains token
   */
  Token = 'token',

  /**
   * Indicates the payload contains url query parameters
   */
  QueryParameters = 'query_parameters',

  /**
   * Indicates the payload contains full or partial url
   */
  Url = 'url',

  /**
   * Indicates it is a raw data that contains all from above or unknown
   */
  AdditionalData = 'additional_data',
  // could be more.
}
