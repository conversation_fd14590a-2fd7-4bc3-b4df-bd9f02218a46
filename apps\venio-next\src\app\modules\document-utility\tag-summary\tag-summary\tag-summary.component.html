<div class="content t-p-5 t-pt-0 !t-min-w-80">
  <div class="t-flex t-flex-col t-mt-2">
    <div
      class="t-flex t-flex-col t-gap-2 t-max-h-36 t-overflow-y-auto"
      *ngIf="tagSummary?.folders?.length">
      <ng-container *ngFor="let folder of tagSummary?.folders; let i = index">
        <div class="t-flex t-gap-2">
          <div class="t-w-40 t-font-bold t-mr-[1.1rem]">
            {{ i === 0 ? 'Folders' : '' }}
          </div>
          <div class="t-flex-1 t-basis-2/3" title="{{ folder }}">
            {{ folder }}
          </div>
        </div>
      </ng-container>
    </div>
    <div
      class="t-flex t-flex-col t-mt-2 t-gap-2 t-max-h-36 t-overflow-y-auto"
      *ngIf="tagSummary?.tags?.length">
      <ng-container *ngFor="let tag of tagSummary?.tags; let i = index">
        <div class="t-flex t-gap-2">
          <div class="t-w-40 t-font-bold t-mr-4" title="{{ tag.tagGroupName }}">
            {{ tag.tagGroupName }}
          </div>
          <div class="t-flex-1 t-basis-2/3" title="{{ tag.tagName }}">
            {{ tag.tagName }}
          </div>
        </div>
      </ng-container>
    </div>

    <!-- <div
      class="t-flex t-mt-2"
      *ngIf="tagSummary?.notes">
      <div class="t-flex-1 t-basis-3/4">{{ tagSummary?.notes }}</div>
    </div>
  </div> -->
  </div>
</div>
