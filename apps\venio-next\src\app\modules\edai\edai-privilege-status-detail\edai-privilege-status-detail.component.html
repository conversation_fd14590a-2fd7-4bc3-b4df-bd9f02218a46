<kendo-grid
  [data]="statusDetailData()"
  [resizable]="true"
  class="t-w-full"
  [height]="350">
  <kendo-grid-column
    [width]="100"
    headerClass="t-text-primary"
    field="privilegedDocCount"
    title="Privilege" />
  <ng-container *ngFor="let column of dynamicPrivilegeColumns()">
    <kendo-grid-column
      [width]="120"
      headerClass="t-text-primary t-capitalize"
      [field]="column"
      [title]="column">
      <ng-template kendoGridHeaderTemplate>
        <span
          class="d-flex t-relative t-overflow-hidden t-text-ellipsis"
          [title]="column"
          kendoTooltip
          tooltipClass="t-capitalize"
          [showAfter]="500"
          >{{ column }}</span
        >
      </ng-template>
    </kendo-grid-column>
  </ng-container>
  <kendo-grid-column
    [width]="140"
    headerClass="t-text-primary"
    field="potentiallyPrivilegedDocCount"
    title="Potential Privilege" />
  <kendo-grid-column
    [width]="120"
    headerClass="t-text-primary"
    field="nonPrivilegedDocCount"
    title="Non Privilege" />
  <kendo-grid-column
    [width]="100"
    headerClass="t-text-primary"
    field="techIssueDocCount"
    title="Tech Issue" />
</kendo-grid>
