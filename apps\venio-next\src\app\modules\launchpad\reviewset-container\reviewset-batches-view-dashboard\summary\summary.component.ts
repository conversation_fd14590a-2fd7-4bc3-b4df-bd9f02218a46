import { Component, computed, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import {
  DAY_ORDINAL_SUFFIX,
  ROUND_TO_TWO_DECIMALS,
} from '@venio/shared/models/constants'
import dayjs from 'dayjs'
import { filter } from 'rxjs'
import { trigger, transition, style, animate } from '@angular/animations'
import { SummaryChartComponent } from './summary-chart/summary-chart.component'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-summary',
  standalone: true,
  imports: [
    CommonModule,
    SummaryChartComponent,
    SvgLoaderDirective,
    SkeletonComponent,
  ],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss',
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('100ms', style({ opacity: 1 })),
      ]),
      transition(':leave', [animate('100ms', style({ opacity: 0 }))]),
      // Animation for value changes
      transition('void => *', [
        // From void state to any other state (initial appearance)
        style({ opacity: 0 }),
        animate('100ms', style({ opacity: 1 })),
      ]),
      transition('* => void', [
        // From any state to void state (when ngIf becomes false)
        animate('100ms', style({ opacity: 0 })),
      ]),
      transition('* => *', [
        // From any state to any other state (value changes)
        animate('100ms', style({ opacity: 1 })), // Ensure opacity is 1 in case of previous fade-out
      ]),
    ]),
  ],
})
export class SummaryComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  /** Signal for review set summary */
  public readonly reviewSetSummaryDetail = toSignal(
    this.reviewSetFacade.selectReviewSetBatchSummary$.pipe(
      filter((e) => typeof e !== 'undefined')
    )
  )

  /** Signal for review set summary rate */
  private readonly reviewSetSummaryRateDetail = toSignal(
    this.reviewSetFacade.selectReviewSetBatchSummaryRate$.pipe(
      filter((e) => typeof e !== 'undefined')
    )
  )

  public readonly reviewerStats = computed(() => {
    const summary = this.reviewSetSummaryDetail() || {}

    return {
      activeReviewerCount: summary.activeReviewerCount || 0,
      reviewerCount: summary.reviewerCount || 0,
    }
  })

  public readonly reviewStats = computed(() => {
    const summary = this.reviewSetSummaryRateDetail() || {}

    return {
      reviewedPerDay: ROUND_TO_TWO_DECIMALS(
        summary.documentsReviewedPerDay || 0
      ),
      reviewedPerHour: ROUND_TO_TWO_DECIMALS(
        summary.documentsReviewedPerHour || 0
      ),
      reviewedPerReviewer: ROUND_TO_TWO_DECIMALS(
        summary.documentsReviewedPerReviewerPerHour?.[0]
          ?.reviewedDocumentCount || 0
      ),
    }
  })

  public readonly completionDate = computed(() => {
    const completionDate = dayjs(
      this.reviewSetSummaryRateDetail()?.projectedCompletionDate || ''
    )
    if (!completionDate.isValid()) return 'NA'
    const day = completionDate.date()
    const ordinalSuffix = DAY_ORDINAL_SUFFIX(day)
    return completionDate.format(`MMM D[${ordinalSuffix}] YYYY`)
  })
}
