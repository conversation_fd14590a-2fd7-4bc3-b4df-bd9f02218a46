import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseLandingReviewSetComponent } from './case-landing-review-set.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseLandingReviewSetComponent', () => {
  let component: CaseLandingReviewSetComponent
  let fixture: ComponentFixture<CaseLandingReviewSetComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseLandingReviewSetComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseLandingReviewSetComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
