import { CommonModule } from '@angular/common'
import {
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  inject,
  Input,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ProductionStatusGraphComponent } from './production-status-graph/production-status-graph.component'
import { ProductionStatusShareComponent } from './production-status-share/production-status-share.component'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  ProductionFacade,
  ProductionShareInvitationService,
} from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { filter, interval, Subject, Subscription, take, takeUntil } from 'rxjs'
import { DownloadProductionComponent } from './download-production/download-production.component'
import {
  DownloadProgressInfoModel,
  ProductionStatusDetailModel,
} from '@venio/data-access/review'
import { DeleteProductionComponent } from './delete-production/delete-production.component'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { IconsModule } from '@progress/kendo-angular-icons'
import { chevronLeftIcon, userOutlineIcon } from '@progress/kendo-svg-icons'
import { ProductionShareSkeletonComponent } from './production-share-skeleton/production-share-skeleton.component'
import { Align, PopupModule } from '@progress/kendo-angular-popup'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import _ from 'lodash'

@Component({
  selector: 'venio-production-status',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    DropDownsModule,
    IndicatorsModule,
    SvgLoaderDirective,
    ProductionStatusShareComponent,
    ProductionStatusGraphComponent,
    DownloadProductionComponent,
    DeleteProductionComponent,
    ReactiveFormsModule,
    FormsModule,
    IconsModule,
    ProductionShareSkeletonComponent,
    PopupModule,
  ],
  templateUrl: './production-status.component.html',
  styleUrl: './production-status.component.scss',
})
export class ProductionStatusComponent implements OnDestroy, OnInit {
  private _selectedProjectIdData = signal<number | undefined>(undefined)

  @Input() public set selectedProjectIdData(value: number) {
    this._selectedProjectIdData.set(_.cloneDeep(value))
  }

  private readonly dialogRef = inject(DialogRef)

  private readonly productionFacade = inject(ProductionFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly cdr = inject(ChangeDetectorRef)

  private productionShareService = inject(ProductionShareInvitationService)

  private notificationService = inject(NotificationService)

  public anchorAlign: Align = { horizontal: 'left', vertical: 'center' }

  public popupAlign: Align = { horizontal: 'right', vertical: 'center' }

  public readonly isProductionStatusLoading = toSignal(
    this.productionFacade.selectProductionStatusLoading$,
    { initialValue: true }
  )

  private readonly productionStatusList = toSignal(
    this.productionFacade.selectProductionStatusSuccessResponse$
  )

  private readonly loadedProductionStatusList = computed<
    ProductionStatusDetailModel[]
  >(() => this.productionStatusList() || [])

  public icons = {
    UserOutlineIcon: userOutlineIcon,
    chevronLeftIcon: chevronLeftIcon,
  }

  public selectedDeleteType = signal<string>('')

  public dialogTitle = 'Production Status'

  public ifProductionShare = false

  public defaultproductionShareFilterItem: { text: string; value: number } = {
    text: 'All',
    value: -1,
  }

  public productionShareFilterData: Array<{ text: string; value: number }> = [
    { text: 'Not Started', value: 0 },
    { text: 'Inprogress', value: 1 },
    { text: 'Completed', value: 2 },
  ]

  public status = 'INPROGRESS'

  public selectedProductionStatus: number

  public selectedProductionStatusData: ProductionStatusDetailModel

  private intervalSubscription: Subscription

  public isPolling = false

  public currentButtonId: string

  public count = 0

  private readonly toDestroy$ = new Subject<void>()

  public getAnchorElement(id: string): HTMLElement | null {
    return document.getElementById(id)
  }

  public svgIconForGridControls = [
    {
      actionType: 'REPRODUCE',
      iconPath: 'assets/svg/icon-measurement-setting.svg',
      hoverColor: '#1EBADC',
      applyFill: 'fill' as any,
      isVisible: (): boolean => true,
    },
    {
      actionType: 'Download',
      iconPath: 'assets/svg/icon-production-setting-download.svg',
      hoverColor: '#FFBB10',
      applyFill: 'stroke' as any,
      isVisible: (): boolean => true,
    },
    {
      actionType: 'Share',
      iconPath: 'assets/svg/icon-review-share.svg',
      hoverColor: '#2F3080',
      applyFill: 'both' as any,
      isVisible: (): boolean => true,
    },
    {
      actionType: 'REVIEW',
      iconPath: 'assets/svg/icon-rate-review-note.svg',
      hoverColor: '#9AD3A6',
      applyFill: 'fill' as any,
      isVisible: (): boolean => true,
    },
    {
      actionType: 'ERROR_LOGS',
      iconPath: 'assets/svg/icon-error.svg',
      hoverColor: '#ED7425',
      applyFill: 'fill' as any,
      isVisible: (data: ProductionStatusDetailModel): boolean => {
        return (
          data?.status === 'Completed' &&
          data?.exportedFromVoD === 'Yes' &&
          data?.isRelativityImportEnabled
        )
      },
    },
    {
      actionType: 'Delete',
      iconPath: 'assets/svg/icon-note-ui-delete.svg',
      hoverColor: '#ED7425',
      applyFill: 'fill' as any,
      isVisible: (): boolean => true,
    },
  ]

  public badgeClassMap: { [key: string]: string } = {
    FAILED: 't-bg-[#E55353]', // Red
    COMPLETED: 't-bg-[#FEB43C]', // Green
    'IN PROGRESS': 't-bg-[#FEB43C]',
    INPROGRESS: 't-bg-[#FEB43C]',
    'NOT STARTED': 't-bg-[#718792] !t-text-[#FFFFFF]', // Gray
  }

  public get productionShareForm(): FormGroup {
    return this.productionShareService.productionShareForm
  }

  public allProductionStatus: ProductionStatusDetailModel[]

  public downloadSourceUrl: string

  public isDownloadProgressUiShown: boolean

  public isDeletedProductionVisible: boolean

  public selectedProjectId: number

  public selectedExportId: number

  constructor() {
    effect(() => {
      if (this.isProductionStatusLoading()) return

      this.loadProductionStatusData()
    })
  }

  public ngOnInit(): void {
    this.#startPolling()
  }

  private loadProductionStatusData(): void {
    const allProductionStatusData = this.loadedProductionStatusList()
    this.allProductionStatus = this.#generateChartData(allProductionStatusData)
    this.cdr.detectChanges()
  }

  public close(): void {
    this.dialogRef.close()
  }

  public browseActionClicked(
    actionType: string,
    dataItem: ProductionStatusDetailModel
  ): void {
    const { projectId, exportId, isRelativityImportEnabled } = dataItem
    this.currentButtonId = `${actionType}_${exportId}`

    const selectedCase = dataItem
    this.selectedProductionStatusData = _.cloneDeep(dataItem)
    this.selectedProjectId = projectId
    this.selectedExportId = exportId
    const payload = {
      actionType,
      selectedCase: _.cloneDeep(selectedCase),
      projectId,
      exportId,
      isRelativityImportEnabled,
      isCaseActionClick: true,
    }
    switch (actionType) {
      case 'Download':
        this.#onDownloadClicked(projectId, exportId)
        break
      case 'Share':
        this.ifProductionShare = true
        break
      case 'Delete':
        this.#openProductionDeleteDialog()
        break
      case 'ERROR_LOGS':
        this.#downloadErrorLogs()
        break
      default:
        this.#notifyParentApp(payload)
        break
    }
  }

  #downloadErrorLogs(): void {
    this.productionFacade.downloadRelativityErrorLog(
      this.selectedProjectId,
      this.selectedExportId,
      true
    )

    this.productionFacade.selectRelativirtErrorLogSuccess$
      .pipe(
        filter((result): boolean => !!result),
        take(1)
      )
      .subscribe((res): void => {
        const data = res?.data
        const byteCharacters = atob(data?.buffer || '')

        const byteArrays = []
        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
          const slice = byteCharacters.slice(offset, offset + 512)
          const byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          const byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        const blob = new Blob(byteArrays, { type: data?.mimeType || '' })
        const url = window.URL.createObjectURL(blob)

        const element: any = document.createElement('a')
        document.body.appendChild(element)
        element.style = 'display: none'
        element.href = url
        element.download = data?.fileName || ''
        element.click()
        document.body.removeChild(element)
        window.URL.revokeObjectURL(url)

        this.selectedProductionStatusData = _.cloneDeep(
          this.selectedProductionStatusData
        )
        this.selectedProductionStatusData.isDownloadProgress = false
      })

    this.productionFacade.selectRelativirtErrorLogFailure$
      .pipe(
        filter((result): boolean => !!result),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res): void => {
        this.selectedProductionStatusData = _.cloneDeep(
          this.selectedProductionStatusData
        )
        this.selectedProductionStatusData.isDownloadProgress = false
        this.showMessage(res?.message, { style: 'error' })
      })
  }

  #generateChartData(
    productionstatusData: ProductionStatusDetailModel[]
  ): ProductionStatusDetailModel[] {
    const baseChartTypes = ['image', 'fulltext', 'native', 'loadFile']
    const relativityChartTypes = [
      'relativityImage',
      'relativityFulltext',
      'relativityNative',
    ]

    const statusTypes = ['Failed', 'Completed', 'InProgress', 'NotStarted']

    const colors = {
      Completed: '#9BD2A7',
      NotStarted: '#718792',
      InProgress: '#FFB300',
      Failed: '#ED7425',
    }

    return productionstatusData.map((data) => {
      const item = _.cloneDeep(data)

      const chartTypes = item.isRelativityImportEnabled
        ? [...baseChartTypes, ...relativityChartTypes]
        : baseChartTypes

      const precomputedChartKeys = chartTypes.map((chartType) => ({
        type: chartType,
        progressKey: `${chartType}Progress`,
        startedOnKey: `${chartType}StartedOn`,
        etaKey: `${chartType}ETA`,
        statusKeys: statusTypes.map((status) => `${chartType}${status}`),
      }))

      if (item.status === 'Completed') {
        return item
      }

      const charts = {}
      const imageTypes = ['image', 'relativityImage']

      for (const chart of precomputedChartKeys) {
        const chartData = chart.statusKeys.map((key, index) => ({
          value: item[key] || 0,
          color: colors[statusTypes[index]],
        }))

        charts[`chart${chart.type}`] = {
          progress: item[chart.progressKey] || 0,
          startedOn: item[chart.startedOnKey] || null,
          eta: item[chart.etaKey] || null,
          totalDocCount: imageTypes.some((type) => chart.type.includes(type))
            ? item.totalPageCount || 0
            : item.totalDocumentCount || 0,
          chartData,
        }
      }

      return { ...item, ...charts }
    })
  }

  public fetchProductionStatus(): void {
    const productionStatus =
      this.selectedProductionStatus ??
      this.defaultproductionShareFilterItem.value
    const projectId = this.selectedProjectId ?? this._selectedProjectIdData()
    this.productionFacade.fetchProductionStatus(projectId, productionStatus, -1)
  }

  public onProductionStatusFilterChange(value: number): void {
    this.selectedProductionStatus = value

    this.fetchProductionStatus()
  }

  public redirectToProduction(actionType: string): void {
    const payload = {
      actionType,
      selectedCase: { projectId: this._selectedProjectIdData() },
      isCaseActionClick: true,
    }
    this.#notifyParentApp(payload)
  }

  #onDownloadClicked(projectId: number, exportId: number): void {
    this.downloadSourceUrl = `production/project/${projectId}/export/${exportId}/download`
    this.isDownloadProgressUiShown = true
  }

  public onDownloadCancel(): void {
    this.isDownloadProgressUiShown = false
  }

  public onDownloadProgressChanged(
    progressEvent: DownloadProgressInfoModel
  ): void {
    if (!progressEvent) return

    const { isDownloadCompleted } = progressEvent
    if (!isDownloadCompleted) return

    this.isDownloadProgressUiShown = false
    ++this.count
    if (
      isDownloadCompleted &&
      this.count === 1 &&
      !this.isDownloadProgressUiShown
    ) {
      this.showMessage('File Downloaded Successfully', { style: 'success' })
      this.fetchProductionStatus()
    }
  }

  #notifyParentApp(content: unknown): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content,
      } as MessageContent,
    })
  }

  private showMessage(content: string, type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #openProductionDeleteDialog(): void {
    this.isDeletedProductionVisible = true
  }

  #startPolling(): void {
    if (!this.isPolling) {
      this.isPolling = true
      this.intervalSubscription = interval(120000).subscribe(() => {
        this.fetchProductionStatus()
      })
    }
  }

  #stopPolling(): void {
    if (this.intervalSubscription) {
      this.intervalSubscription.unsubscribe()
      this.isPolling = false
    }
  }

  public ngOnDestroy(): void {
    this.#stopPolling()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
