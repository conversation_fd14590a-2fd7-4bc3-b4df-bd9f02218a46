import {
  Compo<PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { Subject, filter, takeUntil } from 'rxjs'
import { DocumentCodingControlActionType } from '@venio/shared/models/constants'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'

@Component({
  selector: 'venio-multivalue-coding-field-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './multivalue-coding-field-container.component.html',
  styleUrl: './multivalue-coding-field-container.component.scss',
})
export class MultivalueCodingFieldContainerComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  private EventType: DocumentCodingControlActionType

  @ViewChild('dialogContent', { static: true })
  private readonly dialogContent: TemplateRef<any>

  constructor(
    private documentCodingFacade: DocumentCodingFacade,
    private dialogService: DialogService,
    private vcr: ViewContainerRef
  ) {}

  public ngOnInit(): void {
    this.#selectedPageActionEvent()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetPageActionEventState()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetPageActionEventState(): void {
    this.documentCodingFacade.resetDocumentCodingState('codingActionEventType')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleMultivalueCodingFieldDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetPageActionEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      appendTo: this.vcr,
    })
    const dialogInstance = this.dialogRef.content.instance
    dialogInstance.EventType = this.EventType
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import(
      '../multivalue-coding-field-dialog/multivalue-coding-field-dialog.component'
    ).then((d) => {
      // launch the dialog
      this.#launchDialogContent(d.MultivalueCodingFieldDialogComponent)

      // once the dialogRef instance is created
      this.#handleMultivalueCodingFieldDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedPageActionEvent(): void {
    this.documentCodingFacade.selectCodingActionEvent$
      .pipe(
        filter(
          (event) =>
            event === DocumentCodingControlActionType.MULTIDOCUMENT ||
            event === DocumentCodingControlActionType.MULTIVALUE
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((event) => {
        // launch the dialog
        this.EventType = event
        this.#handleLazyLoadedDialog()
      })
  }
}
