import { ComponentFixture, TestBed } from '@angular/core/testing'
import { VenioTreelistComponent } from './venio-treelist.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import {
  ConvertDocumentFacade,
  OcrImageFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'

describe('VenioTreelistComponent', () => {
  let component: VenioTreelistComponent
  let fixture: ComponentFixture<VenioTreelistComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [VenioTreelistComponent],
      imports: [
        NoopAnimationsModule,
        CommonModule,
        FormsModule,
        TreeListModule,
      ],
      providers: [ConvertDocumentFacade, OcrImageFacade, provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(VenioTreelistComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
