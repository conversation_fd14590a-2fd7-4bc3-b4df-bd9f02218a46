import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentTallyDialogComponent } from './document-tally-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  FieldFacade,
  ReviewSetStateService,
  SearchFacade,
  StartupsFacade,
  TallyFacade,
  ViewFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { TallyDataTransformService } from '@venio/util/utilities'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentTallyDialogComponent', () => {
  let component: DocumentTallyDialogComponent
  let fixture: ComponentFixture<DocumentTallyDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentTallyDialogComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        FieldFacade,
        TallyFacade,
        SearchFacade,
        StartupsFacade,
        ViewFacade,
        {
          provide: ReviewSetStateService,
          useValue: {
            transformData: jest.fn(),
          },
        },
        {
          provide: TallyDataTransformService,
          useValue: {
            transformData: jest.fn(),
          },
        },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentTallyDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
