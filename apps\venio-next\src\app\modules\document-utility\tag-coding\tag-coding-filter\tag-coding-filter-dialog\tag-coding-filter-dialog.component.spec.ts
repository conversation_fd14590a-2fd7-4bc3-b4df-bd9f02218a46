import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingFilterDialogComponent } from './tag-coding-filter-dialog.component'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
} from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import { DialogModule } from '@progress/kendo-angular-dialog'

describe('TagCodingFilterDialogComponent', () => {
  let component: TagCodingFilterDialogComponent
  let fixture: ComponentFixture<TagCodingFilterDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagCodingFilterDialogComponent, DialogModule],
      providers: [
        DocumentTagFacade,
        DocumentCodingFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagCodingFilterDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
