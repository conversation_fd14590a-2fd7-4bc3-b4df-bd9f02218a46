<kendo-grid
  [kendoGridBinding]="users()"
  venioDynamicHeight
  [resizable]="true"
  [kendoGridSelectBy]="userSelectionKey"
  [selectedKeys]="selectedUsers()"
  (selectedKeysChange)="onSelectedKeysChange($event)"
  (selectionChange)="onSelectionChange($event)"
  [ngClass]="{
    'k-disabled t-opacity-50': isGridDisabled(),
    't-opacity-100': !isGridDisabled()
  }"
  [selectable]="true"
  [height]="140"
  scrollable="virtual">
  <kendo-grid-column headerClass="t-text-primary" title="#" [width]="20">
    <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
      {{ rowIndex + 1 }}
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-checkbox-column
    [showSelectAll]="true"
    [width]="30"></kendo-grid-checkbox-column>
  @for (column of columns(); track column.field + column.title){
  <kendo-grid-column
    [resizable]="true"
    [field]="column.field"
    [title]="column.title"
    [width]="column.width"
    headerClass="t-text-primary"
    [minResizableWidth]="50">
  </kendo-grid-column>
  }
  <ng-template kendoGridNoRecordsTemplate>
    No {{ gridType() }} users.
  </ng-template>
</kendo-grid>
