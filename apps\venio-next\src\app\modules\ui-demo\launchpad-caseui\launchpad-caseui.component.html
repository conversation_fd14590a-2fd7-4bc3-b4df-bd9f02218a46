<div class="t-p-5">
  <div class="t-text-lg t-pb-3 t-pt-3">Launchpad UI</div>
</div>

<div class="v-grid-wrap t-block t-h-auto t-relative">
  <kendo-grid
    [kendoGridBinding]="gridView"
    kendoGridSelectBy="id"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    filterable="menu"
    [skip]="skip">
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total">
      <kendo-buttongroup>
        <button
          #browseEl
          class="!t-p-1.5 t-h-fit t-border-[#263238] !t-border-r-0 t-leading-[0.8]"
          kendoButton
          size="none"
          fillMode="outline"
          title="Browse">
          <span
            [parentElement]="browseEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            svgUrl="assets/svg/Funnel-normal.svg"
            height="1rem"
            width="1rem"></span>
        </button>
        <button
          #ExportEl
          class="!t-p-1.5 t-h-fit t-border-[#263238] t-leading-[0.8]"
          kendoButton
          size="none"
          fillMode="outline"
          title="Browse">
          <span
            [parentElement]="ExportEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            svgUrl="assets/svg/Icon-material-import-export.svg"
            height="1rem"
            width="1rem"></span>
        </button>
      </kendo-buttongroup>

      <kendo-grid-spacer></kendo-grid-spacer>
      <kendo-label class="k-form">
        <kendo-numerictextbox
          class="!t-w-[3rem]"
          format="number"
          [step]="1"
          [value]="currentPage"
          [min]="1"
          [max]="totalPages"
          (valueChange)="onPageSizeChange($event)"
          (blur)="onBlur($event)"
          [spinners]="false"
          [selectOnFocus]="true"></kendo-numerictextbox>
      </kendo-label>
      <span> - {{ pageSize }} Of {{ gridView.length }} </span>
      <kendo-dropdownlist
        class="!t-w-[4rem] !t-border-[#707070]"
        [data]="sizes"
        [value]="pageSize"
        (valueChange)="sliderChange($event)"></kendo-dropdownlist>
      per page
      <div class="t-flex t-gap-2">
        <button
          kendoButton
          #parentEl
          *ngFor="let icon of svgIconForPageControls"
          class="!t-p-[0.3rem]"
          (click)="browseActionClicked(icon.actionType)"
          fillMode="outline"
          size="none">
          <span
            [parentElement]="parentEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            [svgUrl]="icon.iconPath"
            height="0.8rem"
            width="1rem"></span>
        </button>
      </div>
    </ng-template>

    <ng-template kendoGridToolbarTemplate>
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[25rem]"
        size="large"
        placeholder="Search Case"
        (valueChange)="onFilter($event)"
        [clearButton]="true">
        <ng-template kendoTextBoxSuffixTemplate>
          <kendo-textbox-separator></kendo-textbox-separator>
          <button kendoButton fillMode="clear">
            <span
              venioSvgLoader
              svgUrl="assets/svg/icon-search.svg"
              height="1rem"
              width="1rem"></span>
          </button>
        </ng-template>
      </kendo-textbox>
      <kendo-grid-spacer></kendo-grid-spacer>
      <button
        kendoButton
        fillMode="outline"
        class="t-capitalize !t-border-[#ccc]">
        Create a Case
      </button>
    </ng-template>
    <kendo-grid-column
      field="id"
      title="#"
      headerClass="t-text-primary"
      [width]="120"
      [filterable]="false">
    </kendo-grid-column>

    <kendo-grid-column field="" title="" [width]="80" [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <!-- Template for the first column -->
        <button kendoButton fillMode="clear" size="none">
          <span
            venioSvgLoader
            svgUrl="assets/svg/icon-action-grid-pencil.svg"
            height="1rem"
            width="1rem"></span>
        </button>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="full_name"
      title="Contact Name"
      [width]="220">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          class="customer-photo"
          [ngStyle]="{ 'background-image': '' }"></div>
        <div class="customer-name">{{ dataItem.full_name }}</div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="job_title"
      title="Job Title"
      [width]="220">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="is_online"
      title="Status"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false"
      filter="boolean">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span *ngIf="dataItem.is_online === true" class="badge badge-success"
          >Online</span
        >
        <span *ngIf="dataItem.is_online === false" class="badge badge-danger"
          >Offline</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="phone"
      title="Phone"
      [width]="130">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="address"
      title="Address"
      [width]="200">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      title="Action"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex">
          <kendo-buttongroup>
            <button
              kendoButton
              #actionGrid
              *ngFor="let icon of svgIconForGridControls"
              class="!t-p-[0.3rem] t-w-1/4"
              (click)="browseActionClicked(icon.actionType)"
              fillMode="outline"
              size="none">
              <span
                [parentElement]="actionGrid.element"
                venioSvgLoader
                hoverColor="#FFFFFF"
                [svgUrl]="icon.iconPath"
                height="0.9rem"
                width="1rem"></span>
            </button>
          </kendo-buttongroup>
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
