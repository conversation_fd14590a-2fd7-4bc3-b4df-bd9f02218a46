{"name": "feature-transcript-viewer", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/transcript-viewer/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/transcript-viewer/ng-package.json", "tailwindConfig": "libs/feature/transcript-viewer/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/transcript-viewer/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/transcript-viewer/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/transcript-viewer/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}