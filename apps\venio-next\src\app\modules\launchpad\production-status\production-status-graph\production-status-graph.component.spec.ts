import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProductionStatusGraphComponent } from './production-status-graph.component'
import { ChartsModule } from '@progress/kendo-angular-charts'
import { NO_ERRORS_SCHEMA } from '@angular/core'

describe('ProductionStatusGraphComponent', () => {
  let component: ProductionStatusGraphComponent
  let fixture: ComponentFixture<ProductionStatusGraphComponent>

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ProductionStatusGraphComponent, ChartsModule],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductionStatusGraphComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
