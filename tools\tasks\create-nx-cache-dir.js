const fs = require('fs')
const chalk = require('chalk')

/**
 * If the base directory of the nx cache, i.e., /.nx, is removed,
 * it will result in an issue where none of the tasks will run.
 * To address this, it is essential to automatically create the missing directory if it does not already exist.
 * @type {string}
 */
const directoryPath = './.nx'

const message =
  chalk.bgBlueBright(chalk.blue('NOTE:')) +
  chalk.greenBright(
    ' NX cache directory .nx ' +
      (!fs.existsSync(directoryPath) ? 'is created' : 'already exists')
  )

// Check if the directory exists
if (!fs.existsSync(directoryPath)) {
  // If it doesn't exist, create it
  fs.mkdirSync(directoryPath)
  console.log(message)
} else {
  // If it already exists, do nothing
  console.log(message)
}
