<kendo-treelist
  [hideHeader]="true"
  [initiallyExpanded]="true"
  [kendoTreeListFlatBinding]="systemDynamicFolders"
  idField="folderId"
  parentIdField="parentFolderId"
  class="!t-border-0 t-w-[calc(100%_-_1px)] t-h-full t-bg-[#FAFAFA] v-hide-scrollbar"
  kendoTreeListExpandable
  [rowHeight]="36"
  [pageSize]="50"
  scrollable="virtual"
  [autoSize]="true"
  [columnMenu]="false"
  kendoTreeListSelectable
  (selectionChange)="onSelectionChange($event)"
  (cellClick)="onCellClick($event)"
  [(expandedKeys)]="expandedFolderIds"
  [(selectedItems)]="selected"
  itemKey="folderId">
  <ng-template kendoTreeListToolbarTemplate>
    <kendo-textbox
      class="!t-border-[#ccc] !t-w-full !t-flex"
      placeholder="Filter"
      (valueChange)="onFilter($event)">
    </kendo-textbox>
  </ng-template>
  <kendo-treelist-column
    [expandable]="true"
    field="folderName"
    title="folderName"
    [resizable]="true"
    class="!t-border-b-0 !t-py-0 !t-flex t-cursor-pointer">
    <ng-template
      kendoTreeListCellTemplate
      let-folder
      let-isExpanded="isExpanded">
      <div class="t-flex t-gap-1 t-items-center">
        <span
          venioSvgLoader
          height="1rem"
          width="1rem"
          [svgUrl]="
            isExpanded
              ? 'assets/svg/icon-folder-fclv-open.svg'
              : 'assets/svg/icon-folder-fclv.svg'
          ">
          <kendo-loader size="small"></kendo-loader>
        </span>
        {{ folder.folderName }}
      </div>
    </ng-template>
  </kendo-treelist-column>
</kendo-treelist>
