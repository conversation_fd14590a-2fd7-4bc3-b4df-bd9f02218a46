import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild,
  ViewContainerRef,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { Subject, filter, takeUntil, distinctUntilChanged } from 'rxjs'
import { PageControlActionType } from '@venio/shared/models/constants'
import { DocumentTagFacade } from '@venio/data-access/document-utility'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { LocalStorage } from '@venio/shared/storage'

@Component({
  selector: 'venio-tag-coding-filter-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tag-coding-filter-container.component.html',
  styleUrls: ['./tag-coding-filter-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCodingFilterContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  @ViewChild('dialogContent', { static: true })
  private readonly dialogContent: TemplateRef<any>

  private iframeMessengerService = inject(IframeMessengerService)

  private get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  constructor(
    private documentTagFacade: DocumentTagFacade,
    private dialogService: DialogService,
    private vcr: ViewContainerRef
  ) {}

  public ngOnInit(): void {
    this.#selectedPageActionEvent()
    this.#handleMessengerEventForView()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetPageActionEventState()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetPageActionEventState(): void {
    this.documentTagFacade.resetDocumentTagState('pageActionEventType')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleTagCodingFilterDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetPageActionEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      appendTo: this.vcr,
      maxWidth: '1100px',
      maxHeight: '550px',
      width: '80%',
      height: '90vh',
    })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import(
      '../tag-coding-filter-dialog/tag-coding-filter-dialog.component'
    ).then((d) => {
      // launch the dialog
      this.#launchDialogContent(d.TagCodingFilterDialogComponent)

      // once the dialogRef instance is created
      this.#handleTagCodingFilterDialogCloseEvent()
    })
  }

  #handleMessengerEventForView(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        distinctUntilChanged(),
        filter(
          (message) =>
            message.type === 'MICRO_APP_DATA_CHANGE' &&
            (message.payload as MessageContent).type ===
              MessageType.WINDOW_CHANGE &&
            message.eventTriggeredBy ===
              AppIdentitiesTypes.UTILITY_PANEL_ACTION &&
            message.payload?.['content']?.['pageControlActionType'] ===
              PageControlActionType.VIEW &&
            this.isTagPanelPopout
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message) => {
        this.#handleLazyLoadedDialog()
      })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedPageActionEvent(): void {
    this.documentTagFacade.selectPageActionEvent$
      .pipe(
        filter((event) => event === PageControlActionType.VIEW),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }
}
