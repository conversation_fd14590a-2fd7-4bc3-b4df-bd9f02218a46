import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  NgZone,
  OnDestroy,
  OnInit,
  TrackByFunction,
  ViewChild,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import {
  GridModule,
  GridItem,
  GridComponent,
} from '@progress/kendo-angular-grid'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { ActivatedRoute } from '@angular/router'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  SearchFacade,
  TempTableResponseModel,
} from '@venio/data-access/review'
import { isEqual } from 'lodash'
import {
  BulkCodingValuesPayloadModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { PagingUtil } from '@venio/util/utilities'

@Component({
  selector: 'venio-document-coding-list',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    GridModule,
    TooltipModule,
    IndicatorsModule,
    SvgLoaderDirective,
    UiPaginationModule,
    DynamicHeightDirective,
  ],
  templateUrl: './document-coding-list.component.html',
  styleUrl: './document-coding-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentCodingListComponent
  implements OnInit, OnDestroy, AfterViewInit, AfterViewChecked
{
  public bulkCodingData: unknown[] = []

  public selectedDocuments: number[]

  public unselectedDocuments: number[]

  public isBatchSelected: boolean

  public pageSize = 10

  public currentPage = 1

  public tempTables: TempTableResponseModel

  /**
   * This holds the total no. of documents selected document
   */
  public totalHitCount = signal<number>(undefined)

  public headers: string[] = []

  public isBulkCodingValuesLoading$ =
    this.documentCodingFacade.selectIsBulkCodingValuesLoading$

  public unsubscribed$: Subject<void> = new Subject<void>()

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public codingListTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['customFieldInfoId'] as TrackByFunction<GridItem>

  @ViewChild(GridComponent)
  public gridlist: GridComponent

  // store the last known width of the gri
  public lastKnownWidth = 0

  public unsubscribe$ = new Subject<void>()

  constructor(
    private documentsFacade: DocumentsFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private searchFacade: SearchFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  public ngOnInit(): void {
    this.#getBulkCodingValues()
  }

  // Workaround solution for the issue where the grid does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.gridlist && this.lastKnownWidth === 0) {
      const currentWidth = this.gridlist.wrapper.nativeElement.offsetWidth
      // Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  public ngAfterViewInit(): void {
    this.#selectBulkCodingValues()
  }

  #getBulkCodingValues(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.documentsFacade.getIsBatchSelected$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(
        filter(([selectedDocuments]) =>
          Boolean(
            selectedDocuments &&
              selectedDocuments.length > 0 &&
              !isEqual(selectedDocuments, this.selectedDocuments)
          )
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(
        ([
          selectedDocuments,
          unselectedDocuments,
          isBatchSelected,
          tempTables,
        ]) => {
          this.selectedDocuments = selectedDocuments
          this.unselectedDocuments = unselectedDocuments
          this.isBatchSelected = isBatchSelected
          this.tempTables = tempTables
          this.#fetchBulkCodingValues()
        }
      )
  }

  #fetchBulkCodingValues(): void {
    const bulkCodingValuesPayload: BulkCodingValuesPayloadModel = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      fileIds: this.selectedDocuments,
      UnselectedFileIds: this.unselectedDocuments,
      SearchResultTempTable: this.tempTables.searchResultTempTable,
      IsBatchSelected: this.isBatchSelected,
    }
    this.documentCodingFacade.getBulkCodingValues(
      this.projectId,
      bulkCodingValuesPayload
    )
  }

  #selectBulkCodingValues(): void {
    this.documentCodingFacade.selectBulkCodingValues$
      .pipe(
        filter((result) => !!result),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((result) => {
        this.#loadBulkCodingValues(result)
      })
  }

  #loadBulkCodingValues(result: ResponseModel): void {
    const { TotalHitCount, bulkCodingValues, Headers } = result?.data || {}
    this.totalHitCount.set(TotalHitCount || 0)
    this.headers = Headers || []
    this.bulkCodingData = bulkCodingValues || []
    this.fitColumns()
    this.changeDetectorRef.markForCheck()
  }

  public onDataStateChange(): void {
    this.fitColumns()
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.gridlist.autoFitColumns()
      })
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.currentPage = args.pageNumber
      this.#fetchBulkCodingValues()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    //Calculate current page when page size changed
    this.currentPage = PagingUtil.getNewPageNumber(
      this.currentPage,
      this.pageSize,
      args.pageSize
    )

    this.currentPage = args.pageNumber
    this.pageSize = args.pageSize
    this.#fetchBulkCodingValues()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
