export enum DuplicateTagOption {
  PropagateTagsDuplicate = 0,
  PropagateTagsCustodianLevel = 1,
  DoNotPropagateTagsDuplicate = 2,
  PropagateTagsDuplicatesInMediaScope = 3,
}

export enum NearDuplicateTagOption {
  NddTagDoNotPropagate = 0,
  NddTagPropagateSelectedScope = 1,
  NddTagPropagateWholseScope = 2,
}

/**
 * Enum for managing tag propagation options for duplicate items in a case.
 * This covers both exact duplicates and near duplicates.
 */
export enum DuplicateTagPropagation {
  /**
   * Propagate tags to all exact duplicates in the entire case.
   * Ensures uniform tagging across all exact duplicates.
   */
  AllDuplicatesCase = 0,

  /**
   * Propagate tags to exact duplicates within a selected scope, such as a specific folder or subset.
   * Limits tag propagation to a user-defined area within the case.
   */
  AllDuplicatesScope = 1,

  /**
   * Avoid propagating tags to any exact duplicates.
   * Tags must be applied individually to each duplicate item.
   */
  NoDuplicates = 2,

  /**
   * Propagate tags to all near duplicates in the entire case.
   * Applies tags uniformly to items that are similar but not exact duplicates.
   */
  NearDuplicateCase = 3,
}

/**
 * Enum for managing tag propagation options for near duplicate groups.
 */
export enum NearDuplicateTagPropagation {
  /**
   * Do not propagate tags to near duplicate groups.
   * Tags on one item in the group won't affect other items in the group.
   */
  NoNearDuplicates = 0,

  /**
   * Propagate tags within a selected scope for near duplicates.
   * Allows controlled tag propagation within a defined area for similar items.
   */
  NearDuplicateSelectedScope = 1,

  /**
   * Propagate tags to near duplicate groups across the entire case.
   * Ensures a uniform tagging approach for all similar items in the case.
   */
  NearDuplicateWholeScope = 2,
}
