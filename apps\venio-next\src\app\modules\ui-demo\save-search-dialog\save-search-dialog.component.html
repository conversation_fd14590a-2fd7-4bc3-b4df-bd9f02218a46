<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Save Search Dialog</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="570"
  [height]="'70vh'"
  [maxWidth]="500"
  [minWidth]="250"
  [width]="'37%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
      <div class="t-flex t-w-full t-flex-wrap t-gap-3">
        <div showHints="always" class="t-flex t-flex-1 t-gap-1 t-flex-col">
          <div class="t-flex t-gap-3 t-mt-1 t-flex-col t-gap-4">
            <div class="t-flex t-flex-col t-gap-1">
              <kendo-label
                for="DynamicName"
                class="t-text-xs t-uppercase t-tracking-widest">
                Search Name <span class="t-text-error">*</span>
              </kendo-label>

              <kendo-textbox
                placeholder="Enter a Search Name"
                data-qa="search-name"
                #DynamicName></kendo-textbox>
            </div>

            <div class="t-flex t-gap-2 t-flex-row-reverse t-justify-end">
              <label class="k-checkbox-label" for="createApplyTags"
                >Create and apply tags using search terms as tag name</label
              >
              <input
                type="checkbox"
                id="createApplyTags"
                data-qa="createApplyTags"
                (change)="isCreateApplyTagsChecked = !isCreateApplyTagsChecked"
                kendoCheckBox />
            </div>

            <div class="t-flex t-gap-2 t-flex-row-reverse t-justify-end t-pl-3">
              <label
                class="k-checkbox-label"
                for="searchTermsAsTag"
                [class.label-disabled]="isCreateApplyTagsChecked"
                >Use existing tag structure from search</label
              >
              <input
                type="checkbox"
                id="searchTermsAsTag"
                data-qa="searchTermsAsTag"
                [disabled]="isCreateApplyTagsChecked"
                (change)="
                  isUseExistingTagStructureChecked =
                    !isUseExistingTagStructureChecked
                "
                kendoCheckBox />
            </div>

            <div class="t-flex t-flex-col t-gap-1 t-pl-3">
              <kendo-multicolumncombobox
                [data]="data"
                [disabled]="isUseExistingTagStructureChecked"
                textField="full_name"
                valueField="id"
                placeholder="Select an employee..."
                [filterable]="true"
                (filterChange)="handleFilterChange($event)">
                <kendo-combobox-column
                  field="full_name"
                  title="Name"
                  [width]="200">
                </kendo-combobox-column>
                <kendo-combobox-column
                  field="country"
                  title="Country"
                  [width]="80">
                </kendo-combobox-column>
              </kendo-multicolumncombobox>
            </div>

            <!-- 2 -->

            <div class="t-flex t-gap-2 t-mt-2 t-flex-row-reverse t-justify-end">
              <label class="k-checkbox-label" for="searchTerms"
                >Save search terms in a custom field</label
              >
              <input
                type="checkbox"
                id="searchTerms"
                data-qa="searchTerms"
                (change)="newCustomFieldDisabled = !newCustomFieldDisabled"
                kendoCheckBox />
            </div>

            <div class="t-flex t-gap-3 t-pl-3 t-flex-col t-gap-4">
              <div class="t-flex">
                <input
                  type="radio"
                  #newCustomFieldYes
                  value="Yes"
                  kendoRadioButton
                  [disabled]="newCustomFieldDisabled"
                  (change)="isUseExistingCustomFieldChecked = true"
                  name="newCustomField" />
                <kendo-label
                  class="t-ml-2"
                  [for]="newCustomFieldYes"
                  [class.label-disabled]="newCustomFieldDisabled"
                  text="Create a new custom field"></kendo-label>
              </div>

              <div class="t-flex">
                <input
                  type="radio"
                  #newCustomFieldNo
                  value="No"
                  kendoRadioButton
                  [disabled]="newCustomFieldDisabled"
                  (change)="isUseExistingCustomFieldChecked = false"
                  name="newCustomField" />
                <kendo-label
                  class="t-ml-2"
                  [for]="newCustomFieldNo"
                  [class.label-disabled]="newCustomFieldDisabled"
                  text=" Use existing custom field"></kendo-label>
              </div>
            </div>

            <div class="t-flex t-flex-col t-gap-1 t-pl-3">
              <kendo-dropdownlist
                [disabled]="isUseExistingCustomFieldChecked"
                [defaultItem]="defaultItemsSaved"
                [data]="[]"
                textField="text"
                valueField="value">
              </kendo-dropdownlist>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        SAVE
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        fillMode="outline"
        themeColor="dark"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
