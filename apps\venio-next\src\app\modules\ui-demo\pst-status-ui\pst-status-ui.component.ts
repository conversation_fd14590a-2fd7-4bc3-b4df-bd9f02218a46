import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { LayoutModule } from '@progress/kendo-angular-layout'
import {
  GridDataResult,
  GridModule,
  PageChangeEvent,
} from '@progress/kendo-angular-grid'
import { UiPaginationModule } from '@venio/ui/pagination'
import {
  NotificationModule,
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

interface GridDataItem {
  id: number
  rootFolder: string
  logicalPath: string
}

interface GridDataItemStatus {
  id: number
  pstName: string
  createdByOn: Date
  createdByGroup: string
  status: string
  action: string
}

@Component({
  selector: 'venio-pst-status-ui',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    NotificationModule,
    SvgLoaderDirective,
  ],
  templateUrl: './pst-status-ui.component.html',
  styleUrl: './pst-status-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PstStatusUiComponent implements OnInit, AfterViewInit {
  @ViewChild('appendNotification', { read: ViewContainerRef, static: false })
  public appendTo: ViewContainerRef

  public dialogTitle = 'Responsive PST'

  public opened = false

  public tabStatus = 0

  public svgIconForGridControls = [
    {
      actionType: 'Download',
      iconPath: 'assets/svg/icon-material-round-sim-card-download.svg',
    },
    {
      actionType: 'Excel',
      iconPath: 'assets/svg/icon-color-excel.svg',
    },
    {
      actionType: 'Delete',
      iconPath: 'assets/svg/icon-note-ui-delete.svg',
    },
  ]

  public gridDataPST: GridDataResult = { data: [], total: 0 }

  public gridDataStatus: GridDataResult = { data: [], total: 0 }

  public pageSize = 10

  public skip = 0

  // Notification Config
  public content = 'Error occurred when getting information for selected files.'

  public type: NotificationType = { style: 'error', icon: true }

  public hideAfter = 3500

  public width = 400

  constructor(private notificationService: NotificationService) {}

  public ngOnInit(): void {
    this.openDialog()
    this.loadItems()
  }

  public ngAfterViewInit(): void {
    // Show notification only if first tab is selected i.e PST tab
    if (this.tabStatus === 0) {
      this.showNotification(this.content, this.type, this.hideAfter, this.width)
    }
  }

  private loadItems(): void {
    const data = this.generateDataPST()
    this.gridDataPST = {
      data: data.slice(this.skip, this.skip + this.pageSize),
      total: data.length,
    }
  }

  private loadStatus(): void {
    const data = this.generateDataStatus()
    this.gridDataStatus = {
      data: data.slice(this.skip, this.skip + this.pageSize),
      total: data.length,
    }
  }

  private generateDataPST(): GridDataItem[] {
    const data: GridDataItem[] = []
    for (let i = 1; i <= 50; i++) {
      data.push({
        id: i,
        rootFolder: 'Email thread',
        logicalPath: `email thread.rar > john doe to melissa > john doe to melissa${i}.msg`,
      })
    }
    return data
  }

  private generateDataStatus(): GridDataItemStatus[] {
    const data: GridDataItemStatus[] = []
    const groups = ['admin', 'super']

    for (let i = 1; i <= 50; i++) {
      const group = groups[i % 2] // Alternate between 'admin' and 'super'
      const day = i < 10 ? '0' + i : i
      const hour = 12
      const minute = i % 60 < 10 ? '0' + (i % 60) : i % 60
      const dateString = `2024-07-${day}T${hour}:${minute}:00`
      const date = new Date(dateString)

      data.push({
        id: i,
        pstName: `ResponsivePst - ${
          i * Math.floor(Math.random() * 1000000000)
        }`,
        createdByOn: date,
        createdByGroup: group,
        status: i % 2 === 0 ? 'Completed' : 'In Progress',
        action: 'View Details',
      })
    }
    return data
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
    if (e.index === 0) {
      this.loadItems()
      this.showNotification(this.content, this.type, this.hideAfter, this.width)
    } else {
      this.loadStatus()
    }
  }

  public pageChange(event: PageChangeEvent): void {
    this.skip = event.skip
    this.loadItems()
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    this.opened = false
  }

  private showNotification(
    content: string,
    type: NotificationType,
    delay: number,
    width: number
  ): void {
    this.notificationService.show({
      appendTo: this.appendTo,
      content: content,
      animation: { type: 'fade', duration: 300 },
      type: type, // Use Kendo Notification Type
      cssClass: 'v-custom-save-notification',
      hideAfter: delay,
      width: width,
      closable: true,
    })
  }

  public browseActionClicked(actionType: any): void {
    switch (actionType) {
      case 'Download':
        // Invoke methods
        break
      case 'Excel':
        // Invoke methods
        break
      case 'Delete':
        // Invoke methods
        break
    }
  }
}
