import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  DropDownListModule,
  DropDownsModule,
} from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-save-search-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    DropDownListModule,
    DropDownsModule,
  ],
  templateUrl: './save-search-dialog.component.html',
  styleUrl: './save-search-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaveSearchDialogComponent implements OnInit {
  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'Select a Value',
    value: null,
  }

  public defaultItemsSaved: { text: string; value: number } = {
    text: 'Select Saved Custom Value',
    value: null,
  }

  public newCustomFieldDisabled = true

  public isCreateApplyTagsChecked = true

  public isUseExistingTagStructureChecked = true

  public isUseExistingCustomFieldChecked = true

  public dialogTitle = 'Save Search'

  public opened = false

  public data: any[] // Changed type to 'any' as we are generating dynamic data

  public ngOnInit(): void {
    this.openDialog()
    this.generateDummyData()
  }

  public openDialog(): void {
    this.opened = true
  }

  private generateDummyData(): void {
    this.data = Array.from({ length: 100 }, (_, index) => ({
      id: index.toString(),
      full_name: `Employee ${index}`,
      country: 'Country ' + (index % 5), // Generate repeating country names
      // Add other fields as necessary
    }))
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  // multiselect combo box
  public handleFilterChange(searchTerm: string): void {
    const normalizedQuery = searchTerm.toLowerCase()
    const filterExpression = (employee: any): boolean =>
      employee.full_name.toLowerCase().includes(normalizedQuery) ||
      employee.country.toLowerCase().includes(normalizedQuery)
    this.data = this.data.filter(filterExpression)
  }
}
