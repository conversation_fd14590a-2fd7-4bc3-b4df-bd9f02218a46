import { ChangeDetectionStrategy, Component, viewChildren } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  ExpansionPanelComponent,
  LayoutModule,
} from '@progress/kendo-angular-layout'
import { ReviewCreateCaseGeneralComponent } from './review-create-case-general/review-create-case-general.component'
import { chevronDownIcon, chevronUpIcon } from '@progress/kendo-svg-icons'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { ReviewCreateCaseSourceComponent } from './review-create-case-source/review-create-case-source.component'
import { ReviewCreateCaseReviewerComponent } from './review-create-case-reviewer/review-create-case-reviewer.component'
import { ReviewCreateCaseSortComponent } from './review-create-case-sort/review-create-case-sort.component'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { ReviewCreateCaseAdvanceComponent } from './review-create-case-advance/review-create-case-advance.component'

@Component({
  selector: 'venio-review-create-case-main-ui',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    ButtonsModule,
    LayoutModule,
    IconsModule,
    ReviewCreateCaseGeneralComponent,
    SkeletonModule,
    ReviewCreateCaseSourceComponent,
    ReviewCreateCaseReviewerComponent,
    ReviewCreateCaseSortComponent,
    DynamicHeightDirective,
    ReviewCreateCaseAdvanceComponent,
  ],
  templateUrl: './review-create-case-main-ui.component.html',
  styleUrl: './review-create-case-main-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseMainUiComponent {
  public listItems: Array<string> = ['Item 1', 'Item 2']

  public icons = { downIcon: chevronDownIcon, upIcon: chevronUpIcon }

  public readonly expandedPanel = viewChildren<ExpansionPanelComponent>(
    ExpansionPanelComponent
  )

  public panelsData = [
    {
      title: 'General Setting',
      name: 'general',
      expanded: true,
    },
    {
      title: 'Source',
      name: 'source',
      expanded: false,
    },
    {
      title: 'Reviewers',
      name: 'reviewers',
      expanded: false,
    },
    {
      title: 'Document Sort Options',
      name: 'documentSort',
      expanded: false,
    },
    {
      title: 'Advanced Options',
      name: 'advance',
      expanded: false,
    },
  ]

  public onPanelExpand(panel: any): void {
    this.expandedPanel().forEach((p) => (p.expanded = false))
    panel.expanded = true
  }
}
