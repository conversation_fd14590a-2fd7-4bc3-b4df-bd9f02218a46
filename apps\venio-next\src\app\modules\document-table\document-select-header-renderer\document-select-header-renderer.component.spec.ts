import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  DocumentsService,
  ReviewFacade,
  SearchResultFacade,
} from '@venio/data-access/review'

import { DocumentSelectHeaderRendererComponent } from './document-select-header-renderer.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentSelectHeaderRendererComponent', () => {
  let component: DocumentSelectHeaderRendererComponent
  let fixture: ComponentFixture<DocumentSelectHeaderRendererComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PopoverModule],
      declarations: [DocumentSelectHeaderRendererComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        SearchResultFacade,
        DocumentsService,
        provideMockStore({}),
        ReviewFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentSelectHeaderRendererComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
