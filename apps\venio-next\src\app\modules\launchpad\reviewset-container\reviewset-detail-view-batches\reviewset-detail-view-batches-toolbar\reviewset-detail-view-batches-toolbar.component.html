<div
  class="t-flex t-flex-row t-justify-end t-items-center t-pl-4 t-py-1 t-border-[#ececec] t-border-t-[1px] t-border-l-0 t-border-r-0">
  <kendo-buttongroup>
    <button
      kendoButton
      #rebatch
      class="v-custom-show-title-disabled-btn t-w-[33px] t-h-[33px]"
      [ngClass]="{
        't-rounded-tr-none t-rounded-br-none hover:t-border-[#2F3080] hover:t-bg-[#2F3080]':
          !shouldDisableRebatch()
      }"
      [disabled]="shouldDisableRebatch()"
      (click)="actionButtonClick(commonActionTypes.REBATCH)"
      size="none">
      <div
        kendoTooltip
        [title]="rebatchTitle()"
        class="!t-py-[0.38rem] !t-px-[0.5rem] !t-w-full !-t-h-full">
        <span
          [parentElement]="rebatch.element"
          venioSvgLoader
          hoverColor="#FFFFFF"
          color="#979797"
          svgUrl="assets/svg/icon-fast-forward-arrow.svg"
          height="0.85rem"
          width="0.85rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </div>
    </button>
    <button
      kendoButton
      #delete
      class="v-custom-show-title-disabled-btn t-w-[33px] t-h-[33px]"
      [ngClass]="{
        't-rounded-tr-none t-rounded-br-none hover:t-border-[#2F3080] hover:t-bg-[#2F3080]':
          !shouldDisableDelete()
      }"
      [disabled]="shouldDisableDelete()"
      (click)="actionButtonClick(commonActionTypes.DELETE)"
      size="none">
      <div
        kendoTooltip
        [title]="deleteTitle()"
        class="!t-py-[0.38rem] !t-px-[0.5rem] !t-w-full !-t-h-full">
        <span
          [parentElement]="delete.element"
          venioSvgLoader
          applyEffectsTo="fill"
          hoverColor="#FFFFFF"
          color="#979797"
          svgUrl="assets/svg/Icon-material-delete.svg"
          height="0.75rem"
          width="0.8rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </div>
    </button>
  </kendo-buttongroup>
  <venio-pagination
    (pageChanged)="pagingControlChange($event)"
    (pageSizeChanged)="pagingControlChange($event)"
    [disabled]="isReviewSetBatchLoading()"
    [totalRecords]="totalRecords()"
    [pageSize]="pageSize()"
    [showPageJumper]="false"
    [showPageSize]="true"
    [showRowNumberInputBox]="true"
    class="t-px-5 t-block t-py-2">
  </venio-pagination>
</div>
