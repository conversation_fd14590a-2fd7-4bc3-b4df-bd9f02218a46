export interface ResponseModel {
  status: string | undefined
  data: any | null
  message: string | undefined
}

export interface HttpErrorResponseModel {
  exceptionMessage: string | undefined
  message: string | undefined
}

export interface VodConfig {
  apiUrl: string
  logoPath: string
  faviconPath: string
  isTwoFactorAuthenticationEnabled: boolean
  isIdpEnabled: boolean
  isOktaIdpEnabled: boolean
  isADEnabled: boolean
  isVodEnabled: boolean
  themeFilePath: string
  companyName: string
  venioVersion: string
  loginImageBackgroundPath: string
}
