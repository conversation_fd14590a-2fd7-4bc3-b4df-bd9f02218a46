import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchHistoryDialogComponent } from './search-history-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchHistoryDialogComponent', () => {
  let component: SearchHistoryDialogComponent
  let fixture: ComponentFixture<SearchHistoryDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchHistoryDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchHistoryDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
