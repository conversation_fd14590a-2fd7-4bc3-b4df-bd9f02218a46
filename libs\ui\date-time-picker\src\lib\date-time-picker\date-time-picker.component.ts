import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  EventEmitter,
  inject,
  OnDestroy,
  Output,
  signal,
  ViewChild,
} from '@angular/core'
import {
  DateRangePopupComponent,
  SelectionRange,
} from '@progress/kendo-angular-dateinputs'
import {
  caretAltDownIcon,
  SVGIcon,
  calendarIcon,
  checkIcon,
  xCircleIcon,
  chevronDownIcon,
  xIcon,
} from '@progress/kendo-svg-icons'
import { Subject } from 'rxjs'
import dayjs from 'dayjs'

@Component({
  selector: 'venio-date-time-picker',
  templateUrl: './date-time-picker.component.html',
  styleUrl: './date-time-picker.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DateTimePickerComponent implements OnDestroy, AfterViewInit {
  private readonly toDestroy$ = new Subject<void>()

  @Output()
  public readonly dateRangeChange: EventEmitter<SelectionRange> =
    new EventEmitter<SelectionRange>()

  private readonly changeDetectorRef = inject(ChangeDetectorRef)

  @ViewChild('popup', { static: true })
  public datePopup: DateRangePopupComponent

  public readonly chevDown = caretAltDownIcon

  public selectedRange = signal<SelectionRange>({
    end: undefined,
    start: undefined,
  })

  public readonly calendarDateIcon: SVGIcon = calendarIcon

  public checkIcon: SVGIcon = checkIcon

  public closeIcon: SVGIcon = xCircleIcon

  public downIcon: SVGIcon = chevronDownIcon

  public xIcon: SVGIcon = xIcon

  public selectedRangeText = signal<string>('')

  public selectedMenu = signal<string>('')

  public popupOpened = false

  public menuItems = [
    { label: 'This Week', value: 'THIS_WEEK' },
    { label: 'Last Week', value: 'LAST_WEEK' },
    { label: 'This Month', value: 'THIS_MONTH' },
    { label: 'Last Month', value: 'LAST_MONTH' },
  ]

  public dateDiffInDays = computed(() => {
    const { start, end } = this.selectedRange()
    if (!start || !end) {
      return 0
    }
    return dayjs(end).add(1, 'day').diff(dayjs(start), 'day')
  })

  public dateSelectionChange(range: SelectionRange): void {
    this.selectedRange.set(range)
    this.dateRangeChange.emit(range)
  }

  public calendarFocus(): void {
    this.resetDates()
  }

  public setDateRange(event: MouseEvent): void {
    event.preventDefault()
    event.stopPropagation()

    this.#setSelectedDateRangeText()

    // Handle closing the date picker gracefully
    setTimeout(() => {
      this.datePopup.cancelPopup()
      this.popupOpened = false
    })
  }

  #setSelectedDateRangeText(): void {
    const { start, end } = this.selectedRange()
    if (!start || !end) {
      this.selectedRangeText.set('')
      return
    }
    this.selectedRangeText.set(
      dayjs(start).format('MM-DD-YYYY') +
        ' - ' +
        dayjs(end).format('MM-DD-YYYY')
    )
  }

  public ngAfterViewInit(): void {
    this.#selectDateRange()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public resetDates(): void {
    this.selectedRange.set({ start: undefined, end: undefined })
    this.dateRangeChange.emit({ start: undefined, end: undefined })
    this.selectedRangeText.set('')
    this.selectedMenu.set('')
  }

  public openCalendar(): void {
    this.datePopup.toggle(true)
    this.popupOpened = true
  }

  public selectRangeMenu(
    selectedMenu: 'THIS_WEEK' | 'LAST_WEEK' | 'THIS_MONTH' | 'LAST_MONTH'
  ): void {
    // Even though we are using the signal to set the value, we need to call markForCheck because the kendo may not update the view
    this.changeDetectorRef.markForCheck()
    this.selectedMenu.set(selectedMenu)
    this.datePopup.dateRangeService.setRange({
      start: undefined,
      end: undefined,
    })
    this.datePopup.dateRangeService.setActiveRangeEnd('start')
    switch (selectedMenu) {
      case 'THIS_WEEK':
        this.dateSelectionChange(this.getThisWeekRange())
        break
      case 'LAST_WEEK':
        this.dateSelectionChange(this.getLastWeekRange())
        break
      case 'THIS_MONTH':
        this.dateSelectionChange(this.getThisMonthRange())
        break
      case 'LAST_MONTH':
        this.dateSelectionChange(this.getLastMonthRange())
        break
    }

    this.#setSelectedDateRangeText()

    setTimeout(() => {
      this.changeDetectorRef.markForCheck()
      this.datePopup.calendar.focusedDate = this.selectedRange().start
      this.datePopup.calendar.focusedDate = this.selectedRange().end
      this.datePopup.dateRangeService.setActiveRangeEnd('end')
    }, 500)
  }

  #selectDateRange(): void {
    this.selectedRange.set({
      end: undefined,
      start: undefined,
    })
    this.dateRangeChange.emit({
      end: undefined,
      start: undefined,
    })
  }

  private getThisWeekRange(): SelectionRange {
    const start = dayjs().startOf('week')
    const end = dayjs().endOf('week')
    return { start: start.toDate(), end: end.toDate() }
  }

  private getLastWeekRange(): SelectionRange {
    const start = dayjs().subtract(1, 'week').startOf('week')
    const end = dayjs().subtract(1, 'week').endOf('week')
    return { start: start.toDate(), end: end.toDate() }
  }

  private getThisMonthRange(): SelectionRange {
    const start = dayjs().startOf('month')
    const end = dayjs().endOf('month')
    return { start: start.toDate(), end: end.toDate() }
  }

  private getLastMonthRange(): SelectionRange {
    const start = dayjs().subtract(1, 'month').startOf('month')
    const end = dayjs().subtract(1, 'month').endOf('month')
    return { start: start.toDate(), end: end.toDate() }
  }
}
