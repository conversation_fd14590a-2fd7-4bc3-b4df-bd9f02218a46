const fs = require('fs')
const process = require('process')
const chalk = require('chalk')

// Function to rename the original JSON file to .bak
const renameToBackup = (filePath) => {
  const backupFilePath = `${filePath}.bak`
  console.log(chalk.blue('INFO: Renaming original file to backup...'))
  fs.renameSync(filePath, backupFilePath)
  console.log(chalk.green(`INFO: Renamed original file to ${backupFilePath}`))
}

// Function to restore from .bak and remove the modified file
const restoreFromBackup = (filePath) => {
  try {
    const backupFilePath = `${filePath}.bak`
    if (fs.existsSync(backupFilePath)) {
      console.log(chalk.blue('INFO: Restoring original file from backup...'))
      fs.renameSync(backupFilePath, filePath)
      console.log(
        chalk.yellow(
          `INFO: Restored original file from ${backupFilePath} to ${filePath}`
        )
      )
    } else {
      console.error(chalk.red('ERROR: Backup file not found. Cannot restore.'))
    }
  } catch (e) {
    console.error(e)
    console.error(
      chalk.red('ERROR: failed to restore from backup. Path:' + filePath)
    )
  }
}

// Function to update the baseHref in the JSON file
const updateBaseHref = (filePath, newBaseHref) => {
  // Rename the original file to .bak
  renameToBackup(filePath)

  // Read the .bak file
  console.log(
    chalk.blue(
      `INFO: Reading backup file to update baseHref with value: ${newBaseHref}...`
    )
  )
  fs.readFile(`${filePath}.bak`, 'utf8', (err, data) => {
    if (err) {
      console.error(chalk.red(`ERROR: Failed to read the file: ${err}`))
      return
    }

    const jsonContent = JSON.parse(data)

    if (
      jsonContent.targets &&
      jsonContent.targets.build &&
      jsonContent.targets.build.options
    ) {
      jsonContent.targets.build.options.baseHref = newBaseHref
      jsonContent.targets.build.options.statsJson = false
      console.log(
        chalk.blue(
          `INFO: baseHref will be updated to ${newBaseHref} in ${filePath}`
        )
      )
    } else {
      console.error(
        chalk.red(
          'ERROR: The JSON structure is not as expected. Make sure it has targets.build.options'
        )
      )
      return
    }

    // Write the updated JSON back to the original file
    fs.writeFile(
      filePath,
      JSON.stringify(jsonContent, null, 2),
      'utf8',
      (err) => {
        if (err) {
          console.error(chalk.red(`ERROR: Failed to write the file: ${err}`))
        } else {
          console.log(
            chalk.green('INFO: Successfully updated baseHref in the JSON file.')
          )
        }
      }
    )
  })
}

// Function to update the base tag in the HTML file
const updateBaseTag = (htmlFilePath, newBaseHref) => {
  // Rename the original HTML file to .bak
  renameToBackup(htmlFilePath)

  // Read the .bak HTML file
  console.log(
    chalk.blue(
      `INFO: Reading backup HTML file to update base tag with value: ${newBaseHref}...`
    )
  )
  const htmlData = fs.readFileSync(`${htmlFilePath}.bak`, 'utf8')

  // Use a regular expression to replace the base href value
  const updatedHtmlData = htmlData.replace(
    /<base href="[^"]*" \/>/,
    `<base href="${newBaseHref}" />`
  )

  // Write the updated HTML back to the original file
  fs.writeFileSync(htmlFilePath, updatedHtmlData, 'utf8')
  console.log(
    chalk.green(
      `INFO: Successfully updated base tag in the HTML file to ${newBaseHref}.`
    )
  )
}

// Main execution starts here
if (process.argv.length < 3) {
  console.error(
    chalk.red(
      'ERROR: Argument not provided. Please pass the new baseHref value as an argument. E.g., node updateBaseHref.js "/new-base-href"'
    )
  )
  process.exit(1)
}

const action = process.argv[2]
const filePath = 'apps/venio-next/project.json' // Replace with the path to your project.json file
const htmlFilePath = 'apps/venio-next/src/index.html' // Replace with the actual path to your index.html file

if (action === 'undo') {
  restoreFromBackup(filePath)
  restoreFromBackup(htmlFilePath) // Restore the HTML file from backup
} else {
  updateBaseHref(filePath, action)
  updateBaseTag(htmlFilePath, action) // Update the base tag in the HTML file
}
