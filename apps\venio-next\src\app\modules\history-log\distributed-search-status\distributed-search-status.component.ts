import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { SearchFacade, SearchStatusModel } from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import {
  Subject,
  debounceTime,
  defer,
  filter,
  interval,
  mergeMap,
  range,
  repeat,
  scan,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { FormsModule } from '@angular/forms'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-distributed-search-status',
  standalone: true,
  templateUrl: './distributed-search-status.component.html',
  styleUrl: './distributed-search-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    GridModule,
    InputsModule,
    DropDownsModule,
    LabelModule,
    UiPaginationModule,
    FormsModule,
  ],
})
export class DistributedSearchStatusComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public readonly toDestroy$ = new Subject<void>()

  public isDSJobStatusLoading$ = this.searchFacade.getIsDSJobStatusLoading$

  public statusOptions = [
    { value: '0', label: 'Not Processed' },
    { value: '1,-1', label: 'In Progress' },
    { value: '-100', label: 'Paused' },
    { value: '2', label: 'Completed' },
    { value: '-2', label: 'Completed with error' },
  ]

  public refreshIntervalInSeconds = 5

  public isAutoRefreshChecked = false

  public selectedStatus: string[] = []

  public defaultSelectedStatus = [
    this.statusOptions[0],
    this.statusOptions[1],
    this.statusOptions[2],
  ]

  public gridViewData: SearchStatusModel[] = []

  public pageSize = 10

  public skip = 0

  public currentPage: number

  public totalRecords = 0

  constructor(
    private searchFacade: SearchFacade,
    private activatedRoute: ActivatedRoute,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.selectedStatus = this.defaultSelectedStatus.flatMap(
      (option) => option.value
    )
    this.#selectDSJobStatus()
    this.#selectDSJobStatusCountFailureResponse()
    this.#selectDSJobStatusFailureResponse()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngAfterViewInit(): void {
    this.#handleAutoRefreshChecked()
  }

  #selectDSJobStatus(): void {
    this.gridViewData = []
    this.totalRecords = this.gridViewData.length
    const status = this.selectedStatus
    const dsStatus: SearchStatusModel = {
      projectId: this.projectId,
      pageNumber: 1,
      selectedValues: status || [],
    }

    this.searchFacade
      .fetchDSJobStatusCountByService$(dsStatus)
      .pipe(
        switchMap((statusCountResponse) => {
          const res: ResponseModel = statusCountResponse as ResponseModel
          if (res.data.pages <= 0)
            this.searchFacade.setDSJobStatusLoadiing(false)
          return range(1, res.data.pages)
        }),
        mergeMap((page) => {
          const dsStatus: SearchStatusModel = {
            projectId: this.projectId,
            pageNumber: page,
            selectedValues: status || [],
          }
          return this.searchFacade.fetchDSJobStatusByService$(dsStatus)
        }),
        scan((accum, statusResponse) => {
          const item: ResponseModel = statusResponse as ResponseModel
          return (accum = [...accum, ...item.data])
        }, []),
        tap(() => (this.gridViewData = [])),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res) => {
          this.searchFacade.setDSJobStatusLoadiing(false)
          this.cdr.markForCheck()
          this.gridViewData = res.sort((a, b) => b.searchId - a.searchId)
          this.totalRecords = this.gridViewData.length
        },
      })
  }

  #selectDSJobStatusCountFailureResponse(): void {
    this.searchFacade.getDSJobStatusCountFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.#showMessage(error.message, {
          style: 'error',
        })

        this.#resetSearchDSJobStatusState()
      })
  }

  #selectDSJobStatusFailureResponse(): void {
    this.searchFacade.getDSJobStatusFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.#showMessage(error.message, {
          style: 'error',
        })

        this.#resetSearchDSJobStatusState()
      })
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #resetSearchDSJobStatusState(): void {
    this.searchFacade.resetSearchState([
      'fetchDSJobStatusSuccessResponse',
      'fetchDSJobStatusFailureResponse',
      'isDSJobStatusLoading',
    ])
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.currentPage = args.pageNumber
      this.skip = (args.pageNumber - 1) * args.pageSize
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    this.pageSize = args.pageSize
  }

  public onStatusSelectChange(selectedStatus): void {
    this.selectedStatus = selectedStatus.flatMap(
      (option) => option.value as string
    )
    this.currentPage = 1
    this.skip = 0
    this.#selectDSJobStatus()
  }

  #handleAutoRefreshChecked(): void {
    // keep tracks on the refresher and loads data.
    defer(() => interval(1000 * this.refreshIntervalInSeconds))
      .pipe(
        take(1),
        repeat(),
        filter(
          () => this.refreshIntervalInSeconds > 0 && this.isAutoRefreshChecked
        )
      )
      .subscribe({
        next: () => this.#selectDSJobStatus(),
      })
  }
}
