<div class="t-flex t-flex-row t-gap-4" [formGroup]="reviewSetForm()">
  <div class="t-basis-1/2">
    <div class="t-flex t-flex-col t-gap-2">
      <div class="t-flex">
        <kendo-dropdownlist
          formControlName="reviewSource"
          class="t-w-full"
          [data]="sourceSelectorItems"
          textField="label"
          valueField="value"
          [valuePrimitive]="true" />
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>

      <div class="t-flex">
        @switch (reviewSetFormService.selectedSource()) { @case
        (reviewSetSourceType.TAG) {
        <div class="t-flex t-flex-col t-grow t-gap-1">
          <kendo-multiselect
            [loading]="isSourceTagLoading()"
            formControlName="tagId"
            [data]="reviewSetFormService.filteredSourceTags()"
            (filterChange)="
              reviewSetFormService.configureFilterTerm($event, 'SOURCE_TAG')
            "
            [filterable]="true"
            [checkboxes]="true"
            [autoClose]="false"
            [tagMapper]="commonMapper"
            textField="name"
            valueField="tagId"
            [valuePrimitive]="true"
            class="!t-w-full"
            placeholder="Select Tags"
            [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
            <ng-template kendoSuffixTemplate>
              <button
                kendoButton
                [svgIcon]="downIcon"
                fillMode="link"
                class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
            </ng-template>
            <ng-template kendoDropDownListHeaderTemplate>
              @if (reviewSetFormService.filteredSourceTags()?.[0]) {
              <div
                class="t-flex t-px-2 t-pt-4 t-pb-2 t-gap-2 t-items-center"
                (click)="$event.stopPropagation()">
                <kendo-checkbox
                  #selectAll
                  [checkedState]="
                    reviewSetFormService.isIntermediateSourceTag()
                  "
                  (checkedStateChange)="toggleCheckAllSourceTags($event)"
                  class="t-block" />
                <kendo-label
                  [for]="selectAll"
                  text="Select All"
                  (click)="$event.stopPropagation()" />
              </div>
              }
            </ng-template>
            <ng-template kendoDropDownListItemTemplate let-dataItem>
              <span
                kendoTooltip
                [title]="dataItem.name.length > 15 ? dataItem.name : ''"
                >{{
                  dataItem.name.length > 15
                    ? dataItem.name.slice(0, 15) + '...'
                    : dataItem.name
                }}</span
              >
              <span
                kendoTooltip
                [title]="
                  dataItem.parentId.length > 14 ? dataItem.parentId : ''
                ">
                <i>{{
                  dataItem.parentId.length > 14
                    ? dataItem.parentId.slice(0, 14) + '...'
                    : dataItem.parentId
                }}</i
                ><b> (Count: {{ dataItem.totalTagCount }})</b></span
              >
            </ng-template>
          </kendo-multiselect>
          @if (sourceTagsInvalid()) {
          <div class="t-text-error t-mb-2 t-w-full">Tag(s) are required</div>
          }
        </div>
        } @case (reviewSetSourceType.FOLDER) {
        <div class="t-flex t-flex-col t-grow t-gap-1">
          <kendo-multiselect
            [loading]="isSourceFolderLoading()"
            formControlName="folderId"
            [data]="reviewSetFormService.filteredSourceFolders()"
            (filterChange)="
              reviewSetFormService.configureFilterTerm($event, 'SOURCE_FOLDER')
            "
            [filterable]="true"
            [checkboxes]="true"
            [autoClose]="false"
            [tagMapper]="commonMapper"
            textField="folderName"
            valueField="folderId"
            [valuePrimitive]="true"
            class="!t-w-full"
            placeholder="Select Folders"
            [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
            <ng-template kendoSuffixTemplate>
              <button
                kendoButton
                [svgIcon]="downIcon"
                fillMode="link"
                class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
            </ng-template>
            <ng-template kendoDropDownListHeaderTemplate>
              @if (reviewSetFormService.filteredSourceFolders()?.[0]) {
              <div
                class="t-flex t-px-2 t-pt-4 t-pb-2 t-gap-2 t-items-center"
                (click)="$event.stopPropagation()">
                <kendo-checkbox
                  #selectAll
                  [checkedState]="
                    reviewSetFormService.isIntermediateSourceTag()
                  "
                  (checkedStateChange)="toggleCheckAllSourceTags($event)"
                  class="t-block" />
                <kendo-label
                  [for]="selectAll"
                  text="Select All"
                  (click)="$event.stopPropagation()" />
              </div>
              }
            </ng-template>
            <ng-template kendoDropDownListItemTemplate let-dataItem>
              <span>{{ dataItem.folderName }}</span>
              <b> (Count: {{ dataItem.totalFileCount }})</b>
            </ng-template>
          </kendo-multiselect>
          @if (sourceFoldersInvalid()) {
          <div class="t-text-error t-mb-2">Folder(s) are required</div>
          }
        </div>
        } @case (reviewSetSourceType.SAVED_SEARCH) {
        <div class="t-flex t-flex-col t-grow t-gap-1">
          <kendo-dropdownlist
            [loading]="isSourceSavedSearchLoading()"
            formControlName="savedSearchId"
            [data]="reviewSetFormService.filteredSourceSavedSearch()"
            (filterChange)="
              reviewSetFormService.configureFilterTerm(
                $event,
                'SOURCE_SAVE_SEARCH'
              )
            "
            [filterable]="true"
            textField="searchName"
            valueField="searchId"
            [defaultItem]="defaultSavedSearchItem()"
            [valuePrimitive]="true"
            class="!t-w-full"
            [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
            <ng-template kendoSuffixTemplate>
              <button
                kendoButton
                [svgIcon]="downIcon"
                fillMode="link"
                class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
            </ng-template>
            <ng-template kendoDropDownListItemTemplate let-dataItem>
              @if (dataItem) {
              <span>{{ dataItem.searchName }}</span>
              @if (dataItem.searchId > 0) {
              <b> (Count: {{ dataItem.totalHitCount }})</b>
              } }
            </ng-template>
            <ng-template kendoDropDownListValueTemplate let-dataItem>
              @if (dataItem) {
              <span>{{ dataItem.searchName }}</span>
              @if (dataItem.searchId > 0) {
              <b> (Count: {{ dataItem.totalHitCount }})</b>
              } }
            </ng-template>
          </kendo-dropdownlist>
          @if (sourceSavedSearchInvalid()) {
          <div class="t-text-error t-mb-2 t-w-full">
            Saved search is required
          </div>
          }
        </div>
        } }
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
      <div
        class="t-flex t-flex-col t-grow t-gap-2"
        *ngIf="
          reviewSetFormService.selectedSource() !==
          reviewSetSourceType.SAVED_SEARCH
        ">
        <div class="t-flex">
          Combining Operator
          <span class="t-text-error t-align-super t-ml-1"> *</span>
        </div>

        <div class="t-flex t-gap-4 t-items-center">
          <div class="t-flex t-items-center t-gap-1">
            @if (reviewSetFormService.selectedSource() ===
            reviewSetSourceType.TAG) {
            <input
              formControlName="tagSelectionOption"
              type="radio"
              #or
              value="OR"
              kendoRadioButton
              size="small" />
            <kendo-label [for]="or" text="OR" />
            } @if (reviewSetFormService.selectedSource() ===
            reviewSetSourceType.FOLDER) {
            <input
              formControlName="folderSelectionOption"
              type="radio"
              #or
              value="OR"
              kendoRadioButton
              size="small" />
            <kendo-label [for]="or" text="OR" />
            }
          </div>
          <div class="t-flex t-items-center t-gap-1">
            @if (reviewSetFormService.selectedSource() ===
            reviewSetSourceType.TAG) {
            <input
              formControlName="tagSelectionOption"
              type="radio"
              #and
              value="AND"
              kendoRadioButton
              size="small" />
            <kendo-label [for]="and" text="AND" />
            } @if (reviewSetFormService.selectedSource() ===
            reviewSetSourceType.FOLDER) {
            <input
              formControlName="folderSelectionOption"
              type="radio"
              #and
              value="AND"
              kendoRadioButton
              size="small" />
            <kendo-label [for]="and" text="AND" />
            }
          </div>
        </div>
      </div>
      <div class="t-flex">
        <div class="t-flex t-flex-col t-grow t-gap-1">
          <kendo-multiselecttree
            [loading]="reviewSetFormService.isTagTreeLoading()"
            formControlName="displayTag"
            [kendoMultiSelectTreeFlatBinding]="reviewSetFormService.allTags()"
            parentIdField="parentId"
            textField="tagName"
            valueField="id"
            [valuePrimitive]="false"
            [filterable]="true"
            [tagMapper]="tagMapper"
            (filterChange)="viewableTagFilterChange($event)"
            (valueChange)="onDisplayTagChange($event)"
            kendoDropDownTreeExpandable
            kendoMultiSelectTreeExpandable
            [loadOnDemand]="false"
            [checkableSettings]="{ checkChildren: true, checkOnClick: true }"
            [expandOnFilter]="{ expandedOnClear: 'all' }"
            [checkAll]="enableCheckAllViewableTags()"
            class="!t-w-full"
            placeholder="Tags Viewable"
            [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
            <ng-template kendoSuffixTemplate>
              <button
                kendoButton
                [svgIcon]="downIcon"
                fillMode="link"
                class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
            </ng-template>
          </kendo-multiselecttree>
          @if (viewableTagsInvalid()) {
          <div class="t-text-error t-mb-2 t-w-full">
            Viewable tag(s) are required
          </div>
          }
        </div>
        <span class="t-text-error t-align-super t-ml-1"> *</span>
      </div>
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-basis-2/3 t-flex t-flex-col t-gap-3">
    <div class="t-w-full t-flex t-flex-col t-gap-2">
      <div class="t-flex t-items-center t-mb-1">
        <kendo-checkbox
          formControlName="enableAutoCollect"
          #ac
          class="t-block" />
        <kendo-label
          class="k-checkbox-label"
          [for]="ac"
          text="Endorse Auto Collection" />
      </div>

      <div
        class="t-flex t-flex-col t-gap-2 t-w-[70%]"
        [ngClass]="{
          't-opacity-60': !reviewSetFormService.isEnableAutoCollect()
        }">
        <div class="t-flex t-items-center">
          <kendo-numerictextbox
            formControlName="autoCollectFrequency"
            id="frequency"
            placeholder="Frequency"
            [format]="'n0'"
            [min]="0"
            [step]="1"
            class="t-w-full t-rounded-r-none t-border-r-none">
          </kendo-numerictextbox>
          <kendo-textbox
            [disabled]="!reviewSetFormService.isEnableAutoCollect()"
            class="v-input-l-none t-w-[60px] t-m-0"
            [readonly]="true"
            placeholder="Hrs" />
        </div>

        <div class="t-flex t-items-center t-space-x-2 t-w-full">
          <kendo-datepicker
            formControlName="autoCollectExpiresOn"
            id="datePicker"
            format="MM/dd/yyyy"
            class="t-w-full"
            placeholder="MM/DD/YYYY"
            formatPlaceholder="formatPattern" />
        </div>

        <div class="t-flex t-items-center">
          <kendo-numerictextbox
            formControlName="autoCollectMinThresholdValue"
            id="minThreshold"
            placeholder="Minimum Threshold"
            [format]="'n0'"
            [min]="0"
            [step]="1"
            class="t-w-full t-rounded-r-none t-border-r-none" />
          <kendo-textbox
            [disabled]="!reviewSetFormService.isEnableAutoCollect()"
            class="v-input-l-none t-w-[60px] t-m-0"
            [readonly]="true"
            placeholder="Docs" />
        </div>
      </div>

      <div
        class="t-space-y-2"
        [ngClass]="{
          't-opacity-60': !reviewSetFormService.isEnableAutoCollect()
        }">
        <div class="t-flex t-gap-2 t-flex-col">
          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #collect
              [value]="true"
              kendoRadioButton
              formControlName="autoCollectionSelectionCriteria"
              size="small" />
            <kendo-label [for]="collect" text="Collected from matched source" />
          </div>
          <div class="t-flex t-items-center t-gap-1">
            <input
              type="radio"
              #collectMatch
              [value]="false"
              kendoRadioButton
              size="small"
              formControlName="autoCollectionSelectionCriteria" />
            <kendo-label
              [for]="collectMatch"
              text="Collected from matched source and only if reviewed in batch" />
          </div>
          <div class="t-flex t-flex-col t-items-center t-gap-1 t-w-[70%]">
            <kendo-dropdownlist
              formControlName="autoCollectReviewset"
              textField="name"
              [loading]="reviewSetsLoading()"
              valueField="reviewSetId"
              [valuePrimitive]="true"
              [filterable]="true"
              (filterChange)="
                reviewSetFormService.configureFilterTerm($event, 'REVIEW_SET')
              "
              [defaultItem]="defaultReviewSetItem()"
              [data]="reviewSetFormService.filteredReviewSets()">
              <ng-template kendoDropDownListValueTemplate let-dataItem>
                <div class="t-inline-flex t-align-middle">
                  {{ dataItem.name }}
                  @if (isReviewSetRequired()) {
                  <span class="t-text-error t-align-super t-ml-1">*</span>
                  }
                </div>
              </ng-template>
            </kendo-dropdownlist>
            @if (autoCollectReviewSetInvalid() && isReviewSetRequired()) {
            <div class="t-text-error t-mb-2 t-w-full">
              Review set is required
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
