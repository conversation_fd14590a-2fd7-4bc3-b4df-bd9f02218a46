<div class="t-inline-block t-py-3 t-pl-6 t-pr-3 t-w-full">
  @if(eDaiDocumentRelevance()?.summary && !isRelevanceDocumentLoading()){
  <h2 class="t-text-lg t-font-bold t-mb-3">AI Summary</h2>
  <p class="t-text-[#707070] t-mb-3">
    {{ eDaiDocumentRelevance()?.summary }}
  </p>
  } @if(!eDaiDocumentRelevance()?.issueDetail?.length &&
  !isRelevanceDocumentLoading()){
  <div class="t-block t-p-4 t-bg-error t-text-white t-mt-2">
    No records found.
  </div>

  }@else { @if(isRelevanceDocumentLoading()){
  <kendo-skeleton height="30px" width="100%" class="t-mb-2" />
  <kendo-skeleton height="30px" width="100%" class="t-mb-2" />
  } @else { @for(item of eDaiDocumentRelevance()?.issueDetail; track (item.id +
  item.issuePrompt); let first = $first){
  <div
    class="t-mb-5 t-rounded-md t-border-2 t-border-[#F3F2F3] t-border-dashed">
    <div class="t-flex t-items-start t-mb-4 t-gap-3">
      <div
        class="t-flex t-min-w-[62px] t-p-1 t-bg-[#1EBADC] t-rounded-md t-flex-col t-items-center t-justify-center t-text-white t-font-bold">
        {{ item.issueName.split(' ')[0] }}
        <div class="t-mt-[-2px]">{{ item.issueName.split(' ')[1] }}</div>
      </div>

      <div class="t-flex t-p-2">
        <p class="t-text-[#707070]">{{ item.issuePrompt }}</p>
      </div>
    </div>

    <div class="t-w-full t-px-2 t-pb-2">
      <div class="t-mb-2">
        <span class="t-font-semibold">Tag</span>
        <p class="t-text-[#707070]">{{ item.tagName }}</p>
      </div>

      <div>
        <p class="t-font-semibold">Explanation</p>
        <p class="t-text-[#707070]">{{ item.explanation }}</p>
      </div>
    </div>
  </div>
  }}}
</div>
