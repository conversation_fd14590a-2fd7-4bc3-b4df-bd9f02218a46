import { InlineWorker } from './inline-service-worker.extension'

// creates array to csv string
function createCsvString(this: InlineWorker): void {
  // use this fn in the worker so we can have worker context as `this`
  const self: InlineWorker = this

  const toCsv = (columns: string[], rows: unknown[]): any => {
    const arrays = rows.map((r: any) => Object.values(r))
    arrays.unshift(columns)

    const processRow = (row: any): string => {
      let finalVal = ''
      for (let j = 0; j < row.length; j++) {
        let innerValue = row[j] === null ? '' : row[j].toString()
        if (row[j] instanceof Date) {
          innerValue = row[j].toLocaleString()
        }
        let result = innerValue.replace(/"/g, '""')
        if (result.search(/([",\n])/g) >= 0) result = '"' + result + '"'
        if (j > 0) finalVal += ','
        finalVal += result
      }
      return finalVal + '\n'
    }

    const parts = []
    for (let i = 0; i < arrays.length; i++) {
      parts.push(processRow(arrays[i]))
    }

    const blob = new Blob([parts.join('')], { type: 'text/csv;charset=utf-8;' })

    // once all operation is done, post the processed data
    self.postMessage({
      processedData: blob,
    })
  }

  // post the input to process and return csv string
  self.onmessage = (e?: any): any => {
    toCsv(e?.data?.inputColumns, e?.data?.inputData)
    return null
  }
}

/**
 * Creates csv file using worker
 * the supplied values must be a correct format and order.
 *  * Took around 10 seconds with 4 column data to create 18,10,000 rows without blocking the UI thread.
 *  @example
 *  ```ts
 *   const columns = ['col1', 'col2']
 *   const rows = [ {col1: 'val1', col2: 'val2'}]
 *   new CsvBuilder('awesome-file-name').createCsvAndDownload(columns, rows)
 *  ```
 */
export class CsvBuilder {
  /**
   * Creates CSV with provided file name
   * @param fileName File name of CSV without `.csv` extension. e.g. `my-file-x`, `hello-file-name``
   */
  constructor(public fileName: string) {}

  private readonly download = (blob: Blob): void => {
    // if (navigator.msSaveBlob) {
    //   // IE 10+
    //   navigator.msSaveBlob(blob, this.fileName)
    // } else {
    const link = document.createElement('a')
    if (link.download !== undefined) {
      // feature detection
      // Browsers that support HTML5 download attribute
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `${this.fileName}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    // }
  }

  /**
   * Creates CSV string in background thread and automatically downloads it.
   * Column and row array length must be same.
   * Example: col = [c1,c2,c3] row = [{o1: 'x', o2: 'y', o3: 'z'}] is valid order.
   * @param columns columns [col1, col2]
   * @param rows Input array
   * @see CsvBuilder
   */
  public createCsvAndDownload = <T>(
    columns: string[],
    rows: T
  ): InlineWorker => {
    const worker = new InlineWorker(createCsvString)
    worker.postMessage({ inputColumns: columns, inputData: rows })
    const sub = worker.onmessage().subscribe({
      next: (d) => {
        this.download(d.data.processedData)
        worker.terminate()
        sub.unsubscribe()
      },
    })

    const erSub = worker.onerror().subscribe({
      next: (e) => {
        console.error(e.message)
        sub?.unsubscribe()
        worker.terminate()
        erSub.unsubscribe()
      },
    })

    return worker
  }
}
