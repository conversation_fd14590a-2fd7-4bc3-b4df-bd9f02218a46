import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingSummaryDialogComponent } from './tag-coding-summary-dialog.component'
import { StartupsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'

describe('TagCodingSummaryDialogComponent', () => {
  let component: TagCodingSummaryDialogComponent
  let fixture: ComponentFixture<TagCodingSummaryDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagCodingSummaryDialogComponent],
      providers: [provideMockStore({}), StartupsFacade],
    }).compileComponents()

    fixture = TestBed.createComponent(TagCodingSummaryDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
