import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  AfterViewInit,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  FilterDescriptor,
  CompositeFilterDescriptor,
} from '@progress/kendo-data-query'
import { FilterService } from '@progress/kendo-angular-grid'

@Component({
  selector: 'venio-casemulticheck-filter',
  standalone: true,
  imports: [CommonModule, InputsModule, FormsModule],
  templateUrl: './casemulticheck-filter.component.html',
  styleUrl: './casemulticheck-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CasemulticheckFilterComponent implements AfterViewInit {
  // TODO: Custom filter logic goes here for reference https://www.telerik.com/kendo-angular-ui/components/grid/filtering/filter-menu

  @Input() public currentFilter: CompositeFilterDescriptor

  @Input() public data: string[]

  @Input() public filterService: FilterService

  @Input() public field: string

  @Output() public readonly valueChange = new EventEmitter<number[]>()

  public currentData: string[]

  public showFilter = true

  private value: string[] = []

  public ngAfterViewInit(): void {
    this.currentData = this.data

    // Check if currentFilter and filters are defined
    if (
      this.currentFilter?.filters &&
      Array.isArray(this.currentFilter.filters)
    ) {
      this.value = this.currentFilter.filters
        .filter((f: FilterDescriptor) => f.value !== undefined) // Ensure value exists
        .map((f: FilterDescriptor) => f.value as string) // Map only valid values
    } else {
      // console.warn('Filters are not properly initialized in currentFilter.')
      this.value = [] // Default to an empty array
    }
  }

  public isItemSelected(item: string): boolean {
    return this.value.includes(item)
  }

  public onSelectionChange(item: string): void {
    if (this.isItemSelected(item)) {
      this.value = this.value.filter((x) => x !== item)
    } else {
      this.value.push(item)
    }

    this.filterService.filter({
      filters: this.value.map((value) => ({
        field: this.field,
        operator: 'eq',
        value,
      })),
      logic: 'or',
    })
  }

  public onInput(event: Event): void {
    const searchText = (event.target as HTMLInputElement).value.toLowerCase()
    this.currentData = this.data.filter((item) =>
      item.toLowerCase().includes(searchText)
    )
  }
}
