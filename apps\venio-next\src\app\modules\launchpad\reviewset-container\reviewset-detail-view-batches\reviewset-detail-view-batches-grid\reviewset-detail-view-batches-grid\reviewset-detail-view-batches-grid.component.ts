import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  output,
  signal,
  TrackByFunction,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  ColumnComponent as GridColumnComponent,
  GridComponent,
  HeaderTemplateDirective,
  NoRecordsTemplateDirective,
  SelectionDirective,
  CheckboxColumnComponent as GridCheckboxColumn,
  PageChangeEvent,
  GridDataResult,
  GridItem,
} from '@progress/kendo-angular-grid'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { toSignal } from '@angular/core/rxjs-interop'
import { ProjectFacade } from '@venio/data-access/common'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  ReviewSetBatchAction,
  ReviewSetBatchModel,
} from '@venio/shared/models/interfaces'
import { cloneDeep } from 'lodash'
import { map } from 'rxjs'
import { DebounceTimer } from '@venio/util/utilities'
import { ReviewsetDetailViewBatchesGridActionsComponent } from '../reviewset-detail-view-batches-grid-actions/reviewset-detail-view-batches-grid-actions.component'

@Component({
  selector: 'venio-reviewset-detail-view-batches-grid',
  standalone: true,
  imports: [
    CommonModule,
    GridComponent,
    SelectionDirective,
    GridColumnComponent,
    HeaderTemplateDirective,
    TooltipDirective,
    CellTemplateDirective,
    NoRecordsTemplateDirective,
    GridCheckboxColumn,
    SkeletonComponent,
    DynamicHeightDirective,
    ReviewsetDetailViewBatchesGridActionsComponent,
  ],
  templateUrl: './reviewset-detail-view-batches-grid.component.html',
  styleUrl: './reviewset-detail-view-batches-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewBatchesGridComponent
  implements OnInit, OnDestroy
{
  private readonly projectFacade = inject(ProjectFacade)

  private readonly injector = inject(Injector)

  public gridView = signal<GridDataResult>(undefined)

  /** Output event for the action invoked.
   * When the actions of grid e.g., reassign, rebatch and delete. are clicked, this event is emitted with type `ReviewSetBatchAction`
   */
  public readonly actionInvoked = output<ReviewSetBatchAction>()

  /** Default Page Size */
  public readonly pageSize = 15

  /** The number of records to skip when virtualization is enabled */
  public skip = 0

  /** Static common action types */
  public readonly commonActionTypes = CommonActionTypes

  /** Signal for the selected projectIds */
  public readonly selectedBatchIds = toSignal(
    this.projectFacade.selecteSelectedBatchDetail$.pipe(
      map(
        (selected: ReviewSetBatchModel[]) =>
          selected?.map((item) => item.uuid) || []
      )
    ),
    { initialValue: [] }
  )

  /** Signal for the review set batch loading state */
  public isReviewSetBatchLoading = toSignal(
    this.projectFacade.selectIsReviewSetBatchLoading$,
    { initialValue: true }
  )

  /** Signal for the review set batch list */
  private readonly reviewSetBatchDetail = toSignal(
    this.projectFacade.selectReviewSetBatchDetail$
  )

  /** Signal for the review batch info */
  private readonly reviewSetBatchRequestInfo = toSignal(
    this.projectFacade.selectReviewSetBatchRequestInfo$
  )

  /** Signal for the review set batch list */
  private readonly loadedReviewSetBatches = computed<ReviewSetBatchModel[]>(
    () => this.reviewSetBatchDetail() || []
  )

  /** Signal for the total reviewset batch count */
  private readonly totalReviewSetBatch = computed<number>(
    () => this.reviewSetBatchRequestInfo()?.totalReviewSetBatchCount || 0
  )

  /**
   * The grid data is rendered as virtual scroll, so when the user scrolls up or down,
   * the data are added or removed based on the pageSize and skip.
   *
   * To reflect actual changes in the UI, we need to track the data by the projectId.
   * @param {number} _ - The index of the item.
   * @param {GridItem} item - The grid item.
   * @returns {TrackByFunction<GridItem>} - The track by function.
   */
  public batchTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['batchId'] as TrackByFunction<GridItem>

  #loadReviewSetBatchDetail(): void {
    effect(
      () => {
        // If the review set batch list is loading, exit early;
        if (this.isReviewSetBatchLoading()) return

        untracked(() => this.loadGridData())
      },
      { injector: this.injector }
    )
  }

  public ngOnInit(): void {
    this.#loadReviewSetBatchDetail()
  }

  /** Handles the paging event for the virtual scroll to avoid loading all the data at once
   * stressing the UI performance. Instead, we load the data in chunks.
   *
   * @see loadGridData
   * @see pageSize
   * @see skip
   *
   * @param {PageChangeEvent} event - The paging event.
   * @returns {void}
   */
  public handlePagingForVirtualScroll(event: PageChangeEvent): void {
    this.skip = event.skip
    this.loadGridData()
  }

  /** Loads the grid data based on the skip and pageSize
   * @see skip
   * @see pageSize
   * @see loadedReviewSets
   * @returns {void}
   */
  private loadGridData(): void {
    const allReviewSetBatch = cloneDeep(this.loadedReviewSetBatches())
    this.gridView.set({
      data: allReviewSetBatch.slice(this.skip, this.skip + this.pageSize),
      total: this.totalReviewSetBatch(),
    })
  }

  public selectReviewSetBatch(event: string[]): void {
    const selectedBatch = this.loadedReviewSetBatches().filter((item) =>
      event.includes(item.uuid)
    )
    this.projectFacade.setSelectedBatchDetail(selectedBatch)
  }

  @DebounceTimer(200)
  public forwardActionControlClick(
    actionType: CommonActionTypes,
    content: ReviewSetBatchModel
  ): void {
    this.actionInvoked.emit({ actionType, content })
  }

  #resetReviewSetBatchStates(): void {
    this.projectFacade.resetProjectState([
      'reviewSetBatchSuccess',
      'reviewSetBatchError',
      'isReviewSetBatchLoading',
      'reviewSetBatchRequestInfo',
      'selectedBatchDetail',
    ])
  }

  public ngOnDestroy(): void {
    this.#resetReviewSetBatchStates()
  }
}
