import { Component } from '@angular/core';
import { mockDocumentTypes } from '../sunburst/mockData';
import { sunburstAnnotationColors } from '../../constants/colors';
import { <PERSON><PERSON><PERSON><PERSON>, NgFor } from '@angular/common';

@Component({
  selector: 'venio-vertical-legends',
  standalone: true,
  imports: [Ng<PERSON>tyle, NgFor],
  templateUrl: './vertical-legends.component.html',
  styleUrl: './vertical-legends.component.scss'
})
export class VerticalLegendsComponent {
  public legends: string[] = mockDocumentTypes.map(docuType => docuType.category);
  public chartColors: string[] = sunburstAnnotationColors;
}
