import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { EditorModule } from '@progress/kendo-angular-editor'
import { PopoverModule, TooltipsModule } from '@progress/kendo-angular-tooltip'
import { RadioButtonModule } from '@progress/kendo-angular-inputs'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { SearchDupOption, UserRights } from '@venio/data-access/review'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { ButtonComponent } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-document-duplicate-options',
  standalone: true,
  imports: [
    CommonModule,
    EditorModule,
    PopoverModule,
    RadioButtonModule,
    ReactiveFormsModule,
    SVGIconModule,
    SvgLoaderDirective,
    LoaderModule,
    TooltipsModule,
    UserGroupRightCheckDirective,
    ButtonComponent,
  ],
  templateUrl: './document-duplicate-options.component.html',
  styleUrls: ['./document-duplicate-options.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentDuplicateOptionsComponent {
  @Input({ required: true })
  public searchFormGroup: FormGroup

  @Input({ required: true })
  public defatulSearchDupOption: SearchDupOption

  public chevronDown = chevronDownIcon

  public searchDuplicateOptionCollections = [
    {
      text: 'Show all hits in the selected scope (No DeDupe)',
      value: SearchDupOption.SHOW_ALL_DUPS,
      allowedPermission: [UserRights.ALLOW_SHOW_ALL_HITS_IN_SELECTED_SCOPE],
    },
    {
      text: 'Show only one instance in the selected scope (DynamicDeDupe™)',
      value: SearchDupOption.HIDE_ALL_DUPS_DYNAMIC,
      allowedPermission: [
        UserRights.ALLOW_SHOW_ONLY_ONE_INSTANCE_IN_SELECTED_SCOPE,
      ],
    },
    {
      text: 'Show only one instance per custodian in the selected scope (DynamicDeDupe™)',
      value: SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_DYNAMIC,
      allowedPermission: [
        UserRights.ALLOW_SHOW_ONLY_ONE_INSTANCE_PER_CUSTODIAN_IN_SELECTED_SCOPE,
      ],
    },
    {
      text: 'Hide project level duplicates (StaticDeDupe™)',
      value: SearchDupOption.HIDE_PROJECT_LEVEL_DUPS_STATIC,
      allowedPermission: [UserRights.ALLOW_HIDE_PROJECT_LEVEL_DUPLICATES],
    },
    {
      text: 'Hide custodian level duplicates (StaticDeDupe™)',
      value: SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_STATIC,
      allowedPermission: [UserRights.ALLOW_HIDE_CUSTODIAN_LEVEL_DUPLICATES],
    },
  ]
}
