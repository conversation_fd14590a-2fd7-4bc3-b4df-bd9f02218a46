import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerSortComponent } from './document-view-designer-sort.component'
import { provideMockStore } from '@ngrx/store/testing'
import { FieldFacade } from '@venio/data-access/review'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentViewDesignerSortComponent', () => {
  let component: DocumentViewDesignerSortComponent
  let fixture: ComponentFixture<DocumentViewDesignerSortComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerSortComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        FieldFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerSortComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
