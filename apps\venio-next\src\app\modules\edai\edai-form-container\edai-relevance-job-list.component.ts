import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  WritableSignal,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { AiFacade, AIRelevanceJobsResponseModel } from '@venio/data-access/ai'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-edai-relevance-job-list',
  standalone: true,
  imports: [CommonModule, TreeViewModule, TooltipModule, SvgLoaderDirective],
  templateUrl: './edai-relevance-job-list.component.html',
  styleUrl: './edai-relevance-job-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiRelevanceJobListComponent {
  private aiFacade = inject(AiFacade)

  public showHistory: WritableSignal<boolean> = signal<boolean>(true)

  public relevanceCompletedJobs = input<ResponseModel>(undefined)

  public onloadJobDetail(jobData: AIRelevanceJobsResponseModel): void {
    this.showHistory.set(true)
    this.aiFacade.loadRelevanceJob$.next(jobData)
  }

  public toggleHistory(): void {
    this.showHistory.set(!this.showHistory())
  }
}
