import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchLogHistoryComponent } from './search-log-history.component'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideAnimations } from '@angular/platform-browser/animations'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchLogHistoryComponent', () => {
  let component: SearchLogHistoryComponent
  let fixture: ComponentFixture<SearchLogHistoryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchLogHistoryComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: BreadcrumbFacade,
          useValue: jest.fn(),
        },
        SearchFacade,
        FieldFacade,
        NotificationService,
        DialogRef,
        provideAnimations(),
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchLogHistoryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
