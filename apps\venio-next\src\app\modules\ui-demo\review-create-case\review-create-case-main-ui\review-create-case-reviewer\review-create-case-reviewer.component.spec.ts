import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseReviewerComponent } from './review-create-case-reviewer.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseReviewerComponent', () => {
  let component: ReviewCreateCaseReviewerComponent
  let fixture: ComponentFixture<ReviewCreateCaseReviewerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseReviewerComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseReviewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
