import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnDestroy,
  OnInit,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DialogService } from '@progress/kendo-angular-dialog'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { filter, Subject, takeUntil } from 'rxjs'
import { SearchRequestModel, StartupsFacade } from '@venio/data-access/review'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { CommandsFacade } from '@venio/data-access/common'
import { CommandEventTypes } from '@venio/shared/models/constants'
import { CommandEvent } from '@venio/shared/models/interfaces'
import { FormGroup } from '@angular/forms'

@Component({
  selector: 'venio-document-additional-actions',
  standalone: true,
  imports: [CommonModule, ButtonModule, LoaderModule],
  templateUrl: './document-additional-actions.component.html',
  styleUrls: ['./document-additional-actions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentAdditionalActionsComponent implements OnDestroy, OnInit {
  private readonly toDestroy$ = new Subject<void>()

  @Input({ required: true })
  public searchFormGroup: FormGroup

  private dialogService = inject(DialogService)

  private changeDetectorRef = inject(ChangeDetectorRef)

  private viewContainerRef = inject(ViewContainerRef)

  private iframeMessengerService = inject(IframeMessengerService)

  private iframeMessengerFacade = inject(IframeMessengerFacade)

  private startupsFacade = inject(StartupsFacade)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private commandsFacade = inject(CommandsFacade)

  public isAdvancedSearchLoading: boolean

  private shouldLoadAsMicroApp = false

  private searchParams: Partial<SearchRequestModel>

  private breadcrumbQuery: string

  private selectedCommandEventPayload: CommandEvent

  public ngOnInit(): void {
    this.#selectSearchParams()
    this.#selectLoadedAsMicroApp()
    this.#selectBreadCrumbQuery()
    this.#selectLaunchAdvancedSearchCommand()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Launches the advanced search UI.
   * When the flag `shouldResetSelectedCommand` is set to `true`, it resets the selected command event.
   * @param {boolean} shouldResetSelectedCommand - A flag to reset the selected command event.
   * @returns {void}
   */
  public launchAdvancedSearch(shouldResetSelectedCommand = true): void {
    if (this.shouldLoadAsMicroApp) {
      if (shouldResetSelectedCommand) {
        this.selectedCommandEventPayload = undefined
      }
      this.#notifyLaunchAdvancedSearch()
    } else {
      this.#launchAdvancedSearch()
    }
  }

  #selectBreadCrumbQuery(): void {
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((syntax) => {
        this.breadcrumbQuery = syntax
      })
  }

  #selectSearchParams(): void {
    this.startupsFacade.getSearchParams$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((searchParams) => {
        this.searchParams = searchParams
      })
  }

  #notifyLaunchAdvancedSearch(): void {
    const { searchDuplicateOption, includePC } =
      this.searchFormGroup.getRawValue()

    this.iframeMessengerService.sendMessage({
      payload: {
        type: MessageType.SEARCH_CHANGE,
        content: {
          launchAdvancedSearchUi: true,
          searchParams: this.searchParams,
          breadcrumbQuery: this.breadcrumbQuery,
          selectedCommandEventPayload: this.selectedCommandEventPayload,
          includePC,
          searchDuplicateOption,
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  #launchAdvancedSearch(): void {
    this.changeDetectorRef.markForCheck()
    this.isAdvancedSearchLoading = true
    import(
      '../document-advanced-search-container/document-advanced-search-container.component'
    ).then(({ DocumentAdvancedSearchContainerComponent }) => {
      this.changeDetectorRef.markForCheck()
      this.isAdvancedSearchLoading = false
      this.dialogService.open({
        content: DocumentAdvancedSearchContainerComponent,
        appendTo: this.viewContainerRef,
        width: 'calc(100vw - 5rem)',
        height: 'calc(100vh - 5rem)',
      })
    })
  }

  #selectLoadedAsMicroApp(): void {
    this.iframeMessengerFacade.selectLoadedAsMicroApp$
      .pipe(
        filter((loadedAsMicroApp) => typeof loadedAsMicroApp === 'boolean'),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isMicroApp) => {
        this.shouldLoadAsMicroApp = isMicroApp
      })
  }

  /**
   * Resets the command state for launching advanced search.
   * @returns {void}
   */
  #resetAdvancedSearchLaunchCommand(): void {
    this.commandsFacade.resetCommandState(
      CommandEventTypes.NotifyLaunchAdvancedSearch
    )
  }

  /**
   * Subscribes to the `selectNotifyLaunchAdvancedSearch$`
   * observable and initiates an advanced search when the command event is triggered.
   * Once the advanced search is launched, it resets the command state to its initial state.
   * @returns {void}
   */
  #selectLaunchAdvancedSearchCommand(): void {
    this.commandsFacade.selectNotifyLaunchAdvancedSearch$
      .pipe(
        filter((event) => Boolean(event?.['isLaunch'])),
        takeUntil(this.toDestroy$)
      )
      .subscribe((event: CommandEvent) => {
        this.selectedCommandEventPayload = event
        this.launchAdvancedSearch(false)
        // Once we have launched the advanced search, reset the command state to its initial state
        this.#resetAdvancedSearchLaunchCommand()
      })
  }
}
