<kendo-dialog-titlebar (close)="close()">
  <div>
    {{ dialogTitle }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-w-full t-mt-3">
  <div class="t-w-full">
    <div class="t-flex t-flex-col t-gap-4 t-w-full">
      <div class="t-flex">
        <kendo-multiselect
          [filterable]="true"
          [clearButton]="false"
          [virtual]="true"
          [listHeight]="300"
          [checkboxes]="true"
          [autoClose]="false"
          [tagMapper]="tagMapper"
          [value]="selectedReports()"
          #reportSelection
          (filterChange)="filterReports($event)"
          (valueChange)="reportTypeChange($event)"
          class="t-min-w-[18rem] t-max-w-[18rem]"
          [data]="filteredReports()"
          (removeTag)="removeTag($event)"
          [valuePrimitive]="true"
          valueField="id"
          textField="label">
          <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
            <div
              class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50"
              (click)="handleAllReportClick($event, allReport)">
              <input
                [checked]="isAllReportChecked()"
                (click)="handleAllReportClick($event, allReport)"
                (change)="allReportSelectionChange($event.target['checked'])"
                type="checkbox"
                #allReport
                id="all-report"
                kendoCheckBox />
              <kendo-label
                (click)="handleAllReportClick($event, allReport)"
                class="k-checkbox-label t-w-full t-h-full"
                for="all-report"
                text="All Reports"></kendo-label>
            </div>
          </ng-template>
          <ng-template kendoSuffixTemplate>
            <kendo-svg-icon
              class="t-cursor-pointer"
              (click)="chevDownIconClick(reportSelection)"
              [icon]="chevronDownIcon"></kendo-svg-icon>
          </ng-template>
          <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
            {{ dataItems.length }} report(s) selected
          </ng-template>
        </kendo-multiselect>
      </div>

      <div class="t-flex t-flex-col">
        <div id="reportGrid" class="t-flex t-mt-4 t-flex-col t-w-full">
          <div
            *ngIf="isPrint"
            class="t-flex t-w-full t-absolute t-top-[40px] t-z-[9999] t-left-[25px]">
            {{ dialogTitle }}
          </div>
          <kendo-grid
            #grid
            [kendoGridBinding]="gridData"
            [loading]="isDataLoading()"
            [pageSize]="pageSize"
            [pageable]="pageable"
            [sortable]="true"
            [groupable]="false"
            [reorderable]="true"
            [resizable]="true"
            [skip]="skip"
            [trackBy]="transcriptReportTrackByFn"
            [ngClass]="{
              't-pt-[70px] !t-border-0 t-mx-5 v-custom-grid-print': isPrint
            }">
            <ng-template kendoPagerTemplate>
              <div class="t-flex t-gap-2"></div>
              <kendo-grid-spacer></kendo-grid-spacer>

              <div class="t-flex t-gap-3">
                <kendo-dropdownbutton
                  [data]="printOptions"
                  [svgIcon]="printIcon"
                  (itemClick)="print($event)">
                  <kendo-svg-icon [icon]="downIcon"></kendo-svg-icon>

                  <ng-template kendoDropDownButtonItemTemplate let-dataItem>
                    <kendo-svgicon [icon]="dataItem.svgIcon"></kendo-svgicon>
                    <span>{{ dataItem.text }}</span>
                  </ng-template>
                </kendo-dropdownbutton>
              </div>
              <venio-pagination
                [disabled]="selectedReportCount() === 0"
                [totalRecords]="selectedReportCount()"
                [pageSize]="pageSize"
                [showPageJumper]="false"
                [showPageSize]="true"
                [showRowNumberInputBox]="true"
                (pageChanged)="pageChanged($event)"
                (pageSizeChanged)="pageSizeChanged($event)"
                class="t-px-5 t-block t-py-2">
              </venio-pagination>
            </ng-template>
            <kendo-grid-column
              [width]="130"
              field="action"
              title="Action"
              headerClass="t-text-primary"></kendo-grid-column>
            <kendo-grid-column
              [width]="150"
              field="rangeNumber"
              title="Line Number"
              headerClass="t-text-primary"></kendo-grid-column>
            <kendo-grid-column
              title="Selected Text"
              headerClass="t-text-primary">
              <ng-template kendoGridCellTemplate let-dataItem>
                <span
                  [ngClass]="{
                    't-align-middle t-leading-none t-inline-block t-max-w-[100%] t-truncate':
                      !isPrint
                  }"
                  kendoTooltip
                  position="top"
                  [title]="
                    dataItem['lineNumber'] + ' ' + dataItem['description']
                  "
                  >{{ dataItem['lineNumber'] }}
                  {{ dataItem['description'] }}</span
                >
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              *ngIf="canShowNotes()"
              field="notes"
              title="Notes"
              headerClass="t-text-primary">
              <ng-template kendoGridCellTemplate let-dataItem>
                <span
                  [ngClass]="{
                    't-align-middle t-leading-none t-inline-block t-max-w-[100%] t-truncate':
                      !isPrint
                  }"
                  kendoTooltip
                  position="top"
                  [title]="dataItem['notes']"
                  >{{ dataItem['notes'] }}</span
                >
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              *ngIf="canShowFileDetail()"
              [width]="120"
              field="fileId"
              title="File Id"
              headerClass="t-text-primary"></kendo-grid-column>
            <kendo-grid-column
              *ngIf="canShowFileDetail()"
              field="fileName"
              title="File Name"
              headerClass="t-text-primary"></kendo-grid-column>
          </kendo-grid>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="close()"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
