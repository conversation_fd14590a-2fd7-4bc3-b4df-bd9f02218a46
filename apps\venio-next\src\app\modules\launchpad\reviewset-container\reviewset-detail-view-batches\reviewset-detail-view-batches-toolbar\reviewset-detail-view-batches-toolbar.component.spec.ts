import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewBatchesToolbarComponent } from './reviewset-detail-view-batches-toolbar.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { ReviewSetBatchRequestModel } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('ReviewsetDetailViewBatchesToolbarComponent', () => {
  let component: ReviewsetDetailViewBatchesToolbarComponent
  let fixture: ComponentFixture<ReviewsetDetailViewBatchesToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewBatchesToolbarComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selecteSelectedBatchDetail$: of([]),
            selectReviewSetBatchDetail$: of([]),
            selectIsReviewSetBatchLoading$: of(false),
            selectReviewSetBatchRequestInfo$: of(
              {} as ReviewSetBatchRequestModel
            ),
            resetProjectState: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      ReviewsetDetailViewBatchesToolbarComponent
    )
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
