import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetGridComponent } from './reviewset-grid.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { ProjectFacade } from '@venio/data-access/common'
import {
  CaseDetailResponseModel,
  ResponseModel,
  ReviewSetSummary,
} from '@venio/shared/models/interfaces'
import { of } from 'rxjs'
import { StartupsFacade } from '@venio/data-access/review'

describe('CaseLaunchpadSubGridComponent', () => {
  let component: ReviewsetGridComponent
  let fixture: ComponentFixture<ReviewsetGridComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetGridComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectSelectedCaseDetail$: of([]),
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({} as CaseDetailResponseModel),
            selectIsReviewSetSummaryDetailLoading$: of(false),
            selectReviewSetSummaryDetail$: of({} as ReviewSetSummary),
            resetProjectState: jest.fn(),
            selectReviewSetDeleteError$: of({} as ResponseModel),
            selectReviewSetDeleteSuccess$: of({} as ResponseModel),
            selectIsReviewSetDeleteLoading$: of(''),
            deleteReviewSet: jest.fn(),
            selectProjectIdsToRights$: of({}),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: StartupsFacade,
          useValue: {
            hasGroupRight$: jest.fn(),
          } satisfies Partial<StartupsFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetGridComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
