import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  LayoutModule,
  ExpansionPanelModule,
  SelectEvent,
} from '@progress/kendo-angular-layout'
import { plusIcon, trashIcon, xIcon } from '@progress/kendo-svg-icons'
import { ListBoxModule } from '@progress/kendo-angular-listbox'
import { Employee, employeestree } from '../edit-tags-coding/employeetree'
import { GridModule } from '@progress/kendo-angular-grid'

import {
  SelectableSettings,
  SelectionItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

export interface FakeDataSchema {
  id: number
  name: string
  managerId: number | null
}

@Component({
  selector: 'venio-document-add-new-view-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    InputsModule,
    SvgLoaderDirective,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    IconsModule,
    ExpansionPanelModule,
    ListBoxModule,
    GridModule,
    TreeListModule,
  ],
  templateUrl: './document-add-new-view-dialog.component.html',
  styleUrls: ['./document-add-new-view-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentAddNewViewDialogComponent implements OnInit {
  public opened = false

  // this needs to be changed when using iteration for the blocks
  public block = 0

  public dialogTitle = 'New View'

  public fakeData: FakeDataSchema[] = [
    { id: 101, name: 'John Doe', managerId: null },
    { id: 102, name: 'Jane Smith', managerId: null },
    { id: 103, name: 'Bob Johnson', managerId: null },
    { id: 104, name: 'Alice Williams', managerId: null },
    { id: 105, name: 'Charlie Brown', managerId: null },
    { id: 106, name: 'Eva Davis', managerId: null },
    { id: 107, name: 'Frank Miller', managerId: null },
    { id: 108, name: 'Grace Taylor', managerId: null },
    { id: 109, name: 'Henry Wilson', managerId: null },
    { id: 110, name: 'Ivy Martinez', managerId: null },
  ]

  public columns: any[] = [
    { field: 'id', title: 'ID', headerClass: 'hidden-header' },
    { field: 'name', title: 'Name' },
    { field: 'managerId', title: 'Manager ID' },
  ]

  public settings: SelectableSettings = {
    enabled: true,
    mode: 'row',
    multiple: true,
    drag: false,
    readonly: false,
  }

  public selected: SelectionItem[] = []

  public menuData = [
    {
      text: 'AND',
    },
    {
      text: 'OR',
    },
    {
      text: 'NOT',
    },
  ]

  public icons = {
    plusIconSvg: plusIcon,
    closeIcon: xIcon,
    trashIconSvg: trashIcon,
  }

  public data: Employee[] = employeestree

  public sampleData = [
    {
      Id: 'ALFKI',
      CompanyName: 'Demo_master-Site Admin Group',
      ContactName: 'Maria Anders',
      ContactTitle: 'Sales Representative',
      City: 'Berlin',
    },
    {
      Id: 'ANATR',
      CompanyName: 'Demo_master-Project Admin Group',
      ContactName: 'Ana Trujillo',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'ANTON',
      CompanyName: 'Demo_master-User Group',
      ContactName: 'Antonio Moreno',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
  ]

  public europeanCountries: string[] = [
    'Germany',
    'France',
    'Austria',
    'Belgium',
    'Denmark',
    'Netherlands',
  ]

  public asianCountries: string[] = [
    'China',
    'India',
    'Indonesia',
    'Nepal',
    'Thailand',
    'Yemen',
  ]

  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'ASC',
    value: null,
  }

  public defaultItem: { text: string; value: number } = {
    text: 'ADD (OR)',
    value: null,
  }

  public listItems: Array<{ text: string; value: number }> = [
    { text: 'ADD', value: 1 },
    { text: 'OR', value: 2 },
  ]

  public ngOnInit(): void {
    this.openDialog()
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public openDialog(): void {
    this.opened = true
  }

  public onTabSelect(e: SelectEvent): void {
    console.log('Selected index: ' + e.index)
  }
}
