import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SVGIcon, chevronLeftIcon } from '@progress/kendo-svg-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule, SelectEvent } from '@progress/kendo-angular-layout'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { LabelModule } from '@progress/kendo-angular-label'
import { PageControlActionType } from '../shared/enum/page-control-action-type.enum'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-review-page-ui',
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    ButtonsModule,
    InputsModule,
    LayoutModule,
    TreeViewModule,
    LabelModule,
  ],
  templateUrl: './review-page-ui.component.html',
  styleUrls: ['./review-page-ui.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewPageUiComponent {
  public chevronLeftIcon: SVGIcon = chevronLeftIcon

  public svgIconForPageControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: PageControlActionType.PREV_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: PageControlActionType.LAST_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  public svgIconForPageDetail = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-toolbar-h.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-toolbar-image.svg',
    },
    {
      actionType: PageControlActionType.PREV_PAGE,
      iconPath: 'assets/svg/icon-toolbar-stack.svg',
    },
    {
      actionType: PageControlActionType.LAST_PAGE,
      iconPath: 'assets/svg/icon-toolbar-search.svg',
    },
  ]

  public svgIconForTagsToolbar = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-review-tag.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-review-paper.svg',
    },
    {
      actionType: PageControlActionType.PREV_PAGE,
      iconPath: 'assets/svg/icon-review-printer.svg',
    },
    {
      actionType: PageControlActionType.LAST_PAGE,
      iconPath: 'assets/svg/icon-review-share.svg',
    },
  ]

  public svgIconForTagControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: PageControlActionType.VIEW_DETAIL,
      iconPath: 'assets/svg/icon-review-eye.svg',
    },
    {
      actionType: PageControlActionType.SAVE_DETAIL,
      iconPath: 'assets/svg/icon-review-save.svg',
    },
    {
      actionType: PageControlActionType.ADD_DETAIL,
      iconPath: 'assets/svg/icon-review-plus.svg',
    },
  ]

  // for tree view of tags
  public expandedKeys: any[] = ['0', '1']

  public checkedKeys: any[] = ['0_1']

  public filterTerm = ''

  public data: any[] = [
    {
      text: 'Furniture',
      items: [
        { text: 'Tables & Chairs' },
        { text: 'Sofas' },
        { text: 'Occasional Furniture' },
      ],
    },
    {
      text: 'Decor',
      items: [
        { text: 'Bed Linen' },
        { text: 'Curtains & Blinds' },
        { text: 'Carpets' },
      ],
    },
  ]

  public browseActionClicked(
    actionType: any //'FIRST_PAGE' | 'NEXT_PAGE' | 'PREV_PAGE' | 'LAST_PAGE'
  ): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }

  public onTabSelect(e: SelectEvent): void {
    // action on any tab click
  }
}
