<div class="t-flex t-w-full t-h-full t-overflow-hidden" #mainContent>
  <div
    #sidebar
    class="t-flex t-flex-col t-w-16 t-transition-all t-border t-border-[#ccc] t-border-l-0 t-border-b-0 t-border-t-0 t-border-r-1 t-h-max t-min-h-full t-overflow-y-auto t-relative"
    venioDynamicHeight
    [ngClass]="{ '!t-w-[230px]': menuState }">
    <div
      class="t-flex t-p-4 t-border t-border-[#cccccc] t-border-r-0 t-border-l-0 t-border-t-0 t-border-b-1 t-sticky t-top-0 t-bg-white t-z-10">
      <button
        kendoButton
        fillMode="clear"
        class="t-p-0 t-bg-white"
        (click)="this.toggleMenuState()">
        <img src="assets/svg/icon-slide-menu-left.svg" alt="Open Menu" />
      </button>
    </div>

    <div class="t-flex t-flex-col" *ngIf="menuState">
      <kendo-expansionpanel
        *ngFor="let item of items; trackBy: trackById; index as i"
        [title]="item.text"
        [expanded]="item.expanded"
        class="v-custom-expansionpanel-trans t-flex t-w-full t-items-center t-mt-0"
        (action)="onAction(i)">
        <ng-template kendoExpansionPanelTitleDirective>
          <div
            class="t-flex t-w-full t-justify-between t-px-4 t-py-2 t-border t-border-[#cccccc] t-border-t-0 t-border-l-0 t-border-r-0 t-border-b-1 t-items-center t-text-[#1EBADC] t-font-medium">
            <span>{{ item.text }}</span>
            <kendo-svgicon
              [icon]="icons.chevronRightIcon"
              class="t-text-[#000000]"
              [ngClass]="{ 't-rotate-90': item.expanded }"></kendo-svgicon>
          </div>
        </ng-template>
        <div class="content t-flex">
          <ng-container
            *ngTemplateOutlet="getTemplate(item.templateName)"></ng-container>
        </div>
      </kendo-expansionpanel>
    </div>
  </div>

  <div
    class="t-flex t-flex-col t-flex-1 t-w-full t-overflow-hidden"
    #transcriptViewerContent>
    <div
      class="t-flex t-w-full t-justify-between t-p-2 t-px-4 t-h-[53px] t-border-b t-border-b-[#cccccc] t-sticky t-top-0">
      <div class="t-flex t-gap-2 t-h-[33px]">
        <!--This logic will be added later-->
        <!-- <kendo-dropdownlist
        defaultItem="Roboto"
        [data]="listItems"
        [valuePrimitive]="true"
        class="t-w-52">
      </kendo-dropdownlist>

      <kendo-dropdownlist
        defaultItem="14"
        [data]="sizeItems"
        [valuePrimitive]="true"
        class="t-w-20">
      </kendo-dropdownlist>
      <kendo-dropdownlist
        defaultItem="Normal"
        [data]="weightItems"
        [valuePrimitive]="true"
        class="t-w-28">
      </kendo-dropdownlist> -->
      </div>

      <div class="t-block t-relative">
        <kendo-dropdownbutton
          *ngIf="!isLoading"
          [data]="reportItems"
          (itemClick)="reportTypeChange($event)"
          valueField="id"
          textField="label"
          fillMode="clear"
          imageUrl="assets/svg/icon-material-graph-box.svg">
        </kendo-dropdownbutton>
      </div>
    </div>
    <!-- FOR DEMO: context menu for add note, highlight & linked documents
    width: 'calc(100% - ' + transcriptViewerContent.offsetWidth + 'px)'
    -->
    <div class="t-block t-p-0 t-w-full">
      <div #target>
        <div *ngIf="isLoading; else noTranscriptTemplate">
          <kendo-loader
            *ngIf="isLoading"
            size="medium"
            type="pulsing"></kendo-loader>
        </div>
        <ng-template #noTranscriptTemplate>
          <div
            *ngIf="!(transcriptItems?.length > 0)"
            class="t-grid t-py-3 t-place-content-center t-text-[#cccccc]">
            No transcript available
          </div>
        </ng-template>
        <div
          class="t-w-full"
          venioTextSelectEvent
          (textSelectEvent)="renderRectangles($event)"
          [hidden]="!(transcriptItems?.length > 0)">
          <cdk-virtual-scroll-viewport
            #cdkviewport
            itemSize="20"
            minBufferPx="600"
            maxBufferPx="800"
            venioHighlight
            [highlight]="wordWheelText?.value"
            [ngStyle]="{
              height: 'calc(' + mainContent.offsetHeight + 'px - 10px)'
            }"
            class="t-w-full">
            <div
              *cdkVirtualFor="let item of dataArray; let i = index"
              class="t-w-full first:t-pt-3">
              <div class="t-block t-w-full t-flex">
                <div class="t-flex t-w-[90px] t-gap-[0.3rem] t-pt-2">
                  <div>
                    {{ item.lineNumber === 1 ? item.pageNumber : '&nbsp;' }}
                  </div>
                  <div>{{ item.lineNumber === 1 ? ':' : '&ensp;' }}</div>
                  <div>{{ item.lineNumber.toString().padStart(2, '0') }}</div>
                </div>

                <div
                  class="t-flex-1 t-border-l-[1px] t-pl-3 t-border-[#CCCCCC] t-pt-2"
                  [id]="item.pageNumber + ':' + item.lineNumber"
                  [innerHTML]="item.text"></div>
              </div>
            </div>
          </cdk-virtual-scroll-viewport>
        </div>
      </div>

      <kendo-contextmenu
        #contextmenu
        [target]="target"
        [items]="contextMenuItems"
        (select)="onContextMenuSelect($event)">
        <ng-template kendoMenuItemTemplate let-item="item">
          <!-- <kendo-svgicon
            [icon]="item.icon"
            class="k-icon k-i-{{ item.icon }}"></kendo-svgicon> -->
          <span [ngClass]="{ 'k-disabled': item.disabled }">{{
            item.text.toLowerCase().indexOf('highlight') > -1
              ? highlightContextMenuText()
              : item.text.toLowerCase().indexOf('document') > -1
              ? linkDocumentContextMenu()
              : item.text.toLowerCase().indexOf('note') > -1
              ? addNoteContextMenu()
              : item.text
          }}</span>
        </ng-template>
      </kendo-contextmenu>
    </div>
  </div>
</div>

<ng-template #transcriptContent>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <kendo-listview
      [data]="transcriptItems"
      class="t-border-0 v-custom-listview-transcript v-hide-scrollbar"
      venioDynamicHeight
      [useMaxHeight]="true">
      <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
        <div
          class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer"
          [ngClass]="{
            't-bg-[#F7F7F7]': selectedTranscriptId === dataItem.Id
          }"
          (click)="onTranscriptItemClick(dataItem)">
          {{ dataItem.Name }}
        </div>
      </ng-template>
    </kendo-listview>
  </div>
</ng-template>

<ng-template #linkedDocuments>
  <div class="t-flex t-flex-col t-flex-1 t-w-full t-overflow-hidden">
    <ng-container
      *ngIf="transcriptState.linkedDocuments().length > 0; else noDataTemplate">
      <kendo-listview
        [data]="transcriptState.linkedDocuments()"
        class="t-border-0 v-custom-listview-transcript t-max-h-[20rem] v-hide-scrollbar">
        <ng-template
          kendoListViewItemTemplate
          let-dataItem="dataItem"
          let-i="index">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer t-group t-relative">
            <span
              class="t-flex-1 t-mr-2 t-max-w-[90%]"
              (click)="openLinkDocument(dataItem)">
              {{ dataItem.id }} - {{ dataItem.fileName }}
            </span>
            <button
              kendoButton
              fillMode="clear"
              (click)="deleteDocument(dataItem)"
              class="t-p-0 t-hidden t-absolute t-right-2 t-top-0 t-w-10 t-h-10 t-place-content-center group-hover:!t-grid">
              <img
                src="assets/svg/Icon-material-delete.svg"
                class="t-w-3.5 t-h-3.5"
                alt="Delete" />
            </button>
          </div>
        </ng-template>
      </kendo-listview>
    </ng-container>
  </div>
</ng-template>

<ng-template #wordWheel>
  <div
    class="t-flex t-flex-col t-flex-1 t-w-full t-max-h-[20rem] t-overflow-auto">
    <div class="t-flex t-pt-2">
      <!-- kendo textbox with placeholder -->
      <kendo-textbox
        placeholder="Search"
        [formControl]="wordWheelText"
        class="t-w-full t-mx-4 t-my-2"></kendo-textbox>
    </div>

    <div class="t-flex t-flex-col t-py-2 t-px-4 t-font-medium">
      <ng-container *ngIf="wheelItems.length > 0; else noDataTemplate">
        <kendo-chiplist size="small">
          <kendo-chip
            *ngFor="let item of wheelItems; trackBy: trackById"
            [label]="item.label"
            kendoTooltip
            [title]="item.words"
            (click)="item.action(item.id)">
          </kendo-chip>
        </kendo-chiplist>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #highlightDocs>
  <div
    class="t-flex t-flex-col t-flex-1 t-w-full t-max-h-[20rem] t-overflow-auto">
    <ng-container *ngIf="items.length > 0; else noDataTemplate">
      <cdk-virtual-scroll-viewport
        #cdkviewportHighlight
        appendOnly
        itemSize="20"
        minBufferPx="200"
        maxBufferPx="400"
        class="viewport v-hide-scrollbar"
        venioDynamicHeight>
        <div
          *cdkVirtualFor="
            let dataItem of transcriptState.highlights();
            let i = index
          ">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer t-group t-relative">
            <span
              class="t-w-[0.4rem] t-h-[1.23rem] t-rounded-[2px] t-mr-1 t-hidden t-absolute t-left-3 group-hover:t-inline-block"
              [style.background-color]="dataItem.color"></span>
            <span
              class="t-flex-1 t-mr-2 t-max-w-[90%]"
              (click)="onActionPerformed('SCROLL', dataItem)"
              >{{ dataItem.id }}</span
            >
            <button
              (click)="onActionPerformed('DELETE_HIGHLIGHT', dataItem)"
              kendoButton
              fillMode="clear"
              class="t-p-0 t-hidden t-absolute t-right-2 t-top-0 t-w-10 t-h-[2.2rem] t-place-content-center group-hover:!t-grid">
              <span
                venioSvgLoader
                svgUrl="assets/svg/Icon-material-delete.svg"
                height="0.9rem"
                width="0.8rem">
              </span>
            </button>
          </div>
        </div>
      </cdk-virtual-scroll-viewport>
    </ng-container>
  </div>
</ng-template>

<ng-template #addNote>
  <div
    class="t-flex t-flex-col t-flex-1 t-w-full t-max-h-[20rem] t-overflow-auto">
    <ng-container
      *ngIf="transcriptState.notes().length > 0; else noDataTemplate">
      <cdk-virtual-scroll-viewport
        #cdkviewportHighlight
        appendOnly
        itemSize="20"
        minBufferPx="200"
        maxBufferPx="400"
        class="viewport v-hide-scrollbar"
        venioDynamicHeight>
        <div
          *cdkVirtualFor="
            let dataItem of transcriptState.notes();
            let i = index
          ">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer t-group t-relative">
            <span
              class="t-flex-1 t-mr-2 t-max-w-[90%]"
              (click)="onActionPerformed('SCROLL', dataItem)"
              >{{ dataItem.index.split('Note')[0].split('Document')[0] }}</span
            >
            <button
              (click)="deleteNote(dataItem)"
              kendoButton
              fillMode="clear"
              class="t-p-0 t-hidden t-absolute t-right-2 t-top-0 t-w-10 t-h-[2.2rem] t-place-content-center group-hover:!t-grid">
              <span
                venioSvgLoader
                svgUrl="assets/svg/Icon-material-delete.svg"
                height="0.9rem"
                width="0.8rem">
              </span>
            </button>
          </div>
        </div>
      </cdk-virtual-scroll-viewport>
    </ng-container>
  </div>
</ng-template>

<!-- No data template -->
<ng-template #noDataTemplate>
  <div class="t-grid t-p-4 t-place-content-center t-text-[#cccccc]">
    No data available
  </div>
</ng-template>

<!-- Add new Note dialog -->
<!-- <kendo-dialog
  *ngIf="noteDialogOpenStatus"
  (close)="close('cancel')"
  [maxHeight]="550"
  [height]="'90vh'"
  [minWidth]="250"
  [width]="'55%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-w-full t-block t-h-full">
    <ng-container
      *ngComponentOutlet="notesViewerComponent | async"></ng-container>
  </div>
</kendo-dialog> -->
