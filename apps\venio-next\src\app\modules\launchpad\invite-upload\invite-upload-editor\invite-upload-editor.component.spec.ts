import { ComponentFixture, TestBed } from '@angular/core/testing'
import { InviteUploadEditorComponent } from './invite-upload-editor.component'
import { FormBuilder } from '@angular/forms'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('InviteUploadEditorComponent', () => {
  let component: InviteUploadEditorComponent
  let fixture: ComponentFixture<InviteUploadEditorComponent>

  const mockInviteUploadForm = new FormBuilder().group({
    shareToExternalUsers: {
      value: false,
      disabled: false,
    },
    instruction: '',
    newEmail: {
      value: '',
      disabled: true,
    },
    validity: 1,
  })

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InviteUploadEditorComponent],
      providers: [provideAnimations()],
    }).compileComponents()

    fixture = TestBed.createComponent(InviteUploadEditorComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('inviteUploadForm', mockInviteUploadForm)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
