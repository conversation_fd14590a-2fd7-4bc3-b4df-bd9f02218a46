import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  TrackByFunction,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridItem, GridModule } from '@progress/kendo-angular-grid'
import {
  DocumentsFacade,
  TagFolderHistoryResponseModel,
} from '@venio/data-access/review'
import { Subject, filter, distinctUntilChanged, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-tag-history',
  standalone: true,
  imports: [CommonModule, GridModule],
  templateUrl: './tag-history.component.html',
  styleUrl: './tag-history.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagHistoryComponent implements OnInit, OnDestroy {
  public isTagHistoryLoading = true

  public tagHistory: TagFolderHistoryResponseModel[]

  private toDestroy$: Subject<void> = new Subject<void>()

  constructor(
    private documentsFacade: DocumentsFacade,
    private changeDetectorRef: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.#fetchTagHistory()
    this.#selectTagHistory()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetTagSumState()
  }

  #resetTagSumState(): void {
    this.documentsFacade.resetDocumentState([
      'tagHistory',
      'tagHistoryErrorResponse',
    ])
  }

  #fetchTagHistory(): void {
    this.isTagHistoryLoading = true
    this.documentsFacade.fetchTagHistory()
  }

  #selectTagHistory(): void {
    this.documentsFacade.getTagHistory$
      .pipe(
        filter((tagHistory) => !!tagHistory),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((tagHistory) => {
        this.tagHistory = tagHistory
        this.isTagHistoryLoading = false
        this.changeDetectorRef.markForCheck()
      })
  }

  public tagHistroryTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['seqNo'] as TrackByFunction<GridItem>
}
