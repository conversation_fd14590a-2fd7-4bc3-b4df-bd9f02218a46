import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FolderUiDialogComponent } from './folder-ui-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FolderUiDialogComponent', () => {
  let component: FolderUiDialogComponent
  let fixture: ComponentFixture<FolderUiDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FolderUiDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(FolderUiDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
