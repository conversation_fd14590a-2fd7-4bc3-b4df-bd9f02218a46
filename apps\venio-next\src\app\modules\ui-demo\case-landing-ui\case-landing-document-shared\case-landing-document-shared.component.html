<div class="t-flex t-gap-3 t-border-l-[1px] t-border-l-[#ccc]">
  <div class="t-flex t-flex-col t-gap-2 t-flex-1">
    <div class="t-flex t-justify-start t-p-2 t-mt-2 t-items-center">
      <div class="t-flex">
        <kendo-dropdownlist
          [data]="listItems"
          textField="text"
          valueField="value"
          title="Select Document"
          class="t-w-48"
          [defaultItem]="placeholderItem"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
          [valuePrimitive]="true">
          <ng-template kendoDropDownListHeaderTemplate>
            <div
              class="t-border-b-[#979797] t-border-b-[2px] t-border-dashed t-w-full t-pb-2">
              All Shared Document
            </div>
          </ng-template>
        </kendo-dropdownlist>
      </div>
    </div>

    <kendo-grid
      class="t-grid t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
      [kendoGridBinding]="batches"
      venioDynamicHeight
      [sortable]="true"
      [groupable]="false"
      [reorderable]="true"
      [resizable]="true"
      kendoGridSelectBy="id"
      [pageable]="{ type: 'numeric', position: 'top' }">
      <ng-template kendoPagerTemplate>
        <div class="t-flex t-gap-2">
          <kendo-textbox
            class="!t-border-[#ccc] !t-w-[25rem]"
            placeholder="Search"
            [clearButton]="true">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                fillMode="clear"
                class="t-text-[#1EBADC]"
                imageUrl="assets/svg/icon-updated-search.svg"></button>
            </ng-template>
          </kendo-textbox>
        </div>
        <kendo-grid-spacer></kendo-grid-spacer>

        <venio-pagination
          [disabled]="batches?.length === 0"
          [totalRecords]="batches?.length"
          [pageSize]="pageSize"
          [showPageJumper]="false"
          [showPageSize]="true"
          [showRowNumberInputBox]="true"
          class="t-px-5 t-block t-py-2">
        </venio-pagination>
      </ng-template>

      <!-- ID Column -->
      <kendo-grid-column
        field="id"
        title="#"
        [width]="50"
        headerClass="t-text-primary">
      </kendo-grid-column>

      <!-- Reference Column -->
      <kendo-grid-column
        field="reference"
        title="Reference"
        [width]="150"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.reference }}
        </ng-template>
      </kendo-grid-column>

      <!-- Size Column -->
      <kendo-grid-column
        field="documents"
        title="Documents"
        [width]="130"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.documents }}
        </ng-template>
      </kendo-grid-column>

      <!-- Expires Column -->
      <kendo-grid-column
        field="expires"
        title="Expires"
        [width]="150"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            kendoTooltip
            [title]="dataItem.createdOn | date : 'MM dd yyyy HH:mm:ss'"
            class="t-text-[#a3a3a3] t-text-sm"
            >{{ dataItem.createdOn | date : 'MM dd yyyy HH:mm:ss' }}</span
          >
        </ng-template>
      </kendo-grid-column>

      <!-- Actions Column (Checks) -->
      <kendo-grid-column
        title="Add Notes"
        [width]="130"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate>
          <kendo-svg-icon
            [icon]="icons.tickIcon"
            class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        title="Tag/Untag"
        [width]="130"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate>
          <kendo-svg-icon
            [icon]="icons.tickIcon"
            class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        title="Analyze Page"
        [width]="150"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate>
          <kendo-svg-icon
            [icon]="icons.tickIcon"
            class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
        </ng-template>
      </kendo-grid-column>

      <!-- Instruction Column -->
      <kendo-grid-column
        field="instruction"
        title="Instruction"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span class="t-text-gray-500 t-text-sm">{{
            dataItem.instruction
          }}</span>
        </ng-template>
      </kendo-grid-column>

      <!-- Created By & On -->
      <kendo-grid-column
        field="createdBy"
        title="Created By & On"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.createdBy }}
          <span class="t-text-[#a3a3a3] t-text-sm">{{
            dataItem.createdOn | date : 'MM dd yyyy HH:mm:ss'
          }}</span>
        </ng-template>
      </kendo-grid-column>

      <!-- Actions -->
      <kendo-grid-column
        title="Actions"
        [width]="150"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <kendo-buttongroup>
            <button
              kendoButton
              #actionGrid1
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-[33px] hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
              (click)="
                caseActionControls(dataItem.id, commonActionTypes.REVIEW)
              "
              kendoTooltip
              [title]="capitalizeTitle(commonActionTypes.REVIEW)"
              size="none"
              *ngIf="dataItem.id === 2">
              <span
                [parentElement]="actionGrid1.element"
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/icon-rate-review-note.svg"
                height="13px"
                width="13px">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>

            <button
              kendoButton
              #actionGrid2
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] hover:t-border-[#9BD2A7] hover:t-bg-[#9BD2A7]"
              (click)="caseActionControls(dataItem, commonActionTypes.EDIT)"
              kendoTooltip
              [title]="capitalizeTitle(commonActionTypes.EDIT)"
              size="none"
              *ngIf="dataItem.id !== 2">
              <span
                [parentElement]="actionGrid2.element"
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/icon-pencil-svg.svg"
                height="0.85rem"
                width="0.85rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>

            <button
              kendoButton
              #actionGrid3
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-[33px] hover:t-border-[#FFBB12] hover:t-bg-[#FFBB12]"
              (click)="caseActionControls(dataItem, commonActionTypes.VIEW)"
              kendoTooltip
              [title]="capitalizeTitle(commonActionTypes.VIEW)"
              size="none"
              *ngIf="dataItem.id !== 2">
              <span
                [parentElement]="actionGrid3.element"
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/eye.svg"
                height="0.9rem"
                width="0.9rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </kendo-buttongroup>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>
