import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiSearchDocumentListComponent } from './ai-search-document-list.component'
import { provideMockStore } from '@ngrx/store/testing'

describe('AiSearchDocumentListComponent', () => {
  let component: AiSearchDocumentListComponent
  let fixture: ComponentFixture<AiSearchDocumentListComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiSearchDocumentListComponent],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(AiSearchDocumentListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
