<kendo-grid
  [data]="statusDetailData()"
  [resizable]="true"
  [selectable]="false"
  [navigable]="false"
  class="t-w-full t-relative"
  [height]="350">
  <kendo-grid-column
    [width]="150"
    headerClass="t-text-primary"
    class="t-relative"
    field="totalDocCount"
    title="Total Documents">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span
        (click)="openSelectedOptions($event)"
        class="t-text-ellipsis t-relative t-overflow-hidden t-text-[#1DBADC] t-cursor-pointer">
        {{ dataItem.totalDocCount }}
      </span>
      <div
        *ngIf="isPiiOptionsVisible()"
        [ngStyle]="popoverPosition()"
        (mouseenter)="$event.stopPropagation()"
        (click)="$event.stopPropagation()"
        class="pii-popover-container t-fixed t-z-[4000] t-bg-[#F5F5F5] t-rounded-lg t-shadow-[0px_3px_6px_#00000029] t-p-4 t-max-h-[300px] t-overflow-visible t-w-[400px]">
        <svg
          class="t-absolute t-left-[-20px] t-top-[8px]"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg">
          <polygon points="0,10 20,0 20,20" fill="#F5F5F5" />
        </svg>
        <h2 class="t-text-sm t-font-bold t-mb-4">Selected options</h2>
        <div
          class="t-grid t-grid-cols-2 t-gap-4 t-items-stretch t-text-xs t-whitespace-normal t-max-h-[215px] t-overflow-auto">
          <div
            class="t-border-r-2 t-gap-2 t-border-dashed t-border-[#9BD2A7] t-flex t-flex-col">
            @for(d of defaultPIITypes(); track d.piiTypeName;){
            <div class="t-font-medium">
              {{ d.piiTypeName }}
            </div>
            }
          </div>
          <div class="t-pl-4 t-gap-2 t-flex t-flex-col">
            @for(d of customPIITypes(); track d.piiTypeName;){
            <div class="t-text-[#B4B4B4]">Custom PII Type</div>
            <div>{{ d.piiTypeName }}</div>
            <div class="t-text-[#B4B4B4]">Custom PII Definition</div>
            <div>
              {{ d.description }}
            </div>
            }
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [width]="100"
    headerClass="t-text-primary"
    field="processedDocCount"
    title="Processed" />
  <kendo-grid-column
    [width]="100"
    headerClass="t-text-primary"
    field="techIssueDocCount"
    title="Tech Issue" />
</kendo-grid>
