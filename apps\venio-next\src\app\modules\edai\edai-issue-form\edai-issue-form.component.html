<div class="t-w-full t-h-full t-pt-4 t-relative">
  <div class="t-flex t-gap-4">
    <div class="t-flex">
      @if(edaiRelevanceCompletedJobs()?.data?.length>0){
      <venio-edai-relevance-job-list
        [relevanceCompletedJobs]="
          edaiRelevanceCompletedJobs()
        "></venio-edai-relevance-job-list>
      }
    </div>

    <div class="t-flex t-grow t-flex-col t-items-center">
      @for(issueControl of issues?.controls; track
      issueControl.get('description'); let last = $last; let count = $count; let
      index = $index) {
      <div class="t-w-full t-flex t-flex-row t-items-center">
        @defer{
        <venio-edai-issue-row
          [index]="index"
          [issueFormControl]="getFormGroup(index)" />
        } @if(last && count < 5) {
        <div
          class="t-flex t-h-full t-p-3 t-text-[#1EBADC] t-items-center t-min-w-[52px] t-gap-2 t-flex-col">
          <kendo-svg-icon
            (click)="addNewIssue()"
            kendoTooltip
            title="Add New Issue"
            [icon]="icons.plusIcon"
            size="medium"
            class="t-w-[10px] t-cursor-pointer t-flex t-items-center t-bg-[#1EBADC] t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>

          <kendo-svg-icon
            (click)="removeIssue(index)"
            kendoTooltip
            *ngIf="last && count > 1"
            [icon]="icons.minusIcon"
            title="Remove Issue"
            size="medium"
            class="t-w-[10px] t-cursor-pointer t-flex t-items-center t-bg-error t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>
        </div>
        } @if(!last && count > 1 || last && count === 5) {
        <div
          class="t-flex t-h-full t-p-3 t-text-[#1EBADC] t-items-center t-min-w-[52px]">
          <kendo-svg-icon
            kendoTooltip
            (click)="removeIssue(index)"
            title="Remove Issue"
            [icon]="icons.minusIcon"
            size="medium"
            class="t-w-[10px] t-cursor-pointer t-flex t-items-center t-bg-error t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>
        </div>
        }
      </div>
      }
    </div>
  </div>
</div>
