@if(!isReviewSetBatchDashboardLoading()){
<kendo-chart
  *ngIf="hasReviewSetBatch()"
  [chartArea]="{ background: 'transparent' }"
  class="t-bg-[#FBFBFB] t-border-0 t-h-[300px] t-w-full">
  <kendo-chart-series>
    <kendo-chart-series-item
      type="pie"
      [data]="pieChartInfo()"
      field="count"
      categoryField="status"
      [colorField]="'color'"
      [labels]="SUMMARY_CHART_OPTIONS.labels">
    </kendo-chart-series-item>
  </kendo-chart-series>

  <kendo-chart-legend
    position="bottom"
    orientation="horizontal"
    [labels]="SUMMARY_CHART_OPTIONS.legendLabels"
    [markers]="SUMMARY_CHART_OPTIONS.legendMarkers">
  </kendo-chart-legend>
</kendo-chart>
} @else {
<div
  class="t-flex t-flex-col t-flex-grow t-relative t-justify-start t-items-center">
  <kendo-skeleton [height]="220" [width]="220" shape="circle" />
</div>
}
