import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EciCheckboxListItemComponent } from './checkbox-list-item.component';

describe('EciCheckboxListItemComponent', () => {
  let component: EciCheckboxListItemComponent;
  let fixture: ComponentFixture<EciCheckboxListItemComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EciCheckboxListItemComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(EciCheckboxListItemComponent);
    component = fixture.componentInstance;
    component.custodian = { id: 1, name: 'Test', email: '<EMAIL>' };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
