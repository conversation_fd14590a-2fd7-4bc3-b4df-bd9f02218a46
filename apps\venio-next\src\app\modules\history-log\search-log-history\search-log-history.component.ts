import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DelSearchHistoryRequestModel,
  DynamicFolderModel,
  FolderModel,
  NavigationType,
  SearchDupOption,
  SearchFacade,
  SearchHistory,
  SearchHistoryRequestModel,
  SearchInputParams,
  SearchScope,
  UserRights,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { Subject, filter, take, takeUntil } from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { SortDescriptor } from '@progress/kendo-data-query'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { UuidGenerator } from '@venio/util/uuid'
import {
  ConditionElement,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-search-log-history',
  standalone: true,
  templateUrl: './search-log-history.component.html',
  styleUrl: './search-log-history.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    GridModule,
    InputsModule,
    IconsModule,
    ButtonsModule,
    DropDownsModule,
    LabelModule,
    SvgLoaderDirective,
    UiPaginationModule,
    TooltipModule,
    UserGroupRightCheckDirective,
    LoaderModule,
  ],
})
export class SearchLogHistoryComponent implements OnInit, OnDestroy {
  @Input() public isUsedInPanel = false

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public readonly toDestroy$ = new Subject<void>()

  public isSavedSearch = false

  private searchScope: SearchScope = SearchScope.SAME_ORIGINAL

  private searchDuplicateOption: SearchDupOption

  private searchOption = [
    {
      id: SearchDupOption.SHOW_ALL_DUPS,
      option: 'Show all hits in the selected scope (No DeDupe)',
      value: 'SHOW_ALL_DUPS',
    },
    {
      id: SearchDupOption.HIDE_ALL_DUPS_DYNAMIC,
      option: 'Show only one instance in the selected scope (DynamicDeDupe™)',
      value: 'HIDE_ALL_DUPS_DYNAMIC',
    },
    {
      id: SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_DYNAMIC,
      option:
        'Show only one instance per custodian in the selected scope (DynamicDeDupe™)',
      value: 'HIDE_CUSTODIAN_LEVEL_DUPS_DYNAMIC',
    },
    {
      id: SearchDupOption.HIDE_PROJECT_LEVEL_DUPS_STATIC,
      option: 'Hide project level duplicates (StaticDeDupe™)',
      value: 'HIDE_PROJECT_LEVEL_DUPS_STATIC',
    },
    {
      id: SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_STATIC,
      option: 'Hide custodian level duplicates (StaticDeDupe™)',
      value: 'HIDE_CUSTODIAN_LEVEL_DUPS_STATIC',
    },
  ]

  public selectedSearchHistory: SearchHistory

  public isSearchHistoryLoading$ = this.searchFacade.getIsSearchHistoryLoading$

  public UserRights = UserRights

  public svgIconForGridControls = [
    {
      actionType: CommonActionTypes.SEARCH,
      iconPath: 'assets/svg/icon-search.svg',
    },
    {
      actionType: CommonActionTypes.DELETE,
      iconPath: 'assets/svg/icon-tagedit-delete.svg',
      allowedPermission: UserRights.ALLOW_TO_DELETE_SEARCH_HISTORY,
    },
  ]

  public currentPage = 1

  public pageSize = 50

  public skip = 0

  public sortField: string | null = null

  public sortOrder: string | null = null

  public searchLogHistory: SearchHistory[]

  public savedSearchHistory: SearchHistory[]

  public totalSearchHistoryRecords = 0

  public totalSavedSearchHistoryRecords = 0

  private confirmationDialogRef: DialogRef

  private searchHistoryConfirmationDialogRef: DialogRef

  private dynamicFolderScope: DynamicFolderModel

  private staticFolderScope: FolderModel

  private breadcrumbFacade = inject(BreadcrumbFacade)

  constructor(
    private searchFacade: SearchFacade,
    private activatedRoute: ActivatedRoute,
    private dialogService: DialogService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef,
    @Optional()
    public dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#fetchSearchHistory()

    this.#selectStaticFolderSearchScope()
    this.#selectDynamicFolderSearchScope()
    this.#selectSearchHistory()
    this.#selectSearchHistoryDeletionResponses()
  }

  public ngOnDestroy(): void {
    this.#clearSearchHistoryCounts()
    this.#resetSearchHistoryState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #fetchSearchHistory(): void {
    const searchHistoryRequestModel: SearchHistoryRequestModel = {
      getSavedSearchOnly: this.isSavedSearch,
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortFieldName: this.sortField,
      sortOrder: this.sortOrder,
    }
    this.searchFacade.fetchSearchHistory(
      this.projectId,
      searchHistoryRequestModel
    )
  }

  /**
   * Selects and handles changes in the dynamic folder search scope.
   * @returns {void}
   */
  #selectDynamicFolderSearchScope(): void {
    this.searchFacade.getDynamicFolderSearchScope$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((folder) => {
        this.dynamicFolderScope = folder
      })
  }

  /**
   * Selects and handles changes in the static folder search scope.
   * @returns {void}
   */
  #selectStaticFolderSearchScope(): void {
    this.searchFacade.getStaticFolderSearchScope$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((folder) => {
        this.staticFolderScope = folder
      })
  }

  #selectSearchHistory(): void {
    this.searchFacade.getSearchHistory$
      .pipe(
        filter((searchHistory) => !!searchHistory),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchHistory) => {
        this.cdr.markForCheck()
        const logs = searchHistory.map((p) => ({
          ...p,
          includeFamily: p.includePC ? 'Yes' : 'No',
          searchDupOption: this.searchOption.find(
            (item) => item.id === p.searchDuplicateOption
          ).value,
        }))
        this.searchLogHistory = logs
      })

    this.searchFacade.getSavedSearchHistory$
      .pipe(
        filter((savedSearchHistory) => !!savedSearchHistory),
        takeUntil(this.toDestroy$)
      )
      .subscribe((savedSearchHistory) => {
        this.cdr.markForCheck()
        const logs = savedSearchHistory.map((p) => ({
          ...p,
          includeFamily: p.includePC ? 'Yes' : 'No',
          searchDupOption: this.searchOption.find(
            (item) => item.id === p.searchDuplicateOption
          ).value,
        }))
        this.savedSearchHistory = logs
      })

    this.searchFacade.getTotalSearchHistoryCount$
      .pipe(
        filter((totalSearchHistoryCount) => !!totalSearchHistoryCount),
        takeUntil(this.toDestroy$)
      )
      .subscribe((totalSearchHistoryCount) => {
        this.cdr.markForCheck()

        if (this.isSavedSearch)
          this.totalSavedSearchHistoryRecords = totalSearchHistoryCount
        else this.totalSearchHistoryRecords = totalSearchHistoryCount
      })

    this.searchFacade.getFetchSearchHistoryFailureMessage$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.#showMessage(error, {
          style: 'error',
        })

        this.#resetSearchHistoryState()
      })
  }

  #selectSearchHistoryDeletionResponses(): void {
    this.searchFacade.getDeleteSearchHistorySuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        // Calculate the adjusted total record count after deletion
        const adjustedTotalRecords = this.isSavedSearch
          ? this.totalSavedSearchHistoryRecords - 1
          : this.totalSearchHistoryRecords - 1

        // Calculate the total number of pages based on adjusted total record count and page size
        const totalPages = Math.ceil(adjustedTotalRecords / this.pageSize)

        // If the current page is greater than the total number of pages, set it to the last page
        if (this.currentPage > totalPages) {
          this.currentPage = totalPages
        }

        this.#fetchSearchHistory()
      })
  }

  public searchHistoryActionClicked(
    selectedSearchHistory: SearchHistory,
    actionType: CommonActionTypes
  ): void {
    this.selectedSearchHistory = selectedSearchHistory
    switch (actionType) {
      case CommonActionTypes.SEARCH:
        if (this.isSavedSearch) {
          this.#performSearchaAndClose()
          this.#updateReviewUI()
        } else {
          this.#launchSearcHistoryConfirmationDialog()
        }
        break
      case CommonActionTypes.DELETE:
        this.#launchConfirmationDialog()
        break
    }
  }

  /**
   * Checks if the search expression contains multiline content
   * @param searchExpression - The search expression to check
   * @returns true if the expression contains newline characters
   */
  public isMultilineExpression(searchExpression: string): boolean {
    return searchExpression?.indexOf('\n') > -1
  }

  #launchSearcHistoryConfirmationDialog(): void {
    import(
      '../../history-log/search-history-confirmation-dialog/search-history-confirmation-dialog.component'
    ).then((comp) => {
      const SearchHistoryConfirmationDialogComponent =
        comp.SearchHistoryConfirmationDialogComponent

      this.searchHistoryConfirmationDialogRef = this.dialogService.open({
        content: SearchHistoryConfirmationDialogComponent,
      })

      this.#performSearchAfterSettingScope()
      this.#updateReviewUI()
    })
  }

  #updateReviewUI(): void {
    //Update Search Expression
    this.searchFacade.setSearchExpression(
      this.selectedSearchHistory.isSqlMode === 'No'
        ? this.selectedSearchHistory.searchExpression || ''
        : ''
    )

    //Update Include PC
    this.searchFacade.setIncludePC(
      this.selectedSearchHistory.includePC || false
    )

    this.searchFacade.setSearchDupOption(
      this.selectedSearchHistory?.searchDuplicateOption
    )
  }

  #getSearchQuery(): string {
    return this.selectedSearchHistory.isSqlMode === 'Yes' ? 'dummyQuery' : ''
  }

  #getSearchExpression(): string {
    return this.selectedSearchHistory.isSqlMode === 'No'
      ? this.selectedSearchHistory.searchExpression || ''
      : ''
  }

  #performSearchAfterSettingScope(): void {
    this.searchHistoryConfirmationDialogRef.result
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((result: any) => {
        const isConfirm =
          typeof result.status === 'boolean' && result.status === true
        if (!isConfirm) return
        this.searchScope = result.scope

        this.#performSearchaAndClose()
      })
  }

  #performSearchaAndClose(): void {
    this.breadcrumbFacade.resetBreadcrumbCurrentStates()
    const payload = {
      id: UuidGenerator.uuid,
      groupStackType: this.isSavedSearch
        ? GroupStackType.SAVED_SEARCH
        : GroupStackType.SEARCH_LOG,
      checked: true,
      conditionType: ConditionType.Group,
      conditions: [
        { conditionSyntax: this.selectedSearchHistory.searchExpression },
      ] as ConditionElement[],
    }
    this.breadcrumbFacade.storeBreadcrumbs([payload])
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((searchExpression) => {
        // If the search expression is empty, default to FileId>0
        searchExpression = searchExpression || 'FileId>0'
        const searchPayload = {
          ...this.#getSearchInputParams(),
          searchExpression,
        }
        this.searchFacade.search(searchPayload)
      })
    this.dialogRef.close()
  }

  #getSearchInputParams(): SearchInputParams {
    let searchInputParams: SearchInputParams
    if (this.isSavedSearch) {
      searchInputParams = {
        searchExpression: this.selectedSearchHistory.searchExpression,
        isSavedSearch: this.selectedSearchHistory.isSavedSearch,
        searchHistoryId: this.selectedSearchHistory.id,
        isLoadFile: this.selectedSearchHistory.isLoadFileSearch,
        isSearchFromHistory: true,
        isResetBaseGuid: true,
        includePC: this.selectedSearchHistory.includePC,
        searchDuplicateOption:
          this.selectedSearchHistory?.searchDuplicateOption,
      }
    } else {
      searchInputParams = {
        searchExpression: this.#getSearchExpression(),
        searchQuery: this.#getSearchQuery(),
        isResetBaseGuid: true,
        includePC: this.selectedSearchHistory?.includePC,
        searchDuplicateOption:
          this.selectedSearchHistory?.searchDuplicateOption,
        medialist: this.#getMediaSearchScope(),
        folderList: this.#getFolderSearchScope(),
        dynamicFolderScope: this.#getDynamicSearchScope(),
        navigationType: this.#getNavigationType(),
        isSqlMode: this.selectedSearchHistory.isSqlMode === 'Yes' ?? true,
        searchHistoryId: this.selectedSearchHistory.id,
      }
    }
    return searchInputParams
  }

  #getMediaSearchScope(): number[] {
    if (this.searchScope === SearchScope.SAME_ORIGINAL) {
      return this.selectedSearchHistory.navigationBy.toLowerCase() === 'media'
        ? this.selectedSearchHistory.navigationList
        : []
    }
    return []
  }

  #getDynamicSearchScope(): string {
    if (this.searchScope === SearchScope.SAME_ORIGINAL) {
      return this.selectedSearchHistory.navigationBy.toLowerCase() ===
        'dynamicfolder'
        ? this.selectedSearchHistory.navigationList.join(',')
        : null
    }
    return this.dynamicFolderScope
      ? `folderId:${this.dynamicFolderScope?.folderId}|isGlobal:${this.dynamicFolderScope?.isGlobal}`
      : null
  }

  #getFolderSearchScope(): number[] {
    if (this.searchScope === SearchScope.SAME_ORIGINAL) {
      return this.selectedSearchHistory.navigationBy.toLowerCase() === 'folder'
        ? this.selectedSearchHistory.navigationList
        : null
    }
    return this.staticFolderScope ? [this.staticFolderScope.folderId] : null
  }

  #getNavigationType(): NavigationType {
    if (this.searchScope === SearchScope.SAME_ORIGINAL) {
      return this.selectedSearchHistory.navigationBy.toLowerCase() === 'folder'
        ? NavigationType.Folder
        : this.selectedSearchHistory.navigationBy.toLowerCase() ===
          'dynamicfolder'
        ? NavigationType.DynamicFolder
        : NavigationType.Media
    }
    return this.staticFolderScope
      ? NavigationType.Folder
      : this.dynamicFolderScope
      ? NavigationType.DynamicFolder
      : NavigationType.Media
  }

  #launchConfirmationDialog(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(this.confirmationDialogRef.content.instance)
    this.#performTaskAfterConfirmation()
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = this.isSavedSearch
      ? 'Delete Saved Search'
      : 'Delete Search History'
    instance.message = `Are you sure you want to delete the selected ${
      this.isSavedSearch ? 'saved search' : 'search history'
    }?`
  }

  #performTaskAfterConfirmation(): void {
    this.confirmationDialogRef.result
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((result) => {
        const isConfirm = typeof result === 'boolean' && result === true
        if (!isConfirm) return

        const isSavedSearch = this.selectedSearchHistory.isSavedSearch

        const deleteSeareRequestModel: DelSearchHistoryRequestModel = {
          selectedSearchHistory: isSavedSearch
            ? []
            : [this.selectedSearchHistory.id],
          selectedSavedSearchId: isSavedSearch
            ? [this.selectedSearchHistory.id]
            : [],
          deleteSearchCustomField: this.#defaultFieldOptionSelection(),
          deleteSearchAutoTags: this.#defaultTagOptionSelection(),
          //while deleting the search from regular review page, delete only the selected search, not other searches having same expression
          deleteOtherSearchesWithSameExpression: false,
        }

        this.searchFacade.deleteSearchHistory(
          this.projectId,
          deleteSeareRequestModel
        )
      })
  }

  // Check and return whether tag delete option be by default checked or unchecked
  #defaultTagOptionSelection(): boolean {
    //To Do: Check user right
    const allowToDeleteTag = true
    return (
      this.selectedSearchHistory?.isSavedSearch &&
      this.selectedSearchHistory?.tagGroupId > 0 &&
      allowToDeleteTag
    )
  }

  // Check and return whether the custom field delete option by by default checked or not
  #defaultFieldOptionSelection(): boolean {
    //To Do: Check user right
    const allowToDeleteCustomField = true
    return (
      this.selectedSearchHistory?.isSavedSearch &&
      this.selectedSearchHistory?.customFieldId > 0 &&
      allowToDeleteCustomField
    )
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #clearSearchHistoryCounts(): void {
    this.totalSavedSearchHistoryRecords = 0
    this.totalSearchHistoryRecords = 0
  }

  #resetSearchHistoryState(): void {
    this.searchFacade.resetSearchState([
      'searchHistory',
      'fetchSearchHistoryFailureMessage',
      'totalSearchHistoryCount',
    ])
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      this.currentPage = args.pageNumber

      this.#fetchSearchHistory()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    //Calculate current page when page size changed
    this.currentPage =
      ((this.currentPage - 1) * this.pageSize + 1) / args.pageSize
    this.pageSize = args.pageSize

    this.currentPage = Math.ceil(this.currentPage)

    this.#fetchSearchHistory()
  }

  public onSortChange(sort: SortDescriptor[]): void {
    if (sort[0].dir) {
      this.sortField = sort[0].field
      this.sortOrder = sort[0].dir
    } else {
      this.sortField = null
      this.sortOrder = null
    }

    this.#fetchSearchHistory()
  }
}
