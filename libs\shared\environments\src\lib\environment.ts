// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  // This value here is required to fetch base setting from the server.
  // If any changes in the server where asmx are hosted, replace here with updated url or port.
  // Avoid committing it if it was only meant to be for local changes.
  // 2066 port is standard and any changes for local should not be committed.
  baseUrl: 'http://localhost:2066',
  // Eventually the base setting will override it anyway.
  apiUrl: 'http://localhost:6984/vod/services/' as string,
  applicationUrl: 'http://localhost:4300',
  // Derives from libs/shared/assets/src/files/json/app-settings.json
  chatApiUrl: 'http://localhost:11901',
  // Derives from libs/shared/assets/src/files/json/app-settings.json
  aiSearchEnabled: true,
  deployUrl: '' as string,
  pspdfkitLicense: '',
  allowedOrigin: '*' as string,
  version: '' as string,
  // Elastic Fleet API token see app-settings.json
  elasticFleetApiToken: '',
  // Elastic Fleet API URL see app-settings.json
  elasticFleetApiUrl: '',
}

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
import 'zone.js/plugins/zone-error' // Included with Angular CLI.
