.relevance-container {
  background: var(--viz-bg, #fcfbff);
  border: 1px solid var(--viz-border, #ccc9da);
  border-radius: 8px;
  padding: 1rem;
  height: 100%;
}

.relevance-header {
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    color: var(--viz-text, #0f0f0f);
    font-size: 1.2rem;
    font-weight: 600;
  }
}

.relevance-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 3rem);
}

.chart-container {
  flex: 1;
  min-height: 300px;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 1rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-text {
  font-size: 0.875rem;
  color: var(--viz-text, #0f0f0f);
}
