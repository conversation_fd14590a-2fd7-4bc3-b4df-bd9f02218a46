import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkFolderAcitonDialogComponent } from './bulk-Folder-aciton-dialog.component'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import {
  SearchFacade,
  FolderFacade,
  DocumentsFacade,
  DocumentsService,
  FieldFacade,
  SearchResultFacade,
  SearchResponseModel,
} from '@venio/data-access/review'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentCodingState,
  TagSettings,
} from '@venio/data-access/document-utility'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('BulkFolderAcitonDialogComponent', () => {
  let component: BulkFolderAcitonDialogComponent
  let fixture: ComponentFixture<BulkFolderAcitonDialogComponent>

  // Mock data for SearchResponseModel
  const mockSearchResponse: SearchResponseModel = {
    tempTables: undefined,
    error: undefined,
    searchResultIntialParameters: undefined,
  }

  // Mock data for searchInitialState
  const initialSearchState = {
    searchResponse: mockSearchResponse,
    breadcrumbs: null,
    mainSearchBreadcrumb: [],
    conditionalBreadcrumbs: [],
    filterBreadcrumbs: [],
    isSearchLoading: null,
    dynamicFolderSearchScope: null,
    staticFolderSearchScope: null,
  }

  // Mock data for searchResultMockData
  const mockSearchResultData = {
    searchResults: {
      ids: [1, 2, 3],
      entities: {
        1: { fileId: 1 },
        2: { fileId: 2 },
      },
      normalizedMetadata: {
        1: {
          ids: [10, 20],
          entities: {
            10: { key: 'meta1', value: 'Foo' },
            20: { key: 'meta2', value: 'Bar' },
          },
        },
        2: {
          ids: [30],
          entities: {
            30: { key: 'meta3', value: 'Baz' },
          },
        },
      },
    },
  }

  // Mock data for searchResultInitialState
  const initialSearchResultState = {
    searchResults: mockSearchResultData,
  }

  // Mock data for documentTagState
  const initialDocumentTagState = {
    projectTags: [],
    documentTags: {
      ids: [1, 2, 3],
      entities: {
        1: { id: 1, name: 'Document Tag 1' },
        2: { id: 2, name: 'Document Tag 2' },
      },
    },
    searchDocumentTags: '',
    userSelectedDocumentTag: [],
    tagsProfileCategory: [],
    projectTagSettings: {} as TagSettings,
    isDocumentTagLoading: false,
    areTagCommentsMissing: false,
    applyDocumentTagSuccessResponse: undefined,
    applyDocumentTagErrorResponse: undefined,
    tagProjectTagsErrorResponse: undefined,
    tagDocumentTagsErrorResponse: undefined,
    tagProfileCategoryErrorResponse: undefined,
    tagTagSettingErrorResponse: undefined,
  }

  // Mock data for DocumentCodingState
  const initialDocumentCodingState: DocumentCodingState = {
    documentCodingFields: [],
    fieldCodingModel: [],
    visibleCodingFields: undefined,
    updatedCodingFieldInfoIds: [],
    searchDocumentCoding: '',
    isDocumentCodeLoading: false,
    isCodingDataModified: false,
    isCodingDataValid: undefined,
    codingFailureResponse: undefined,
    selectedMultiCodingValue: undefined,
    codingActionEventType: undefined,
    bulkCodingValues: undefined,
    isBulkCodingValuesLoading: false,
    bulkCodingFailureResponse: undefined,
    isDocumentCodingUpdated: undefined,
  }

  // Mock data for initialState
  const initialState = {
    venioSearch: initialSearchState,
    venioDocumentTag: initialDocumentTagState,
    venioDocumentCoding: initialDocumentCodingState,
    venioSearchResult: initialSearchResultState,
    venioDocuments: {
      isBatchSelected: false,
      currentDocument: 1,
      currentDocumentName: 'mockDocument',
      currentDocumentTablePage: 0,
      selectedDocuments: [1, 2],
      unselectedDocuments: [],
      menuEventPayload: null,
      isDocumentMenuLoading: false,
      isBulkDocument: true,
    },
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BrowserAnimationsModule,
        BulkFolderAcitonDialogComponent,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({ initialState }),
        SearchFacade,
        FieldFacade,
        SearchResultFacade,
        DocumentsFacade,
        FolderFacade,
        DocumentsService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkFolderAcitonDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
