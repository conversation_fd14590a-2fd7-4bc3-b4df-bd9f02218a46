<div class="t-flex t-flex-col">
  <kendo-dialog
    *ngIf="opened"
    (close)="close('cancel')"
    [maxHeight]="580"
    [height]="'80vh'"
    [minWidth]="250"
    [maxWidth]="1600"
    [width]="'80%'">
    <kendo-dialog-titlebar>
      <div>
        {{ dialogTitle }}
      </div>
    </kendo-dialog-titlebar>

    <div class="t-flex t-w-full t-mt-3">
      <form [formGroup]="myForm" class="t-w-full">
        <div class="t-flex t-flex-col t-gap-4 t-w-full">
          <div class="t-flex">
            <kendo-dropdownlist
              defaultItem="Login & Logout"
              [data]="listItems"
              [valuePrimitive]="true"
              class="t-w-64">
            </kendo-dropdownlist>
          </div>

          <div class="t-flex t-gap-3 t-w-full t-justify-between t-items-center">
            <div class="t-flex t-gap-3">
              <kendo-multiselect
                formControlName="gender"
                [data]="genders"
                textField="text"
                valueField="value"
                [valuePrimitive]="true"
                [listHeight]="500"
                [checkboxes]="true"
                [autoClose]="false"
                [tagMapper]="tagMapper"
                [clearButton]="false"
                class="!t-w-64">
              </kendo-multiselect>

              <kendo-daterange class="t-w-auto">
                <button
                  kendoButton
                  [svgIcon]="downIcon"
                  #anchor
                  (click)="popup.toggle()"
                  class="t-flex t-flex-row-reverse !t-capitalize t-px-3 t-w-auto">
                  Filter By Date
                </button>
                <kendo-daterange-popup
                  #popup
                  [anchor]="{ nativeElement: anchor.element }">
                  <ng-template kendoDateRangePopupTemplate>
                    <div
                      class="t-flex t-flex-col t-absolute t-right-[-132px] t-w-[132px] t-h-full t-bg-white t-shadow-[31px_-10px_35px_-25px_rgba(0,0,0,0.2)]">
                      <ul
                        class="t-list-none t-flex t-flex-col t-w-full t-h-[299px] t-text-[#979797] t-gap-1 t-border-l-[1px] t-border-b-[1px] t-border-b-[#cccccc] t-border-l-[#cccccc] t-text-xs t-pt-5">
                        <li
                          class="hover:t-text-[var(--kendo-neutral-220)] v-active-menu t-cursor-pointer t-px-4 t-py-2">
                          This Week
                        </li>
                        <li
                          class="hover:t-text-[var(--kendo-neutral-220)] t-cursor-pointer t-px-4 t-py-2">
                          Last Week
                        </li>
                        <li
                          class="hover:t-text-[var(--kendo-neutral-220)] t-cursor-pointer t-px-4 t-py-2">
                          This Month
                        </li>
                        <li
                          class="hover:t-text-[var(--kendo-neutral-220)] t-cursor-pointer t-px-4 t-py-2">
                          Last Month
                        </li>
                      </ul>

                      <div class="t-flex">
                        <div class="t-flex t-p-3 t-w-full t-justify-end">
                          <kendo-buttongroup class="t-flex t-gap-3">
                            <button
                              kendoButton
                              fillMode="clear"
                              [toggleable]="true"
                              [svgIcon]="xIcon"
                              class="t-bg-[#FF5F521A] t-text-[#EC3737]"></button>
                            <button
                              kendoButton
                              fillMode="clear"
                              [toggleable]="true"
                              [svgIcon]="checkIcon"
                              class="t-bg-[#BAE36E3D] t-text-[#88B13F]"></button>
                          </kendo-buttongroup>
                        </div>
                      </div>
                    </div>
                    <div
                      class="t-flex t-text-right t-w-full t-gap-2 t-justify-end t-p-4 t-pb-1 t-text-[#979797] t-items-center t-h-[40px]">
                      <kendo-svg-icon [icon]="calendarIcon"></kendo-svg-icon>

                      @if(range.start && range.end){
                      {{ range.start | date : 'dd-MM-yyyy' }} -
                      {{ range.end | date : 'dd-MM-yyyy' }}
                      <button
                        kendoButton
                        size="small"
                        fillMode="clear"
                        [svgIcon]="closeIcon"
                        class="t-text-[#979797]"></button>
                      }
                    </div>
                    <kendo-multiviewcalendar
                      kendoDateRangeSelection
                      class="v-custom-daterange"
                      [selectionRange]="range"
                      (selectionRangeChange)="onChange($event)">
                    </kendo-multiviewcalendar>

                    <div
                      class="t-flex t-border t-border-[#ccc] t-border-l-0 t-border-b-0 t-border-r-0 t-relative t-p-5 t-justify-center">
                      <div class="t-flex t-text-[#979797]">
                        Selected: 15 Days
                      </div>
                    </div>
                  </ng-template>
                </kendo-daterange-popup>
              </kendo-daterange>

              <button
                kendoButton
                themeColor="secondary"
                class="t-flex t-flex-row-reverse v-custom-secondary-button t-justify-between !t-px-2">
                Generate
              </button>
            </div>
            <div class="t-flex t-text-right t-gap-2 t-flex-col">
              <p>Create by <span class="t-font-semibold">Mick Black</span></p>
              <p>
                Generated on
                <span class="t-font-semibold"
                  >04 08 2024 <span class="t-text-xs">12:24 AM</span></span
                >
              </p>
            </div>
          </div>

          <div class="t-flex">
            <div class="t-flex t-mt-4 t-flex-col t-w-full">
              <kendo-grid
                [kendoGridBinding]="gridData"
                kendoGridSelectBy="id"
                [pageSize]="pageSize"
                [pageable]="{ type: 'numeric', position: 'top' }"
                [sortable]="true"
                [groupable]="false"
                [reorderable]="true"
                [resizable]="true"
                [skip]="skip">
                <ng-template kendoPagerTemplate>
                  <div class="t-flex t-gap-2"></div>
                  <kendo-grid-spacer></kendo-grid-spacer>

                  <div class="t-flex t-gap-3">
                    <kendo-dropdownbutton
                      [data]="settings"
                      [svgIcon]="printIcon">
                      <kendo-svg-icon [icon]="downIcon"></kendo-svg-icon>

                      <ng-template kendoDropDownButtonItemTemplate let-dataItem>
                        <kendo-svgicon
                          [icon]="dataItem.svgIcon"></kendo-svgicon>
                        <span>{{ dataItem.text }}</span>
                      </ng-template>
                    </kendo-dropdownbutton>

                    <kendo-dropdownbutton [data]="settings" [svgIcon]="pdfIcon">
                      <kendo-svg-icon [icon]="downIcon"></kendo-svg-icon>

                      <ng-template kendoDropDownButtonItemTemplate let-dataItem>
                        <kendo-svgicon
                          [icon]="dataItem.svgIcon"></kendo-svgicon>
                        <span>{{ dataItem.text }}</span>
                      </ng-template>
                    </kendo-dropdownbutton>
                  </div>
                  <venio-pagination
                    [disabled]="gridData?.length === 0"
                    [totalRecords]="gridData?.length"
                    [pageSize]="pageSize"
                    [showPageJumper]="false"
                    [showPageSize]="true"
                    [showRowNumberInputBox]="true"
                    class="t-px-5 t-block t-py-2">
                  </venio-pagination>
                </ng-template>
                <kendo-grid-column
                  headerClass="t-text-primary"
                  field="userName"
                  title="User Name"></kendo-grid-column>
                <kendo-grid-column
                  headerClass="t-text-primary"
                  field="ipAddress"
                  title="IP Address"></kendo-grid-column>
                <kendo-grid-column
                  headerClass="t-text-primary"
                  field="loginDateTime"
                  title="Login DateTime"
                  [format]="{
                    date: 'dd/MM/yyyy HH:mm:ss'
                  }"></kendo-grid-column>
                <kendo-grid-column
                  headerClass="t-text-primary"
                  field="logoutDateTime"
                  title="Logout DateTime"
                  [format]="{
                    date: 'dd/MM/yyyy HH:mm:ss'
                  }"></kendo-grid-column>
              </kendo-grid>
            </div>
          </div>
        </div>
      </form>
    </div>

    <kendo-dialog-actions>
      <div class="t-flex t-gap-4 t-justify-end">
        <!-- <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        data-qa="save-button"
        fillMode="outline">
        SAVE
      </button> -->
        <button
          kendoButton
          (click)="close('yes')"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CANCEL
        </button>
      </div>
    </kendo-dialog-actions>
  </kendo-dialog>
</div>
