import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BrowsePanelSystemFolderComponent } from './browse-panel-system-folder.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import {
  DynamicFolderFacade,
  FieldFacade,
  FolderFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BrowserPanelTabsComponent', () => {
  let component: BrowsePanelSystemFolderComponent
  let fixture: ComponentFixture<BrowsePanelSystemFolderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BrowsePanelSystemFolderComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DynamicFolderFacade,
        SearchFacade,
        FieldFacade,
        FolderFacade,
        provideMockStore({}),
        {
          provide: BreadcrumbFacade,
          useValue: jest.fn(),
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BrowsePanelSystemFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
