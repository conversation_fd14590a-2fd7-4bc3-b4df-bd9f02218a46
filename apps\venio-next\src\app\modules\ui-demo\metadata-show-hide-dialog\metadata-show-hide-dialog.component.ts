import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { eyeIcon, searchIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { FormsModule } from '@angular/forms'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'

interface CheckboxItem {
  label: string
  checked: boolean
}

@Component({
  selector: 'venio-metadata-show-hide-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    LabelModule,
    InputsModule,
    DropDownListModule,
    IconsModule,
    ButtonsModule,
    FormsModule,
    DynamicHeightDirective,
  ],
  templateUrl: './metadata-show-hide-dialog.component.html',
  styleUrl: './metadata-show-hide-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MetadataShowHideDialogComponent implements OnInit {
  public selectAllChecked = false

  public opened = true

  public checkboxes: CheckboxItem[] = []

  public dialogTitle = 'Hide and Show Metadata'

  public icons = { search: searchIcon, eyeIcon: eyeIcon }

  public close(status: string): void {
    //this.dialogRef.close()
    this.opened = false
  }

  public ngOnInit(): void {
    this.opened = true
    this.generateDummyData()
  }

  public generateDummyData(): void {
    this.checkboxes = Array.from({ length: 50 }, (_, index) => ({
      label: `Field Label ${index + 1}`,
      checked: false,
    }))
    this.updateSelectAllState()
  }

  public toggleSelectAll(): void {
    this.checkboxes.forEach((item) => (item.checked = this.selectAllChecked))
  }

  public updateSelectAllState(): void {
    this.selectAllChecked = this.checkboxes.every((item) => item.checked)
  }
}
