<div class="t-flex t-w-full t-mt-3">
  <div class="t-flex t-flex-col t-gap-4 t-w-full">
    <div class="t-flex t-gap-3 t-w-full t-justify-between t-items-center">
      <div class="t-flex t-gap-3 t-items-center">
        <kendo-multiselect
          [filterable]="true"
          [virtual]="{ itemHeight: 33.6 }"
          [kendoDropDownFilter]="{
            caseSensitive: false,
            operator: 'contains'
          }"
          [data]="userList()"
          textField="userName"
          valueField="userID"
          (valueChange)="selectedUser.set($event)"
          [valuePrimitive]="true"
          [listHeight]="500"
          [checkboxes]="true"
          [autoClose]="false"
          [tagMapper]="tagMapper"
          [clearButton]="false"
          placeholder="All users"
          class="!t-w-56 t-max-h-[33.6px] v-custom-multiselect-auto-w">
          <ng-template kendoSuffixTemplate>
            <button
              kendoButton
              [svgIcon]="downIcon"
              fillMode="link"
              class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
          </ng-template>
          <ng-template kendoMultiSelectItemTemplate let-dataItem>
            <div
              kendoTooltip
              [title]="dataItem.userName"
              class="t-overflow-hidden t-text-ellipsis t-whitespace-nowrap t-px-2 t-py-1">
              {{ dataItem.userName }}
            </div>
          </ng-template>
        </kendo-multiselect>
        <venio-report-date-picker
          class="t-min-w-[150px] t-w-56 t-self-center" />
        <kendo-multiselect
          [filterable]="true"
          [virtual]="{ itemHeight: 33.6 }"
          [kendoDropDownFilter]="{
            caseSensitive: false,
            operator: 'contains'
          }"
          textField="label"
          valueField="value"
          (valueChange)="selectedStatus.set($event)"
          [data]="statusList()"
          [listHeight]="500"
          [valuePrimitive]="true"
          [checkboxes]="true"
          [autoClose]="false"
          [tagMapper]="tagMapper"
          [clearButton]="false"
          placeholder="All Status"
          class="!t-w-56 t-max-h-[33.6px] v-custom-multiselect-auto-w">
          <ng-template kendoSuffixTemplate>
            <button
              kendoButton
              [svgIcon]="downIcon"
              fillMode="link"
              class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
          </ng-template>
          <ng-template kendoMultiSelectItemTemplate let-dataItem>
            <div
              kendoTooltip
              [title]="dataItem.label"
              class="t-overflow-hidden t-text-ellipsis t-whitespace-nowrap t-px-2 t-py-1">
              {{ dataItem.label }}
            </div>
          </ng-template>
        </kendo-multiselect>
        <button
          (click)="refreshStatusList()"
          kendoButton
          class="v-custom-secondary-button t-p-0"
          themeColor="secondary"
          fillMode="outline"
          data-qa="refresh-button"
          #parentEl>
          <span
            [parentElement]="parentEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            color="#9BD2A7"
            svgUrl="assets/svg/refresh.svg"
            height="1rem"
            width="1rem">
          </span>
        </button>
      </div>
    </div>

    <div class="t-flex">
      <div class="t-flex t-mt-4 t-flex-col t-w-full">
        @defer{
        <venio-edai-status-grid (pagingChange)="pageChange($event)" />
        }
      </div>
    </div>
  </div>
</div>
