import {
  ChangeDetectionStrategy,
  Component,
  HostBinding,
  inject,
  Input,
  OnDestroy,
  OnInit,
  signal,
  ViewEncapsulation,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  checkIcon,
  plusIcon,
  trashIcon,
  xIcon,
} from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  combineLatest,
  distinctUntilChanged,
  filter,
  Observable,
  Subject,
  takeUntil,
} from 'rxjs'
import {
  animate,
  query,
  stagger,
  style,
  transition,
  trigger,
} from '@angular/animations'

import {
  GroupStackType,
  ConditionElement,
  ConditionGroup,
  ConditionRowChangeTypes,
  ConditionUiType,
  GroupOperator,
  SearchField,
  SearchFieldModel,
} from '@venio/shared/models/interfaces'
import { DocumentViewFacade } from '@venio/data-access/common'
import { DocumentViewDesignerConditionsRowComponent } from '../document-view-designer-conditions-row/document-view-designer-conditions-row.component'
import { FormsModule } from '@angular/forms'
import {
  ConditionStackManager,
  DebounceTimer,
  SyntaxManagerWorkerService,
} from '@venio/util/utilities'
import { ViewModel } from '@venio/data-access/review'
import { cloneDeep, isEqual } from 'lodash'

export const listAnimation = trigger('listAnimation', [
  transition('* => *', [
    query(
      ':enter',
      [
        style({
          opacity: 0,
          height: 0,
          transform: 'translateY(-15px)',
          overflow: 'hidden',
        }),
        stagger('100ms', [
          animate(
            '300ms ease-out',
            style({ opacity: 1, height: '*', transform: 'none' })
          ),
        ]),
      ],
      { optional: true }
    ),
    query(
      ':leave',
      [
        stagger('100ms', [
          animate(
            '300ms ease-in',
            style({
              opacity: 0,
              height: 0,
              transform: 'translateY(-15px)',
              overflow: 'hidden',
            })
          ),
        ]),
      ],
      { optional: true }
    ),
  ]),
])

@Component({
  selector: 'venio-document-view-designer-conditions',
  standalone: true,
  imports: [
    CommonModule,
    DocumentViewDesignerConditionsRowComponent,
    ButtonsModule,
    IconsModule,
    FormsModule,
  ],
  animations: [listAnimation],
  templateUrl: './document-view-designer-conditions.component.html',
  styleUrl: './document-view-designer-conditions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class DocumentViewDesignerConditionsComponent
  implements OnDestroy, OnInit
{
  @HostBinding('class')
  public applyHostCss =
    't-block t-overflow-auto t-min-h-[5rem] t-max-h-[calc(100vh_-_15rem)] t-relative'

  /**
   * Represents the UI type for the condition.
   * This helps decide the manner in which the condition is presented in the UI.
   * We have different types with the UI for the latter being reused from the View condition.
   * @see ConditionUiType
   */
  @Input({ required: true })
  public conditionUiType: ConditionUiType

  public readonly conditionUiTypes = ConditionUiType

  /**
   * Assigns the condition stacks.
   * Under specific circumstances, the condition stacks are derived from the input.
   * When the view is in use, setting the condition stack isn't necessary.
   * However, in the case of the breadcrumb,
   * the condition stack may need to be set using the input for editing purposes.
   *
   * This stack is utilized to handle conditions and their syntax.
   * @param {ConditionGroup[]} value - The condition stack to be assigned.
   * @see ConditionGroup
   * @see ConditionStackManager
   */
  @Input()
  public set conditionStack(value: ConditionGroup[]) {
    if (!value) return

    this.conditionStackManager.setStacks(value, this.fields())
    this.conditionStacks.set(this.conditionStackManager.getStacks())
  }

  private documentViewFacade = inject(DocumentViewFacade)

  private toDestroy$ = new Subject<void>()

  public searchField = signal<SearchFieldModel>(undefined)

  public fields = signal<SearchField[]>([])

  private shouldCleanConditionGroup = new Map<number, boolean>()

  public iconCheck = checkIcon

  public groupOperators = [GroupOperator.AND, GroupOperator.OR]

  public groupCombinatorOperator = [
    GroupOperator.AND,
    GroupOperator.OR,
    GroupOperator.NOT,
  ]

  public icons = {
    plusIconSvg: plusIcon,
    closeIcon: xIcon,
    trashIconSvg: trashIcon,
  }

  public readonly conditionStackManager = new ConditionStackManager()

  public conditionStacks = signal<ConditionGroup[]>(
    this.conditionStackManager.getStacks()
  )

  public ngOnInit(): void {
    this.#selectFields()
    this.#selectConditionJsonFormData()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Changes the logical operator of a specified group and updates the condition syntax.
   *
   * @param {string} groupId - The ID of the group whose operator is to be changed.
   * @param {GroupOperator} operator - The new logical operator for the group.
   * @returns {void}
   */
  public changeGroupOperator(groupId: string, operator: GroupOperator): void {
    this.conditionStackManager.changeGroupMainOperator(groupId, operator)
    this.#storeConditionsAndSyntax()
  }

  /**
   * Updates a condition group with a new operator and stores the updated conditions and syntax.
   * This method is debounced to limit the rate of execution.
   *
   * @param {ConditionGroup} group - The condition group to update.
   * @param {GroupOperator} operator - The new operator for the group.
   * @returns {void}
   */
  @DebounceTimer(200)
  public updateConditionGroup(
    group: ConditionGroup,
    operator: GroupOperator
  ): void {
    this.conditionStackManager.updateConditionGroup({
      ...group,
      operator,
    })
    this.#storeConditionsAndSyntax()
  }

  /**
   * Adds a new condition to a specified group and updates the condition syntax.
   *
   * @param {string} groupId - The ID of the group to which the new condition will be added.
   * @returns {void}
   */
  public addNewCondition(groupId: string): void {
    this.conditionStackManager.addConditionToGroup(groupId, null)
    this.#storeConditionsAndSyntax()
  }

  /**
   * Removes a specified group from the condition stack and updates the condition syntax.
   *
   * @param {string} groupId - The ID of the group to be removed.
   * @returns {void}
   */
  public removeGroup(groupId: string): void {
    this.conditionStackManager.removeGroup(groupId)
    this.#storeConditionsAndSyntax()
  }

  /**
   * Handles various condition row change events such as updating, adding, or removing conditions and groups.
   *
   * @param {string} groupId - The ID of the group where the change occurred.
   * @param {string} conditionId - The ID of the condition affected by the change.
   * @param {Partial<Record<ConditionRowChangeTypes, unknown>>} event - The event indicating the type of change.
   * @returns {void}
   */
  public onConditionRowChange(
    groupId: string,
    conditionId: string,
    event: Partial<Record<ConditionRowChangeTypes, unknown>>
  ): void {
    if (event[ConditionRowChangeTypes.UPDATE_CONDITION]) {
      const conditionElement = event[
        ConditionRowChangeTypes.UPDATE_CONDITION
      ] as ConditionElement
      this.conditionStackManager.updateCondition(
        groupId,
        conditionId,
        conditionElement
      )
    }

    if (event[ConditionRowChangeTypes.ADD_CONDITION]) {
      this.addNewCondition(groupId)
    }

    if (event[ConditionRowChangeTypes.REMOVE_CONDITION]) {
      this.conditionStackManager.removeConditionFromGroup(groupId, conditionId)
    }

    if (event[ConditionRowChangeTypes.ADD_GROUP]) {
      const groupOperator = event[
        ConditionRowChangeTypes.ADD_GROUP
      ] as GroupOperator

      this.conditionStackManager.addGroup(groupOperator)
    }

    if (event[ConditionRowChangeTypes.REMOVE_GROUP]) {
      this.conditionStackManager.removeGroup(groupId)
    }

    this.conditionStackManager.updateConditionSyntax(groupId, conditionId)

    this.conditionStacks.set(this.conditionStackManager.getStacks())

    this.#storeConditionsAndSyntax()
  }

  /**
   * Provides a unique track-by function identifier for a condition in a list.
   *
   * @param {number} _ - The index of the condition (unused, hence underscore).
   * @param {ConditionElement} condition - The condition element.
   * @returns {string} A unique identifier for the condition.
   */
  public trackByConditionFn(_: number, condition: ConditionElement): string {
    return `${condition.id}_${condition.fieldName}`
  }

  /**
   * Provides a unique track-by function identifier for a group in a list.
   *
   * @param {number} _ - The index of the group (unused, hence underscore).
   * @param {ConditionGroup} condition - The condition group.
   * @returns {string} A unique identifier for the group.
   */
  public trackByGroupFn(_: number, condition: ConditionGroup): string {
    return condition.id
  }

  /**
   * Stores the current condition stack and its generated syntax.
   * Invokes a worker service to generate the syntax and then stores both the condition stack
   * and the syntax in the document view facade.
   * @returns {void}
   */
  #storeConditionsAndSyntax(): void {
    const conditions = this.conditionStacks()

    const syntaxManagerWorker = new SyntaxManagerWorkerService()

    // TODO: if this is for view condition, we use view facade, otherwise we use breadcrumb facade
    switch (this.conditionUiType) {
      case ConditionUiType.VIEW_CONDITION:
        syntaxManagerWorker.getGeneratedSyntax(conditions).then((syntax) => {
          this.documentViewFacade.storeCurrentFormData({
            viewExpressionJson: JSON.stringify(conditions),
            viewExpression: syntax,
          })
          syntaxManagerWorker.terminate()
        })
        break
      case ConditionUiType.BREADCRUMB_CONDITION:
        // avoid readonly property error by cloning the conditions
        this.documentViewFacade.storeConditionGroup(
          cloneDeep(
            conditions.map((condition) => ({
              ...condition,
              groupStackType: GroupStackType.FIELDS,
            }))
          )
        )
        break
    }
  }

  /**
   * Sets the search fields after filtering out excluded fields.
   * Excludes certain fields (like 'tags', 'folders') from the search fields.
   *
   * @param {SearchFieldModel} fieldModel - The model containing search fields.
   * @returns {void}
   */
  #setSearchFields(fieldModel: SearchFieldModel): void {
    if (!fieldModel) return

    this.searchField.set(fieldModel)

    const excludedFields = ['tags', 'folders']
    const fields = fieldModel.searchFields.filter(
      (f) => !excludedFields.includes(f.displayName.toLowerCase())
    )

    this.fields.set(fields)
  }

  /**
   * Subscribes to search fields and condition JSON form data, then updates search fields and condition stack accordingly.
   * Sets up a subscription that listens for changes in search fields and any errors. Updates the condition stack based on these changes.
   * Cleans up the condition stack if required.
   * @returns {void}
   */
  #selectFields(): void {
    combineLatest([
      this.documentViewFacade.selectSearchFields$,
      this.documentViewFacade.selectSearchFieldsErrorResponse$,
      this.#selectConditionJsonFormData(),
    ])
      .pipe(
        filter(([success, error]) => Boolean(success || error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([fieldModel, error, view]) => {
        this.#setSearchFields(fieldModel)

        if (
          fieldModel?.searchFields?.[0] &&
          this.conditionUiType === ConditionUiType.VIEW_CONDITION
        ) {
          this.setConditionJsonFormData(view)
        }

        if (error) {
          // TODO: show notification with error state
        }
      })
  }

  /**
   * Parses a JSON string to manage and potentially filter the condition stacks
   * for a given view model, with special handling for new and existing views.
   *
   * The function follows these steps:
   * 1. It parses the JSON string from the view model's `viewExpressionJson` property into condition groups.
   * 2. If the parsed JSON contains elements, the function proceeds with validation.
   * 3. For existing views (where viewId > 0), it filters out any conditions that are not present
   *    in the current fields (obtained via fields()).
   *    This ensures that only relevant conditions are kept.
   * 4. For new views (where viewId <= 0) or views without conditions, it sets a default state,
   * avoiding unnecessary filtering.
   * 5. The cleanup and filtering process is designed to run once, not repeatedly, to avoid unexpected removal.
   *
   * @param {ViewModel} vm - The view model instance.
   * It includes 'viewExpressionJson', a JSON string
   *                         representing condition groups, and 'viewId', which differentiates
   *                         between new and existing views.
   *
   * Note: This method is particularly important in scenarios where a view is being edited.
   *       If the view's conditions (as per the JSON string) contain fields that are no longer
   *       present in the current fields set, those conditions are filtered out.
   *       This is
   *       relevant when the view has a positive viewId, indicating an edit scenario.
   *       In the case of creating a new view, or if the existing view has no conditions,
   *       the method sets a default state to avoid unnecessary filtering operations.
   *
   * @returns {void}
   *
   * Example:
   * Let's assume we are editing a view (vm.viewId > 0) with an existing `viewExpressionJson`
   * that includes four conditions.
   * However, the current `fields()` method only returns two relevant fields.
   * This function will parse the JSON string, filter out the two irrelevant conditions,
   * and update the condition stack accordingly.
   */
  private setConditionJsonFormData(vm: ViewModel): void {
    const jsonString = vm.viewExpressionJson
    const parsedJsonStacks: ConditionGroup[] = JSON.parse(jsonString || '[]')

    // we need to filter the conditions that are not existed in the fields
    //const filteredStacks = parsedJsonStacks.
    this.conditionStackManager.setStacks(parsedJsonStacks, this.fields())

    // if the view is not new and there are no conditions, we should clean up the stacks,
    // which are saved on form data previously.
    if (
      parsedJsonStacks.length > 0 &&
      !this.shouldCleanConditionGroup.has(vm.viewId)
    ) {
      const shouldClean =
        vm.viewId > 0 &&
        parsedJsonStacks.some((s) => s.conditions?.length === 0)
      this.shouldCleanConditionGroup.set(vm.viewId, shouldClean)
    } else {
      this.shouldCleanConditionGroup.set(vm.viewId, false)
    }

    if (this.shouldCleanConditionGroup.get(vm.viewId)) {
      this.conditionStackManager.cleanUpStacks(
        this.conditionStackManager.getStacks()
      )
    }

    this.conditionStacks.set(this.conditionStackManager.getStacks())
  }

  /**
   * Creates an observable that emits the current form data containing condition JSON and view ID.
   * Emits only when the condition JSON or the view ID changes and the model is defined.
   * @returns {Observable<Object>} An observable emitting the condition JSON and view ID.
   */
  #selectConditionJsonFormData(): Observable<ViewModel> {
    return this.documentViewFacade.selectCurrentFormData$.pipe(
      distinctUntilChanged((a, b) =>
        isEqual(a.viewExpressionJson, b.viewExpressionJson)
      ),
      filter((model) => Boolean(model))
    )
  }
}
