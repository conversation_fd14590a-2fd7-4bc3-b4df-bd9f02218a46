import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FilterExpandSettings } from '@progress/kendo-angular-treeview'
import { chevronDownIcon, SVGIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-review-create-case-sort',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    InputsModule,
    FormsModule,
    IconsModule,
    SVGIconModule,
    ButtonsModule,
    LabelModule,
    DateInputsModule,
  ],
  templateUrl: './review-create-case-sort.component.html',
  styleUrl: './review-create-case-sort.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseSortComponent {
  public isDisabled = true

  public downIcon: SVGIcon = chevronDownIcon

  public listItems: Array<string> = ['DSC', 'ASC']

  public sortOrders: Array<string> = ['Ingestion_Order', 'Media_Name']

  public tagDropdowndata: any[] = [
    {
      text: 'Node One',
      items: [{ text: 'Node A0' }, { text: 'Node A1' }, { text: 'Node A2' }],
    },
    {
      text: 'Node Two',
      items: [{ text: 'Node B0' }, { text: 'Node B1' }, { text: 'Node B2' }],
    },
  ]

  public expandedOnClear: 'none' | 'all' | 'initial' | 'unchanged' = 'none'

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public get filterExpandSettings(): FilterExpandSettings {
    return { expandedOnClear: this.expandedOnClear }
  }
}
