import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  DropDownListComponent,
  DropDownTreesExpandDirective,
  MultiSelectTreeComponent,
  MultiSelectTreeFlatBindingDirective,
} from '@progress/kendo-angular-dropdowns'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { FormsModule } from '@angular/forms'
import { KENDO_ADORNMENTS } from '@progress/kendo-angular-common'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import {
  ResponseModel,
  ReviewSetEntry,
  ReviewSetViewDetailSearchTypes,
  TagsModel,
} from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { TextBoxComponent } from '@progress/kendo-angular-inputs'
import { SearchRequestModel, SearchService } from '@venio/data-access/review'
import { filter, Subject, take, takeUntil } from 'rxjs'
import { NotificationService, Type } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-reviewset-detail-view-filter-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DropDownListComponent,
    DropDownTreesExpandDirective,
    FormsModule,
    MultiSelectTreeComponent,
    KENDO_ADORNMENTS,
    SvgLoaderDirective,
    MultiSelectTreeFlatBindingDirective,
    TextBoxComponent,
  ],
  templateUrl: './reviewset-detail-view-filter-toolbar.component.html',
  styleUrl: './reviewset-detail-view-filter-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewFilterToolbarComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly batchDeletionStatus = input.required<boolean>()

  public readonly searchClicked = output()

  public readonly downIcon = chevronDownIcon

  private readonly notificationService = inject(NotificationService)

  private readonly projectFacade = inject(ProjectFacade)

  private readonly searchService = inject(SearchService)

  private readonly userFacade = inject(UserFacade)

  private readonly injector = inject(Injector)

  public readonly isReviewSetDocumentViewLoading = toSignal(
    this.projectFacade.selectIsReviewSetDocumentViewLoading$.pipe(
      filter((loading) => typeof loading === 'boolean')
    )
  )

  private readonly isExternalUser = toSignal(
    this.userFacade.selectCurrentUserDetails$.pipe(
      map((user) => user?.userRole === 'external')
    ),
    {
      initialValue: false,
    }
  )

  public readonly searchOptionTypes = signal([
    ReviewSetViewDetailSearchTypes.TAGS,
    ReviewSetViewDetailSearchTypes.QUERY,
  ])

  public readonly selectedSearchOptionType = signal(
    ReviewSetViewDetailSearchTypes.TAGS
  )

  public readonly reviewSetViewDetailSearchTypes =
    ReviewSetViewDetailSearchTypes

  public readonly searchTermOrTags = signal<string | TagsModel[]>([])

  public readonly isValidSearch = computed(() => {
    const term = this.searchTermOrTags()
    return typeof term === 'string' ? term?.trim() : term?.[0]
  })

  public readonly taggedDocumentTags = toSignal(
    this.projectFacade.selectTaggedDocumentTagsSuccess$.pipe(
      map((response) => this.#mapTreeToIdParentId(response?.data || []))
    ),
    {
      initialValue: [],
    }
  )

  public ngOnInit(): void {
    this.#fetchTaggedDocumentTags()
    this.#reloadAfterDeletionSuccess()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public tagMapper(tags: TagsModel[]): unknown[] {
    return tags.length < 2 ? tags : [tags]
  }

  public clearSearchValues(value: string | TagsModel[]): void {
    this.searchTermOrTags.set(typeof value === 'string' ? '' : [])
  }

  /**
   * Trigger review set search and emit search event
   * @returns {void}
   */
  public searchReviewSetViewDetail(): void {
    // we need to clear existing to avoid previous value being displayed
    this.#clearPreviousDocumentView()
    this.#performSearch()
    this.searchClicked.emit()
  }

  #clearPreviousDocumentView(): void {
    this.projectFacade.resetProjectState([
      'reviewSetDocumentViewSuccess',
      'reviewSetDocumentViewError',
    ])
  }

  /**
   * Executes the search operation
   * @returns {void}
   */
  #performSearch(): void {
    const projectId = this.selectedReviewSetEntry().projectId
    const searchExpression = this.#buildSearchExpression()

    this.projectFacade.updateReviewSetDocumentViewLoader(true)
    const payload = this.#createSearchRequest(projectId, searchExpression)
    this.searchService
      .search$(payload)
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (response) => this.#handleSearchSuccess(response, projectId),
        error: (error: unknown) => this.#handleSearchError(error),
      })
  }

  /**
   * Creates search request parameters
   * @param {string} projectId - ID of the project to search
   * @param {string} searchExpression - Constructed search query
   * @returns {SearchRequestModel} Configured search request object
   */
  #createSearchRequest(
    projectId: number,
    searchExpression: string
  ): SearchRequestModel {
    return {
      searchExpression,
      baseGUID: '',
      searchDuplicateOption: -1,
      includePC: false,
      projectId: String(projectId),
      userType: this.isExternalUser() ? 'EXTERNAL' : 'INTERNAL',
    } as SearchRequestModel
  }

  /**
   * Constructs search expression from input
   * @returns {string} Valid search syntax string
   */
  #buildSearchExpression(): string {
    const input = this.searchTermOrTags()
    return typeof input === 'string'
      ? String(input)
      : `TAGS(${this.#buildTagExpression(input)})`
  }

  /**
   * Formats tag models into search syntax
   * @param {TagsModel[]} tags - Array of tag models
   * @returns {string} OR-joined tag names string
   */
  #buildTagExpression(tags: TagsModel[]): string {
    return tags
      .filter((t) => t.treeParentId !== '-1')
      .map((t) => `"${t.tagName}"`)
      .join(' OR ')
  }

  /**
   * Handles successful search response
   * @param {ResponseModel} response - Search service response
   * @param {string} projectId - ID of the current project
   * @returns {void}
   */
  #handleSearchSuccess(response: ResponseModel, projectId: number): void {
    if (!response?.data) return

    const { reviewSetId } = this.selectedReviewSetEntry()
    this.projectFacade.fetchReviewSetDocumentView(projectId, reviewSetId, {
      searchResultTempTable: response.data.tempTables.searchResultTempTable,
    })
  }

  /**
   * Handles search errors
   * @param {unknown} error - Caught error object
   * @returns {void}
   */
  #handleSearchError(error: unknown): void {
    this.projectFacade.updateReviewSetDocumentViewLoader(false)
    const message = (error as Record<string, ResponseModel>)?.['error']?.message
    this.#showMessage(message, { style: 'error', icon: true })
  }

  /**
   * Displays notification message
   * @param {string} [content=''] - Message content to display
   * @param {Type} type - Notification type configuration
   * @returns {void}
   */
  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void =>
      notificationRef.hide()
  }

  #fetchTaggedDocumentTags(): void {
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    this.projectFacade.fetchTaggedDocumentTags(projectId, reviewSetId)
  }

  #mapTreeToIdParentId(data: TagsModel[]): TagsModel[] {
    if (!data.length) {
      return []
    }

    // 1) Build adjacency: parentKey -> array of childKeyIds
    //    Also track each node by its treeKeyId for easy lookup
    const adjacency = new Map<string, string[]>()
    const itemByKeyId = new Map<string, TagsModel>()

    for (const item of data) {
      itemByKeyId.set(item.treeKeyId, item)

      const parentKey = item.treeParentId
      if (!adjacency.has(parentKey)) {
        adjacency.set(parentKey, [])
      }
      adjacency.get(parentKey).push(item.treeKeyId)
    }

    // 2) Identify root node keys: those whose treeParentId === '-1'
    const rootKeys = data
      .filter((x) => x.treeParentId === '-1')
      .map((x) => x.treeKeyId)

    // 3) BFS to assign a unique integer ID for each treeKeyId
    const visited = new Set<string>()
    const queue = [...rootKeys]

    // key -> numeric ID
    const keyToNumericId = new Map<string, number>()

    let currentId = 1

    while (queue.length) {
      const currentKey = queue.shift()
      if (!visited.has(currentKey)) {
        visited.add(currentKey)

        // Assign the next available integer ID to this key
        keyToNumericId.set(currentKey, currentId++)

        // Enqueue its children
        const children = adjacency.get(currentKey) || []
        for (const childKey of children) {
          if (!visited.has(childKey)) {
            queue.push(childKey)
          }
        }
      }
    }

    // 4) Map the data array to new objects with `id` and `parentId`
    return data.map((item) => {
      const numericId = keyToNumericId.get(item.treeKeyId) ?? null
      let numericParentId: number | null = null

      if (item.treeParentId !== '-1') {
        numericParentId = keyToNumericId.get(item.treeParentId) ?? null
      }

      // If `treeParentId = -1`, parentId is null => root
      return {
        ...item,
        id: numericId,
        parentId: numericParentId,
      }
    })
  }

  #reloadAfterDeletionSuccess(): void {
    effect(
      () => {
        const isSuccess = this.batchDeletionStatus()
        if (!isSuccess) return
        untracked(() => this.searchReviewSetViewDetail())
      },
      { injector: this.injector }
    )
  }
}
