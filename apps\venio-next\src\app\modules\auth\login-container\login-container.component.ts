import {
  ChangeDetectionStrategy,
  Component,
  inject,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { LoginFormComponent } from '../login-form/login-form.component'
import {
  chevronLeftIcon,
  eyeIcon,
  eyeSlashIcon,
} from '@progress/kendo-svg-icons'
import { DialogService } from '@progress/kendo-angular-dialog'
import { LoginTermsComponent } from '../login-terms/login-terms.component'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { catchError, map, Observable, of } from 'rxjs'
import { HttpClient } from '@angular/common/http'

@Component({
  selector: 'venio-login-container',
  standalone: true,
  imports: [CommonModule, LoginFormComponent, NgOptimizedImage],
  templateUrl: './login-container.component.html',
  styleUrl: './login-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginContainerComponent {
  public icons = {
    eyeIcon: eyeIcon,
    slashIcon: eyeSlashIcon,
    leftIcon: chevronLeftIcon,
  }

  public dialogTitle = 'LOGIN'

  public ContentType = ContentType

  private readonly dialogService = inject(DialogService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly controlSettingService = inject(ControlSettingService)

  private readonly http = inject(HttpClient)

  public logoImage$ = this.getValidLogoPath()

  private getValidLogoPath(): Observable<string> {
    const logoPath =
      this.controlSettingService.getControlSetting.LOGIN_LOGO_PATH
    const defaultLogoPath = 'assets/img/venio-logo-full-vertical.png'

    if (!logoPath) {
      return of(defaultLogoPath)
    }

    return this.http
      .get(logoPath, {
        observe: 'response',
        responseType: 'blob',
        headers: { Accept: 'image/*' },
      })
      .pipe(
        map((response: any) =>
          response.status === 200 ? logoPath : defaultLogoPath
        ),
        catchError((err: unknown) => {
          console.error('error', err)
          return of(defaultLogoPath)
        })
      )
  }

  public onTermsOfUse(contentType: ContentType): void {
    const dialogRef = this.dialogService.open({
      appendTo: this.viewContainerRef,
      content: LoginTermsComponent,
      maxHeight: '80vh',
      height: '70vh',
      minWidth: 250,
      maxWidth: 1130,
      width: '60%',
    })
    const instance: LoginTermsComponent = dialogRef.content.instance
    instance.contentType = contentType
  }
}

export enum ContentType {
  PRIVACY_POLICY,
  TERMS_OF_SERVICE,
}
