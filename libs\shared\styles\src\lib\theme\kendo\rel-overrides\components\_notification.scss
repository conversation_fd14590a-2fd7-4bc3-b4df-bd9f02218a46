@layer {
  // for badge
  .k-badge-container {
    .k-badge-inside {
      @apply t-z-10 #{!important};
    }
  }

  // for notification
  .k-notification-group {
    @apply t-z-[30000] #{!important};

    .k-notification {
      @apply t-m-2.5 t-shadow-[0_0.14rem_0.42rem_-0.1875rem_var(--kendo-neutral-100)] t-border-0 t-p-5 t-rounded t-text-sm t-tracking-wide #{!important};

      .k-notification-status {
        @apply t-hidden #{!important};
      }

      .k-notification-action {
        background-size: 1rem !important;
        background-image: url('~apps/venio-next/src/assets/svg/Icon-ionic-md-close-circle.svg') !important;
        @apply t-mt-[-2rem] t-w-4 t-h-4 t-bg-no-repeat t-bg-center;
      }

      .k-svg-icon {
        @apply t-hidden #{!important};
      }

      .k-notification-content {
        // for fixed width of notification this should fix the coninuous text breaking - not available in kendo with this scenario
        @apply t-max-w-[50vw] t-w-full t-break-words t-font-medium;
      }

      &.k-notification-success {
        @apply t-bg-[var(--kendo-custom-secondary-10)] #{!important};
      }

      &.k-notification-warning {
        @apply t-bg-[var(--kendo-warning-20)] #{!important};
      }

      &.k-notification-error {
        @apply t-bg-[var(--v-custom-error-color)] #{!important};
      }

      &.k-notification-info {
        @apply t-bg-[var(--kendo-primary-10)] #{!important};
      }
    }
  }

  .v-append-notification-container {
    .k-notification-group {
      @apply t-inline-block t-flex-row-reverse  #{!important};
      .v-custom-save-notification {
        @apply t-py-0 t-m-0 t-w-full #{!important};
        @apply t-left-0 t-top-0 t-h-8;
        .k-notification-close-action {
          @apply t-mt-0;
        }

        .k-notification-content {
          @apply t-text-xs;
        }

        &.v-custom-notification-multiline {
          @apply t-h-auto t-p-2.5  #{!important};
          .k-notification-content {
            @apply t-break-words t-whitespace-pre-wrap;
          }
        }
      }
      .v-rule-notification{
        @apply t-p-4 t-bg-white #{!important};
        .k-notification-actions{
          @apply t-absolute t-right-[20px] t-top-[45px] #{!important};
        }
      }
    }
  }
  .v-rule-notification{
    @apply t-p-4 t-bg-white #{!important};
    .k-notification-actions{
      @apply t-absolute t-right-[20px] t-top-[45px] #{!important};
    }
  }

  .v-coding-error-notification{
    .k-notification-content{
      @apply t-pr-7 #{!important};
    }
  }
}