<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Search History Dialog</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="320"
  [height]="'33vh'"
  [minWidth]="250"
  [width]="'37%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
      <div class="t-flex t-w-full t-flex-wrap t-gap-3">
        <div showHints="always" class="t-flex t-flex-1 t-gap-1 t-flex-col">
          <div class="t-flex t-gap-3 t-mt-2 t-flex-col t-gap-4">
            <div class="t-flex">
              <input
                type="radio"
                #enableYes
                value="Yes"
                kendoRadioButton
                name="enableYesNo" />
              <kendo-label
                class="t-ml-2"
                [for]="enableYes"
                text="Search in same scope as in original search"></kendo-label>
            </div>

            <div class="t-flex">
              <input
                type="radio"
                #enableNo
                value="No"
                kendoRadioButton
                name="enableYesNo" />
              <kendo-label
                class="t-ml-2"
                [for]="enableNo"
                text="Run Search in the current scope"></kendo-label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        OK
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
