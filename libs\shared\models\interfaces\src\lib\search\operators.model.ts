export enum OperatorGroup {
  ALL = 'ALL',
  NUMERIC = 'NUMERIC',
  STRING = 'STRING',
  NULL = 'NULL',
}
/**
 * Operator model between field and inputs
 */
export interface OperatorData {
  displayName: string
  operator: FieldOperator
  group: OperatorGroup
}

export interface PrimaryValueType {
  value: string
}

/**
 * Represents the set of operators used in field comparisons.
 * Each operator is used to compare a field value against one or more values,
 * based on the specific operation it represents.
 */
export enum FieldOperator {
  /** Represents an equality comparison. */
  EQUALS = '=',

  /** Represents a non-equality comparison. */
  NOT_EQUALS = '!=',

  /** Represents a comparison to determine if a value is less than another. */
  LESS_THAN = '<',

  /** Represents a comparison to determine if a value is less than or equal to another. */
  LESS_THAN_OR_EQUAL = '<=',

  /** Represents a comparison to determine if a value is greater than another. */
  GREATER_THAN = '>',

  /** Represents a comparison to determine if a value is greater than or equal to another. */
  GREATER_THAN_OR_EQUAL = '>=',

  /** Represents a range comparison, checking if a value lies between two other values. */
  BETWEEN = 'BETWEEN',

  /**
   * Represents a 'LIKE' comparison where the field value should contain a specified substring.
   * The placeholder {{value}} will be replaced with the actual value, e.g., LIKE "*text*".
   */
  LIKE = 'LIKE *{{value}}*',

  /**
   * Represents a 'LIKE' comparison where the field value should begin with a specified substring.
   * The placeholder {{value}}* will be replaced with the actual value, e.g., LIKE "text*".
   */
  BEGINS_WITH = 'LIKE {{value}}*',

  /**
   * Represents a 'LIKE' comparison where the field value should end with a specified substring.
   * The placeholder *{{value}} will be replaced with the actual value, e.g., LIKE "*text".
   */
  ENDS_WITH = 'LIKE *{{value}}',

  /** Represents a comparison to check if a field value is null. */
  IS_NULL = 'IS NULL',

  /** Represents a comparison to check if a field value is not null. */
  IS_NOT_NULL = 'IS NOT NULL',
}

/**
 * Contains constant values and predefined sets of operators and types used in query operations.
 * This class serves as a central repository for various constants used throughout the application.
 */
export class CONSTANTS {
  /**
   * Array of `OperatorData` for equate operations. Includes operators like 'EQUALS'.
   * This set is typically used in scenarios where equality comparison is required.
   */
  public static EQUATE_OPERATORS: OperatorData[] = [
    {
      displayName: 'EQUALS ( = )',
      operator: FieldOperator.EQUALS,
      group: OperatorGroup.ALL,
    },
  ]

  /**
   * Array of `OperatorData` for not-equate operations. Includes operators like 'NOT EQUALS'.
   * This set is used for non-equality comparisons.
   */
  public static NOT_EQUATE_OPERATORS: OperatorData[] = [
    {
      displayName: 'NOT EQUALS ( != )',
      operator: FieldOperator.NOT_EQUALS,
      group: OperatorGroup.ALL,
    },
  ]

  /**
   * Array of `OperatorData` for numeric operations.
   * Includes operators like 'LESS THAN', 'GREATER THAN', etc., specifically for numeric comparisons.
   * These operators are suitable for fields that contain numeric values.
   */
  public static NUMERIC_OPERATORS: OperatorData[] = [
    {
      displayName: 'LESS THAN ( < )',
      operator: FieldOperator.LESS_THAN,
      group: OperatorGroup.NUMERIC,
    },
    {
      displayName: 'LESS THAN EQUAL TO ( <= )',
      operator: FieldOperator.LESS_THAN_OR_EQUAL,
      group: OperatorGroup.NUMERIC,
    },
    {
      displayName: 'GREATER THAN ( > )',
      operator: FieldOperator.GREATER_THAN,
      group: OperatorGroup.NUMERIC,
    },
    {
      displayName: 'GREATER THAN EQUAL TO ( >= )',
      operator: FieldOperator.GREATER_THAN_OR_EQUAL,
      group: OperatorGroup.NUMERIC,
    },
    {
      displayName: 'BETWEEN',
      operator: FieldOperator.BETWEEN,
      group: OperatorGroup.NUMERIC,
    },
  ]

  /**
   * Array of field names related to size measurements like 'FILE_SIZE', 'FULLTEXT_SIZE'.
   * These fields are typically used in file or document size related queries.
   */
  public static fieldForSize: string[] = ['FILE_SIZE', 'FULLTEXT_SIZE']

  /**
   * Array of `OperatorData` for string operations.
   * Includes string comparison operators like 'CONTAINS (LIKE)', 'BEGINS WITH', and 'ENDS WITH'.
   * Useful for text-based fields where string pattern matching is required.
   */
  public static STRING_OPERATORS: OperatorData[] = [
    {
      displayName: 'CONTAINS (LIKE)',
      operator: FieldOperator.LIKE,
      group: OperatorGroup.STRING,
    },
    {
      displayName: 'BEGINS WITH',
      operator: FieldOperator.BEGINS_WITH,
      group: OperatorGroup.STRING,
    },
    {
      displayName: 'ENDS WITH',
      operator: FieldOperator.ENDS_WITH,
      group: OperatorGroup.STRING,
    },
  ]

  /**
   * Array of `OperatorData` for null-check operations.
   * Includes operators for checking null values like 'IS NULL' and 'IS NOT NULL'.
   * These are used primarily for fields where the existence of a value is in question.
   */
  public static NULL_OPERATORS: OperatorData[] = [
    {
      displayName: 'IS NULL',
      operator: FieldOperator.IS_NULL,
      group: OperatorGroup.NULL,
    },
    {
      displayName: 'IS NOT NULL',
      operator: FieldOperator.IS_NOT_NULL,
      group: OperatorGroup.NULL,
    },
  ]

  /**
   * Array of `PrimaryValueType` representing different types of search document types like 'EDOC', 'EMAIL', etc.
   * These types are used to categorize and filter search results based on document type.
   */
  public static SEARCH_DOCUMENT_TYPES: PrimaryValueType[] = [
    {
      value: 'EDOC',
    },
    {
      value: 'EDOC_ATTACH',
    },
    {
      value: 'EMAIL',
    },
    {
      value: 'EMAIL_ATTACH',
    },
  ]

  /**
   * Array of `PrimaryValueType` representing family document types for searches.
   * These are a subset of document types, typically used for more focused search operations.
   */
  public static SEARCH_FAMILY_DOCUMENT_TYPES: PrimaryValueType[] = [
    {
      value: 'EDOC',
    },
    {
      value: 'EMAIL',
    },
  ]

  /**
   * Array of `PrimaryValueType` representing full-text search preferences.
   * Includes values like 'EXTRACTED', 'TIFF_OCR', and 'REDACTED_OCR'.
   * These preferences are used to specify the type of text extraction used in full-text searches.
   */
  public static SEARCH_FULLTEXT_PREFERENCES: PrimaryValueType[] = [
    {
      value: 'EXTRACTED',
    },
    {
      value: 'TIFF_OCR',
    },
    {
      value: 'REDACTED_OCR',
    },
  ]
}
