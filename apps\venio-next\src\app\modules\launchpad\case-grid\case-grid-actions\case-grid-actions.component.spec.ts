import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseGridActionsComponent } from './case-grid-actions.component'
import { CUSTOM_ELEMENTS_SCHEMA, input, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { CaseDetailModel } from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('CaseGridActionsComponent', () => {
  let component: CaseGridActionsComponent
  let fixture: ComponentFixture<CaseGridActionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CaseGridActionsComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            isFavoriteProjectToggleLoading$: of(false),
            selectProjectIdsToRights$: of({}),
            selectIsMediaStatusLoading$: of({} as Record<number, boolean>),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseGridActionsComponent)
    component = fixture.componentInstance
    TestBed.runInInjectionContext(() => {
      component.rowDataItem = input<CaseDetailModel>({} as CaseDetailModel)
    })
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
