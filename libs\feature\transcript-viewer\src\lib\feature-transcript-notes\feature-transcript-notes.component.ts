import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
  computed,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  TreeViewComponent,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  TranscriptFacade,
  TranscriptNote,
  TranscriptState,
} from '@venio/data-access/review'
import { SVGIcon, checkIcon, xIcon } from '@progress/kendo-svg-icons'
import { Subject, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'venio-feature-transcript-notes',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    ButtonsModule,
    InputsModule,
    SvgLoaderDirective,
    TooltipsModule,
    DropDownsModule,
    ReactiveFormsModule,
    DialogsModule,
    DynamicHeightDirective,
  ],
  templateUrl: './feature-transcript-notes.component.html',
  styleUrl: './feature-transcript-notes.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeatureTranscriptNotesComponent implements OnInit, OnDestroy {
  public dialogTitle = 'Notes'

  public opened = false

  public commonActionTypes = CommonActionTypes

  public checkIcon: SVGIcon = checkIcon

  public xIcon: SVGIcon = xIcon

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  @ViewChild(TreeViewComponent, { static: false })
  public treeView: TreeViewComponent

  public transcriptNoteForm: FormGroup

  public errorMessages = signal(null)

  public showNewNotePanel = computed(
    () => this.transcriptNoteActionType() === CommonActionTypes.NEW_NOTE
  )

  public transcriptNotes: TranscriptNote[]

  public transcriptNoteActionType = signal(CommonActionTypes.NEW_NOTE)

  public toDestory$: Subject<void> = new Subject<void>()

  @ViewChild('addEditNotePosition')
  public addEditNotePosition: ElementRef<HTMLDivElement>

  constructor(
    private transcriptState: TranscriptState,
    private transcriptFacade: TranscriptFacade,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#fetchTranscriptNotes()
    this.#fetchTranscriptNoteStatus()
    this.#initForm()
  }

  #fetchTranscriptNoteStatus(): void {
    this.transcriptFacade.getNoteUpdateStatus
      .pipe(takeUntil(this.toDestory$))
      .subscribe((isNoteUpdated) => {
        if (isNoteUpdated) this.#fetchTranscriptNotes()
      })
  }

  #fetchTranscriptNotes(): void {
    this.transcriptNotes =
      this.transcriptState.selectedNote()?.note?.filter(Boolean) ?? []
  }

  #initForm(): void {
    this.cdr.markForCheck()
    this.transcriptNoteForm = this.formBuilder.group({
      note: ['', Validators.required],
    })
  }

  #getErrorMessageByControl(controlName: string): string {
    const labels: { [key: string]: string } = {
      note: 'Note is required',
    }

    return labels[controlName] || controlName
  }

  #validateForm(isFromSubmit = false): void {
    //clear the all existing error messages
    this.errorMessages.set({})
    const errors: { [key: string]: string } = {}
    Object.keys(this.transcriptNoteForm.controls).forEach((key) => {
      const control = this.transcriptNoteForm.get(key)
      if (isFromSubmit) control.markAsTouched()
      if (control.invalid && (control.dirty || control.touched)) {
        const errorMessage = this.#getErrorMessageByControl(key)
        if (errorMessage) {
          errors[key] = errorMessage
        }
      }
    })

    this.errorMessages.set(errors)
  }

  /**
   * Handles tag group action.
   * @param {CommonActionTypes} actionType - The type of action performed.
   * @param {DocumentNote} editDocumentNote - The document note being edited.
   */
  public onDocumentNoteAction(actionType: CommonActionTypes): void {
    this.transcriptNoteActionType.set(actionType)
    this.transcriptNoteForm.controls['note'].markAsUntouched()
    switch (actionType) {
      case CommonActionTypes.NEW_NOTE:
        this.#validateForm()
        this.handleNewNoteAction()
        this.scrollToElement()
        break
      case CommonActionTypes.CANCEL:
        this.close()
        break
    }
  }

  private handleNewNoteAction(): void {
    this.transcriptNoteForm.patchValue({
      note: '',
    })
  }

  // Scroll to the new note panel
  public scrollToElement(): void {
    this.addEditNotePosition.nativeElement.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    })
  }

  public addNewNote(): void {
    const note = this.transcriptNoteForm?.value?.note
    this.#validateForm(true)
    if (
      this.transcriptNoteForm.invalid ||
      Object.keys(this.errorMessages()).length > 0
    )
      return
    if (this.transcriptNoteActionType() === CommonActionTypes.NEW_NOTE) {
      const newNote: TranscriptNote = {
        note: note,
        createdOn: new Date(),
        creadtedBy: this.transcriptState.currentUserDetails()?.fullName ?? '',
      }
      this.transcriptFacade.setTranscriptNote = newNote
      this.transcriptNoteActionType.set(CommonActionTypes.CANCEL)
    }
  }

  // Scroll to a specific node for better user experience
  public handleScroll(event: any): void {
    // Finding the node index or using a unique identifier
    const lookupKey = event.index // This could be another identifier depending on your data structure
    const nodeElement = this.treeView.element.nativeElement.querySelector(
      `[data-treeindex="${lookupKey}"]`
    )
    if (nodeElement) {
      nodeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      })
    }
  }

  public close(): void {
    this.opened = false
    this.dialogRef.close()
  }

  public ngOnDestroy(): void {
    this.toDestory$.next()
    this.toDestory$.complete()
  }
}
