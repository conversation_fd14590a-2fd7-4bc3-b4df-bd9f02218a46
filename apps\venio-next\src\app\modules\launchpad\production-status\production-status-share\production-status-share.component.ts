import { CommonModule } from '@angular/common'
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  ViewChild,
} from '@angular/core'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { EditorModule } from '@progress/kendo-angular-editor'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { NotificationModule } from '@progress/kendo-angular-notification'
import {
  chevronLeftIcon,
  plusIcon,
  userOutlineIcon,
} from '@progress/kendo-svg-icons'
import {
  ProductionFacade,
  ProductionShareInvitationService,
} from '@venio/data-access/common'
import { ProductionShareUserModel } from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { Subject, debounceTime, takeUntil, combineLatest, filter } from 'rxjs'

@Component({
  selector: 'venio-production-status-share',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    ButtonsModule,
    GridModule,
    DropDownsModule,
    InputsModule,
    EditorModule,
    LayoutModule,
    LabelModule,
    DialogsModule,
    SvgLoaderDirective,
    NotificationModule,
    LoaderModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './production-status-share.component.html',
  styleUrl: './production-status-share.component.scss',
})
export class ProductionStatusShareComponent implements OnInit, OnDestroy {
  @ViewChild('treeListContainer', { static: true })
  private treelistContainerDivRef!: ElementRef

  public projectId = input<number>()

  public exportId = input<number>()

  public backToProductionShare = output<boolean>()

  private readonly dialogRef = inject(DialogRef)

  private readonly productionFacade = inject(ProductionFacade)

  private noticationService = inject(VenioNotificationService)

  private productionShareService = inject(ProductionShareInvitationService)

  public icons = {
    UserOutlineIcon: userOutlineIcon,
    chevronLeftIcon: chevronLeftIcon,
  }

  public plusIcon = plusIcon

  public infoSvgUrl = 'assets/svg/material_info_outline.svg'

  public internalUsers: ProductionShareUserModel[] = []

  public externalUsers: ProductionShareUserModel[] = []

  public shareToExternalUsers = false

  public filteredInternalUsers: ProductionShareUserModel[] = []

  public filteredExternalUsers: ProductionShareUserModel[] = []

  public fileTypeGridHeight!: number

  private resizeEvent = new Subject<Event>()

  public defaultItemExternal: { text: string; value: number } = {
    text: 'Add/Select External User',
    value: null,
  }

  private readonly toDestroy$ = new Subject<void>()

  public validityOptions = ['1', '7', '30']

  public internalUserSelectionKey: string

  public externalUserSelectionKey: string

  public get productionShareForm(): FormGroup {
    return this.productionShareService.productionShareForm
  }

  public selectedExternalUsers: string[] = []

  public selectedInternalUsers: string[] = []

  public invitationInProgress$ =
    this.productionFacade.selectInvitationInProgressFlag$

  constructor(private readonly cdr: ChangeDetectorRef) {}

  public ngOnInit(): void {
    this.resizeEvent
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe((): void => {
        this.resizeFileTypeGrid()
        this.cdr.markForCheck()
      })

    this.productionFacade.fetchInternalUser(this.projectId())
    this.productionFacade.fetchExternalUser()

    this.initSlices()
  }

  @HostListener('window:resize', ['$event'])
  public onContainerResize(event: Event): void {
    this.resizeEvent.next(event)
  }

  private resizeFileTypeGrid(): void {
    this.fileTypeGridHeight = 0
    setTimeout((): void => {
      const divElement = this.treelistContainerDivRef.nativeElement
      this.fileTypeGridHeight =
        divElement.offsetHeight - 8 < 140 ? 140 : divElement.offsetHeight - 8
      this.cdr.markForCheck()
    }, 30)
  }

  private initSlices(): void {
    combineLatest([
      this.productionFacade.selectInternalUserSuccessResponse$,
      this.productionFacade.selectExternalUserSuccessResponse$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: any): void => {
        this.externalUsers = response[1]
        this.internalUsers = response[0]
        this.internalUsers = this.internalUsers.map(
          (user: ProductionShareUserModel) => ({
            ...user,
            userInfoString:
              user.userID +
              ':' +
              user.email +
              ':' +
              user.userName +
              ':' +
              user.groupName,
          })
        )

        this.filteredInternalUsers = [...this.internalUsers]
        this.filteredExternalUsers = [...this.externalUsers]
        this.internalUserSelectionKey = 'userInfoString'
        this.externalUserSelectionKey = 'email'
        this.cdr.detectChanges()
      })

    this.productionShareForm
      .get('shareToExternalUsers')
      ?.valueChanges.pipe(takeUntil(this.toDestroy$))
      .subscribe((value: boolean): void => {
        this.shareToExternalUsers = value
        if (value) {
          this.productionShareForm.get('newEmail')?.enable()
        } else {
          this.productionShareForm.get('newEmail')?.disable()
        }
        this.cdr.detectChanges()
      })

    this.productionFacade.selectUserMessage$
      .pipe(
        filter((msg): boolean => !!msg && !!msg.message),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response): void => {
        if (response.success) {
          this.noticationService.showSuccess(response.message)
          this.backToProductionShare.emit(true)
        } else {
          this.noticationService.showError(response.message)
          this.backToProductionShare.emit(true)
        }
        this.productionFacade.setUserMessage(null, false)
        this.cdr.detectChanges()
      })

    this.productionFacade.selectInvitationInProgressFlag$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((inProgress: boolean): void => {
        if (!inProgress) {
          this.productionShareForm.get('shareToExternalUsers')?.enable()
        } else {
          this.productionShareForm.get('shareToExternalUsers')?.disable()
          this.productionShareForm.get('newEmail')?.disable()
        }
        this.cdr.detectChanges()
      })
  }

  public onFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.toLowerCase()
    this.filteredInternalUsers = this.internalUsers.filter(
      (user: ProductionShareUserModel) =>
        user.userName.toLowerCase().includes(filterValue)
    )
    this.cdr.detectChanges()
  }

  public onFilterExternalUser(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.toLowerCase()
    this.filteredExternalUsers = this.externalUsers.filter(
      (user: ProductionShareUserModel) =>
        user.userName.toLowerCase().includes(filterValue)
    )
    this.cdr.detectChanges()
  }

  public addExternalUser(): void {
    const emailControl = this.productionShareForm.get('newEmail')
    const newEmail = emailControl?.value?.trim().toLowerCase() ?? ''
    emailControl?.setValue(newEmail)

    if (!newEmail || !this.validateEmail(newEmail)) {
      this.productionFacade.setUserMessage(
        'Please enter a valid email address.',
        false
      )
      return
    } else if (this.externalUserExists(newEmail)) {
      this.productionFacade.setUserMessage(
        'The provided email already exists in the list.',
        false
      )
      return
    }

    const newUser: ProductionShareUserModel = {
      email: newEmail,
      userName: newEmail,
      userID: 0,
      groupName: '',
      userInfoString: '',
    }
    this.externalUsers = [...this.externalUsers, newUser]
    this.filteredExternalUsers = [...this.externalUsers]
    emailControl?.setValue('')
    this.cdr.detectChanges()
  }

  private validateEmail(email: string): boolean {
    const emailRegex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    const isEmailFormatOk = emailRegex.test(email)
    if (!isEmailFormatOk) {
      return false
    }
    const emailParts = email.split('@')
    if (
      emailParts[0]?.length > 64 ||
      (emailParts?.length > 1 && emailParts[1].length > 255) ||
      email?.length > 320
    ) {
      return false
    }
    return true
  }

  private externalUserExists(email: string): boolean {
    return this.filteredExternalUsers?.some(
      (user: ProductionShareUserModel) => user?.email === email
    )
  }

  public onExternalUserSelectionChange(): void {
    this.productionShareService.setExternalUser(this.selectedExternalUsers)
  }

  public onInternalUserSelectionChange(): void {
    this.productionShareService.setInternalUser(this.selectedInternalUsers)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public onShare(): void {
    const formValue = this.productionShareService.getFormValue()

    // Manually remove unwanted properties
    const filteredData = { ...formValue }
    delete filteredData.newEmail
    delete filteredData.shareToExternalUsers

    filteredData.recipientUserIds = this.#extractRecipientsIds(
      formValue?.invitedIntUserInfo
    )
    filteredData.exportId = this.exportId()

    this.productionFacade.sendProductionDownloadInvitation(
      this.projectId(),
      filteredData
    )

    this.productionFacade.selectSendProductionDownloadInvitationSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.productionFacade.setUserMessage(
            'Invitation Sent Successfully',
            true
          )
        }
      })
    this.productionFacade.selectSendProductionDownloadInvitationErrorResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.productionFacade.setUserMessage(res?.message, false)
        }
      })
  }

  #extractRecipientsIds(internalUser: string[]): number[] {
    return internalUser.map((item) => {
      return +item.split(':')[0]
    })
  }

  public close(): void {
    this.dialogRef.close()
  }
}
