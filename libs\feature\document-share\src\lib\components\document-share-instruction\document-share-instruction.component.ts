import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { FormGroup } from '@angular/forms'
import { DocumentShareFacade } from '@venio/data-access/review'
import { Subject, takeUntil } from 'rxjs'
import { EditorComponent } from '@progress/kendo-angular-editor'
import { SharedDocInjectModel } from '../../models/document-share.models'

@Component({
  selector: 'venio-document-share-instruction',
  templateUrl: './document-share-instruction.component.html',
  styleUrl: './document-share-instruction.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentShareInstructionComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() public sharedDocData: SharedDocInjectModel | null

  private readonly toDestroy$ = new Subject<void>()

  @ViewChild('editor', { static: true }) public editor!: EditorComponent

  public invitationInProgress$ =
    this.documentShareFacade.getInvitationInProgressFlag$

  public get documentShareForm(): FormGroup {
    return this.documentShareFormService.documentShareForm
  }

  constructor(
    private documentShareFacade: DocumentShareFacade,
    private documentShareFormService: DocumentShareFormService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    if (this.sharedDocData?.isDocShareEdit) {
      this.documentShareForm.get('instruction').disable()
    } else {
      this.invitationInProgress$
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((inProgress) => {
          if (inProgress) {
            this.documentShareForm.get('instruction').disable()
          } else {
            this.documentShareForm.get('instruction').enable()
          }
          this.cdr.detectChanges()
        })
    }
  }

  public ngAfterViewInit(): void {
    const interval = setInterval(() => {
      if (this.editor.view) {
        clearInterval(interval)
        this.editor?.exec('fontSize', '14px')
      }
    }, 100)
  }
}
