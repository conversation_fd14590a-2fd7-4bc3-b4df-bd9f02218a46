import {
  FlattenedTranscriptData,
  LinkedDocument,
  Page,
  TranscriptAnnotations,
  TranscriptAnnotationType,
  TranscriptReportData,
  TranscriptViewModel,
} from '@venio/shared/models/interfaces'
import { expose } from 'comlink'

const flattenTranscriptData = (
  data: Page,
  documentArray: any
): TranscriptViewModel => {
  const flattenedData: FlattenedTranscriptData[] = []
  const linkedDocuments: LinkedDocument[] = []

  for (const [pageNumber, lines] of Object.entries(data)) {
    lines.forEach((line) => {
      for (const [lineNumber, text] of Object.entries(line)) {
        flattenedData.push({
          pageNumber: +pageNumber,
          lineNumber: +lineNumber,
          text: text as string,
        })
      }
    })
  }

  // get the linked documents details
  documentArray?.forEach((item) => {
    const linkedFile = item?.linkedFile
      ? Array.isArray(item.linkedFile)
        ? [...item.linkedFile]
        : [item.linkedFile]
      : []
    linkedFile?.forEach((file) => {
      if (file.fileId && file.fileName) {
        linkedDocuments.push({
          fileId: file.fileId,
          fileName: file.fileName,
          index: item.index,
          id: item.id,
        })
      }
    })
  })
  const transcriptViewModel: TranscriptViewModel = {
    linkedDocuments: linkedDocuments,
    flattenedTranscriptData: flattenedData,
  }
  return transcriptViewModel
}

const transcriptReport = (
  transcriptAnnotationData: any
): TranscriptReportData[] => {
  const reportGroups = new Map<string, Map<number, number[]>>()
  const reportData: TranscriptReportData[] = []
  const cobimedReportData = cobimedReport(transcriptAnnotationData)

  // Group highlights by page and collect line numbers
  cobimedReportData.forEach((item) => {
    const index = item?.index?.replace(/[a-zA-Z]+/g, '')
    const [page, line] = index.split(':').map(Number)
    const reportName = item.annotationType
    if (!reportGroups.has(reportName)) {
      reportGroups.set(reportName, new Map())
    }
    const reportPages = reportGroups.get(reportName)
    if (!reportPages.has(page)) {
      reportPages.set(page, [])
    }
    reportPages.get(page).push(line)
  })

  reportGroups.forEach((reportPages, reportName) => {
    let rowId = 1
    reportPages.forEach((lines, page) => {
      const minLine = Math.min(...lines)
      const maxLine = Math.max(...lines)
      const range = `[${page}:${minLine}-${page}:${maxLine}]`
      let i = 0

      // Add each highlight from this page and report to reportData
      cobimedReportData
        .filter(
          (h) =>
            h.index.startsWith(`${page}:`) && h.annotationType === reportName
        )
        .forEach((item) => {
          reportData.push({
            action: i === 0 ? item.annotationType : '',
            rangeNumber: i === 0 ? range : '',
            lineNumber: item.index,
            description: item.selectedText,
            reportType: item.annotationType,
            reportId: item.reportId,
            notes: item.notes,
            fileId: item.fileId,
            fileName: item.fileName,
            rowId: rowId,
          })
          i++
          rowId++
        })
    })
  })
  return reportData
}

/**
 * Process the cobimed report data.
 * @param {transcriptAnnotationData} report input data.
 * @returns {TranscriptAnnotations} combined report.
 */
function cobimedReport(transcriptAnnotationData: any): TranscriptAnnotations[] {
  const annotations: TranscriptAnnotations[] = []
  transcriptAnnotationData.highlights.forEach((item) => {
    annotations.push({
      index: item.index.replace(/[a-zA-Z]+/g, ''),
      selectedText: item.selectedText,
      annotationType: 'Highlight',
      reportId: TranscriptAnnotationType.HIGHLIGHT,
    })
  })
  transcriptAnnotationData.notes.forEach((item) => {
    const notes = item.note
      .filter(Boolean)
      .map((n) => n.note)
      .join(', ')
    annotations.push({
      index: item.index.replace(/[a-zA-Z]+/g, ''),
      selectedText: item.selectedText,
      annotationType: 'Notes',
      reportId: TranscriptAnnotationType.NOTES,
      notes: notes,
    })
  })
  transcriptAnnotationData.linkDocuments.forEach((item) => {
    annotations.push({
      index: item.index.replace(/[a-zA-Z]+/g, ''),
      selectedText: item.selectedText,
      annotationType: 'Document Link',
      reportId: TranscriptAnnotationType.DOCUMENTLINK,
      fileId: item.linkedFile.fileId,
      fileName: item.linkedFile.fileName,
    })
  })
  return annotations
}

expose({
  flattenTranscriptData,
  transcriptReport,
})
