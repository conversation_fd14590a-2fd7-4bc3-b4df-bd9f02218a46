import { ComponentFixture, TestBed } from '@angular/core/testing'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms'
import { DirectExportDetailComponent } from './direct-export-detail.component'
import { ProjectFacade, DirectExportFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  CaseDetailResponseModel,
  ReviewSetSummary,
} from '@venio/shared/models/interfaces'

jest.mock(
  'libs/data-access/common/src/lib/+state/direct-export/direct-export.facade'
)
jest.mock('@venio/data-access/common')

describe('DirectExportDetailComponent', () => {
  let component: DirectExportDetailComponent
  let fixture: ComponentFixture<DirectExportDetailComponent>
  let directExportFacade: Partial<jest.Mocked<DirectExportFacade>>
  let projectFacade: Partial<jest.Mocked<ProjectFacade>>

  beforeEach(async () => {
    directExportFacade = {
      selectCaseData$: of(null),
      selectDefaultData$: of(null),
      selectIsRelativityWorkspaceLoading$: of(false),
      selectIsRelativityWorkspaceFileshareLoading$: of(false),
      selectConnctorEnvironmentSuccess$: of([]),
      selectRelativityFieldTemplatesSuccess$: of([]),
      selectRelativityWorkspaceSuccess$: of([]),
      selectRelativityWorkspaceFileshareSuccess$: of([]),
      fetchExistingCaseData: jest.fn(),
      fetchServiceTypeDefaultData: jest.fn(),
      fetchConnectorEnvironments: jest.fn(),
      fetchRelativityTemplates: jest.fn(),
      fetchRelativityWorkspaces: jest.fn(),
      fetchRelativityWorkspaceFileshares: jest.fn(),
    }

    projectFacade = {
      selectCaseDetail$: of({
        caseDetailEntries: [],
        totalCaseCount: 0,
        totalCustodianCount: 0,
        totalReviewSetCount: { count: 0 } as unknown as ReviewSetSummary,
      } as CaseDetailResponseModel),
      selectIsCaseDetailLoading$: of(false),
    }

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        ReactiveFormsModule,
        DirectExportDetailComponent,
      ],
      providers: [
        { provide: DirectExportFacade, useValue: directExportFacade },
        { provide: ProjectFacade, useValue: projectFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DirectExportDetailComponent)
    component = fixture.componentInstance

    component.settingsForm = new FormGroup({
      caseName: new FormControl(''),
      ServiceRequestType: new FormControl(''),
      selectedCase: new FormControl(''),
      generalSettings: new FormGroup({}),
    })

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
