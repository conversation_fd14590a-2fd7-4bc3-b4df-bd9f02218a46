import { Component, OnDestroy, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  IframeMessengerService,
  MessageType,
  AppIdentitiesTypes,
} from '@venio/data-access/iframe-messenger'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import {
  Subject,
  combineLatest,
  filter,
  take,
  takeUntil,
  withLatestFrom,
} from 'rxjs'
import dayjs from 'dayjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { NotificationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-send-to-production',
  standalone: true,
  imports: [],
  template: '',
  styles: '',
})
export class SendToProductionComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private dialogService: DialogService,
    private iframeMessengerService: IframeMessengerService,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade
  ) {}

  public ngOnInit(): void {
    // setup menu select event
    this.#selectedDocumentEvent()

    // handle save search response for production
    this.#handleSaveSearchForProduction()

    // handle failed save search response to show message and reset events
    this.#handleFailedSaveSearchResponse()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetEvents()
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.PRODUCTION),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#handleSendToProduction()
      })
  }

  /**
   * Listens for successful saved search response and if flag is set for production, it launches production.
   * @returns {void}
   */
  #handleSaveSearchForProduction(): void {
    // handles successful save search response
    this.searchFacade.getSaveSearchSuccessResponse$
      .pipe(
        filter((res) => !!res),
        withLatestFrom(this.searchFacade.getIsSaveSearchForProduction$),
        filter(([_, isSaveSearchForProduction]) => isSaveSearchForProduction),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([res, _]: [ResponseModel, boolean]) => {
        // reset save search for production flag
        this.searchFacade.resetIsSaveSearchForProductionFlag()

        // launch production
        const savedSearchId = +res.data
        if (savedSearchId > 0) this.#launchProduction(savedSearchId)
      })
  }

  /**
   * Listens for failed saved search response and if flag is set for production, it shows a notification message.
   * @returns {void}
   */
  #handleFailedSaveSearchResponse(): void {
    this.searchFacade.getSaveSearchFailureResponse$
      .pipe(
        filter((res) => !!res),
        withLatestFrom(this.searchFacade.getIsSaveSearchForProduction$),
        filter(([_, isSaveSearchForProduction]) => isSaveSearchForProduction),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([res, _]: [ResponseModel | any, boolean]) => {
        // reset save search for production flag
        this.searchFacade.resetIsSaveSearchForProductionFlag()

        // stop showing search loading indicator
        this.searchFacade.IsSearchLoading(false)

        // show notification message. `res.ExceptionMessage` or `res.Message` is dotnet error message incase of exceptions
        this.#showNotificationMessage(
          res.message ?? res.ExceptionMessage ?? res.Message
        )
        this.#resetEvents()
      })
  }

  /**
   * Handles production menu selection. It takes the latest document selection, validates and saves the search.
   * @returns {void}
   */
  #handleSendToProduction(): void {
    combineLatest([
      this.searchFacade.getSearchTempTables$,
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
    ])
      .pipe(take(1))
      .subscribe(
        ([
          tempTables,
          isBatchSelected,
          selectedDocs,
          unselectedDocs,
          totalHitCount,
        ]) => {
          // if no documents is selected, show an error message
          if (
            !this.#validateDocSelection(
              isBatchSelected,
              selectedDocs,
              unselectedDocs,
              totalHitCount
            )
          ) {
            return
          }

          // show loading animation
          this.searchFacade.IsSearchLoading(true)

          //else save the search
          const searchGuid = tempTables.searchGuid
          this.#saveSearchForProduction(
            searchGuid,
            isBatchSelected,
            selectedDocs,
            unselectedDocs
          )
        }
      )
  }

  /**
   * Validates document selection. If no document is selected, it shows a notification message.
   * @param {boolean} isBatchSelected flag indicating if all documents were selected as "select all from all pages"
   * @param {number[]} selectedDocs array of selected documents
   * @param {number[]} unselectedDocs array of unselected documents
   * @param {number} totalHitCount total hit count of the search
   * @returns {boolean} true if at least one document is selected, otherwise false.
   */
  #validateDocSelection(
    isBatchSelected: boolean,
    selectedDocs: number[],
    unselectedDocs: number[],
    totalHitCount: number
  ): boolean {
    let selectedDocCount = 0
    if (isBatchSelected) {
      selectedDocCount = totalHitCount - unselectedDocs.length
    } else {
      selectedDocCount = selectedDocs.length
    }
    if (!selectedDocCount) {
      this.#showNotificationMessage(
        'Please select at least one document to send to production.'
      )
      return false
    }
    return true
  }

  /**
   * Saves search with saveSearchForProduction flag set to true, which will be used to redirect to production page.
   * @param {string} searchGuid search guid taken from the search temp tables
   * @param {boolean} isBatchSelected flag indicating if all documents were selected as "select all from all pages"
   * @param {number[]} selectedDocs array of selected documents
   * @param {number[]} unselectedDocs array of unselected documents
   * @returns {void}
   */
  #saveSearchForProduction(
    searchGuid: string,
    isBatchSelected: boolean,
    selectedDocs: number[],
    unselectedDocs: number[]
  ): void {
    const saveSearchRequest = {
      searchGuid: searchGuid,
      searchName: 'ProduceSelected_' + dayjs().format('YYYYMMDD_HHmmss'),
      saveOnCustomField: true,
      isNewCustomField: true,
      customFieldId: null,
      customFieldName: null,
      applyAutoTagBasedOnSearchTerm: false,
      useExistingTag: false,
      tagGroupIdOfExistingSavedSearch: null,
      isBatchSelected: isBatchSelected,
      selectedFileIds: !isBatchSelected ? selectedDocs : [],
      unSelectedFileIds: isBatchSelected ? unselectedDocs : [],
    }
    this.searchFacade.saveSearch(this.projectId, saveSearchRequest, true)
  }

  /**
   * Sends a message to the parent window (old vod) to launch production with the saved search id.
   * The savedSearchId is stored in old vod store and will be used to set the source in the production page.
   * @param {number} savedSearchId id of the saved search
   * @returns {void}
   */
  #launchProduction(savedSearchId: number): void {
    this.iframeMessengerService.sendMessage({
      payload: {
        type: MessageType.PRODUCTION_LAUNCH,
        content: {
          savedSearchId,
          projectId: this.projectId,
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
    // stop showing search loading indicator
    this.searchFacade.IsSearchLoading(false)
    this.#resetEvents()
  }

  /**
   * Shows a notification dialog with the given message.
   * @param {string} message message to be displayed in the notification dialog
   * @returns {void}
   */
  #showNotificationMessage(message: string): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance, message)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetEvents()
      })
  }

  /**
   * Sets the title and message in the notification dialog.
   * @param {NotificationDialogComponent} instance notification dialog instance
   * @param {string} message message to be displayed in the dialog
   * @returns {void}
   */
  #setDialogInput(
    instance: NotificationDialogComponent,
    message: string
  ): void {
    instance.title = 'Production'
    instance.message = message
  }

  #resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }
}
