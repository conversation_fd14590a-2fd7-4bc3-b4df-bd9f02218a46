import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  OnDestroy,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { ReviewsetDetailViewFilterToolbarComponent } from '../reviewset-detail-view-filter-toolbar/reviewset-detail-view-filter-toolbar.component'
import {
  ReviewSetEntry,
  ReviewSetViewDetailViewTypes,
} from '@venio/shared/models/interfaces'
import { ReviewsetDetailViewContainerComponent } from '../reviewset-detail-view-container/reviewset-detail-view-container.component'
import { ProjectFacade } from '@venio/data-access/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-reviewset-detail-view-dialog-container',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    LoaderComponent,
    SvgLoaderDirective,
    ReviewsetDetailViewFilterToolbarComponent,
    ReviewsetDetailViewContainerComponent,
  ],
  templateUrl: './reviewset-detail-view-dialog-container.component.html',
  styleUrl: './reviewset-detail-view-dialog-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewDialogContainerComponent implements OnDestroy {
  private readonly projectFacade = inject(ProjectFacade)

  // default view type
  public readonly selectedViewType = signal(
    ReviewSetViewDetailViewTypes.BATCH_VIEW
  )

  public readonly batchDeletionStatus = signal<boolean>(false)

  public readonly isTreeView = computed(() => {
    return this.selectedViewType() === ReviewSetViewDetailViewTypes.TREE_VIEW
  })

  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public ngOnDestroy(): void {
    this.#resetViewDetailState()
  }

  public switchToTreeView(): void {
    this.selectedViewType.set(ReviewSetViewDetailViewTypes.TREE_VIEW)
  }

  public switchToBatchView(): void {
    this.selectedViewType.set(ReviewSetViewDetailViewTypes.BATCH_VIEW)
  }

  public batchDeletionStatusChanged(status: boolean): void {
    this.batchDeletionStatus.set(status)
  }

  #resetViewDetailState(): void {
    this.projectFacade.resetProjectState([
      'reviewSetDocumentViewSuccess',
      'reviewSetDocumentViewError',
      'isReviewSetDocumentViewLoading',
    ])
  }
}
