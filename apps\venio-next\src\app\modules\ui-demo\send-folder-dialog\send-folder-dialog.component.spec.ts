import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SendFolderDialogComponent } from './send-folder-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SendFolderDialogComponent', () => {
  let component: SendFolderDialogComponent
  let fixture: ComponentFixture<SendFolderDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SendFolderDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SendFolderDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
