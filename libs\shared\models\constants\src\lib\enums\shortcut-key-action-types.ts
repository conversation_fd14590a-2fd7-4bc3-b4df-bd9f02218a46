export enum ShortcutKeyActions {
  UNDO_TAG_CODING = 'undo tag coding',
  SAVE_TAG_CODING = 'save tag coding',
}

export enum ShortcutKeyBindings {
  UNDO_TAG_CODING = 'ctrl+z',
  SAVE_TAG_CODING = 'alt+s',
  NAVIGATE_PREVIOUS_HIGHLIGHT = 'alt+up',
  NAVIGATE_NEXT_HIGHLIGHT = 'alt+down',
  MOVE_NEXT_PAGE = 'pagedown',
  MOVE_PREVIOUS_PAGE = 'pageup',
  FIRST_PAGE = 'home',
  LAST_PAGE = 'end',
  SHOW_KEYBOARD_SHORTCUTS = 'ctrl+/',
}

export enum ShortcutKeyDescriptions {
  MOVE_NEXT_PAGE = 'Go to next page',
  MOVE_PREVIOUS_PAGE = 'Go to previous page',
  FIRST_PAGE = 'Go to first page',
  LAST_PAGE = 'Go to last page',
  NAVIGATE_NEXT_HIGHLIGHT = 'Navigate to next highlight',
  NAVIGATE_PREVIOUS_HIGHLIGHT = 'Navigate to previous highlight',
}
