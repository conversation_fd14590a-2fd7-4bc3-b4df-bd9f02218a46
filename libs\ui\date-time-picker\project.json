{"name": "date-time-picker", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/date-time-picker/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/ui/date-time-picker/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/ui/date-time-picker/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/ui/date-time-picker/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui/date-time-picker/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}