import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Signal,
  ViewChild,
  ViewContainerRef,
  WritableSignal,
  computed,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TreeViewComponent,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  Observable,
  Subject,
  combineLatest,
  filter,
  of,
  switchMap,
  take,
  takeUntil,
} from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { SVGIcon, checkIcon, xIcon } from '@progress/kendo-svg-icons'
import {
  DOCUMENT_NOTES_ACCESSIBILITY,
  DOCUMENT_NOTES_SVG_ICONS,
  DocumentNote,
  DocumentNoteAccessibility,
  DocumentNotesFacade,
  DocumentsFacade,
  EditDocumentNoteRequestModel,
  UserRights,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { isEqual } from 'lodash'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { UserFacade } from '@venio/data-access/common'
import {
  ConfirmationDialogComponent,
  VenioNotificationService,
} from '@venio/feature/notification'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { toSignal } from '@angular/core/rxjs-interop'
@Component({
  selector: 'venio-document-notes',
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    ButtonsModule,
    InputsModule,
    SvgLoaderDirective,
    TooltipsModule,
    DropDownsModule,
    ReactiveFormsModule,
    DialogsModule,
    //ConfirmationDialogComponent,
  ],
  templateUrl: './document-notes.component.html',
  styleUrl: './document-notes.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentNotesComponent implements OnInit, OnDestroy {
  public commonActionTypes = CommonActionTypes

  public documentNoteAccessibility: Array<DocumentNoteAccessibility>

  public readonly rights = UserRights

  public hasAnyRightToAddNotes: boolean

  public checkIcon: SVGIcon = checkIcon

  public xIcon: SVGIcon = xIcon

  public docNotesSvgIcons = DOCUMENT_NOTES_SVG_ICONS

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public selectedDocuments: number[]

  public documentNotes: DocumentNote[]

  public currentDocument: number

  @ViewChild(TreeViewComponent, { static: false })
  public treeView: TreeViewComponent

  public documentNoteForm: FormGroup

  public errorMessages = signal(null)

  public showNewNotePanel = computed(
    () =>
      this.documentNoteActionType() === CommonActionTypes.NEW_NOTE ||
      this.documentNoteActionType() === CommonActionTypes.REPLY_NOTE ||
      this.documentNoteActionType() === CommonActionTypes.EDIT
  )

  private selectedEditDocumentNote: WritableSignal<DocumentNote> = signal(null)

  public currentUserDetails = toSignal(
    this.userFacade.selectCurrentUserDetails$
  )

  public documentNoteActionType = signal(CommonActionTypes.CLEAR)

  public showAccessibilityOption: Signal<boolean> = computed(
    () =>
      (this.documentNoteActionType() === CommonActionTypes.EDIT &&
        this.selectedEditDocumentNote().inReplyToCommentId === 0) ||
      // (this.documentNoteActionType() === CommonActionTypes.REPLY_NOTE &&
      //   this.selectedEditDocumentNote().inReplyToCommentId === 0) ||
      this.documentNoteActionType() === CommonActionTypes.NEW_NOTE
  )

  private confirmationDialogRef: DialogRef

  public toDestory$: Subject<void> = new Subject<void>()

  @ViewChild('addEditNotePosition')
  public addEditNotePosition: ElementRef<HTMLDivElement>

  constructor(
    private documentNotesFacade: DocumentNotesFacade,
    private activatedRoute: ActivatedRoute,
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService,
    private userFacade: UserFacade,
    private cdr: ChangeDetectorRef,
    private notificationService: VenioNotificationService,
    private formBuilder: FormBuilder,
    private vcr: ViewContainerRef
  ) {}

  public ngOnDestroy(): void {
    this.toDestory$.next()
    this.toDestory$.complete()
  }

  public ngOnInit(): void {
    this.#selectDocumentNotes()
    this.#getSelectedDocuments()
    this.#selectHasAnyRightToAddNotes()
    this.#initForm()
  }

  #selectHasAnyRightToAddNotes(): void {
    combineLatest([
      this.documentNotesFacade.allowToAddNotesForAllUsers(),
      this.documentNotesFacade.allowToAddNotesForCreatorGroupOnly(),
      this.documentNotesFacade.allowToAddNotesForCreatorOnly(),
    ])
      .pipe(takeUntil(this.toDestory$))
      .subscribe(
        ([
          allowToAddNotesForAllUsers,
          allowToAddNotesForCreatorGroupOnly,
          allowToAddNotesForCreatorOnly,
        ]) => {
          this.cdr.markForCheck()
          this.documentNoteAccessibility = []
          this.hasAnyRightToAddNotes =
            allowToAddNotesForAllUsers ||
            allowToAddNotesForCreatorGroupOnly ||
            allowToAddNotesForCreatorOnly

          if (allowToAddNotesForAllUsers)
            this.documentNoteAccessibility = [
              ...this.documentNoteAccessibility,
              DOCUMENT_NOTES_ACCESSIBILITY['public'],
            ]
          if (allowToAddNotesForCreatorGroupOnly)
            this.documentNoteAccessibility = [
              ...this.documentNoteAccessibility,
              DOCUMENT_NOTES_ACCESSIBILITY['shared'],
            ]
          if (allowToAddNotesForCreatorOnly)
            this.documentNoteAccessibility = [
              ...this.documentNoteAccessibility,
              DOCUMENT_NOTES_ACCESSIBILITY['private'],
            ]
        }
      )
  }

  #selectDocumentNotes(): void {
    this.documentNotesFacade.fetchDocumentNotesAction
      .pipe(
        switchMap(({ fileId, projectId }) => {
          return this.documentNotesFacade.fetchDocumentNotes(projectId, fileId)
        }),
        takeUntil(this.toDestory$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.documentNotes = response?.data?.documentNotes
      })
  }

  #initForm(): void {
    this.cdr.markForCheck()
    this.documentNoteForm = this.formBuilder.group({
      comment: ['', Validators.required],
      accessibility: ['Public', Validators.required],
      isReply: [false],
    })
  }

  #getErrorMessageByControl(controlName: string): string {
    const labels: { [key: string]: string } = {
      comment: 'Comment is required',
      accessibility: 'Accessibility option is required',
    }

    return labels[controlName] || controlName
  }

  #validateForm(isFromSubmit = false): void {
    //clear the all existing error messages
    this.errorMessages.set({})
    const errors: { [key: string]: string } = {}
    Object.keys(this.documentNoteForm.controls).forEach((key) => {
      const control = this.documentNoteForm.get(key)
      if (isFromSubmit) control.markAsTouched()
      if (control.invalid && (control.dirty || control.touched)) {
        const errorMessage = this.#getErrorMessageByControl(key)
        if (errorMessage) {
          errors[key] = errorMessage
        }
      }
    })
    if (this.selectedEditDocumentNote()) {
      const hasAccessibilityPermission = this.documentNoteAccessibility.some(
        (a) => a.value === this.selectedEditDocumentNote().accessibility
      )
      if (!hasAccessibilityPermission)
        if (this.documentNoteActionType() === CommonActionTypes.REPLY_NOTE)
          errors['accessibility'] = `You do not have permission add '${
            this.selectedEditDocumentNote().accessibility
          }' reply note`
        else if (this.documentNoteActionType() === CommonActionTypes.EDIT)
          errors['accessibility'] =
            this.#getErrorMessageByControl('accessibility')
    }

    this.errorMessages.set(errors)
  }

  #getSelectedDocuments(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(
        filter((selectedDocuments) =>
          Boolean(
            selectedDocuments?.length > 0 &&
              !isEqual(selectedDocuments, this.selectedDocuments)
          )
        ),
        takeUntil(this.toDestory$)
      )
      .subscribe((selectedDocuments) => {
        this.selectedDocuments = selectedDocuments
        this.currentDocument = selectedDocuments[0]
        this.dispatchDocumentNotesFetchAction()
      })
  }

  public hasChildren = (item: DocumentNote): boolean => !!item?.children?.length

  public getChildren = (node: DocumentNote): Observable<DocumentNote[]> => {
    return of(node.children || [])
  }

  /**
   * Dispatches action to fetch document notes.
   * @private
   */
  private dispatchDocumentNotesFetchAction(): void {
    this.documentNotesFacade.fetchDocumentNotesAction.next({
      fileId: this.currentDocument,
      projectId: this.projectId,
    })
  }

  /**
   * Handles tag group action.
   * @param {CommonActionTypes} actionType - The type of action performed.
   * @param {DocumentNote} editDocumentNote - The document note being edited.
   */
  public onDocumentNoteAction(
    actionType: CommonActionTypes,
    editDocumentNote: DocumentNote
  ): void {
    this.selectedEditDocumentNote.set(undefined)
    this.documentNoteActionType.set(actionType)
    this.documentNoteForm.controls['comment'].markAsUntouched()
    this.documentNoteForm.controls['accessibility'].markAsUntouched()
    switch (actionType) {
      case CommonActionTypes.NEW_NOTE:
        this.#validateForm()
        this.handleNewNoteAction()
        this.scrollToElement()
        break
      case CommonActionTypes.REPLY_NOTE:
        this.#validateForm()
        this.handleReplyNoteAction(editDocumentNote)
        this.scrollToElement()
        break
      case CommonActionTypes.EDIT:
        this.#validateForm()
        this.handleEditNoteAction(editDocumentNote)
        this.scrollToElement()
        break
      case CommonActionTypes.DELETE:
        this.handleDeleteNoteAction(editDocumentNote)
        break
    }
  }

  /**
   * Handles new note action.
   * @private
   */
  private handleNewNoteAction(): void {
    this.documentNoteForm.patchValue({
      comment: '',
      accessibility: this.#getDefaultAccessibilityOption(),
      isReply: false,
    })
  }

  /**
   * Handles reply note action.
   * @param {DocumentNote} editDocumentNote - The document note being replied to.
   * @private
   */
  private handleReplyNoteAction(editDocumentNote: DocumentNote): void {
    this.selectedEditDocumentNote.set(editDocumentNote)

    this.documentNoteForm.patchValue({
      comment: '',
      accessibility: editDocumentNote.accessibility,
      isReply: true,
    })
  }

  /**
   * Handles edit note action.
   * @param {DocumentNote} editDocumentNote - The document note being edited.
   * @private
   */
  private handleEditNoteAction(editDocumentNote: DocumentNote): void {
    const isDefaultAccessAvailable = this.documentNoteAccessibility.some(
      (a) => a.value === editDocumentNote.accessibility
    )
    const defaultAccessibilityOption = isDefaultAccessAvailable
      ? editDocumentNote.accessibility
      : undefined
    this.selectedEditDocumentNote.set(editDocumentNote)
    this.documentNoteForm.patchValue({
      comment: editDocumentNote.comment,
      accessibility: defaultAccessibilityOption,
      isReply: true,
    })
  }

  #getDefaultAccessibilityOption(): string | undefined {
    const isPublicAccessAvailable = this.documentNoteAccessibility.some(
      (a) => a.value === 'Public'
    )
    const defaultAccessibilityOption = this.selectedEditDocumentNote()
      ? this.selectedEditDocumentNote().accessibility
      : isPublicAccessAvailable
      ? 'Public'
      : undefined
    return defaultAccessibilityOption
  }

  /**
   * Handles delete note action.
   * @param {DocumentNote} editDocumentNote - The document note being deleted.
   * @private
   */
  private handleDeleteNoteAction(editDocumentNote: DocumentNote): void {
    this.selectedEditDocumentNote.set(editDocumentNote)
    this.openDeleteConfirmationDialog()
    this.deleteDocumentNote()
  }

  /**
   * Opens delete confirmation dialog.
   * @private
   */
  private openDeleteConfirmationDialog(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.vcr,
    })
    this.confirmationDialogRef.content.instance.title = 'Delete Note'
    this.confirmationDialogRef.content.instance.message =
      'Are you sure you want to delete this note?'
  }

  /**
   * Deletes document note.
   * @private
   */
  private deleteDocumentNote(): void {
    this.confirmationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        switchMap(() =>
          this.documentNotesFacade.deleteDocumentNote(
            this.selectedEditDocumentNote()?.commentId,
            this.projectId
          )
        ),
        take(1),
        takeUntil(this.toDestory$)
      )
      .subscribe((response: ResponseModel) => {
        this.dispatchDocumentNotesFetchAction()
        this.documentNoteActionType.set(CommonActionTypes.CANCEL)
      })
  }

  // Scroll to a specific node for better user experience
  public handleScroll(event: any): void {
    // Finding the node index or using a unique identifier
    const lookupKey = event.index // This could be another identifier depending on your data structure
    const nodeElement = this.treeView.element.nativeElement.querySelector(
      `[data-treeindex="${lookupKey}"]`
    )
    if (nodeElement) {
      nodeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      })
    }
  }

  public addNewNote(): void {
    const comment = this.documentNoteForm?.value?.comment
    const isReply = this.documentNoteForm?.value?.isReply
    const accessibility = this.documentNoteForm?.value?.accessibility
    const docShareToken = this.activatedRoute.snapshot.queryParams.docShareToken
    this.#validateForm(true)
    if (
      this.documentNoteForm.invalid ||
      Object.keys(this.errorMessages()).length > 0
    )
      return

    const editDocumentNoteRequest: EditDocumentNoteRequestModel = {
      accessibility: accessibility,
      comment: comment,
      commentedBy: this.selectedEditDocumentNote()
        ? this.selectedEditDocumentNote().commentedBy
        : this.currentUserDetails().userName,
      commentId: this.selectedEditDocumentNote()?.commentId,
      datetime: null,
    }

    let actionObservable$ = null
    if (
      this.documentNoteActionType() === CommonActionTypes.NEW_NOTE ||
      this.documentNoteActionType() === CommonActionTypes.REPLY_NOTE
    )
      actionObservable$ = this.documentNotesFacade.addDocumentNote(
        editDocumentNoteRequest,
        this.currentDocument,
        isReply,
        this.projectId,
        docShareToken
      )
    else if (this.documentNoteActionType() === CommonActionTypes.EDIT)
      actionObservable$ = this.documentNotesFacade.editDocumentNote(
        editDocumentNoteRequest,
        this.projectId
      )
    if (actionObservable$)
      actionObservable$
        .pipe(take(1), takeUntil(this.toDestory$))
        .subscribe((response: ResponseModel) => {
          this.dispatchDocumentNotesFetchAction()
          this.documentNoteActionType.set(CommonActionTypes.CANCEL)
        })
  }

  // Scroll to the new note panel
  public scrollToElement(): void {
    this.addEditNotePosition.nativeElement.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    })
  }

  public shouldShowButton(icon: any, dataItem: any): boolean {
    return (
      (icon.actionType === CommonActionTypes.EDIT &&
        dataItem.userId === this.currentUserDetails().userId) ||
      (icon.actionType === CommonActionTypes.DELETE &&
        dataItem.userId === this.currentUserDetails().userId) ||
      (icon.actionType === CommonActionTypes.REPLY_NOTE &&
        this.hasAnyRightToAddNotes)
    )
  }
}
