import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  input,
  OnChanges,
  output,
  Renderer2,
  signal,
  TrackByFunction,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  SelectionItem,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { FieldManagerModel } from '@venio/util/utilities'
import {
  CheckBoxComponent,
  CheckBoxState,
  KENDO_INPUTS,
} from '@progress/kendo-angular-inputs'

@Component({
  selector: 'venio-document-view-designer-field-selection',
  standalone: true,
  imports: [CommonModule, TreeListModule, CheckBoxComponent, KENDO_INPUTS],
  templateUrl: './document-view-designer-field-selection.component.html',
  styleUrl: './document-view-designer-field-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerFieldSelectionComponent
  implements AfterViewInit, OnChanges
{
  public readonly fields = input<FieldManagerModel[]>([])

  public readonly title = input('')

  public readonly clearSelection = input(false)

  public readonly fieldSelected = output<FieldManagerModel[]>()

  constructor(private renderer: Renderer2, private elementRef: ElementRef) {}

  public readonly selected = signal<SelectionItem[]>([])

  public readonly allItemsSelected = signal(false)

  public toggleSelectAll(checked: CheckBoxState): void {
    if (checked) {
      // Build a SelectionItem for each data item in 'fields'
      this.selected.set(
        this.fields().map((item) => ({
          itemKey: item.displayFieldName,
        }))
      )
      this.allItemsSelected.set(true)
    } else {
      this.selected.set([])
      this.allItemsSelected.set(false)
    }
  }

  public trackByFieldName(
    _: number,
    item: TreeListItem
  ): TrackByFunction<TreeListItem> {
    return item.data['displayFieldName']
  }

  public ngAfterViewInit(): void {
    // Set the placeholder for the filter inputs
    // This is a workaround for the issue with the filter inputs not having a placeholder
    const filterInput = this.elementRef.nativeElement.querySelector(
      '.k-filtercell .k-input'
    )
    if (filterInput) {
      this.renderer.setAttribute(filterInput, 'placeholder', 'Search')
    }
  }

  public selectionItemChanged(items: Array<{ itemKey: string }>): void {
    const selected = this.fields().filter((c) =>
      items.some((d) => d.itemKey === c.displayFieldName)
    )

    this.fieldSelected.emit(selected)
  }

  public ngOnChanges(): void {
    if (this.clearSelection) {
      this.selected.set([])
      this.allItemsSelected.set(false)
    }
  }
}
