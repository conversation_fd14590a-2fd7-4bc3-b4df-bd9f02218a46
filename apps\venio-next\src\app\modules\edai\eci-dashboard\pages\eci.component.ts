import { Component, OnInit } from '@angular/core';
import { KENDO_LAYOUT } from '@progress/kendo-angular-layout';
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons';
import { CommonModule } from '@angular/common';
import { DropDownButtonModule } from '@progress/kendo-angular-buttons';
import { filterIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { SummaryComponent } from '../shared/summary/summary.component';
import { KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { KENDO_POPUP } from '@progress/kendo-angular-popup';
import { KENDO_LISTVIEW } from '@progress/kendo-angular-listview';
import { custodians } from '../shared/mock-data';
import { EciCheckboxListItemComponent } from '../shared/checkbox-list-item/checkbox-list-item.component';
import { EciRelevanceComponent } from '../shared/relevance/relevance.component';
import { EciSunburstComponent } from '../shared/sunburst/sunburst.component';
import { EciInappropriateContentComponent } from '../shared/inappropriate-content/inappropriate-content.component';
import { EciWordCloudComponent } from '../shared/word-cloud/word-cloud.component';
import { EciFocusedSectionComponent } from '../shared/focused-section/focused-section.component';
import { EciDataService } from '../shared/eci-data.service';
@Component({
  selector: 'venio-eci-dashboard',
  standalone: true,
  imports: [
    KENDO_LAYOUT,
    KENDO_BUTTONS,
    KENDO_POPUP,
    KENDO_BUTTONS,
    KENDO_INPUTS,
    KENDO_LISTVIEW,
    DropDownButtonModule,
    CommonModule,
    SummaryComponent,
    EciCheckboxListItemComponent,
    EciRelevanceComponent,
    EciSunburstComponent,
    EciInappropriateContentComponent,
    EciWordCloudComponent,
    EciFocusedSectionComponent
  ],
  templateUrl: './eci.component.html',
  styleUrls: ['./eci.component.scss']
})
export class EciDashboardComponent implements OnInit {
  isFocusedSectionOpened = false;
  constructor(private dataService: EciDataService) { }

  public custodians: any[] = custodians;
  public showFocused = true;
  public show = false;
  public showCustodianFilters = false;

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(val => {
      this.isFocusedSectionOpened = val;
      // You can now use this boolean in your template or logic
      console.log('Focused section opened:', val);
    });
  }

  public onToggle(): void {
    this.show = !this.show;
  }
  public svgFilter: SVGIcon = filterIcon;
  public onCustodianClick(): void {
    this.showCustodianFilters = !this.showCustodianFilters;
  }
}
