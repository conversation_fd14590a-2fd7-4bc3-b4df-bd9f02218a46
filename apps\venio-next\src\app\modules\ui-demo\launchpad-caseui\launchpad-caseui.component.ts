import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { IconsModule, SVGIcon } from '@progress/kendo-angular-icons'
import {
  ButtonsModule,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { DataBindingDirective, GridModule } from '@progress/kendo-angular-grid'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { searchIcon } from '@progress/kendo-svg-icons'
import { employees } from './employees'
import { process } from '@progress/kendo-data-query'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-launchpad-caseui',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    InputsModule,
    IconsModule,
    ButtonsModule,
    DropDownButtonModule,
    DropDownsModule,
    LabelModule,
    SvgLoaderDirective,
  ],
  templateUrl: './launchpad-caseui.component.html',
  styleUrls: ['./launchpad-caseui.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LaunchpadCaseuiComponent implements OnInit {
  public svgIconForPageControls = [
    {
      actionType: 'FIRST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: 'NEXT_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: 'PREV_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: 'LAST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  public svgIconForGridControls = [
    {
      actionType: 'ACTION_REFRESH',
      iconPath: 'assets/svg/icon-grid-action-refresh.svg',
    },
    {
      actionType: 'ACTION_UPLOAD',
      iconPath: 'assets/svg/icon-grid-action-cloud-upload.svg',
    },
    {
      actionType: 'ACTION_GRAPH',
      iconPath: 'assets/svg/icon-grid-action-graph.svg',
    },
    {
      actionType: 'ACTION_DOWNLOAD',
      iconPath: 'assets/svg/icon-grid-action-cloud-download.svg',
    },
  ]

  public searchSvg: SVGIcon = searchIcon

  public pageSize = 5

  public buttonCount = 2

  public sizes = [5, 10, 20, 50]

  public skip = 0

  @ViewChild(DataBindingDirective) public dataBinding: DataBindingDirective

  public gridData: unknown[] = employees

  public gridView: unknown[]

  public currentPage: number

  public ngOnInit(): void {
    this.gridView = this.gridData
  }

  public onFilter(value: Event): void {
    const inputValue = value

    this.gridView = process(this.gridData, {
      filter: {
        logic: 'or',
        filters: [
          {
            field: 'full_name',
            operator: 'contains',
            value: inputValue,
          },
          {
            field: 'job_title',
            operator: 'contains',
            value: inputValue,
          },
          {
            field: 'budget',
            operator: 'contains',
            value: inputValue,
          },
          {
            field: 'phone',
            operator: 'contains',
            value: inputValue,
          },
          {
            field: 'address',
            operator: 'contains',
            value: inputValue,
          },
        ],
      },
    }).data

    this.dataBinding.skip = 0
  }

  public sliderChange(selPageSize: number): void {
    this.pageSize = selPageSize
  }

  public onPageSizeChange(inputValue: number): void {
    this.skip = (inputValue - 1) * this.pageSize
  }

  public onBlur(inputValue: number): void {
    if (inputValue) {
      this.skip = (inputValue - 1) * this.pageSize
    } else {
      this.currentPage = 1
    }
  }

  public browseActionClicked(actionType: any): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }
}
