const fs = require('fs')
const chalk = require('chalk')
const path = require('path')

const iisBasedApps = ['venio-next']
const sourceFile = 'apps/{appName}/web.config'
const destinationDir = 'dist/{appName}/browser/web.config'

const message = (appName) =>
  chalk.bgBlueBright(chalk.blue('NOTE:')) +
  chalk.greenBright(
    ' IIS config file is copied from "' +
      sourceFile.replace('{appName}', appName) +
      '" to "' +
      destinationDir.replace('{appName}', appName) +
      '" successfully!'
  )
const directoryExists = (directoryPath) => {
  try {
    // Resolve the path to an absolute path
    const absolutePath = path.resolve(
      directoryPath.substring(0, directoryPath.lastIndexOf('/'))
    )
    console.log(chalk.cyanBright('INFO: Absolute path to copy -> ', absolutePath))
    // Check if the path exists and is a directory
    const stats = fs.statSync(absolutePath)
    console.log(chalk.cyanBright('INFO: isDirectory -> ', stats.isDirectory()))
    return stats.isDirectory()
  } catch (error) {
    //console.log(error)
    // If an error is thrown, the directory doesn't exist
    if (error.code === 'ENOENT') {
      return false
    } else {
      // Handle other errors (e.g., permission issues)
      throw error
    }
  }
}

iisBasedApps.forEach((appName) => {
  const isDirExist = directoryExists(
    destinationDir.replace('{appName}', appName)
  )
  if (!isDirExist) {
    console.log(
      chalk.yellowBright(
        'NOTE: When trying to copy the web.config file for "' +
          appName +
          '", the destination "' +
          appName +
          '" is not found!'
      )
    )
    return
  }
  fs.copyFile(
    sourceFile.replace('{appName}', appName),
    destinationDir.replace('{appName}', appName),
    (err) => {
      if (err) throw err
      console.log(message(appName))
    }
  )
})
