<div
  class="t-relative t-h-[34.7rem] t-rounded-md t-bg-[#F8F8F6]"
  [ngClass]="showHistory() ? 't-w-[17.6875rem]' : 't-w-[0rem]'">
  <div *ngIf="showHistory()" class="t-flex t-flex-col">
    <span class="t-m-4 t-text-[0.875rem] t-font-bold">Recent Jobs</span>
    <div class="t-max-h-[32.1875rem] t-overflow-y-auto">
      <kendo-treeview
        class="v-ai-job-tree"
        [nodes]="relevanceCompletedJobs()?.data"
        [textField]="['jobName', 'issuePrompt']"
        kendoTreeViewHierarchyBinding
        childrenField="issueDetail"
        kendoTreeViewExpandable>
        <ng-template kendoTreeViewNodeTemplate let-dataItem>
          @if(dataItem.issueDetail!==undefined){
          <div class="t-cursor-pointer" (click)="onloadJobDetail(dataItem)">
            <span class="!t-text-[0.75rem]"> {{ dataItem.jobName }}</span>
            <span class="!t-text-[0.75rem] t-text-[#1EBADC] t-pl-1"
              >({{ dataItem.issueDetail.length }})
            </span>
          </div>
          } @else{
          <div class="t-w-[12.5rem]">
            <p
              kendoTooltip
              [title]="dataItem.issuePrompt"
              class="!t-text-[0.75rem] t-line-clamp-2 t-whitespace-pre-line">
              {{ dataItem.issuePrompt }}
            </p>
          </div>
          }
        </ng-template>
      </kendo-treeview>
    </div>
  </div>
  <span
    (click)="toggleHistory()"
    class="t-absolute t-bottom-4 t-bg-[#F8F8F6] t-rounded-full t-grid t-place-content-center t-w-[2.5rem] t-h-[2.5rem] t-cursor-pointer"
    [ngClass]="showHistory() ? 't-right-[-1.25rem]' : 't-right-[-2.1875rem]'">
    <span
      [tabindex]="-1"
      venioSvgLoader
      color="#1EBADC"
      svgUrl="assets/svg/icon-history-clock.svg"
      height="2.6875rem"
      width="3.75rem"
      class="t-flex t-justify-center t-w-16 t-h-[3.1875rem] t-p-1 t-pl-[1.125rem] t-items-center"></span>
  </span>
</div>
