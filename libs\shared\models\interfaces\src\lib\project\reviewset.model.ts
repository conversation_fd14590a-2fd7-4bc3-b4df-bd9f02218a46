import { FormControl } from '@angular/forms'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { TagsModel } from '../tags/tags.model'

export enum PropagationRules {
  PROPAGATE_TO_DUPLICATES = 0,
  PROPAGATE_WITHIN_CUSTODIAN = 1,
  DO_NOT_PROPAGATE_TO_DUPLICATES = 2,
}

/**
 * Represents a complete review set template
 */
export interface ReviewSetTemplateModel {
  reviewSetInformation: ReviewSetTemplateInformation
  templateName: string
  notes: string
  templateId: number
  clientName: string
  clientId: number
  projectId: number
}

/**
 * Represents the configuration information of a review set template
 */
export interface ReviewSetTemplateInformation {
  // Basic information
  reviewSetTemplateId: number
  reviewSetId: number
  name: string
  batchSize: number
  batchPrefix: string
  batchPaddingLength: number
  batchStartNumber: number
  purpose: string
  showPurposeInUi: boolean

  // Document selection options
  parentChildIncluded: boolean
  msgThreadIncluded: boolean
  nearDupsIncluded: boolean
  savedSearchId: number
  tagId: string | null
  tagSelectionOption: string | null
  folderSelectionOption: string | null
  folderId: string | null
  sampleId: string | null
  sampleSelectionOption: string | null

  // Profile and user settings
  profileId: number
  profileSelectedType: string | null
  reviewTagId: number
  selectedUserGroups: TemplateUserGroup[]
  assignedUserGroup: {
    assignedUser: string
    assignedGroup: string
  }
  selectedUserGroupId: string

  // Auto collection settings
  enableAutoCollect: boolean
  autoCollectFrequency: number
  autoCollectExpiresOn: string
  autoCollectReviewset: number
  autoCollectMinThresholdValue: number

  // Review source and sorting settings
  reviewSource: number
  sourceReviewSetId: number
  orderByField: string | null
  sortOrder: string | null
  sortByCustodian: boolean
  custodianSortOrder: string | null

  // Audit information
  createdUser: string
  createdBy: number
  createdOn: string
  lastUpdatedBy: number
  lastUpdatedOn: string

  // Tag propagation settings
  selectedField: string
  tagPropagationRule: number | boolean
  propagateTagPcSet: boolean
  propagateTagEmailThread: boolean

  // Email and notification settings
  coddingField: string
  displayTag: string
  autoCollectEmailSubject: string
  reviewCreationEmailSubject: string
  autoCollectEmailBody: string
  reviewCreationEmailBody: string
  emailUserIds: string
  emailUserInfos: any[] | null
  sendEmailAfterAutoCollection: boolean
  sendEmailAfterReviewSetCreation: boolean

  // Review behaviors
  excludePrevReviewSetDoc: boolean
  showEmailHeaderInFulltext: boolean
  visibleReviewPanel: any[]
  published: boolean
  reviewType: number
  predictionId: string | null
  predictionScoreClause: string | null
  varOption: number
  redactionObjectIdListForSource: any | null
  reviewDuplicatePropagationRule: number | boolean
  propagateReviewPcSet: boolean
  propagateReviewEmailThread: boolean
  enforceTagForReview: boolean
  showHideReviewedFiles: boolean

  // Document conversion options
  autoQueueForHtmlConversion: boolean
  autoQueueForTiff: boolean

  // Additional settings
  excludeNonInclusiveEmails: boolean
  tagGroupIds: {
    TagGroups: any | null
  }
  tagRuleIds: {
    TagRule: any | null
  }
  markTaggedDocsAsReviewed: boolean
  reviewedDocCount: number
  totalDocCount: number
  layout: number
  layoutName: string

  // CAL profile settings
  calProfileInfo: CalProfileInfo
  isCalReviewSet: boolean
  highlightGroup: number
  viewableTags: any[] | null

  // Form-specific properties (mapped in the code)
  useCALProfileForReviewSet?: boolean
  allowReviewAfterCALThreshold?: boolean

  // CAL properties at top level (mapped from calProfileInfo)
  trainingRecallThreshold?: number
  categoryTrainingThreshold?: number
  predictionAccuracyThreshold?: number
  batchRichnessThreshold?: number
  reviewRelevanceThreshold?: number
  allowReviewAfterCalThreshold?: boolean
  controlSetSizeDerivedBy?: number
  percentageOfPopulation?: number
  numberOfDocuments?: number
  isDynamicControlSet?: boolean
  calControlSetDocCount?: number
  confidenceLevel?: number
  confidenceInterval?: number
  controlSetPercentFromTrainingBatch?: number
  calControlSetMinDocCount?: number
  calControlSetMaxDocCount?: number
  calTrainingSetMinDocCount?: number
  calTrainingSetMaxDocCount?: number

  // Additional property needed for control logic
  autoCollectionSelectionCriteria?: boolean
}

/**
 * Represents a user or group in the template
 */
export interface TemplateUserGroup {
  type: number // 0 = USER, 1 = GROUP
  userGroupId: number
  userGroupName: string | null
}

/**
 * Represents CAL (Continuous Active Learning) profile settings
 */
export interface CalProfileInfo {
  profileSettingTemplateId: number | null
  profileName: string
  actualProfileName: string | null
  purpose: string | null
  categoryName: string | null
  categoryDesc: string | null
  manualCatTag: string | null
  autoCatTag: string | null
  trainingRecallThreshold: number
  categoryTrainingThreshold: number
  predictionAccuracyThreshold: number
  isCalProfile: boolean
  batchRichnessThreshold: number
  reviewRelevanceThreshold: number
  allowReviewAfterCalThreshold: boolean
  controlSetSizeDerivedBy: number
  percentageOfPopulation: number
  numberOfDocuments: number
  isDynamicControlSet: boolean
  calControlSetDocCount: number
  confidenceLevel: number
  confidenceInterval: number
  controlSetPercentFromTrainingBatch: number
  calControlSetMinDocCount: number
  calControlSetMaxDocCount: number
  calTrainingSetMinDocCount: number
  calTrainingSetMaxDocCount: number
  isDiscontinueDynamicControlSet: boolean
  showReviewStatistics: boolean
  calPrimaryCategoryName: string | null
  createdBy: number
  excludeTrainingDocsOnPrediction: boolean
  categoryInfo: any | null
  categoryInfos: any[] | null
  options: any | null
}
export interface ReviewSetModel {
  reviewSetTemplateId: number
  projectId: number
  reviewSetId: number
  name: string
  batchSize: number
  batchPrefix: string
  batchPaddingLength: number
  batchStartNumber: number
  purpose: string
  showPurposeInUI: boolean
  parentChildIncluded: boolean
  msgThreadIncluded: boolean
  nearDupsIncluded: boolean
  savedSearchID: number
  tagID: string
  tagSelectionOption: string
  folderSelectionOption: string
  folderID: string
  sampleID: any | null
  sampleSelectionOption: any | null
  profileID: number
  profileSelectedType: any | null
  reviewTagID: number
  selectedUserGroups: SelectedUserGroup[]
  assignedUserGroup: AssignedUserGroup
  selectedUserGroupId: string
  enableAutoCollect: boolean
  useCALProfileForReviewSet: boolean
  // CAL properties:
  trainingRecallThreshold?: number
  categoryTrainingThreshold?: number
  predictionAccuracyThreshold?: number
  batchRichnessThreshold?: number
  reviewRelevanceThreshold?: number
  allowReviewAfterCALThreshold?: boolean
  controlSetSizeDerivedBy?: number
  percentageOfPopulation?: number
  numberOfDocuments?: number
  isDynamicControlSet?: boolean
  calControlSetDocCount?: number
  confidenceLevel?: number
  confidenceInterval?: number
  controlSetPercentFromTrainingBatch?: number
  calControlSetMinDocCount?: number
  calControlSetMaxDocCount?: number
  calTrainingSetMinDocCount?: number
  calTrainingSetMaxDocCount?: number
  autoCollectFrequency: number
  autoCollectExpiresOn: string | null
  autoCollectReviewset: number
  autoCollectionSelectionCriteria: boolean
  autoCollectMinThresholdValue: number
  reviewSource: number
  sourceReviewSetId: number
  orderByField: string
  sortOrder: string
  sortByCustodian: boolean
  custodianSortOrder: string
  createdUser: string
  createdBy: number
  createdOn: string
  lastUpdatedBy: number
  lastUpdatedOn: string
  selectedField: any | null
  tagPropagationRule: number
  propagateTagPCSet: boolean
  propagateTagEmailThread: boolean
  coddingField: string
  displayTag: string
  autoCollectEmailSubject: string
  reviewCreationEmailSubject: string
  autoCollectEmailBody: string
  reviewCreationEmailBody: string
  emailUserIds: string
  emailUserInfos: any | null
  sendEmailAfterAutoCollection: boolean
  sendEmailAfterReviewSetCreation: boolean
  excludePrevReviewSetDoc: boolean
  showEmailHeaderInFulltext: boolean
  visibleReviewPanel: any[]
  published: boolean
  reviewType: number
  predictionId: any | null
  predictionScoreClause: any | null
  varOption: number
  redactionObjectIdListForSource: string
  reviewDuplicatePropagationRule: number
  propagateReviewPCSet: boolean
  propagateReviewEmailThread: boolean
  enforceTagForReview: boolean
  showHideReviewedFiles: boolean
  autoQueueForHtmlConversion: boolean
  autoQueueForTiff: boolean
  excludeNonInclusiveEmails: boolean
  tagGroupIds: TagGroups
  tagRuleIds: TagRule
  markTaggedDocsAsReviewed: boolean
  reviewedDocCount: number
  totalDocCount: number
  layout: number
  layoutName: string
  calProfileInfo: CalProfileInfo
  isCalReviewSet: boolean
  highlightGroup: number
  viewableTags: ViewableTag[]
}

interface SelectedUserGroup {
  type: number
  userGroupId: number
  userGroupName: string
}

interface AssignedUserGroup {
  assignedUser: string
  assignedGroup: string
}

interface TagGroups {
  TagGroups: any | null
}

interface TagRule {
  TagRule: any | null
}

interface ViewableTag {
  tagId: number
  tagName: string
}
export interface ReviewSetDetailRequestModel {
  pageNumber: number
  pageSize: number
  searchText?: string
  sortField?: string
  sortOrder?: string

  // Only for the client side as it won't be sent to the server
  totalReviewSetCount?: number
}

export interface ReviewSetEntry {
  // Client side only to display sequence number table
  sn?: number
  // Client side only to make row unique
  uuid?: string
  reviewSetId: number
  projectId: number
  reviewSetName: string
  isCqlReviewSet: boolean
  projectName: string
  batchCount: number
  completedBatchCount: number
  totalDocCount: number
  reviewedDocCount: number
  reviewerCount: number
  reviewSetStatus: string
  isReviewSetAssignedToCurrentUser: boolean
  assignedReviewers: string
  createdBy: string
  createdDate: string
  createdTime: string
}

export interface ReviewSetSummary {
  reviewSetEntries?: ReviewSetEntry[]
  totalDocCount?: number
  totalReviewerCount?: number
  totalReviewSetCount: number
  completedReviewSetCount: number
  inProgressReviewSetCount: number
  notStartedReviewSetCount: number
}

export interface SelectedReviewSetActionEvent {
  actionType: CommonActionTypes
  selectedReviewSet: ReviewSetEntry
}

export enum ReviewSetViewDetailSearchTypes {
  TAGS = 'Tags',
  QUERY = 'Query',
}

export enum ReviewSetViewDetailViewTypes {
  TREE_VIEW = 'Tree View',
  BATCH_VIEW = 'Batch View',
}

export interface BatchFileDetails {
  // Client side only to display sequence number table
  id?: number
  parentId?: number

  // Returned from the server
  batchId: number
  treeId: string
  treeParentId: string
  batchName: string
  batchStatus: string
  reviewer: string
  batchFileAssocicationId: number
  fileId: number
  fileSize: number
  fileType: string
  fileName: string
}

export interface SelectedReviewSetBatchModel {
  reviewSetId: number
  projectId: number
}

export interface ReviewSetBatchRequestModel {
  pageNumber: number
  pageSize: number
  BatchStatus?: string

  // Only for the client side as it won't be sent to the server
  totalReviewSetBatchCount?: number
}

export interface ReviewSetBatchModel {
  batchId: number
  reviewSetId: number
  name: string
  batchStatus: string
  reviewer: string
  totalFiles: number
  remainingFiles: number
  totalGeneratedHtmlFiles: number
  // Client side only to display sequence number table
  sn?: number
  // Client side only to make row unique
  uuid?: string
}

export interface ReviewSetBatchBulkAction {
  actionType: CommonActionTypes
  batchIds: number[]
}

export interface ReviewSetBatchAction {
  actionType: CommonActionTypes
  content: ReviewSetBatchModel
}
interface CommonId {
  keyId: number
  parentId: number
}
export interface UserGroupTree extends CommonId {
  keyID: number
  parentID: number
  groupId: number
  name: string
  type: string
  userId: number
  treeParentId?: number
}
export interface NavigationMode {
  folderList: string
  mediaList: string
  navigationList: string
  navigationType: string
  restrictedList: string
  restrictedMode: string
  reviewSetList: string
}
export interface ReviewSetSourceTags {
  id: string
  parentId: string
  name: string
  totalTagCount: number
  tagId: number
  isGroup: boolean
  tagGroupId: number
  isExclusive: boolean
}
export interface ReviewSetSourceFolders {
  folderName: string
  id: number
  folderId: number
  parentId: number
  totalFileCount: number
  folderLineage: string
  accessType: string
  systemFolder: string
}
export interface ReviewSetSourceSavedSearch {
  searchId: number
  searchName: string
  totalHitCount: number
}
export interface ReviewSetReassignAction {
  actionType: CommonActionTypes
  reviewerId?: number
  reviewerName?: string
}

export const PreDefinedSortingFields: string[] = [
  'INGESTION_ORDER',
  'GROUP_DATE',
  'EMAIL_THREAD',
  'ORIGINAL_FOLDER_PATH',
  'RELATIVE_FILE_PATH',
  'ORIGINAL_FILE_NAME',
  'MEDIA_NAME',
]

export interface ReviewSetDialogData {
  type: ReviewSetDialogType
  counts?: {
    reviewFileCount: number
    tiffConversionCount: number
    tiffRemainingCount: number
    htmlConversionCount: number
    htmlRemainingCount: number
  }
  isNativeToHtmlChecked?: boolean
  isNativeToImageChecked?: boolean
  message?: string
  title?: string
}
export enum ReviewSetDialogType {
  NONE = 'NONE',
  HTML_RECOMMENDATION = 'HTML_RECOMMENDATION',
  IMAGE_HTML_CONFIRMATION = 'IMAGE_HTML_CONFIRMATION',
  NOT_SUPPORTED = 'NOT_SUPPORTED',
}
export enum ReviewSetSourceTypes {
  SAVED_SEARCH = 0,
  FOLDER = 1,
  TAG = 2,
}
export interface SamplingCountModel {
  populationSize: number
  sampleSize: number
}
export interface FileConversionCounts {
  reviewFileCount: number
  tiffConversionCount: number
  tiffRemainingCount: number
  htmlConversionCount: number
  htmlRemainingCount: number
}

export interface ReviewSetForm {
  projectId: FormControl<number>
  reviewSetTemplateId: FormControl<number>
  reviewSetId: FormControl<number>
  name: FormControl<string>
  batchPrefix: FormControl<string>
  batchStartNumber: FormControl<number>
  batchPaddingLength: FormControl<number>
  batchSize: FormControl<number>
  purpose: FormControl<string>
  reviewSource: FormControl<ReviewSetSourceTypes>
  tagId: FormControl<number[]>
  tagSelectionOption: FormControl<string>
  folderId: FormControl<number[]>
  folderSelectionOption: FormControl<string>
  savedSearchId: FormControl<number>
  orderByField: FormControl<string>
  sortOrder: FormControl<string>
  sortByCustodian: FormControl<boolean>
  custodianSortOrder: FormControl<string>
  displayTag: FormControl<TagsModel[]>
  enableAutoCollect: FormControl<boolean>
  autoCollectFrequency: FormControl<number>
  autoCollectExpiresOn: FormControl<string>
  autoCollectMinThresholdValue: FormControl<number>
  autoCollectionSelectionCriteria: FormControl<boolean>
  autoCollectReviewset: FormControl<number>
  selectedUserGroups: FormControl<any[]>
  layout: FormControl<number>
  highlightGroup: FormControl<number>
  parentChildIncluded: FormControl<boolean>
  msgThreadIncluded: FormControl<boolean>
  excludePrevReviewSetDoc: FormControl<boolean>
  excludeNonInclusiveEmails: FormControl<boolean>
  propagateTagPCSet: FormControl<boolean>
  tagPropagationRule: FormControl<boolean>
  propagateTagEmailThread: FormControl<boolean>
  propagateReviewPCSet: FormControl<boolean>
  reviewDuplicatePropagationRule: FormControl<boolean>
  propagateReviewEmailThread: FormControl<boolean>
  useCALProfileForReviewSet: FormControl<boolean>
  trainingRecallThreshold: FormControl<number>
  categoryTrainingThreshold: FormControl<number>
  predictionAccuracyThreshold: FormControl<number>
  batchRichnessThreshold: FormControl<number>
  reviewRelevanceThreshold: FormControl<number>
  allowReviewAfterCALThreshold: FormControl<boolean>
  controlSetSizeDerivedBy: FormControl<number>
  percentageOfPopulation: FormControl<number>
  numberOfDocuments: FormControl<number>
  isDynamicControlSet: FormControl<boolean>
  calControlSetDocCount: FormControl<number>
  confidenceLevel: FormControl<number>
  confidenceInterval: FormControl<number>
  controlSetPercentFromTrainingBatch: FormControl<number>
  calControlSetMinDocCount: FormControl<number>
  calControlSetMaxDocCount: FormControl<number>
  calTrainingSetMinDocCount: FormControl<number>
  calTrainingSetMaxDocCount: FormControl<number>
  autoQueueForHtmlConversion: FormControl<boolean>
  autoQueueForTiff: FormControl<boolean>
  markTaggedDocsAsReviewed: FormControl<boolean>
}

export interface ReviewUserModel {
  userId: number
  fullName: string
}

export interface ReviewSetBatchSummaryModel {
  activeReviewerCount?: number
  batchCount?: number
  completedBatchCount?: number
  documentCount?: number
  documentReviewedCount?: number
  inProgressBatchCount?: number
  notStartedBatchCount?: number
  reviewSetId?: number
  reviewerCount?: number
}

export interface ReviewSetBatchSummaryRateModel {
  documentsReviewedPerDay?: number
  documentsReviewedPerHour?: number
  projectedCompletionDateInfo?: string
  projectedCompletionDate?: string | Date
  documentsReviewedPerReviewerPerHour?:
    | DocumentsReviewedReviewerModel[]
    | number
}

export interface DocumentsReviewedReviewerModel {
  userId?: number
  reviewedDocumentCount?: number
}

export interface ReviewStatusRequestModel {
  reviewers: number[]
  startDate: string
  endDate: string
}

export interface ReviewStatusResponseModel {
  userId: number
  userName: string
  tagId: number
  tagName: string
  taggedDocCount: number
  reviewedDocCount: number
  reviewedDate: string
}

export interface ReviewStatusModel {
  userId?: number
  username?: string
  tagId?: number
  tagName?: string
  taggedDocCount?: number
  reviewedDocCount?: number
  reviewedDate?: string
  // extra
  day: string
  userName?: string
  color?: string
}

export interface ReviewerSummaryRequestModel {
  reviewers: number[]
  reviewSets: number[]
}

export interface ReviewBatchSummary {
  userId: number
  userFullName: string
  reviewSetId: number
  reviewSetName: string
  batchId: number
  batchName: string
  batchStatus: string
  batchTotalCount: number
  batchReviewedCount: number
  batchNotReviewedCount: number
}

export interface TagRateUserData {
  tag: string
  [userName: string]: string
}

export interface FilterField {
  id: number
  name: string
}

export interface ReviewerChartDataOutput {
  formattedDates: string[]
  uniqueUsers: string[]
  stackedData: Record<string, number[]>
  usersColor: Record<string, string>
}

export interface ProgressGroupedData {
  date: string
  value: number
  color: string
}

export interface ProgressChartDataOutput {
  formattedDates: string[]
  groupedData: ProgressGroupedData[]
}
