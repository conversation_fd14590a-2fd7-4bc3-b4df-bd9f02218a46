import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureTranscriptReportComponent } from './feature-transcript-report.component'
import { ActivatedRoute } from '@angular/router'
import { provideAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureTranscriptReportComponent', () => {
  let component: FeatureTranscriptReportComponent
  let fixture: ComponentFixture<FeatureTranscriptReportComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureTranscriptReportComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureTranscriptReportComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
