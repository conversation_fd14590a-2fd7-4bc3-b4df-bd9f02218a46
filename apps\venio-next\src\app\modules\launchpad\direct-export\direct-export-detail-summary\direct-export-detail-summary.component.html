<div class="t-flex t-flex-col t-flex-1 t-p-2 t-gap-2">
  <p
    class="t-my-3 t-text-[16px] t-font-bold t-tracking-[0.01px] t-leading-[20px] t-text-[#263238]">
    Summary
  </p>

  <div class="t-flex t-flex-row t-flex-1 t-gap-[30px] t-max-w-7xl">
    <div
      class="t-flex t-flex-col t-p-4 t-bg-[#BAE36E0F] t-rounded-[8px] t-flex-1 t-gap-[15px] t-w-[15%]">
      <p
        class="t-my-1 t-text-[16px] t-text-[#000] t- t-font-normal t-tracking-[0.75px] t-leading-[24px] t-text-left">
        Case, Service Type & General Settings
      </p>

      <div
        class="t-p-5 t-bg-white t-flex t-flex-col t-gap-5 t-flex-1 t-rounded-[8px] t-shadow-[0px_3px_20px_rgba(0,0,0,0.29)]">
        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            CASE NAME
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ caseName }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            SERVICE TYPE
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ getServiceTypeName() }}
          </p>
        </div>
        <div class="v-dashed-sperator t-opacity-100 t-w-auto t-h-[1px]"></div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            DEDUPLICATION OPTION
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ deduplicationOption }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            TIMEZONE
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ timeZone }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            CSV/EXCEL HANDLING
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ csvHandling }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            DISCOVERY EXCEPTION HANDLING
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ discoveryExceptionHandling }}
          </p>
        </div>

        <!-- <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            PASSWORDS
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ passwords }}
          </p>
        </div> -->
      </div>
    </div>
    <div
      class="t-flex t-flex-col t-p-4 t-bg-[#BAE36E0F] t-rounded-[8px] t-flex-1 t-gap-[15px] t-w-[15%]">
      <p
        class="t-my-1 t-text-[16px] t-text-[#000] t-font-normal t-tracking-[0.75px] t-leading-[24px] t-text-left">
        Image Conversion & Control Number
      </p>

      <div
        class="t-p-5 t-bg-white t-flex t-flex-col t-gap-5 t-flex-1 t-rounded-[8px] t-shadow-[0px_3px_20px_rgba(0,0,0,0.29)]">
        <div
          class="t-flex t-flex-col t-gap-2 t-my-1"
          *ngIf="isConcordanceOrSummationService">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            CREATE IMAGE
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ directExportImageType }}
          </p>
        </div>

        <div
          class="t-flex t-flex-col t-gap-2 t-my-1"
          *ngIf="showImageConversionSettings">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            IMAGE FILE
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ imageColorConversion }}
          </p>
        </div>

        <div
          class="t-flex t-flex-col t-gap-2 t-my-1"
          *ngIf="showImageConversionSettings">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            PDF FILES
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ pdfColorConversion }}
          </p>
        </div>
        <div
          class="t-flex t-flex-col t-gap-2 t-my-1"
          *ngIf="showImageConversionSettings">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            POWERPOINT
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ powerpointColorConversion }}
          </p>
        </div>
        <div class="v-dashed-sperator t-opacity-100 t-w-auto t-h-[1px]"></div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            SORT ORDER
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ sortOrder }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            PREFIX
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ prefix }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            PREFIX DELIMITER
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ delimiter }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            STARTING NUMBER
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ startNumber }}
          </p>
        </div>

        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            VOLUME ID
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
            {{ volumeId }}
          </p>
        </div>
      </div>
    </div>
    <div
      class="t-flex t-flex-col t-p-4 t-bg-[#BAE36E0F] t-rounded-[8px] t-flex-1 t-gap-[15px] t-w-[15%]">
      <p
        class="t-my-1 t-text-[16px] t-text-[#000] t-font-normal t-tracking-[0.75px] t-leading-[24px] t-text-left">
        Production Option
        <span
          *ngIf="
            getServiceTypeName() === 'PDF Service' ||
            getServiceTypeName() === 'Relativity Service' ||
            getServiceTypeName() === 'Print Service'
          ">
          {{ '& ' + getServiceTypeName() }}
        </span>
      </p>
      <div
        class="t-p-5 t-bg-white t-flex t-flex-col t-gap-5 t-flex-1 t-rounded-[8px] t-shadow-[0px_3px_20px_rgba(0,0,0,0.29)]">
        <div class="t-flex t-flex-col t-gap-2 t-my-1">
          <p
            class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
            SELECTED FIELD TEMPLATE
          </p>
          <p
            class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px] t-break-words">
            {{ fieldTemplateName }}
          </p>
        </div>
        <div class="v-dashed-sperator t-opacity-100 t-w-auto t-h-[1px]"></div>

        <ng-container *ngIf="getServiceTypeName() === 'PDF Service'">
          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              PDF TYPE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ pdfType }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              PDF FILE NAMING
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ pdfFileNaming }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              FAMILY FILE HANDLING
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ pdfFamilyFileHandling }}
            </p>
          </div>
        </ng-container>

        <ng-container *ngIf="getServiceTypeName() === 'Relativity Service'">
          <div class="t-flex t-flex-col t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              OPTION SELECTION
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              ENVIRONMENT
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ connectorName }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              WORK SPACE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ workspaceName }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1" *ngIf="isRelativityOne">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              WORKSPACE FILE SHARE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px] t-break-words">
              {{ connectorFileSharePath }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              BASE API URL
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ selectedBaseAPIUrl() }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              MAPPING TEMPLATE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ relativityFieldMappingTemplateName }}
            </p>
          </div>
        </ng-container>

        @if(getServiceTypeName() === 'Print Service'){
        <ng-container>
          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              BINDING
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ bindingType }}
            </p>
            @if (bindingTypeOption === BIND_TYPE.BINDERS) {
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              Size : <span>{{ binderSize }}</span>
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              Color : <span>{{ binderColor }}</span>
            </p>
            }
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              PRINT SET
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ printSet }}
              @if(printSetOption === PRINT_SET.NUMBER_OF_SET){
              <span
                class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]"
                >{{ printSetNumber }}</span
              >
              }
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              PAPER SIDE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ paperSide }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              PAPER TYPE
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ paperType }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              DOCUMENT SEPERATOR
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ documentSeparator }}
            </p>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-my-1">
            <p
              class="t-text-[12px] t-font-medium t-tracking-[1.8px] t-leading-[16px]">
              FAMILY FILE HANDLING
            </p>
            <p
              class="t-text-[12px] t-font-normal t-text-[#00000099] t-tracking-[0.4px] t-leading-[16px]">
              {{ printFamilyFileHandling }}
            </p>
          </div> </ng-container
        >}
      </div>
    </div>
  </div>
</div>
