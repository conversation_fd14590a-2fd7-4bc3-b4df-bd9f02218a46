import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerDialogContainerComponent } from './document-view-designer-dialog-container.component'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { NotificationService } from '@progress/kendo-angular-notification'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('DocumentViewDesignerDialogContainerComponent', () => {
  let component: DocumentViewDesignerDialogContainerComponent
  let fixture: ComponentFixture<DocumentViewDesignerDialogContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        DocumentViewDesignerDialogContainerComponent,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        FieldFacade,
        provideMockStore({}),
        NotificationService,
        {
          provide: DialogRef,
          useValue: {
            content: {}, // Mock the content if needed
            dialog: {
              // Add more mock functions as needed based on your usage
              open: jest.fn(),
              close: jest.fn(),
              onDestroy: jest.fn(),
            },
            // Add any additional methods or properties you access from DialogRef in your tests
            context: {}, // Mock context if it's being used
            result: Promise.resolve(), // If you use the result property in your tests
            close: jest.fn(), // If you directly use dialogRef.close()
            // ... other methods or properties
          },
        },
        SearchFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      DocumentViewDesignerDialogContainerComponent
    )
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
