@layer {
  kendo-treelist {
    // By Default
    .k-grid-content {
      .k-table {
        tr {
          td {
            @apply t-text-[#000000];
            @apply t-items-center #{!important};
            &:focus {
              @apply t-shadow-none #{!important};
            }
            .k-icon-wrapper-host {
              .k-treelist-toggle {
                &.k-svg-i-caret-alt-down {
                  svg {
                    @apply t-hidden #{!important};
                  }
                  &::before {
                    content: ' ';
                    width: 20px;
                    height: 20px;
                    background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                      no-repeat center/16px 16px;
                  }
                }

                &.k-svg-i-caret-alt-right {
                  svg {
                    @apply t-hidden #{!important};
                  }
                  &::before {
                    content: ' ';
                    width: 20px;
                    height: 20px;
                    background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                      no-repeat center/16px 16px;
                    transform: rotate(268deg);
                  }
                }
              }
            }
          }
        }
      }
    }

    &.v-reprocess-setting-treelist{
      .k-header{
        @apply t-hidden #{!important};
      }
      .k-grid-header-wrap{
        @apply t-border-0 #{!important};
      }
      .k-grid-header{
        @apply t-bg-[#F9F9F9] #{!important};
        .k-table-thead{
          @apply t-bg-[#F9F9F9] #{!important};

        }
      }
      kendo-treelist-filter-wrapper-cell{
        @apply t-gap-0 t-flex-row-reverse #{!important};
        .k-textbox{
          @apply t-border-l-0 t-pr-[34px]  #{!important};
        }
        &::before{
          content: ' ';
          background: url('~apps/venio-next/src/assets/svg/icon-updated-search.svg')
          no-repeat center/20px 20px;

        @apply t-absolute t-right-[21px] t-z-10 t-w-[22px] t-h-[22px];
        }
      }
      kendo-treelist-filter-cell-operators{
      
        .k-dropdown-operator{
          @apply t-border-r-0 t-rounded-sm t-rounded-r-none #{!important};
        }
        button{
          &.k-icon-button{
            @apply t-hidden;
          }
        }
        .k-dropdown-operator{
          .k-icon-button{
            @apply t-inline-flex t-h-[32px];
          }
        }
        
        .k-icon-wrapper-host{
          kendo-svgicon{
            @apply t-hidden #{!important};
           
          }
          &::after{
            content: ' ';
            width: 20px;
            height: 20px;
            background: url('~apps/venio-next/src/assets/svg/icon-custom-dropdown-filter.svg')
            no-repeat center/20px 20px;
          @apply t-absolute;

          }
        }
      }
    }

    &.v-no-data{
      .k-grid-header{
        .k-grid-header-wrap{
          @apply t-opacity-0;
        }
      }
      .k-grid-content {
        .k-table {
          @apply t-w-full #{!important};
        }
      }
    }

    
    &.v-custom-tagtree {
      .v-custom-tagheader {
        @apply t-flex t-flex-row-reverse t-justify-end t-items-center t-gap-2 #{!important};
      }

      .k-svg-i-reorder {
        @apply t-relative;
        svg {
          @apply t-invisible;
        }
        &::after {
          pointer-events: none;
          content: ' ';
          width: 20px;
          height: 20px;
          background: url('~apps/venio-next/src/assets/svg/icon-drag-and-drop-universal.svg')
            no-repeat center/20px 20px;
          @apply t-absolute;
        }
      }
    }

    &.v-custom-view-tree {
      .k-filtercell {
        .k-filtercell-wrapper {
          @apply t-gap-0 #{!important};
          .k-input {
            @apply t-w-[100%] #{!important};
          }
        }
      }

      .k-table-head {
        .k-filter-row {
          td:first-child {
            @apply t-hidden #{!important};
          }
        }
      }

      .k-header {
        @apply t-align-middle #{!important};
      }
      .k-column-title {
        @apply t-text-xs #{!important};
      }
      @apply t-border-0  #{!important};
      .k-grid-container {
        @apply t-border t-border-[#D5D5D5] #{!important};
        .k-grid-content {
          @apply t-overflow-x-hidden #{!important};
        }
      }
      .k-grid-header {
        @apply t-pb-2 #{!important};
        .k-grid-header-wrap {
          @apply t-border-0 t-pb-1 #{!important};

          .k-table-th {
            @apply t-shadow-none t-cursor-pointer #{!important};
            .k-checkbox{
              @apply t-mt-[3px] #{!important};
            }
            &:focus,&:active{
              @apply t-shadow-none #{!important};
            }
          }
        }
      }
      th {
        @apply t-border-0 t-p-0 #{!important};
      }
      td {
        @apply t-border-0 t-p-0 #{!important};
      }

      tbody {
        td {
          @apply t-overflow-visible #{!important};
          .k-checkbox {
            @apply t-ml-2 t-relative t-z-10 #{!important};
          }
        }
      }

      .k-sort-icon {
        @apply t-flex t-place-content-center;
        .k-svg-i-sort-asc-small {
          @apply t-relative t-w-[5px] #{!important};
          &::after {
            content: ' ';
            position: absolute;
            width: 8px !important;
            height: 13px !important;
            background: url('~apps/venio-next/src/assets/svg/Icon-sort-asc.svg')
              no-repeat center/8px 13px;
          }
          svg {
            @apply t-invisible #{!important};
          }
        }

        .k-svg-i-sort-desc-small {
          @apply t-relative t-w-[5px] #{!important};
          &::after {
            content: ' ';
            position: absolute;
            width: 8px !important;
            height: 13px !important;
            background: url('~apps/venio-next/src/assets/svg/Icon-sort-desc.svg')
              no-repeat center/8px 13px;
          }
          svg {
            @apply t-invisible #{!important};
          }
        }
      }
    }

    &.v-custodian-tree {
      tbody {
        .k-table-row {
          &.k-selected {
            td {
             @apply t-bg-white #{!important};
            }
          }
        }
      }
      .k-grid-container{
        @apply t-border-0 #{!important};
      }
      .k-grid-content {
        .k-table {
          tr {
            &.k-selected{
              td{
                @apply t-bg-[#FFFFFF] t-items-center t-text-[var(--tb-kendo-primary-100)] t-font-semibold #{!important};
              }
            }
          
          }
        }
      }
    }
    &.v-custom-folder-tree-list {
      @apply t-max-h-[200px] t-overflow-y-auto #{!important};
      .k-grid-header {
        @apply t-hidden #{!important};
      }
    }

    // For document list even rows
    &.v-custom-even-treelist {
      .k-grid-content {
        .k-table {
          tr:nth-child(even) {
            td {
              @apply t-bg-[#F7F7F7];
            }
          }
        }
      }
    }

    // for family tree list in review
    &.v-custom-family-treelist {
      &.k-grid {
        @apply t-border-0 #{!important};

        .k-grid-header {
          .k-table-th {
            color: var(--tb-kendo-primary-100);
          }
        }

        .k-icon-wrapper-host {
          color: #1ebadc;
        }
      }
    }
    // for custom folder tree list
    &.v-custom-folder-container {
      .v-custom-folder-cell-actions {
        @apply t-opacity-0 t-invisible t-transition t-ease-in t-delay-75;
      }

      .v-custom-folder-container-cell {
        &:hover {
          .v-custom-folder-cell-actions {
            @apply t-opacity-100 t-visible;
          }
        }
      }
    }

    .k-grid-content {
      background: transparent !important;
    }
  }
}
