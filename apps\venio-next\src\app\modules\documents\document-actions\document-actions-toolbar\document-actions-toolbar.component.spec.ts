import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentActionsToolbarComponent } from './document-actions-toolbar.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import {
  BulkRedactFacade,
  CaseInfoFacade,
  FieldFacade,
  ReviewSetStateService,
  SearchFacade,
} from '@venio/data-access/review'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject } from 'rxjs'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentActionsToolbarComponent', () => {
  let component: DocumentActionsToolbarComponent
  let fixture: ComponentFixture<DocumentActionsToolbarComponent>
  let queryParamsSubject: BehaviorSubject<any>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })
    await TestBed.configureTestingModule({
      imports: [DocumentActionsToolbarComponent, BrowserAnimationsModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        VenioNotificationService,
        NotificationService,
        SearchFacade,
        BulkRedactFacade,
        FieldFacade,
        DialogService,
        DialogContainerService,
        ReviewSetStateService,
        provideMockStore({}),
        CaseInfoFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentActionsToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
