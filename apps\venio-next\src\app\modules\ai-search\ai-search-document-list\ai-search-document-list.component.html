<div
  *ngFor="let sum of summaryList(); trackBy: trackBySummary; let e = even"
  class="t-flex t-flex-col t-w-full t-p-3 t-overflow-x-hidden v-hide-scrollbar t-relative"
  [ngStyle]="{ 'background-color': e ? '' : '#F7F7F7' }">
  <div
    [ngStyle]="{ 'background-color': e ? 't-bg-white' : '#F7F7F7' }"
    class="t-block t-text-[#1EBADC] t-font-semibold t-sticky t-top-[-12px] t-pt-2">
    <kendo-popover #searchTermPopover [width]="400">
      <ng-template kendoPopoverBodyTemplate>
        <div class="t-block t-max-h-52 v-hide-scrollbar">
          {{ sum.value.original_query }}
        </div>
      </ng-template>
    </kendo-popover>
    <div
      class="t-truncate t-inline-block t-max-w-full"
      kendoPopoverAnchor
      [popover]="searchTermPopover"
      showOn="hover">
      {{ sum.value.original_query }}
    </div>
  </div>

  @for(doc of sum.value.content?.documents; track doc.file_id){ @if(doc.file_id
  > 0) {
  <div class="t-flex t-flex-col t-gap-3 t-w-full t-mt-4 t-pr-2.5 t-relative">
    <div class="t-flex t-gap-5 t-w-full">
      <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
        <div class="t-text-primary t-uppercase t-font-semibold">
          Internal file ID
        </div>
        <div class="t-block">{{ doc.file_id }}</div>
      </div>

      <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
        <div class="t-text-primary t-uppercase t-font-semibold">
          Original File Name
        </div>
        <div
          class="t-block t-relative"
          venioTextTruncate
          [lineCount]="2"
          kendoTooltip
          tooltipClass="t-break-words"
          [tooltipWidth]="300"
          [title]="doc.name"
          [showAfter]="500">
          {{ doc.name }}
        </div>
      </div>
    </div>
    <div class="t-flex t-gap-5 t-w-full t-relative">
      <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
        <div class="t-text-primary t-uppercase t-font-semibold">
          Internal Extension
        </div>
        <div class="t-block">{{ doc.extension }}</div>
      </div>

      <div class="t-flex t-flex-1 t-gap-2 t-flex-col t-relative t-break-words">
        <div class="t-text-primary t-uppercase t-font-semibold">
          Original File Path
        </div>
        <div
          class="t-block t-relative t-text-ellipsis"
          venioTextTruncate
          [lineCount]="2"
          kendoTooltip
          tooltipClass="t-break-words"
          [tooltipWidth]="300"
          [title]="doc.path"
          [showAfter]="500">
          {{ doc.path }}
        </div>
      </div>
    </div>
  </div>
  } }
</div>
