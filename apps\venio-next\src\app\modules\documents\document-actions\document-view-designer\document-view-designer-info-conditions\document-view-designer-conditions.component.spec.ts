import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerConditionsComponent } from './document-view-designer-conditions.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('DocumentViewDesignerConditionsComponent', () => {
  let component: DocumentViewDesignerConditionsComponent
  let fixture: ComponentFixture<DocumentViewDesignerConditionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerConditionsComponent, NoopAnimationsModule],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerConditionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
