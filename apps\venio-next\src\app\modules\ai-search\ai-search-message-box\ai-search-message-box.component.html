<div
  class="t-relative t-w-full t-h-full t-flex"
  [ngClass]="{
    'v-custom-magic-ai-box':
      !isFocused() &&
      ((isAiSearchLoading$ | async) === false ||
        (isAiSearchLoading$ | async) === undefined),
    'v-custom-magic-ai-box-default': isFocused()
  }">
  <kendo-textarea
    [disabled]="isAiSearchLoading$ | async"
    (keydown.enter)="search($event)"
    (focus)="validateInputFocus()"
    (blur)="validateInputFocus()"
    [placeholder]="
      isConnected()
        ? 'Type your query here...'
        : 'Establishing connection please wait...'
    "
    [(ngModel)]="messageBox.value"
    resizable="none"
    [rows]="lines"
    flow="horizontal"
    [ngClass]="{ 't-py-1': messageBox.value?.trim().length > 0 && lines > 6 }"
    class="t-w-full v-hide-scrollbar">
    <kendo-textarea-suffix>
      <button
        (click)="search($event)"
        kendoButton
        class="t-p-0 t-px-2 t-m-0 v-custom-ai-search-btn"
        fillMode="link">
        <span
          venioSvgLoader
          applyEffectsTo="stroke"
          color="#1EBADC"
          svgUrl="assets/svg/icon-enter-key-down.svg"
          title="Press Enter or Click to perform search"
          height="1.5rem"
          width="1.5rem"></span>
      </button>
      <button
        *ngIf="hasSummaryList()"
        (click)="resetALl()"
        kendoTooltip
        [showAfter]="500"
        kendoButton
        class="t-px-2 t-mr-2 v-custom-ai-search-btn"
        fillMode="link">
        <span
          venioSvgLoader
          applyEffectsTo="fill"
          color="#ED7425"
          svgUrl="assets/svg/icon-preview-refresh.svg"
          title="Reset All"
          height="1.1rem"
          width="1.1rem"></span>
      </button>
    </kendo-textarea-suffix>
  </kendo-textarea>
</div>
@if(hasMinimumToken()){
<div class="t-text-error t-flex t-w-full t-py-1 t-px-2">
  For better results, please add some unique and relevant terms
</div>
} @if(hasMaximumToken()){
<div class="t-text-error t-flex t-w-full t-py-1 t-px-2">
  Your query is a bit wordy. Please remove any unnecessary parts.
</div>
}
