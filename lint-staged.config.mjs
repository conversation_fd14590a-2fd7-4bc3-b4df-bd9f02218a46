export default {
  '{apps,libs}/**/*.{js,ts,jsx,tsx,mjs,cjs}': (filenames) => {
    const fileList = filenames.join(' ')
    return [
      `eslint ${fileList} --fix=true --quiet=true --cache=true`
    ]
  },
  '{apps,libs}/**/*.{html,js,ts,json,mjs,cjs,scss, css}': (filenames) => {
    const fileList = filenames.join(' ')
    return `nx format:write --skip-nx-cache --libs-and-apps --files=${fileList}`
  }
}
