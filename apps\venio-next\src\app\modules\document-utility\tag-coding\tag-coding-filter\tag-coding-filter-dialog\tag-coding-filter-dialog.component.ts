import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { Subject, switchMap, tap, takeUntil } from 'rxjs'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  ProjectTag,
  ProjectTagViewModel,
} from '@venio/data-access/document-utility'
import {
  SelectableSettings,
  SelectionChangeItem,
  SelectionItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { DocumentCodingModel } from '@venio/shared/models/interfaces'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { cloneDeep, uniq } from 'lodash'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-tag-coding-filter-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    TreeListModule,
    GridModule,
    InputsModule,
    LabelModule,
    ButtonsModule,
    IndicatorsModule,
  ],
  templateUrl: './tag-coding-filter-dialog.component.html',
  styleUrls: ['./tag-coding-filter-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCodingFilterDialogComponent implements OnInit, OnDestroy {
  public tagSearchComponent = import('@venio/feature/tag-email-thread').then(
    (m) => m.TagSearchComponent
  )

  public codingSearchComponent = import(
    '../../../tag-coding/document-coding/coding-search/coding-search.component'
  ).then((m) => m.CodingSearchComponent)

  public readonly toDestroy$ = new Subject<void>()

  public opened = false

  public dialogTitle = 'Show and Hide Tags & Coding'

  public tagNodes: ProjectTagViewModel[]

  public clonedTagNodes: ProjectTagViewModel[]

  public codingNodes: DocumentCodingModel[]

  public clonedCodingNodes: DocumentCodingModel[]

  public selectedTags: SelectionItem[] = []

  public selectedCoding = []

  public isAllTagsSelected = false

  public isIndeterminateAllTags = false

  public isTagNodesLoading = true

  public isCodingNodesLoading = true

  public initialSelectedCoding: number[] = []

  public initialSelectedTags: number[] = []

  public settings: SelectableSettings = {
    mode: 'row',
    multiple: true,
    drag: false,
  }

  constructor(
    private documentTagFacade: DocumentTagFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private changeDetectorRef: ChangeDetectorRef,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#fetchProjectTags()
    this.#fetchCodingFields()
    this.#searchDocumentTags()
    this.#searchDocumentCoding()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public close(): void {
    this.dialogRef.close()
  }

  #fetchProjectTags(): void {
    this.isTagNodesLoading = true
    this.documentTagFacade.selectTagFilterProjectTags$
      .pipe(
        tap((tags) => {
          this.tagNodes = tags
          this.clonedTagNodes = cloneDeep(tags)
        }),
        switchMap(() => this.documentTagFacade.selectVisibleTags$),
        takeUntil(this.toDestroy$)
      )
      .subscribe((visibleTagFields) => {
        this.isTagNodesLoading = false
        this.#updateTagVisibleFields(visibleTagFields)
        this.changeDetectorRef.markForCheck()
      })
  }

  #fetchCodingFields(): void {
    this.isCodingNodesLoading = true
    this.documentCodingFacade.selectDocumentCodingFields$
      .pipe(
        tap((codingFields) => {
          this.codingNodes = codingFields
          this.clonedCodingNodes = cloneDeep(codingFields)
        }),
        switchMap(() => this.documentCodingFacade.selectVisibleCodingFields$),
        takeUntil(this.toDestroy$)
      )
      .subscribe((visibleCodingFields) => {
        this.#updateCodingVisibleFields(visibleCodingFields)
        this.isCodingNodesLoading = false
        this.changeDetectorRef.markForCheck()
      })
  }

  #updateTagVisibleFields(visibleTagFields: ProjectTag[]): void {
    if (visibleTagFields?.[0]) {
      this.selectedTags = visibleTagFields.map((fileId) => ({
        itemKey: fileId.TreeKeyId,
      }))
    } else {
      this.selectedTags = !visibleTagFields
        ? this.tagNodes.map((fileId) => ({
            itemKey: fileId.TreeKeyId,
          }))
        : []
    }
    this.initialSelectedTags = this.selectedTags.map((t) => t.itemKey)
    this.#updateHeaderCheckboxStatus()
  }

  #updateCodingVisibleFields(visibleCodingFields: DocumentCodingModel[]): void {
    if (visibleCodingFields?.[0]) {
      this.selectedCoding = visibleCodingFields.map(
        (item) => item.customFieldInfoId
      )
    } else {
      this.selectedCoding = !visibleCodingFields
        ? this.codingNodes.map((item) => item.customFieldInfoId)
        : []
    }
    this.initialSelectedCoding = this.selectedCoding
  }

  public onHeaderCheckBoxSelected(): void {
    this.isAllTagsSelected = !this.isAllTagsSelected
    this.isIndeterminateAllTags = false
    if (this.isAllTagsSelected) {
      this.selectedTags = this.tagNodes.map((fileId) => ({
        itemKey: fileId.TreeKeyId,
      }))
    } else {
      this.selectedTags = []
    }
  }

  public onSelectionChange(e): void {
    const items: SelectionChangeItem[] = e.items

    if (e.action === 'add') {
      let selectedTagsKey: SelectionItem[] = []
      items.forEach((item) => {
        const tag = item.dataItem.TreeKeyId
        const currentTag = this.tagNodes.find((t) => t.TreeKeyId === tag)

        if (currentTag.TreeParentId === '-1') {
          selectedTagsKey = this.tagNodes
            .filter(
              (t) =>
                t.TreeKeyId.startsWith(currentTag.TreeKeyId + '_') &&
                t.TreeKeyId !== tag
            )
            ?.map((t) => ({ itemKey: t.TreeKeyId }))
          selectedTagsKey.push({ itemKey: currentTag.TreeKeyId })
        } else {
          const keys = tag.split('_')
          keys.reduce((accum, current) => {
            accum = accum.length === 0 ? current : `${accum}_${current}`
            if (
              !selectedTagsKey.some((keyItem) => keyItem.itemKey === accum) &&
              !this.selectedTags.some((keyItem) => keyItem.itemKey === accum)
            ) {
              selectedTagsKey.push({ itemKey: accum })
            }
            return accum
          }, '')
        }
        const selectedTags = uniq([...this.selectedTags, ...selectedTagsKey])
        this.#updateTagSelectionView(selectedTags)
      })
    } else if (e.action === 'remove') {
      const deselectedKey = items[0].dataItem.TreeKeyId
      const selectedKeys = this.selectedTags
        .filter((keyItem) => keyItem.itemKey !== deselectedKey)
        .map((keyItem) => keyItem.itemKey)
      const childKeys = selectedKeys.filter((key) =>
        key.startsWith(deselectedKey + '_')
      )
      this.#deselectTagGroupIfRequired(deselectedKey, selectedKeys)
      this.selectedTags = selectedKeys
        .filter((t) => !childKeys.includes(t))
        .map((t) => ({ itemKey: t }))
      this.#updateTagSelectionView(this.selectedTags)
    }
  }

  #deselectTagGroupIfRequired(
    deselectedKey: string,
    selectedKeys: string[]
  ): void {
    const ids: string[] = deselectedKey.toString().split('_')
    if (ids.length > 1) {
      const tagGroupId = ids[0]
      if (!selectedKeys.some((key) => key.startsWith(tagGroupId + '_'))) {
        this.selectedTags = this.selectedTags.filter(
          (t) => t.itemKey !== tagGroupId
        )
      }
    }
  }

  #updateTagSelectionView(selectedTags: SelectionItem[]): void {
    setTimeout(() => {
      this.selectedTags = selectedTags
      this.#updateHeaderCheckboxStatus()
      this.changeDetectorRef.markForCheck()
    }, 300)
  }

  #updateHeaderCheckboxStatus(): void {
    const selectedTagCount = this.selectedTags.length
    const totalTagCount = this.tagNodes.length

    if (totalTagCount === selectedTagCount) {
      this.isAllTagsSelected = true
      this.isIndeterminateAllTags = false
    } else if (selectedTagCount > 0) {
      this.isAllTagsSelected = false
      this.isIndeterminateAllTags = true
    } else {
      this.isAllTagsSelected = false
      this.isIndeterminateAllTags = false
    }
  }

  private getSelectedTag(): ProjectTag[] {
    return this.clonedTagNodes.filter((obj) =>
      this.selectedTags.some((t) => t.itemKey === obj.TreeKeyId)
    )
  }

  private getSelectedCoding(): DocumentCodingModel[] {
    return this.clonedCodingNodes.filter((obj) =>
      this.selectedCoding.includes(obj.customFieldInfoId)
    )
  }

  #searchDocumentCoding(): void {
    this.documentCodingFacade.selectSearchDocumentCoding$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((search) => {
        this.#searchCodingNodes(search)
      })
  }

  #searchCodingNodes(filterTerm: string): void {
    const clonedCodingNodes = cloneDeep(this.clonedCodingNodes)
    if (filterTerm.trim() === '') {
      this.codingNodes = clonedCodingNodes
    } else {
      this.codingNodes = clonedCodingNodes.filter((node) =>
        node.displayName.toLowerCase().includes(filterTerm.toLowerCase())
      )
    }

    this.changeDetectorRef.markForCheck()
  }

  #searchDocumentTags(): void {
    this.documentTagFacade.selectSearchDocumentTag$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((search) => {
        this.#searchTagNodes(search)
      })
  }

  #searchTagNodes(filterTerm: string): void {
    const clonedTagNodes = cloneDeep(this.clonedTagNodes)
    if (filterTerm.trim() === '') {
      this.tagNodes = clonedTagNodes
    } else {
      this.tagNodes = this.filterNodes(clonedTagNodes, filterTerm)
    }

    this.changeDetectorRef.markForCheck()
  }

  private filterNodes(nodes: any, searchText: string): any {
    const searchTagsKey: ProjectTagViewModel[] = []
    const searchNodes = nodes
      .filter((node) =>
        node.TagName.toLowerCase().includes(searchText.toLowerCase())
      )
      .sort((a, b) => a.TagGroupId - b.TagGroupId)

    searchNodes.forEach((item) => {
      const tag = item.TreeKeyId
      const keys = tag.split('_')
      keys.reduce((accum, current) => {
        accum = accum.length === 0 ? current : `${accum}_${current}`
        if (
          searchTagsKey &&
          !searchTagsKey.some((keyItem) => keyItem.TreeKeyId === accum)
        ) {
          searchTagsKey.push(this.tagNodes.find((t) => t.TreeKeyId === accum))
        }
        return accum
      }, '')
    })
    return searchTagsKey
  }

  #checkIfDataModified(initial: number[], current: number[]): boolean {
    if (initial.length !== current.length) return true
    return initial.some((value, index) => value !== current[index])
  }

  #checkIfTagsModified(): boolean {
    const currentSelectedTags = this.selectedTags.map((t) => t.itemKey)
    const isTagModified = this.#checkIfDataModified(
      this.initialSelectedTags,
      currentSelectedTags
    )
    return isTagModified
  }

  #checkIfCodingModified(): boolean {
    const isCodeModified = this.#checkIfDataModified(
      this.initialSelectedCoding,
      this.selectedCoding
    )
    return isCodeModified
  }

  #updateTagFields(): void {
    const selectedTags = this.getSelectedTag()
    const tags: ProjectTag[] = selectedTags?.[0] ? selectedTags : []
    this.documentTagFacade.updatVisibleTagFields(tags)
  }

  #updateCodingFields(): void {
    const selectedCoding = this.getSelectedCoding()
    const coding: DocumentCodingModel[] = selectedCoding?.[0]
      ? selectedCoding
      : []
    this.documentCodingFacade.updatVisibleCodingFields(coding)
  }

  public save(): void {
    if (this.#checkIfTagsModified()) {
      this.#updateTagFields()
    }

    if (this.#checkIfCodingModified()) {
      this.#updateCodingFields()
    }

    this.close()
  }
}
