<div class="t-flex t-flex-col t-gap-[10px] t-p-[20px]">
  <div class="t-flex t-justify-between t-w-full t-items-center mt-ml-[10px]">
    <div
      class="t-block t-tracking-[0.14px] t-text-[#2F3080DE] t-font-medium t-gap-3">
      <span
        class="t-w-10 t-h-10 t-p-3 t-bg-[#ED74251A] t-rounded-full t-inline-flex t-justify-center">
        <img
          src="assets/svg/icon-material-delete-orang.svg"
          alt="delete icon" />
      </span>
      Delete Production
    </div>
  </div>

  <div class="t-flex t-flex-col t-h-full t-mt-2 t-gap-5">
    <div>
      <b>Please choose a deletion mode:</b>
      <form class="t-pt-2">
        <div class="t-space-y-2">
          <div class="t-flex t-items-center">
            <input
              type="radio"
              size="small"
              id="deleteAll"
              name="deleteType"
              [value]="'DeleteAll'"
              [(ngModel)]="deletionType"
              kendoRadioButton />
            <label for="deleteAll" class="ml-2">
              Delete All (Database records, Preserved images, Produced files)
            </label>
          </div>

          <div class="t-flex t-items-center">
            <input
              type="radio"
              size="small"
              id="deleteFilesOnly"
              name="deleteType"
              [value]="'DeleteFilesOnly'"
              [(ngModel)]="deletionType"
              kendoRadioButton />
            <label for="deleteFilesOnly" class="ml-2">
              Delete Files Only (Preserved images, Produced files)
            </label>
          </div>

          <div class="t-flex t-items-center">
            <input
              type="radio"
              size="small"
              id="deleteExportVolume"
              name="deleteType"
              [value]="'DeleteExportVolume'"
              [(ngModel)]="deletionType"
              kendoRadioButton />
            <label for="deleteExportVolume" class="ml-2">
              Delete Produced Volume (Produced files only)
            </label>
          </div>

          <div class="t-flex t-items-center">
            <input
              type="radio"
              size="small"
              id="deleteDatabaseRecordsOnly"
              name="deleteType"
              [value]="'DeleteDatabaseRecordsOnly'"
              [(ngModel)]="deletionType"
              kendoRadioButton />
            <label for="deleteDatabaseRecordsOnly" class="ml-2">
              Delete Database Records Only
            </label>
          </div>
        </div>
      </form>
    </div>

    <p
      class="t-font-medium t-text-[14px] t-text-[#263238] t-opacity-100 t-tracking-[0.1px]">
      Are you sure you want to delete Production?
    </p>
    <div class="t-flex t-pt-2">
      <div class="t-w-1/12 t-relative t-top-[-5px]">
        <span
          class="t-font-bold t-text-[10px] t-text-[#ED7428] t-uppercase t-opacity-100 t-tracking-[0.14px]"
          >Notes:</span
        >
      </div>
      <div
        class="t-font-medium t-text-[10px] t-text-[#232323] t-w-11/12 t-ml-2">
        <span>* Deleting a production deletes its re-production(s) also.</span>
        <br />
        <span class="t-font-medium t-text-[10px] t-text-[#232323] t-pt-1">
          * Files from locations get deleted as per delete workflow scheduled.
        </span>
      </div>
    </div>
  </div>

  <div class="t-flex t-flex-row t-gap-3 t-justify-end t-mt-5">
    <button
      class="t-bg-[#FF5F521A] t-w-[30px] t-h-[23px] t-text-[#EC3737]"
      (click)="closeDeletePopup.emit(true)"
      #cancelProductionDeleteBtn>
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
    <button
      class="t-bg-[#BAE36E3D] t-w-[30px] t-h-[23px] t-text-[#88B13F]"
      (click)="deleteProduction()"
      #productionDeleteBtn>
      <kendo-svg-icon [icon]="icons.tickIcon"></kendo-svg-icon>
    </button>
  </div>
</div>
