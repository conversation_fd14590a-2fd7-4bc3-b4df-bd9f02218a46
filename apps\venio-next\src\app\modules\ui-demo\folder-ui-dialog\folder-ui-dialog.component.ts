import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { employees } from '../launchpad-caseui/employees'

import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { FormsModule } from '@angular/forms'
import {
  ExpansionPanelModule,
  LayoutModule,
  SelectEvent,
} from '@progress/kendo-angular-layout'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-folder-ui-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    GridModule,
    InputsModule,
    SvgLoaderDirective,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    IconsModule,
    ExpansionPanelModule,
  ],
  templateUrl: './folder-ui-dialog.component.html',
  styleUrls: ['./folder-ui-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderUiDialogComponent implements OnInit {
  public opened = false

  public sizes = [5, 10, 20, 50]

  public sampleSecurityData = [
    {
      Id: 'ALFKI',
      CompanyName: 'Demo_master-Site Admin Group',
      ContactName: 'Maria Anders',
      ContactTitle: 'Sales Representative',
      City: 'Berlin',
    },
    {
      Id: 'ANATR',
      CompanyName: 'Demo_master-Project Admin Group',
      ContactName: 'Ana Trujillo',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'ANTON',
      CompanyName: 'Demo_master-User Group',
      ContactName: 'Antonio Moreno',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'AROUT',
      CompanyName: 'Demo_master-Viewer Group',
      ContactName: 'Thomas Hardy',
      ContactTitle: 'Sales Representative',
      City: 'London',
    },
  ]

  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'Tag Group Name',
    value: null,
  }

  // for dropdown module - create folder
  public defaultItemFolder: { text: string; value: number } = {
    text: '----Root Folder----',
    value: null,
  }

  public svgIconForPageControls = [
    {
      actionType: 'FIRST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: 'NEXT_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: 'PREV_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: 'LAST_PAGE',
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  public pageSize = 5

  public gridData: unknown[] = employees

  public saveTitle = 'CREATE'

  public ngOnInit(): void {
    this.openDialog()
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public openDialog(): void {
    this.opened = true
  }

  public onTabSelect(e: SelectEvent): void {
    if (e.index === 0) {
      this.saveTitle = 'CREATE'
    } else if (e.index === 2) {
      this.saveTitle = 'AUTO-FOLDER'
    } else {
      this.saveTitle = 'SAVE'
    }
  }
}
