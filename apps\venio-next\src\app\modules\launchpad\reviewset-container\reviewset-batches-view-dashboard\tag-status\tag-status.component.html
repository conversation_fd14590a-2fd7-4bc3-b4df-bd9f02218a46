<div class="t-flex t-items-center t-justify-between t-mb-3 t-w-full">
  <div class="t-flex t-items-center t-w-full">
    <span
      class="t-inline-block t-w-3 t-h-3 t-bg-[#02A9D3] t-rounded-full t-mr-2"></span>
    <span class="t-text-[#393939] t-text-sm t-font-medium">Tag Status</span>
  </div>

  <button
    kendoButton
    fillMode="clear"
    size="none"
    class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-leading-none t-overflow-hidden t-group"
    [disabled]="shouldDisableTagFilter()"
    (click)="openTagFilter()">
    <span
      class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>
    <kendo-svgicon
      class="t-text-[#6C6C6C] group-hover:t-text-[#1EBADC]"
      [icon]="icons.eyeIcon"></kendo-svgicon>
  </button>
</div>

<div class="t-relative">
  @if (isReviewSetTagStatusLoading()) {
  <div
    class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
  }

  <div class="t-flex">
    @if (loadedTagStatus()) {
    <kendo-chart
      class="t-shadown-none t-border-0 t-w-full v-custom-char-production-bar t-h-[210px]">
      <kendo-chart-category-axis>
        <kendo-chart-category-axis-item
          [categories]="['']"
          [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [line]="COMMON_CHART_OPTIONS.lineOptions"
          [labels]="COMMON_CHART_OPTIONS.categoryLabels">
        </kendo-chart-category-axis-item>
      </kendo-chart-category-axis>
      <kendo-chart-value-axis>
        <kendo-chart-value-axis-item
          [min]="0"
          [max]="7"
          [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [minorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [labels]="COMMON_CHART_OPTIONS.valueAxisLabels">
        </kendo-chart-value-axis-item>
      </kendo-chart-value-axis>

      <kendo-chart-series>
        <kendo-chart-series-item
          type="bar"
          [data]="loadedTagStatus()"
          field="taggedDocCount"
          categoryField="tagName"
          color="color"
          [gap]="10"
          [spacing]="3"
          [border]="COMMON_CHART_OPTIONS.border"
          [labels]="COMMON_CHART_OPTIONS.seriesLabels">
        </kendo-chart-series-item>
      </kendo-chart-series>
    </kendo-chart>
    }
  </div>
</div>
