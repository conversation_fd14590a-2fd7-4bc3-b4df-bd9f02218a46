import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  Field,
  FieldFacade,
  SortType,
  ViewSortSetting,
} from '@venio/data-access/review'
import {
  combineLatest,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs'
import {
  DragOverEvent,
  DragStartEvent,
  NavigateEvent,
  SortableComponent,
  SortableModule,
} from '@progress/kendo-angular-sortable'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { DocumentViewFacade } from '@venio/data-access/common'
import { DebounceTimer } from '@venio/util/utilities'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { isEqual } from 'lodash'
import { debounceTime } from 'rxjs/operators'

type SortFormGroup = {
  fieldSortType: SortType
  displayFieldName: string
}

@Component({
  selector: 'venio-document-view-designer-sort',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    DropDownsModule,
    SortableModule,
    ButtonsModule,
    SvgLoaderDirective,
    ReactiveFormsModule,
    LoaderModule,
  ],
  templateUrl: './document-view-designer-sort.component.html',
  styleUrl: './document-view-designer-sort.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerSortComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private toDestroy$ = new Subject<void>()

  public fields = signal<Field[]>([])

  public isPermittedFieldLoading = signal<boolean>(false)

  @ViewChild(SortableComponent)
  public sortable: SortableComponent

  public staticRows = [
    {
      formGroup: this.#createSortFormGroup(),
      availableFields: signal([]),
    },
    { formGroup: this.#createSortFormGroup(), availableFields: signal([]) },
    { formGroup: this.#createSortFormGroup(), availableFields: signal([]) },
  ]

  public staticSortOrderData = [
    { text: 'ASC', value: SortType.Ascending },
    { text: 'DESC', value: SortType.Descending },
  ]

  private dragInitiated = false

  constructor(
    private fieldFacade: FieldFacade,
    private formBuilder: FormBuilder,
    private documentViewFacade: DocumentViewFacade
  ) {}

  public ngOnInit(): void {
    this.#selectIsPermittedFieldLoading()
  }

  public setDragInitiated(value: boolean): void {
    this.dragInitiated = value
  }

  public onDragStart(event: DragStartEvent): void {
    if (!this.dragInitiated) {
      event.preventDefault()
    }
    this.dragInitiated = false
  }

  public ngAfterViewInit(): void {
    this.#selectCurrentFormDataSortSetting()
    this.#selectFields()
    this.#monitorSortFormGroupChanges()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public onDragOver(event: DragOverEvent): void {
    event.preventDefault()
    this.sortable.moveItem(event.oldIndex, event.index)

    this.updateSortStateAfterReorder()
  }

  public onNavigate(event: NavigateEvent): void {
    this.sortable.moveItem(event.oldIndex, event.index)
  }

  @DebounceTimer(500)
  private updateSortStateAfterReorder(): void {
    const sortedSettings = this.#mapSortSetting(
      this.sortable.data.map((s) => s.formGroup.getRawValue() as SortFormGroup)
    )

    this.#storeSortSettingState(sortedSettings)
  }

  #mapSortSetting(data: SortFormGroup[]): ViewSortSetting[] {
    return data.map((sortSetting) => {
      return {
        fieldName: sortSetting.displayFieldName,
        fieldSortType: sortSetting.fieldSortType,
      } as ViewSortSetting
    })
  }

  #storeSortSettingState(viewSortSettings: ViewSortSetting[]): void {
    this.documentViewFacade.storeCurrentFormData({
      viewSortSettings,
    })
  }

  #monitorSortFormGroupChanges(): void {
    combineLatest(this.staticRows.map((c) => c.formGroup.valueChanges))
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.updateSortStateAfterReorder()
      })
  }

  #createSortFormGroup(): FormGroup {
    return this.formBuilder.group({
      fieldSortType: SortType.Ascending,
      displayFieldName: '',
    })
  }

  #selectIsPermittedFieldLoading(): void {
    this.fieldFacade.selectIsPermittedFieldLoading$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isLoading) => {
        this.isPermittedFieldLoading.set(isLoading)
        this.staticRows.forEach((row) => {
          if (isLoading) {
            row.formGroup.disable()
          } else {
            row.formGroup.enable()
          }
        })
      })
  }

  #monitorDisplayFieldChange(): void {
    this.staticRows.forEach((row, rowIndex) => {
      // initially we need to set the available fields
      this.#updateAvailableFields(rowIndex)

      // and then we need to monitor the changes
      row.formGroup
        .get('displayFieldName')
        .valueChanges.pipe(takeUntil(this.toDestroy$))
        .subscribe(() => {
          this.#updateAvailableFields(rowIndex)
        })
    })
  }

  #updateAvailableFields(changedRowIndex: number): void {
    const selectedValues = this.staticRows.map(
      (row) => row.formGroup.get('displayFieldName').value as string
    )

    this.staticRows.forEach((row, rowIndex) => {
      if (rowIndex !== changedRowIndex) {
        const filtered = this.fields().filter(
          (field) =>
            !selectedValues.includes(field.displayFieldName) ||
            field.displayFieldName ===
              row.formGroup.get('displayFieldName').value
        )
        row.availableFields.set(filtered)
      }
    })
  }

  #selectCurrentFormDataSortSetting(): void {
    this.documentViewFacade.selectCurrentFormData$
      .pipe(
        filter((form) => Boolean(form?.viewSortSettings?.length)),
        distinctUntilChanged((a, b) =>
          isEqual(a?.viewSortSettings, b?.viewSortSettings)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((data) => {
        this.staticRows.forEach((row, index) => {
          const sortSetting = data.viewSortSettings[index]
          if (!sortSetting) return
          row.formGroup.patchValue(
            {
              fieldSortType: sortSetting.fieldSortType,
              displayFieldName: sortSetting.fieldName,
            } as SortFormGroup,
            {
              emitEvent: false,
            }
          )
        })
      })
  }

  #selectFields(): void {
    this.fieldFacade.getPermittedFields$
      .pipe(
        filter((fields) => Boolean(fields)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fields) => {
        const onlySearchableFields = fields.filter(
          (f) => f.isSearchField || f.isCustomField
        )
        this.fields.set(onlySearchableFields)
        this.#monitorDisplayFieldChange()
      })
  }
}
