import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LaunchpadSubToolbarComponent } from './launchpad-sub-toolbar.component'
import { StoreModule } from '@ngrx/store'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { CaseDetailModel } from '@venio/shared/models/interfaces'

describe('CaseLaunchpadSubToolbarComponent', () => {
  let component: LaunchpadSubToolbarComponent
  let fixture: ComponentFixture<LaunchpadSubToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LaunchpadSubToolbarComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectSelectedCaseDetail$: of([]),
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({} as CaseDetailModel),
            selectCaseDetailPagingInfo$: of({} as CaseDetailModel),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LaunchpadSubToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
