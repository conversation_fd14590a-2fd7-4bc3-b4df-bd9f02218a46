<div class="t-p-6 t-bg-gray-50 t-min-h-screen">
  <!-- Filter Section -->
  <div class="t-mb-6">
    <button kendoButton [svgIcon]="svgFilter" #anchor (click)="onToggle()" themeColor="secondary">
      Filters
    </button>
    @if (show) {
    <kendo-popup [anchor]="anchor.element">
      @if (showCustodianFilters) {
      <kendo-listview [data]="custodians">
        <ng-template kendoListViewItemTemplate let-dataItem="dataItem" let-isFirst="isFirst">
          <venio-eci-checkbox-list-item [custodian]="dataItem"></venio-eci-checkbox-list-item>
        </ng-template>
      </kendo-listview>
      } @else {
      <div class="t-p-6 t-bg-white t-rounded-lg t-shadow-sm">
        <button kendoButton (click)="onCustodianClick()" themeColor="primary">Custodians</button>
      </div>
      }
    </kendo-popup>
    }
  </div>

  <!-- Summary Section -->
  <app-summary></app-summary>

  <!-- Focused Section -->
  @if (isFocusedSectionOpened) {
  <venio-eci-focused-section></venio-eci-focused-section>
  }

  <!-- Dashboard Content -->
  <div class="t-flex t-flex-col t-gap-6">
    <!-- Two Column Section: Relevance + Word Cloud -->
    <div class="t-grid t-grid-cols-1 lg:t-grid-cols-3 t-gap-6">
      <div class="t-col-span-1">
        <venio-eci-relevance></venio-eci-relevance>
      </div>
      <div class="t-col-span-2">
        <venio-eci-word-cloud></venio-eci-word-cloud>
      </div>
    </div>

    <!-- Three Column Section: Document Types -->
    <div class="t-grid t-grid-cols-1 lg:t-grid-cols-3 t-gap-6">
      <div class="t-col-span-1">
        <venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst>
      </div>
      <div class="t-col-span-1">
        <venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst>
      </div>
      <div class="t-col-span-1">
        <venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst>
      </div>
    </div>

    <!-- Single Column Section: Content Filter -->
    <div class="t-grid t-grid-cols-1">
      <div class="t-col-span-1">
        <venio-eci-inappropriate-content></venio-eci-inappropriate-content>
      </div>
    </div>
  </div>
</div>