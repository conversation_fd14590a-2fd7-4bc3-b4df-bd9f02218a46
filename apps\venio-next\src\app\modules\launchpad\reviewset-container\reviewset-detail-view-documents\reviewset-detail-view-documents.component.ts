import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  untracked,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  BatchFileDetails,
  ResponseModel,
  ReviewSetEntry,
} from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  filter,
  Observable,
  of,
  Subject,
  switchMap,
  take,
  takeUntil,
  throwError,
} from 'rxjs'
import { catchError, map, tap } from 'rxjs/operators'
import {
  LoaderComponent,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import {
  CheckDirective,
  ExpandDirective,
  FlatDataBindingDirective,
  NodeTemplateDirective,
  TreeViewComponent,
} from '@progress/kendo-angular-treeview'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { CheckBoxDirective } from '@progress/kendo-angular-inputs'
import { LabelComponent } from '@progress/kendo-angular-label'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { DebounceTimer } from '@venio/util/utilities'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { DialogService } from '@progress/kendo-angular-dialog'
import { HttpClient, HttpErrorResponse } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { NotificationService, Type } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-reviewset-detail-view-documents',
  standalone: true,
  imports: [
    CommonModule,
    SkeletonComponent,
    TreeViewComponent,
    FlatDataBindingDirective,
    ExpandDirective,
    CheckDirective,
    DynamicHeightDirective,
    NodeTemplateDirective,
    ButtonComponent,
    CheckBoxDirective,
    LabelComponent,
    LoaderComponent,
    SvgLoaderDirective,
    TooltipDirective,
  ],
  templateUrl: './reviewset-detail-view-documents.component.html',
  styleUrl: './reviewset-detail-view-documents.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewDocumentsComponent
  implements OnInit, OnDestroy
{
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly batchDeletionStatus = output<boolean>()

  private readonly toDestroy$ = new Subject<void>()

  private readonly projectFacade = inject(ProjectFacade)

  private readonly dialogService = inject(DialogService)

  private readonly notificationService = inject(NotificationService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly httpClient = inject(HttpClient)

  private readonly injector = inject(Injector)

  public selectedRowKeys: number[] = []

  public readonly isBatchesDeleting = signal(false)

  public expandedRowKeys = signal<number[]>([])

  public selectedBatchIds = signal<number[]>([])

  public readonly isReviewSetDocumentViewLoading = toSignal(
    this.projectFacade.selectIsReviewSetDocumentViewLoading$.pipe(
      filter((loading) => typeof loading === 'boolean')
    )
  )

  public readonly reviewSetDocumentView = toSignal(
    this.projectFacade.selectReviewSetDocumentViewSuccess$.pipe(
      map((response) => this.#convertBatchTreeIds(response?.data || []))
    ),
    {
      initialValue: [],
    }
  )

  public readonly isAllSelected = computed(() => {
    return (
      this.selectedBatchIds().length > 0 &&
      this.selectedBatchIds().length === this.reviewSetDocumentView().length
    )
  })

  public readonly isIntermediateSelected = computed(() => {
    return (
      this.selectedBatchIds().length > 0 &&
      this.selectedBatchIds().length < this.reviewSetDocumentView().length
    )
  })

  public ngOnInit(): void {
    this.#resetPreviousSelectionWhenLoading()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public toggleSelectAll(isChecked: boolean): void {
    if (isChecked) {
      this.selectedRowKeys = [
        ...new Set(this.reviewSetDocumentView().map((node) => node.id)),
      ]
    } else {
      this.selectedRowKeys = []
    }

    this.selectedBatchIds.set(this.selectedRowKeys)
  }

  public handleDisable = (): boolean => this.isBatchesDeleting()

  public confirmBatchDeletion(): void {
    this.#launchAndSetupConfirmationDialog()
  }

  @DebounceTimer(1)
  public selectionChange(): void {
    this.selectedBatchIds.set(this.selectedRowKeys)
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Delete'
    instance.message = 'Are you sure you want to delete the selected batch(es)?'
  }

  #launchAndSetupConfirmationDialog(): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(confirmationDialogRef.content.instance)

    confirmationDialogRef.result
      .pipe(
        filter((action) => typeof action === 'boolean' && action),
        tap(() => this.batchDeletionStatus.emit(null)), // need to reset if deleting for second time
        switchMap(() => this.#performDeletion()),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.isBatchesDeleting.set(false)
        const { status, message } = response
        const style = status.toLowerCase() === 'success' ? 'success' : 'error'
        const isSuccess = style === 'success'
        if (isSuccess) {
          this.projectFacade.fetchReviewSetSummaryDetail() // Refresh the summary
        }

        this.#showMessage(message, { style })
        this.batchDeletionStatus.emit(isSuccess)
      })
  }

  #resetPreviousSelectionWhenLoading(): void {
    effect(
      () => {
        if (this.isReviewSetDocumentViewLoading()) {
          untracked(() => {
            this.selectedRowKeys = []
            this.selectedBatchIds.set([])
          })
        }
      },
      { injector: this.injector }
    )
  }

  #performDeletion(): Observable<ResponseModel> {
    this.isBatchesDeleting.set(true)
    const { projectId, reviewSetId } = this.selectedReviewSetEntry()
    const batchFileAssocicationIds = this.reviewSetDocumentView()
      .filter((c) => this.selectedBatchIds().includes(c.id))
      .map((c) => c.batchFileAssocicationId)

    if (batchFileAssocicationIds.length === 0) {
      this.isBatchesDeleting.set(false)
      return throwError(
        () =>
          ({
            status: 'error',
            message: 'No batch selected',
          } as ResponseModel)
      )
    }

    return this.httpClient
      .post<ResponseModel>(
        `${environment.apiUrl}project/${projectId}/reviewSets/${reviewSetId}/delete-batch-file`,
        {
          batchFileAssocicationIds,
        }
      )
      .pipe(
        catchError((error: unknown) => {
          this.isBatchesDeleting.set(false)
          return of((error as HttpErrorResponse).error as ResponseModel)
        })
      )
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #convertBatchTreeIds(nodes: BatchFileDetails[]): BatchFileDetails[] {
    if (nodes.length === 0) {
      return []
    }
    // 1. Build adjacency list: parentId -> list of child treeIds
    const adjacency = new Map<string, string[]>()
    for (const n of nodes) {
      const { treeId, treeParentId } = n
      if (!adjacency.has(treeParentId)) {
        adjacency.set(treeParentId, [])
      }
      adjacency.get(treeParentId).push(treeId)
    }

    // 2. Collect all rootIDs (i.e. those whose parent is '-1')
    //    This handles the typical scenario where -1 means "no parent".
    //    If you have multiple root-level nodes, you push them all here.
    const rootIds = nodes
      .filter((n) => n.treeParentId === '-1')
      .map((r) => r.treeId)

    // 3. BFS to get a top-down ordering of nodes
    //    visited Set ensures we skip repeats or cycles (infinite loops)
    const visited = new Set<string>()
    const queue: string[] = [...rootIds]
    const bfsOrder: string[] = []

    while (queue.length > 0) {
      const currentId = queue.shift()
      if (!visited.has(currentId)) {
        visited.add(currentId)
        bfsOrder.push(currentId)

        const children = adjacency.get(currentId) || []
        for (const childId of children) {
          if (!visited.has(childId)) {
            queue.push(childId)
          }
        }
      }
    }

    // 4. Assign numeric IDs in BFS order
    //    We store -1 => -1 to handle "no parent".
    const idMap = new Map<string, number>([['-1', -1]])
    let numericCounter = 1

    for (const treeId of bfsOrder) {
      // If it's not already in the map (and it's not -1), assign the next int
      if (!idMap.has(treeId) && treeId !== '-1') {
        idMap.set(treeId, numericCounter++)
      }
    }

    // 5. Return a new array with numeric id/parentId
    //    If a node is unreachable (not in BFS), numericId or parentId can be undefined → handle as needed
    return nodes.map((node) => {
      const numericId = idMap.get(node.treeId)
      const numericParentId = idMap.get(node.treeParentId)

      // If you want strict checks, you could throw if either is undefined:
      // if (numericId === undefined || numericParentId === undefined) {
      //   throw new Error(`Broken relationship or cycle for node ${node.treeId}`)
      // }

      return {
        ...node,
        id: numericId ?? null, // or handle differently if needed
        parentId: numericParentId === -1 ? null : numericParentId,
        // Any additional custom transformations:
        batchStatus: node.batchStatus?.toUpperCase(),
      }
    })
  }
}
