import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DynamicFolderComponent } from './dynamic-folder.component'
import { AnimationBuilder } from '@angular/animations'
import { ActivatedRoute } from '@angular/router'
import { DynamicFolderFacade, StartupsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('DynamicFolderComponent', () => {
  let component: DynamicFolderComponent
  let fixture: ComponentFixture<DynamicFolderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DynamicFolderComponent],
      providers: [
        DynamicFolderFacade,
        StartupsFacade,
        AnimationBuilder,
        NotificationService,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DynamicFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
