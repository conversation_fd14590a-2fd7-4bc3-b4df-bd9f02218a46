import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { DocumentSelectHeaderRendererComponent } from '../document-select-header-renderer/document-select-header-renderer.component'
import { DocumentTableComponent } from './document-table.component'
import { PopupModule } from '@progress/kendo-angular-popup'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { UiPaginationModule } from '@venio/ui/pagination'
import { RowFilterComponent } from '../row-filter/row-filter.component'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  TooltipsModule,
  PopoverModule,
  TooltipModule,
} from '@progress/kendo-angular-tooltip'
import { FormsModule } from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'

@NgModule({
  imports: [
    CommonModule,
    TreeListModule,
    PopupModule,
    FormsModule,
    PopoverModule,
    InputsModule,
    UiPaginationModule,
    RowFilterComponent,
    IndicatorsModule,
    ButtonModule,
    SvgLoaderDirective,
    TooltipsModule,
    TooltipModule,
    LabelModule,
  ],
  declarations: [DocumentTableComponent, DocumentSelectHeaderRendererComponent],
  exports: [DocumentTableComponent],
})
export class DocumentTableModule {}
