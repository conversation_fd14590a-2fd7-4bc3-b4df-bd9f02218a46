import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentTallyContainerComponent } from './document-tally-container.component'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentTallyContainerComponent', () => {
  let component: DocumentTallyContainerComponent
  let fixture: ComponentFixture<DocumentTallyContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentTallyContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        DialogService,
        DialogContainerService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentTallyContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
