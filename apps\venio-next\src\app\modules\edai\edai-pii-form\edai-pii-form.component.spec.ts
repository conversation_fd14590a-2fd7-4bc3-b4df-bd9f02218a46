import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiPiiFormComponent } from './edai-pii-form.component'
import { provideMockStore } from '@ngrx/store/testing'
import { AiFacade } from '@venio/data-access/ai'
import { of } from 'rxjs'
import { createJobFormGroup } from '../mocks/form-group.mock'

describe('EdaiPiiFormComponent', () => {
  let component: EdaiPiiFormComponent
  let fixture: ComponentFixture<EdaiPiiFormComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiPiiFormComponent],
      providers: [
        provideMockStore({}),
        {
          provide: AiFacade,
          useValue: {
            selectIsEdaiPIIEntitiesLoading$: of(false),
            selectEdaiPIIEntities$: of([]),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiPiiFormComponent)
    component = fixture.componentInstance
    // Create the FormGroup for JobForm
    const mockFormGroup = createJobFormGroup()

    // Set the input for the component
    fixture.componentRef.setInput('edaiFormGroup', mockFormGroup)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
