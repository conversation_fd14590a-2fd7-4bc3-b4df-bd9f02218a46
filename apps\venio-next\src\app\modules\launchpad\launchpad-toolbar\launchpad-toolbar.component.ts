import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
  output,
  signal,
  viewChildren,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import {
  ButtonComponent,
  DropDownButtonComponent,
  KENDO_BUTTONS,
} from '@progress/kendo-angular-buttons'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CommonActionTypes, ReportTypes } from '@venio/shared/models/constants'
import { UuidGenerator } from '@venio/util/uuid'
import { DebounceTimer } from '@venio/util/utilities'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { StartupsFacade, UserRights } from '@venio/data-access/review'
import { groupBy } from '@progress/kendo-data-query'
import { CaseType } from '@venio/shared/models/interfaces'
import {
  DropDownListComponent,
  GroupTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { FormsModule } from '@angular/forms'
import { toSignal } from '@angular/core/rxjs-interop'

/**
 * Interface for menu item local state.
 */
interface MenuItem {
  text?: string
  requiredRight?: UserRights
  actionType: CommonActionTypes
  type?: ReportTypes
  uniqueId: string
  icon: string
}

/**
 * Interface for toolbar action local state.
 */
interface ToolbarAction {
  svgIconPath: string
  cssClass?: string
  menuItems?: MenuItem[]
  type: 'icon-button' | 'dropdown-button' | 'label-button'
  actionType: CommonActionTypes
  title?: string
  label?: string
  uniqueId: string
  backgroundColor?: string
  foregroundColor?: string
  requiredRight?: UserRights | UserRights[]
  anyOfRequiredRights?: boolean
}

interface LaunchpadFilterOption {
  uniqueId?: string
  name: string
  category: string
  caseType: CaseType
  actionType: CommonActionTypes
}

@Component({
  selector: 'venio-launchpad-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DropDownButtonComponent,
    SVGIconComponent,
    SvgLoaderDirective,
    TooltipDirective,
    KENDO_BUTTONS,
    DropDownListComponent,
    FormsModule,
    GroupTemplateDirective,
  ],
  templateUrl: './launchpad-toolbar.component.html',
  styleUrl: './launchpad-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LaunchpadToolbarComponent implements OnInit {
  public readonly chevronDownIcon = chevronDownIcon

  private readonly tooltips = viewChildren(TooltipDirective)

  private readonly permissionFacade = inject(StartupsFacade)

  private readonly launchpadFilterOptions: LaunchpadFilterOption[] = [
    {
      name: 'All Cases',
      category: null,
      caseType: CaseType.ALL_CASES,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'Standard Cases',
      category: null,
      caseType: CaseType.VOD_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'All',
      category: 'Direct Export',
      caseType: CaseType.DIRECT_EXPORT_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'PDF Service',
      category: 'Direct Export',
      caseType: CaseType.PDF_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'Print Service',
      category: 'Direct Export',
      caseType: CaseType.PRINT_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'Concordance',
      category: 'Direct Export',
      caseType: CaseType.VoDR_STANDARD_CONCORDANCE_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'Summation',
      category: 'Direct Export',
      caseType: CaseType.VoDR_STANDARD_SUMMANTION_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
    {
      name: 'Relativity',
      category: 'Direct Export',
      caseType: CaseType.VODR_IMPORT_TO_RELATIVITY_SERVICE,
      actionType: CommonActionTypes.SEARCH,
      uniqueId: UuidGenerator.uuid,
    },
  ]

  public launchpadGroupFilterOptions = groupBy(this.launchpadFilterOptions, [
    { field: 'category' },
  ])

  public launchpadFilterSelectedOption = signal<LaunchpadFilterOption>(
    this.launchpadFilterOptions[0]
  )

  /**
   * Output event for toolbar action click. Emits `CommonActionTypes`.
   * @see CommonActionTypes
   */
  public readonly toolbarActionClick =
    output<Record<CommonActionTypes, object>>()

  public readonly commonActionTypes = CommonActionTypes

  /**
   * Signal to indicate if the project is favourite.
   */
  public readonly isFavoriteProjectFilter = signal(false)

  private readonly isFullFeatureLicense = toSignal<boolean>(
    this.permissionFacade.selectFullFeatureAvailability$
  )

  /**
   * Toolbar actions configuration including icon, CSS class, menu items
   * and action type.
   * @see CommonActionTypes
   **/
  private readonly toolbarActions = signal<ToolbarAction[]>([
    {
      svgIconPath: 'assets/svg/icon-heart-solid-fill-outline.svg',
      cssClass: '',
      menuItems: [],
      type: 'icon-button',
      actionType: CommonActionTypes.FAVOURITE,
      title: 'Favorite',
      uniqueId: UuidGenerator.uuid,
    },
    {
      svgIconPath: 'assets/svg/icon-chart-uphigh.svg',
      cssClass: 'invert-button',
      menuItems: [
        {
          text: 'Matter Detail Report',
          type: ReportTypes.MATTER_DETAIL_REPORT,
          actionType: CommonActionTypes.REPORT,
          requiredRight:
            UserRights['ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-accesskey.svg',
        },
        {
          text: 'Activated & Deactivated Custodian Report',
          type: ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT,
          actionType: CommonActionTypes.REPORT,
          requiredRight:
            UserRights[
              'ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT'
            ],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-creation.svg',
        },
        {
          text: 'Login / Logout Report',
          type: ReportTypes.LOG_IN_OUT_REPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights['ALLOW_TO_VIEW_SYSTEM_LOGIN_LOGOUT_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-login.svg',
        },
        {
          text: 'Locked Users Report',
          type: ReportTypes.LOCKED_USERS_REPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights['ALLOW_TO_VIEW_LOCKED_USERS_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-user.svg',
        },
        // TODO: future implementation
        // {
        //   title: 'Unlocked Users Report',
        //   icon: 'fas fa-unlock-alt',
        //   type: ReportTypes.UNLOCKED_USERS_REPORTS
        // },
        {
          text: 'Creation & Deactivation Report',
          type: ReportTypes.CREATION_AND_DEACTIVATION_REPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight:
            UserRights['ALLOW_TO_VIEW_USER_CREATION_AND_DEACTIVATION_REPORTS'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-creation.svg',
        },
        {
          text: 'Data Export & Download Report',
          type: ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights['ALLOW_TO_VIEW_EXPORT_AND_DOWNLOAD_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-export.svg',
        },
        {
          text: 'Role Change Report',
          type: ReportTypes.ROLE_CHANGE_REPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights['ALLOW_TO_VIEW_ROLE_CHANGE_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-role-change.svg',
        },
        {
          text: 'Deleted Export Report',
          type: ReportTypes.DELETED_EXPORTS,
          actionType: CommonActionTypes.REPORT,
          requiredRight:
            UserRights['ALLOW_TO_GENERATE_PRODUCTION_DELETE_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/Icon-material-delete.svg',
        },
        {
          text: 'Project Access Report',
          type: ReportTypes.PROJECT_ACCESS_REPORT,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights['ALLOW_TO_VIEW_PROJECT_ACCESS_REPORT'],
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-accesskey.svg',
        },
        {
          text: 'Activity Report',
          type: ReportTypes.ACTIVITY_REPORT,
          actionType: CommonActionTypes.REPORT,
          requiredRight: UserRights.ALLOW_TO_VIEW_USER_ACTIVITY_REPORT,
          uniqueId: UuidGenerator.uuid,
          icon: 'assets/svg/icon-launchpad-dropdown-activity.svg',
        },
      ],
      type: 'dropdown-button',
      actionType: CommonActionTypes.REPORT,
      title: 'Reports',
      uniqueId: UuidGenerator.uuid,
    },
    {
      svgIconPath: 'assets/svg/icon-case-branch-tree.svg',
      cssClass: 'invert-button',
      menuItems: [],
      type: 'icon-button',
      actionType: CommonActionTypes.DIRECT_EXPORT,
      title: 'Direct Export',
      uniqueId: UuidGenerator.uuid,
      requiredRight: [
        UserRights.CREATE_NEW_PROJECT,
        UserRights.ADD_CUSTODIAN_MEDIA,
      ],
      anyOfRequiredRights: true,
    },
    {
      svgIconPath: '',
      cssClass: 'v-custom-secondary-button t-uppercase',
      menuItems: [],
      type: 'label-button',
      label: 'Create New',
      actionType: CommonActionTypes.CREATE_CASE,
      title: 'Create New Case',
      uniqueId: UuidGenerator.uuid,
      requiredRight: [UserRights.CREATE_NEW_PROJECT],
    },
  ])

  private readonly rights = toSignal(this.permissionFacade.getUserRights$)

  public readonly updatedToolbarActions = computed(() => {
    const allActions = this.getFilteredByLicense()
    const rightsFilteredActions = this.filterByUserRights(allActions)

    // remove dropdown menu items without any childrens (eg: Hide report dropdown menu if user has no report acceessible)
    return this.removeEmptyDropdowns(rightsFilteredActions)
  })

  // Filters out actions based on license (e.g., DIRECT_EXPORT)
  private getFilteredByLicense(): ToolbarAction[] {
    const isFullFeatureLicense = this.isFullFeatureLicense()
    const allActions = this.toolbarActions().filter((action) => {
      // Hide direct export button if full feature license is not installed
      if (action.actionType === CommonActionTypes.DIRECT_EXPORT) {
        return isFullFeatureLicense
      }
      return true
    })
    return allActions
  }

  // Filters actions and their menu items based on user rights
  private filterByUserRights(actions: ToolbarAction[]): ToolbarAction[] {
    const rights = this.rights()

    if (!rights) return []

    const invalidGlobalRights = rights.Invalid_Global_Right_List
    return actions
      .filter((action) => this.hasRequiredRights(action, invalidGlobalRights))
      .map((action) => {
        if (action.actionType === CommonActionTypes.REPORT) {
          return {
            ...action,
            menuItems: action.menuItems.filter(
              (item) =>
                item.requiredRight && !invalidGlobalRights[item.requiredRight]
            ),
          }
        }
        return action
      })
  }

  // Determines if the user has required rights for the action
  private hasRequiredRights(
    action: ToolbarAction,
    invalidGlobalRights: Record<string, boolean>
  ): boolean {
    const { requiredRight, anyOfRequiredRights } = action

    if (!requiredRight) return true

    if (Array.isArray(requiredRight)) {
      return anyOfRequiredRights
        ? requiredRight.some((right) => !invalidGlobalRights[right])
        : requiredRight.every((right) => !invalidGlobalRights[right])
    }

    return !invalidGlobalRights[requiredRight]
  }

  // Removes dropdown-button actions with no menuItems
  private removeEmptyDropdowns(actions: ToolbarAction[]): ToolbarAction[] {
    return actions.filter(
      (action) =>
        !(
          action.type === 'dropdown-button' &&
          (!action.menuItems || action.menuItems.length === 0)
        )
    )
  }

  public ngOnInit(): void {
    this.#fetchLicenseStatus()
    this.#fetchRights()
  }

  public getFavoriteTooltip(action: ToolbarAction): string {
    if (action.actionType !== this.commonActionTypes.FAVOURITE) {
      return action.title || ''
    }

    return this.isFavoriteProjectFilter()
      ? 'Click to show all projects'
      : 'Click to show favourite projects'
  }

  /**
   * Emits the toolbar action click event.
   * @param {CommonActionTypes} actionType - The action type.
   * @param {Object} menuRef - The menu reference.
   * @returns {void}
   */
  @DebounceTimer(200)
  public toolbarActionClicked(
    actionType: CommonActionTypes,
    menuRef: ToolbarAction | MenuItem | LaunchpadFilterOption
  ): void {
    // hide any active tooltips
    this.tooltips().forEach((tooltip) => {
      tooltip.hide()
    })

    // There might be a case that view is not updated after the action is clicked.
    // We use this uniqueId to force the view to update in ngFor.
    menuRef['uniqueId'] = UuidGenerator.uuid
    if (actionType === CommonActionTypes.FAVOURITE) {
      this.isFavoriteProjectFilter.update((value) => !value)
    }

    const actionEvent = { [actionType]: menuRef } as unknown as Record<
      CommonActionTypes,
      object
    >

    this.toolbarActionClick.emit(actionEvent)
  }

  #fetchRights(): void {
    this.permissionFacade.fetchUserRights(0)
  }

  #fetchLicenseStatus(): void {
    this.permissionFacade.fetchFullFeatureLicenseAvailability()
  }

  public getButtonClass(action: ToolbarAction): string {
    switch (action.actionType) {
      case CommonActionTypes.FAVOURITE:
        return 't-text-[#ED7425] hover:!t-border-[#ED7425] hover:!t-bg-[#ED7425] hover:!t-text-[#FFFFFF]'
      case CommonActionTypes.DIRECT_EXPORT:
        return 'v-custom-caselaunchpad-directexport t-text-[#323130] hover:!t-border-[#323130] hover:!t-bg-[#323130] hover:!t-text-[#FFFFFF] v-custom-btn-group'
      case CommonActionTypes.REPORT:
        return 'v-custom-dropdown-caselaunchpad-report v-custom-btn-group'
    }
    return ''
  }
}
