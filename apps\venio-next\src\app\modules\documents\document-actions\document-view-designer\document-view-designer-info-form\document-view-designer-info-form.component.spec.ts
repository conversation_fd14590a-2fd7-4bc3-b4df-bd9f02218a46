import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerInfoFormComponent } from './document-view-designer-info-form.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

jest.mock('@progress/kendo-angular-notification')
describe('DocumentViewDesignerInfoFormComponent', () => {
  let component: DocumentViewDesignerInfoFormComponent
  let fixture: ComponentFixture<DocumentViewDesignerInfoFormComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentViewDesignerInfoFormComponent, NoopAnimationsModule],
      providers: [provideMockStore({}), NotificationService],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentViewDesignerInfoFormComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
