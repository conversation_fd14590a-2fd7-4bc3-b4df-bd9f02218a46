import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DistributedSearchStatusComponent } from './distributed-search-status.component'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { provideAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DistributedSearchStatusComponent', () => {
  let component: DistributedSearchStatusComponent
  let fixture: ComponentFixture<DistributedSearchStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DistributedSearchStatusComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        SearchFacade,
        NotificationService,
        FieldFacade,
        provideAnimations(),
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DistributedSearchStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
