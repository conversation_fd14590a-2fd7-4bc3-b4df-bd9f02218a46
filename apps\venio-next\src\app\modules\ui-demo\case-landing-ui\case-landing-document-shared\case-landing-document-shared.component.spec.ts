import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseLandingDocumentSharedComponent } from './case-landing-document-shared.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseLandingDocumentSharedComponent', () => {
  let component: CaseLandingDocumentSharedComponent
  let fixture: ComponentFixture<CaseLandingDocumentSharedComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseLandingDocumentSharedComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseLandingDocumentSharedComponent)
    component = fixture.componentInstance

    component.batches = [
      {
        id: 1,
        reference: '12345ABC',
        documents: '10K',
        expires: new Date().toISOString(),
        instruction: 'Lorem Ipsum',
        createdBy: '<PERSON>',
        createdOn: new Date().toISOString(),
      },
    ]

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
