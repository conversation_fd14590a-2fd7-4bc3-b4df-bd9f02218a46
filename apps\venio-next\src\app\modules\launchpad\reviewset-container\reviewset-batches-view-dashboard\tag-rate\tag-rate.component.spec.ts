import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagRateComponent } from './tag-rate.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

jest.mock('@venio/util/utilities', () => {
  return {
    TagRateWorkerService: jest.fn().mockImplementation(() => {
      return {
        tagRateUserDataTransform: jest.fn().mockResolvedValue([]),
        terminate: jest.fn(),
      }
    }),
  }
})

describe('TagRateComponent', () => {
  let component: TagRateComponent
  let fixture: ComponentFixture<TagRateComponent>

  const mockReviewSetFacade = {
    isReviewSetTagRateLoading$: of(false),
    selectReviewSetTagRateSuccess$: of(undefined),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagRateComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(TagRateComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
