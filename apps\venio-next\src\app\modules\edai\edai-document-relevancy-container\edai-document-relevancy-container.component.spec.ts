import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { EdaiDocumentRelevancyContainerComponent } from './edai-document-relevancy-container.component'

describe('EdaiDocumentRelevancyContainerComponent', () => {
  let component: EdaiDocumentRelevancyContainerComponent
  let fixture: ComponentFixture<EdaiDocumentRelevancyContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiDocumentRelevancyContainerComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiDocumentRelevancyContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create instance', () => {
    expect(component).toBeTruthy()
  })
})
