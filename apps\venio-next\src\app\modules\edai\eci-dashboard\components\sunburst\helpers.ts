export function calculateSunburstData(documentTypes: any[]) {
  const parentLabels = documentTypes.map(doc => doc.category);
  const childrenLabels = documentTypes.map(doc => 
    doc.subcategories.map((sub: any) => sub.subcategory)
  );
  
  const chartOneTotal = documentTypes.reduce((sum, doc) => sum + doc.count, 0);
  const chartOneSubTotal = documentTypes.map(doc => doc.count);
  
  const chartOnePercents = chartOneSubTotal.map(count => 
    Math.round((count / chartOneTotal) * 100)
  );
  
  const chartOneChildPercents = documentTypes.map(doc => 
    doc.subcategories.map((sub: any) => 
      Math.round((sub.count / doc.count) * 100)
    )
  );
  
  const allCountsOne = [
    chartOneTotal,
    ...chartOneSubTotal,
    ...documentTypes.flatMap(doc => doc.subcategories.map((sub: any) => sub.count))
  ];
  
  const allValsOne = [...allCountsOne];
  
  const formattedCounts = allCountsOne.map(count => count.toLocaleString());
  
  return {
    parentLabels,
    childrenLabels,
    chartOneTotal,
    chartOneSubTotal,
    chartOnePercents,
    chartOneChildPercents,
    allCountsOne,
    allValsOne,
    formattedCounts
  };
}
