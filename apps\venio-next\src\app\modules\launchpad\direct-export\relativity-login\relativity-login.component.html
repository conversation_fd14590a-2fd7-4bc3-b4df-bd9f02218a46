<kendo-dialog-titlebar (close)="onCancelAction()" class="t-mb-2">
  <div class="t-flex t-gap-5">
    <div class="t-flex t-flex-col">
      <h1 class="t-text-[#030303] t-text-xl t-font-black t-mb-1 t-text-left">
        RELATIVITY LOGIN
      </h1>

      <p class="t-text-[#FFBB12] t-text-lg t-font-black t-text-left">
        Please enter login details
      </p>
    </div>
  </div>
</kendo-dialog-titlebar>

<form
  class="t-flex t-flex-col"
  [formGroup]="loginFormGroup"
  (keydown.enter)="loginClick()">
  @if(!relativityData?.isRelativityOne){
  <div class="t-flex t-flex-col t-items-start">
    <kendo-textbox
      #username
      formControlName="username"
      placeholder="Username"
      class="t-w-full t-rounded"
      style="box-shadow: 0 1px 2px 0 rgba(55, 65, 81, 0.08)"></kendo-textbox>
    <div
      class="t-accent-error !t-text-[#ED7425] t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.username.untouched &&
          formControls.username.dirty &&
          formControls.username.invalid) ||
        (!formControls.username.untouched &&
          formControls.username.dirty &&
          formControls.username.value?.trim() === '')
      ">
      {{
        formControls.username.hasError('required')
          ? 'Username is required'
          : 'Please enter a valid username'
      }}
    </div>

    <div class="t-my-4 t-w-full">
      <kendo-textbox
        formControlName="password"
        placeholder="Password"
        [type]="togglePasswordVisibility() ? 'text' : 'password'"
        class="t-w-full t-rounded v-input-shadow">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            [tabIndex]="-1"
            kendoButton
            type="button"
            class="t-pr-2"
            look="clear"
            themeColor="none"
            fillMode="clear"
            (click)="passwordVisibilityClicked($event)">
            <kendo-svgicon
              [icon]="
                togglePasswordVisibility() ? iconEye : iconSlashEye
              "></kendo-svgicon>
          </button>
        </ng-template>
      </kendo-textbox>
      <div
        class="t-accent-error !t-text-[#ED7425] t-m-1 t-text-[11.23px]"
        *ngIf="
          (!formControls.password.untouched &&
            formControls.password.dirty &&
            formControls.password.errors !== null) ||
          (!formControls.password.untouched &&
            formControls.password.dirty &&
            formControls.password.value?.trim() === '')
        ">
        {{
          formControls.password.hasError('required')
            ? 'Password is required'
            : 'Please enter a valid password'
        }}
      </div>
    </div>
  </div>
  }@else{
  <div class="t-mb-4 t-flex t-flex-col t-items-start">
    <kendo-textbox
      #apiClientId
      formControlName="apiClientId"
      placeholder="Client Id"
      class="t-w-full t-rounded"
      style="box-shadow: 0 1px 2px 0 rgba(55, 65, 81, 0.08)"></kendo-textbox>
    <div
      class="t-accent-error !t-text-[#ED7425] t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.apiClientId.untouched &&
          formControls.apiClientId.dirty &&
          formControls.apiClientId.invalid) ||
        (!formControls.apiClientId.untouched &&
          formControls.apiClientId.dirty &&
          formControls.apiClientId.value?.trim() === '')
      ">
      {{
        formControls.apiClientId.hasError('required')
          ? 'Client Id is required'
          : 'Client Id is not valid'
      }}
    </div>
  </div>

  <div class="t-mb-4 t-w-full">
    <kendo-textbox
      formControlName="apiClientSecret"
      name="apiClientSecret"
      placeholder="Client Secret"
      class="t-w-full t-rounded v-input-shadow"></kendo-textbox>
    <div
      class="t-accent-error !t-text-[#ED7425] t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.apiClientSecret.untouched &&
          formControls.apiClientSecret.dirty &&
          formControls.apiClientSecret.errors !== null) ||
        (!formControls.apiClientSecret.untouched &&
          formControls.apiClientSecret.dirty &&
          formControls.apiClientSecret.value?.trim() === '')
      ">
      {{
        formControls.apiClientSecret.hasError('required')
          ? 'Client Secret is required'
          : 'Client Secret is not valid'
      }}
    </div>
  </div>
  }

  <label class="t-flex t-w-[40%] t-mb-3">
    <input
      #remember
      type="checkbox"
      kendoCheckBox
      rounded="small"
      size="small"
      formControlName="rememberMe" />
    <span
      class="t-text-[#5E6366] t-font-normal t-text-[14px] t-tracking-[0.42px] t-leading-[19px] t-ml-2">
      Remember me</span
    >
  </label>

  <div class="t-flex t-flex-row t-mt-1 t-mb-4">
    <button
      type="button"
      kendoButton
      themeColor="secondary"
      fillMode="outline"
      class="v-custom-secondary-button"
      (click)="loginClick()"
      [disabled]="isLoginLoading()">
      @if(isLoginLoading()){
      <kendo-loader themeColor="success" type="pulsing" />} Login
    </button>
  </div>

  @if(loginServerMessage()){
  <div
    class="t-flex t-items-center t-grow t-justify-center t-text-center t-my-2.5 t-accent-error !t-text-[#ED7425] t-text-[12px]">
    {{ loginServerMessage() }}
  </div>
  }
</form>
