import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';

@Component({
  selector: 'venio-eci-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss'
})
export class EciInappropriateContentComponent {
  public graph = {
    data: [
      { 
        x: ['Offensive Language', 'Discriminatory Remarks', 'Public Reactions', 'Internal Investigations', 'Misconduct Allegations'],
        y: [442, 401, 467, 8, 129],
        type: 'bar',
        marker: {
          color: '#FF6B6B'
        }
      },
    ],
    layout: { 
      autosize: true, 
      title: 'Inappropriate Content Analysis', 
      automargin: true, 
      margin: { t: 40, r: 0, b: 60, l: 40 }, 
      showlegend: false,
      xaxis: {
        tickangle: -45
      }
    }
  };
  
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage', 
      'sendDataToCloud', 
      'editInChartStudio', 
      'zoom2d', 
      'select2d', 
      'pan2d', 
      'lasso2d', 
      'autoScale2d', 
      'resetScale2d'
    ]
  };
}
