{"name": "util-uuid", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/util/uuid/src", "prefix": "venio", "tags": ["uuid"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/util/uuid"], "options": {"tsConfig": "libs/util/uuid/tsconfig.lib.json", "project": "libs/util/uuid/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/util/uuid/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/util/uuid/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/util/uuid"], "options": {"jestConfig": "libs/util/uuid/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}