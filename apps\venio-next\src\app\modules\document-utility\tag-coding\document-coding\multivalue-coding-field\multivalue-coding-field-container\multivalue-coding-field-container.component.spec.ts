import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MultivalueCodingFieldContainerComponent } from './multivalue-coding-field-container.component'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'

describe('MultivalueCodingFieldContainerComponent', () => {
  let component: MultivalueCodingFieldContainerComponent
  let fixture: ComponentFixture<MultivalueCodingFieldContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MultivalueCodingFieldContainerComponent],
      providers: [
        DocumentCodingFacade,
        DialogService,
        DialogContainerService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(MultivalueCodingFieldContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
