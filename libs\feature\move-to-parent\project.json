{"name": "move-to-parent", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/move-to-parent/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/move-to-parent/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/feature/move-to-parent/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/move-to-parent/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/move-to-parent/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}