{"tasksRunnerOptions": {"default": {"options": {"accessToken": ""}}}, "cli": {"packageManager": "npm"}, "extends": "nx/presets/core.json", "pluginsConfig": {"@nrwl/js": {"analyzeSourceFiles": true, "targetDefaults": true}}, "generators": {"@nx/angular:application": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest", "e2eTestRunner": "cypress", "strict": false, "prefix": "venio"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest", "style": "scss", "strict": false, "buildable": true, "prefix": "venio"}, "@nx/angular:component": {"style": "scss", "changeDetection": "OnPush", "prefix": "venio"}}, "$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "format": {"cache": true}, "test": {"cache": true}, "app-shell": {"cache": true}, "stylelint": {"cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "@nx/angular:webpack-browser": {"inputs": [{"env": "NX_MF_DEV_SERVER_STATIC_REMOTES"}, "production", "^production"]}, "@nx/angular:ng-packagr-lite": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": ["{workspaceRoot}/shared-assets"], "production": ["default", "!{projectRoot}/src/test-setup.[jt]s"]}, "cacheDirectory": ".nx/cache", "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "defaultBase": "master"}