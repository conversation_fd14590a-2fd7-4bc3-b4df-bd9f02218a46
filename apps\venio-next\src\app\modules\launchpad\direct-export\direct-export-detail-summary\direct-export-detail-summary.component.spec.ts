import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DirectExportDetailSummaryComponent } from './direct-export-detail-summary.component'
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { Store } from '@ngrx/store'
import { of } from 'rxjs'

describe('DirectExportDetailSummaryComponent', () => {
  let component: DirectExportDetailSummaryComponent
  let fixture: ComponentFixture<DirectExportDetailSummaryComponent>
  let mockStore: any

  // Mock data
  const mockSettingsForm: FormGroup = new FormBuilder().group({
    caseName: ['Test Case Name'],
    generalSettings: new FormBuilder().group({
      timeZone: [''],
    }),
    imageConversionSettings: new FormBuilder().group({
      imageColorConversion: new FormBuilder().group({
        imageFileType: [1],
      }),
    }),
    controlNumberAndEndorsementSettings: new FormBuilder().group({
      sortOrder: ['RELATIVE_FILE_PATH'],
      ControlNumberSetting: new FormBuilder().group({
        controlNumberStartingNumber: [1000],
        controlNumberPrefix: ['PREFIX'],
        controlNumberDelimiter: [','],
      }),
    }),
    productionSettings: new FormBuilder().group({
      fieldTemplateId: [10], // Mocked fieldTemplateId
    }),
    printServiceSettings: new FormBuilder().group({
      paperType: ['Glossy'],
      paperSide: ['Double-sided'],
    }),
    pdfServiceSettings: new FormBuilder().group({
      pdfType: [0],
    }),
  })

  beforeEach(async () => {
    // Mock the Store with necessary methods
    mockStore = {
      pipe: jest.fn().mockReturnValue(of([])),
      dispatch: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [
        DirectExportDetailSummaryComponent, // Add the standalone component to the imports array
        ReactiveFormsModule,
      ],
      providers: [{ provide: Store, useValue: mockStore }],
    }).compileComponents()

    fixture = TestBed.createComponent(DirectExportDetailSummaryComponent)
    component = fixture.componentInstance

    // Assign mockSettingsForm to component
    component.settingsForm = mockSettingsForm

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should return case name from settingsForm', () => {
    expect(component.caseName).toBe('Test Case Name')
  })

  it('should return sort order', () => {
    expect(component.sortOrder).toBe('Original Discovery Order')
  })

  it('should return pdf type', () => {
    expect(component.pdfType).toBeDefined()
    // Assuming `PDFTypeDescription.get(0)` returns a valid description for `pdfType`
  })
})
