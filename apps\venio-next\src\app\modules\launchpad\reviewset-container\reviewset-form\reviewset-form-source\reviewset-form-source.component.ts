import {
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  input,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import {
  ReviewSetForm,
  ReviewSetSourceTypes,
  TagsModel,
} from '@venio/shared/models/interfaces'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  CheckBoxComponent,
  CheckBoxState,
  NumericTextBoxComponent,
  RadioButtonDirective,
  SuffixTemplateDirective,
  TextBoxComponent,
} from '@progress/kendo-angular-inputs'
import { DatePickerComponent } from '@progress/kendo-angular-dateinputs'
import {
  CheckAllDirective,
  DropDownListComponent,
  DropDownTreesExpandDirective,
  HeaderTemplateDirective,
  ItemTemplateDirective,
  MultiSelectComponent,
  MultiSelectTreeComponent,
  MultiSelectTreeFlatBindingDirective,
  ValueTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { LabelComponent } from '@progress/kendo-angular-label'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { ReviewsetFormService } from '../reviewset-form.service'
import { combineLatest, Observable, switchMap } from 'rxjs'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-reviewset-form-source',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    CheckBoxComponent,
    DatePickerComponent,
    DropDownListComponent,
    DropDownTreesExpandDirective,
    LabelComponent,
    MultiSelectTreeComponent,
    NumericTextBoxComponent,
    RadioButtonDirective,
    SuffixTemplateDirective,
    TextBoxComponent,
    MultiSelectComponent,
    ItemTemplateDirective,
    HeaderTemplateDirective,
    ReactiveFormsModule,
    MultiSelectTreeFlatBindingDirective,
    ValueTemplateDirective,
    CheckAllDirective,
    TooltipDirective,
  ],
  templateUrl: './reviewset-form-source.component.html',
  styleUrl: './reviewset-form-source.component.scss',
})
export class ReviewsetFormSourceComponent {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  public readonly reviewSetFormService = inject(ReviewsetFormService)

  private readonly changeDetectorRef = inject(ChangeDetectorRef)

  public readonly reviewSetSourceType = ReviewSetSourceTypes

  private defaultReviewSetPlaceholder = {
    reviewSetId: 0,
    name: 'Select a review set',
  }

  private defaultSaveSearchPlaceholder = {
    searchId: 0,
    searchName: 'Select saved search',
  }

  public readonly enableCheckAllViewableTags = signal(true)

  public readonly downIcon = chevronDownIcon

  public readonly sourceSelectorItems: unknown[] = [
    {
      label: 'Tags',
      value: ReviewSetSourceTypes.TAG,
    },
    {
      label: 'Folder',
      value: ReviewSetSourceTypes.FOLDER,
    },
    {
      label: 'Saved Search',
      value: ReviewSetSourceTypes.SAVED_SEARCH,
    },
  ]

  public readonly defaultReviewSetItem = computed(() =>
    this.reviewSetFormService.selectedAutoCollectReviewSetId() <= 0
      ? this.defaultReviewSetPlaceholder
      : undefined
  )

  public readonly defaultSavedSearchItem = computed(() =>
    this.reviewSetFormService.selectedSavedSearchId() <= 0
      ? this.defaultSaveSearchPlaceholder
      : undefined
  )

  public readonly isReviewSetRequired = computed(
    () =>
      this.reviewSetFormService.selectedAutoCollectReviewSetId() <= 0 &&
      this.defaultReviewSetPlaceholder &&
      this.reviewSetFormService.isAutoCollectCriteriaMatchSource()
  )

  public readonly isSourceTagLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isSourceTagLoading
  )

  public readonly isSourceFolderLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isSourceFolderLoading
  )

  public readonly isSourceSavedSearchLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isSourceSaveSearchLoading
  )

  public readonly reviewSetsLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isReviewSetsLoading
  )

  public readonly sourceTagsInvalid = toSignal(
    this.#createControlInvalidSignal('tagId')
  )

  public readonly sourceFoldersInvalid = toSignal(
    this.#createControlInvalidSignal('folderId')
  )

  public readonly sourceSavedSearchInvalid = toSignal(
    this.#createControlInvalidSignal('savedSearchId')
  )

  public readonly viewableTagsInvalid = toSignal(
    this.#createControlInvalidSignal('displayTag')
  )

  public readonly autoCollectReviewSetInvalid = toSignal(
    this.#createControlInvalidSignal('autoCollectReviewset')
  )

  public viewableTagFilterChange(term: string): void {
    term = term.trim().toLowerCase()
    const all = this.reviewSetFormService.allTags()
    const any = all.some((t) => t.tagName.toLowerCase().includes(term))
    const enable = (!!all?.[0] && !term) || (any && !!term)
    this.enableCheckAllViewableTags.set(enable)
  }

  public onDisplayTagChange(selectedTags: TagsModel[]): void {
    if (!selectedTags) {
      return
    }

    const allTags = this.reviewSetFormService.allTags()
    if (!allTags || allTags.length === 0) {
      return
    }

    const allTagsMap = new Map<number, TagsModel>(
      allTags.map((tag) => [tag.id, tag])
    )
    const requiredTagsMap = new Map<number, TagsModel>(
      selectedTags.map((tag) => [tag.id, tag])
    )

    for (const selectedTag of selectedTags) {
      let currentTag = selectedTag
      while (currentTag && currentTag.parentId > -1) {
        const parentTag = allTagsMap.get(currentTag.parentId)
        if (parentTag) {
          if (requiredTagsMap.has(parentTag.id)) {
            break
          }
          requiredTagsMap.set(parentTag.id, parentTag)
          currentTag = parentTag
        } else {
          break
        }
      }
    }

    if (requiredTagsMap.size !== selectedTags.length) {
      const newSelectedTags = Array.from(requiredTagsMap.values())
      this.reviewSetForm().controls.displayTag.setValue(newSelectedTags, {
        emitEvent: false,
      })
    }
  }

  public tagMapper(tags: TagsModel[]): any[] {
    const validTags = tags.filter((t) => t.id > -1)
    return validTags.length < 2 ? validTags : [validTags]
  }

  public commonMapper = (items: any[]): any[] => {
    return items.length < 2 ? items : [items]
  }

  public toggleCheckAllSourceTags(state: CheckBoxState): void {
    const selectedSource = this.reviewSetFormService.selectedSource()
    if (selectedSource === ReviewSetSourceTypes.TAG) {
      const all = this.reviewSetFormService.filteredSourceTags()
      if (all.length === 0) {
        return
      }
      const ids =
        typeof state === 'boolean' && state ? all.map((t) => t.tagId) : []
      this.reviewSetForm().controls.tagId.setValue(ids)
    }

    if (selectedSource === ReviewSetSourceTypes.FOLDER) {
      const all = this.reviewSetFormService.filteredSourceFolders()
      if (all.length === 0) {
        return
      }
      const ids =
        typeof state === 'boolean' && state ? all.map((t) => t.folderId) : []
      this.reviewSetForm().controls.folderId.setValue(ids)
    }

    if (selectedSource === ReviewSetSourceTypes.SAVED_SEARCH) {
      const all = this.reviewSetFormService.filteredSourceSavedSearch()
      if (all.length === 0) {
        return
      }
      const ids =
        typeof state === 'boolean' && state
          ? all.find((t) => t.searchId)?.searchId
          : 0
      this.reviewSetForm().controls.savedSearchId.setValue(ids)
    }
  }

  #createControlInvalidSignal(
    controlName: keyof ReviewSetForm
  ): Observable<boolean> {
    return toObservable(signal(false)).pipe(
      switchMap(() =>
        combineLatest([
          this.reviewSetForm().controls[controlName].valueChanges,
          this.reviewSetForm().controls[controlName].statusChanges,
        ])
      ),
      map(() => {
        const control = this.reviewSetForm().controls[controlName]
        const isNumber = typeof control.value === 'number'
        const isArray =
          typeof control.value === 'object' &&
          control.value !== null &&
          Array.isArray(control.value)

        const invalidValue = isNumber
          ? control.value === 0
          : isArray
          ? control.value.length === 0
          : control.value === null

        return invalidValue && (control.touched || control.dirty)
      })
    )
  }
}
