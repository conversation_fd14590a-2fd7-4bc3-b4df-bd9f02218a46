import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormGroup,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'
import {
  DateInputsModule,
  SelectionRange,
} from '@progress/kendo-angular-dateinputs'
import {
  SVGIcon,
  imageIcon,
  chevronDownIcon,
  calendarIcon,
  checkIcon,
  xIcon,
  filePdfIcon,
  printIcon,
  fileExcelIcon,
  fileCsvIcon,
  fileIcon,
  copyIcon,
} from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { UiPaginationModule } from '@venio/ui/pagination'

interface GridDataItem {
  fullName: string
  userName: string
  globalUserRole: string
  lockedDate: Date
}

@Component({
  selector: 'venio-locked-user-report',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    DropDownsModule,
    FormsModule,
    DateInputsModule,
    LabelModule,
    ReactiveFormsModule,
    IconsModule,
    GridModule,
    InputsModule,
    UiPaginationModule,
  ],
  templateUrl: './locked-user-report.component.html',
  styleUrl: './locked-user-report.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LockedUserReportComponent implements OnInit {
  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public genders: Array<{ text: string; value: number }> = [
    { text: 'John Doe', value: 1 },
    { text: 'Ronnie', value: 2 },
    { text: 'Anthony', value: 3 },
  ]

  public myForm: FormGroup = new FormGroup({
    gender: new FormControl([2]),
  })

  public range = { start: null, end: null }

  public imageSVG: SVGIcon = imageIcon

  public downIcon: SVGIcon = chevronDownIcon

  public calendarIcon: SVGIcon = calendarIcon

  public checkIcon: SVGIcon = checkIcon

  public xIcon: SVGIcon = xIcon

  public pdfIcon: SVGIcon = filePdfIcon

  public printIcon: SVGIcon = printIcon

  public pageSize = 10

  public buttonCount = 2

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public gridData: GridDataItem[] = []

  public dialogTitle = 'Locked User Report'

  public opened: boolean

  public saveOptions: any[] = [
    {
      text: 'Save as PDF',
      svgIcon: filePdfIcon,
    },
    {
      text: 'Save as Excel',
      svgIcon: fileExcelIcon,
    },
    {
      text: 'Save as CSV',
      svgIcon: fileCsvIcon,
    },
  ]

  public printOptions: any[] = [
    {
      text: 'print Current Page',
      svgIcon: fileIcon,
    },
    {
      text: 'print All Pages',
      svgIcon: copyIcon,
    },
  ]

  constructor() {
    this.generateData()
  }

  public ngOnInit(): void {
    this.openDialog()
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public onChange(e: SelectionRange): void {
    this.range = e
  }

  public generateData(): void {
    const fullNames = ['John Doe', 'Jane Smith', 'Alice Johnson', 'Bob Lee']
    const userRoles = ['Administrator', 'User', 'Guest', 'Supervisor']

    for (let i = 0; i < 20; i++) {
      const nameIndex = Math.floor(Math.random() * fullNames.length)
      const roleIndex = Math.floor(Math.random() * userRoles.length)
      const hours = Math.floor(Math.random() * 1000) * 3600000 // Random hours to milliseconds

      this.gridData.push({
        fullName: fullNames[nameIndex],
        userName: `user${i}`,
        globalUserRole: userRoles[roleIndex],
        lockedDate: new Date(new Date().getTime() + hours), // Current time plus random hours
      })
    }
  }
}
