import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ShortcutKeyDictionaryComponent } from './shortcut-key-dictionary.component'
import { PLATFORM_ID } from '@angular/core'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
describe('InfoShortCodesComponent', () => {
  let component: ShortcutKeyDictionaryComponent
  let fixture: ComponentFixture<ShortcutKeyDictionaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ShortcutKeyDictionaryComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ShortcutKeyDictionaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
