import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiSearchDialogContainerComponent } from './ai-search-dialog-container.component'
import { provideMockStore } from '@ngrx/store/testing'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { ActivatedRoute } from '@angular/router'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'

describe('AiSearchDialogContainerComponent', () => {
  let component: AiSearchDialogContainerComponent
  let fixture: ComponentFixture<AiSearchDialogContainerComponent>

  class MockDialogRef {
    public close = jest.fn()

    public dialog = {
      location: jest.fn(),
    }
    // Add other methods and properties as needed
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiSearchDialogContainerComponent, DialogsModule],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        { provide: DialogRef, useClass: MockDialogRef },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                id: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AiSearchDialogContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
