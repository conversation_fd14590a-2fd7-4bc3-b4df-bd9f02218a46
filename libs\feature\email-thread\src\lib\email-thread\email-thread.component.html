<kendo-treelist
  kendoTreeListSelectable
  kendoTreeListExpandable
  [kendoTreeListFlatBinding]="tableData()"
  [idField]="idField"
  [parentIdField]="parentIdField"
  [selectable]="selectableSettings"
  [resizable]="true"
  [isSelected]="isSelected"
  [(expandedKeys)]="expandedKeys"
  scrollable="virtual"
  [rowHeight]="36"
  [pageSize]="pageSize"
  [autoSize]="true"
  [navigable]="true"
  [loading]="isEmailThreadLoading$ | async"
  [trackBy]="documentTrackByFn"
  (expand)="fitColumns()"
  (collapse)="fitColumns()"
  (dataStateChange)="fitColumns()"
  [ngClass]="{ 'v-no-data': tableData().length === 0 }"
  class="v-custom-family-treelist t-w-full t-overflow-y-visible t-relative">
  <kendo-treelist-column
    title="Details"
    [width]="100"
    [filterable]="false"
    [autoSize]="true"
    *ngIf="tableData().length > 0">
    <ng-template kendoTreeListCellTemplate let-dataItem>
      <div class="">
        <div
          venioSvgLoader
          [hoverColor]="'#FFBB12'"
          color="#979797"
          [svgUrl]="
            enableDetailView(dataItem)
              ? 'assets/svg/eye.svg'
              : 'assets/svg/eye-slash.svg'
          "
          height="1.2rem"
          width="1.2rem"
          kendoTooltip
          [title]="enableDetailView(dataItem) ? 'View' : ''"
          class="t-mx-4"
          [ngClass]="{
            't-font-bold t-text-[#000000] t-cursor-pointer':
              enableDetailView(dataItem)
          }"
          (click)="onDetailsClicked(dataItem)"></div>
      </div>
    </ng-template>
  </kendo-treelist-column>

  <kendo-treelist-column
    *ngIf="tableData().length > 0"
    [resizable]="false"
    [width]="30"
    [filterable]="false"
    [expandable]="true" />
  <kendo-treelist-column
    headerClass="!t-py-[0.40rem] !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
    [resizable]="true"
    [width]="400"
    [filterable]="false"
    *ngIf="hasEmailSubjectColumnExists"
    class="t-flex t-items-baseline !t-border-0">
    <ng-template
      kendoTreeListHeaderTemplate
      let-column
      let-columnIndex="columnIndex">
      <div class="t-flex t-w-1 t-gap-2">
        <div class="t-flex t-items-center" *ngIf="tableData().length > 0">
          Subject
        </div>
      </div>
    </ng-template>
    <ng-template kendoTreeListCellTemplate let-dataItem>
      <div
        class="t-flex t-gap-2 t-inline-block t-w-full"
        [ngClass]="{ 't-ml-6': dataItem['__treelistParentId'] !== null }">
        <span
          venioSvgLoader
          applyEffectsTo="fill"
          [color]="getIconColor(dataItem)"
          [svgUrl]="getEmailIcon(dataItem)"
          [title]="getIconTitle(dataItem)"
          height="1rem"
          width="1rem"></span>

        <span
          *ngIf="dataItem.__treelistParentId === null"
          class="k-text-ellipsis t-w-full"
          [ngStyle]="{ color: getSubjectTextColor(dataItem) }"
          >{{ dataItem.__emailSubject }}</span
        >

        <span
          *ngIf="dataItem['__treelistParentId'] !== null"
          class="k-text-ellipsis t-w-full"
          [ngStyle]="{ color: getSubjectTextColor(dataItem) }"
          kendoTooltip
          [title]="dataItem.__emailSubject"
          >{{ dataItem.__emailSubject }}</span
        >
      </div>
    </ng-template>
  </kendo-treelist-column>
  @for (field of headers(); track field) {
  <kendo-treelist-column [width]="300" [field]="field">
    <ng-template kendoTreeListHeaderTemplate let-column>
      <span kendoTooltip [title]="field">{{ field }}</span>
    </ng-template>
    <ng-template kendoTreeListCellTemplate let-dataItem>
      <span kendoTooltip [title]="dataItem[field]">{{ dataItem[field] }}</span>
    </ng-template>
  </kendo-treelist-column>
  }
  <ng-template kendoTreeListNoRecordsTemplate>
    <div
      *ngIf="(isEmailThreadLoading$ | async) === false && !tableData().length"
      class="t-flex t-place-content-center">
      {{ noRecordsMessage }}
    </div>
  </ng-template>
</kendo-treelist>
