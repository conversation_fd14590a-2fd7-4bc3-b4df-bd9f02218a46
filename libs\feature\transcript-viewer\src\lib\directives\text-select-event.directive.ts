import {
  Directive,
  ElementRef,
  EventEmitter,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Output,
} from '@angular/core'
import { AnnotationProperties } from '@venio/data-access/review'
export interface TextSelectEvent {
  text: string
  selectElement: any
  viewportRectangle: SelectionRectangle | null
  hostRectangle: SelectionRectangle | null
  startOffset: any
  endOffset: any
  id: string
}

interface SelectionRectangle {
  left: number
  top: number
  width: number
  height: number
}
@Directive({
  selector: '[venioTextSelectEvent]',
  standalone: true,
})
export class TextSelectEventDirective implements OnInit, OnDestroy {
  /**
   * emits ontext selection
   */
  //public textSelectEvent: EventEmitter<any>

  /**
   * selected element array
   */
  public textEmitData = []

  /**
   * element ref on selection
   */
  private elementRef: ElementRef

  /**
   * selected boolean
   */
  private hasSelection: boolean

  /**
   * angular zone instance
   */
  private zone: NgZone

  @Output()
  public readonly warning: EventEmitter<any> = new EventEmitter()

  @Output()
  public readonly textSelectEvent: EventEmitter<any> = new EventEmitter()

  constructor(elementRef: ElementRef, zone: NgZone) {
    this.elementRef = elementRef
    this.zone = zone
    this.hasSelection = false
    this.textSelectEvent = new EventEmitter()
  }

  public ngOnInit(): void {
    this.zone.runOutsideAngular(() => {
      this.elementRef.nativeElement.addEventListener(
        'mouseup',
        this.handleMouseup,
        false
      )
    })
  }

  /**
   *emits on mouse down click
   * @constructor
   * * @param event - mouse down event
   */
  private handleMousedown = (event): void => {
    document.addEventListener('mouseup', this.handleMouseup, false)
  }

  /**
   *emits on mouse release
   * @constructor
   * * @param event - mouse up event
   */
  private handleMouseup = (event): void => {
    if (document.getSelection().toString()) {
      // The delay allows the browser to complete the selection process before we try to process it.
      setTimeout(() => {
        this.processSelection(event.button === 0), 100
      })
    }
  }

  /**
   *emits on mouse selection
   * @constructor
   */
  private handleSelectionchange = (): void => {
    if (this.hasSelection) {
      setTimeout(() => {
        this.processSelection(), 1
      })
    }
  }

  private getTranscriptLineElement(node: Node): HTMLElement {
    if (this.isNodeValid(node.parentElement.id)) {
      return node.parentElement
    }
    return this.getTranscriptLineElement(node.parentNode)
  }

  #checkAndHandleTripleClick(range): boolean {
    let isTripleClickEvent = false
    // If the user triple-clicks on a text, the entire text will be selected, and the range.endOffset will be 0.
    // This case needs to be handled separately.
    // Handle triple click event for Chrome & Edge browser
    if (range.endOffset === 0) {
      // Need to pass the id (page:line) to the selectElementById function.
      // This funtion will return the selected text details.
      const startIdElement: HTMLElement = this.getTranscriptLineElement(
        range.startContainer
      )
      const data = this.selectElementById(startIdElement.id)
      this.textSelectEvent.emit([data])
      isTripleClickEvent = true
    }
    // Handle triple click event for Firefox browser
    const node = range.startContainer
    if (!node.parentElement.id && range.startOffset === 0) {
      const data = this.selectElementById(node.id)
      this.textSelectEvent.emit([data])
      isTripleClickEvent = true
    }
    return isTripleClickEvent
  }

  private isNodeValid(nodeId): boolean {
    const regex = /^\d+:\d+$/gi
    return regex.test(nodeId)
  }

  /**
   *process the selected element
   * @constructor
   */
  private async processSelection(leftBool?: boolean): Promise<void> {
    const selection = document.getSelection()
    try {
      this.textEmitData = []
      if (!selection.rangeCount || !selection.toString()) {
        return
      }
      if (!leftBool) return
      if (this.hasSelection) {
        this.zone.runGuarded(() => {
          this.hasSelection = false
        })
      }
      const range: any = selection.getRangeAt(0)
      // Check if the user triple-clicks on a text. If yes, emit the selected text else proceed further.
      const isTripleClickEvent = this.#checkAndHandleTripleClick(range)
      if (isTripleClickEvent) return
      const startIdElement: HTMLElement = this.getTranscriptLineElement(
        range.startContainer
      )
      const endIdElement: HTMLElement = this.getTranscriptLineElement(
        range.endContainer
      )
      if (startIdElement.id === '' && endIdElement.id === '') return
      if (startIdElement.id === endIdElement.id) {
        const data = this.selectElement(
          startIdElement.id,
          selection.toString(),
          range
        )
        this.textSelectEvent.emit([data])
      } else {
        this.iterateThroughLines(startIdElement.id, endIdElement.id)
        if (this.textEmitData.length > 0)
          this.textSelectEvent.next(this.textEmitData)
      }
    } catch (e) {
      this.warning.emit(e)
    }
  }

  private iterateThroughLines(start: string, end: string): void {
    // Function to parse the input strings

    const startPos = this.parsePosition(start)
    const endPos = this.parsePosition(end)

    const LINES_PER_PAGE = 25

    // Loop through each page
    for (let page = startPos.page; page <= endPos.page; page++) {
      // Determine start line for the current page
      const startLine = page === startPos.page ? startPos.line : 1

      // Determine end line for the current page
      const endLine = page === endPos.page ? endPos.line : LINES_PER_PAGE

      // Loop through each line of the current page
      for (let line = startLine; line <= endLine; line++) {
        const emitdata = this.selectElementById(`${page}:${line}`)
        this.textEmitData.push(emitdata)
      }
    }
  }

  private parsePosition(position: string): { page: number; line: number } {
    const [pageStr, lineStr] = position.split(':')
    return { page: parseInt(pageStr), line: parseInt(lineStr) }
  }

  private selectElement(
    startId: string,
    selectedText: string,
    range: any
  ): AnnotationProperties {
    const startIdElement = document.getElementById(startId)
    if (!startIdElement) return
    const text = startIdElement.textContent
    const regex = RegExp(this.escapeRegExp(selectedText), 'gi')
    const matches = [...text.matchAll(regex)]
    let startOffset = 0
    let endOffset = 0

    if (matches.length === 1) {
      startOffset = matches[0].index
      endOffset = matches[0].index + selectedText.length
    } else {
      startOffset = range.startOffset
      endOffset = range.endOffset
    }
    return {
      text: selectedText,
      startOffset,
      endOffset,
      id: startIdElement.id,
    }
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  private selectElementById(startId: string): AnnotationProperties {
    const startIdElement = document.getElementById(startId)
    if (!startIdElement) return
    const text = startIdElement.textContent
    const startOffset = 0
    const endOffset = startIdElement.textContent.length

    return {
      text: text,
      startOffset,
      endOffset,
      id: startIdElement.id,
    }
  }

  public ngOnDestroy(): void {
    this.elementRef.nativeElement.removeEventListener(
      'mousedown',
      this.handleMousedown,
      false
    )
    document.removeEventListener('mouseup', this.handleMouseup, false)
    document.removeEventListener(
      'selectionchange',
      this.handleSelectionchange,
      false
    )
  }
}
