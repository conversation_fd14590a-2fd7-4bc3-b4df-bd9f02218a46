import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentAdvancedSearchContainerComponent } from './document-advanced-search-container.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('DocumentAdvancedSearchContainerComponent', () => {
  let component: DocumentAdvancedSearchContainerComponent
  let fixture: ComponentFixture<DocumentAdvancedSearchContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentAdvancedSearchContainerComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [DialogRef],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentAdvancedSearchContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
