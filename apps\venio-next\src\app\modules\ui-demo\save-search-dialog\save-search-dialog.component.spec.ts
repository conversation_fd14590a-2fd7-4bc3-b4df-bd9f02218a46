import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SaveSearchDialogComponent } from './save-search-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SaveSearchDialogComponent', () => {
  let component: SaveSearchDialogComponent
  let fixture: ComponentFixture<SaveSearchDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SaveSearchDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SaveSearchDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
