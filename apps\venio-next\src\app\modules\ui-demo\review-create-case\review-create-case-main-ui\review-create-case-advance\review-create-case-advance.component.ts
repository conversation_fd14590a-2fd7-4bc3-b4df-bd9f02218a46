import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-review-create-case-advance',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InputsModule,
    LabelModule,
    DropDownsModule,
  ],
  templateUrl: './review-create-case-advance.component.html',
  styleUrl: './review-create-case-advance.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseAdvanceComponent {
  public tagOptions = [
    {
      id: 'propagateFamily',
      label: 'Propagate tag to family (parent/child)',
      checked: false,
    },
    {
      id: 'propagateDuplicates',
      label: 'Propagate tag to all duplicates',
      checked: false,
    },
    {
      id: 'propagateEmailThread',
      label: 'Propagate tag to email thread',
      checked: false,
    },
  ]

  public incOptions = [
    {
      id: 'incFamily',
      label: 'Include all documents in family (parent/child)',
      checked: false,
    },
    {
      id: 'incDoc',
      label:
        'Include all documents that are part of email thread in selected document source',
      checked: false,
    },
  ]

  public excOptions = [
    {
      id: 'excludeDoc',
      label:
        'Exclude documents that are already part of other review set in this case',
      checked: false,
    },
    {
      id: 'nonInc',
      label: 'Exclude non-inclusive emails',
      checked: false,
    },
  ]

  public propOptions = [
    {
      id: 'propRev',
      label: 'Propagate review set status to family (parent/child)',
      checked: false,
    },
    {
      id: 'propDuplicates',
      label: 'Propagate review set to all duplicates',
      checked: false,
    },
    {
      id: 'emailThread',
      label: 'Propagate review set to email thread',
      checked: false,
    },
  ]

  public useCal = false

  public controlSet: Array<string> = [
    'Normal distribution',
    'Percentage of population',
    'Number of documents',
  ]

  public selectedControlSet = 'Normal distribution'

  public controlSetFormatData: Array<string> = ['Fixed', 'Dynamic']

  public selectedControlSetFormat = 'Fixed'
}
