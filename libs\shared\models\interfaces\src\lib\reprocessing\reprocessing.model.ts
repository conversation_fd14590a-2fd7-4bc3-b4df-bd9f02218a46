export interface ReprocessRequestModel {
  tempTable: string // If temp table is provided then, we can just get the data from temp table based on pagesize and pagenumber
  RecomputeData: boolean // Query again for document list even if the temp table is provided
  selectedMedias: number[]
  exceptionTypes: string
  reprocessOption: ReprocessOption
  tagids: number[]
  pageSize: number
  pageNumber: number
  settingId: number
}

export interface ReprocessFileInfoDetail {
  fileInfos: ReprocessFileInfo[]
  documentCount: number
  pageNumber: number
  pageSize: number
  tempTable: string
}

export interface ReprocessData {
  replacementFilePath: string
  fsid: number
  userIdPath: string
  password: string
  passwordBankId: number
  replacementType: string
  timeoutValueInMin: number
  fileIndex: number
  fileType: string
  extractor: Record<string, { item1: number; item2: number }>
  metaExtractor: number
  textExtractor: number
  childExtractor: number
  updateMeta: boolean
  updateEdocMeta: boolean
  edocMeta: Record<string, boolean>
  updateEmailMeta: boolean
  emailMeta: Record<string, boolean>
  updateFulltext: boolean
  updateChild: boolean
  rextractAllChild: boolean
  extractOnlyMissingChild: boolean
  updateHashValue: boolean
  usePasswordBank: boolean
  useTimeOut: boolean
  repairPST: boolean
}

export interface ReprocessFileInfo extends ReprocessData {
  fileId: number
  mediaName: string
  fileName: string
  originalFilePath: string
  settingsType: SettingsType
}

export interface FileReprocessModel {
  fileInfos: ReprocessFileInfo[]
  bulkSettings: Record<string, ReprocessData>
  reprocessSettings: Record<
    number,
    { item1: SettingsType; item2: ReprocessData }
  >
  maxTimevalue: number
  unselectedFileInfos: number[]
  tempTable: string
  exceptionTypes: string
  reprocessOption: ReprocessOption
  tagids: number[]
  originalMediaId: number
  mediaName: string
  token: string
}

export interface DocumentListModel {
  fileId: number
  accessPath: string
  fileName: string
  extension: string
  originalFilePath: string
  exportedFilePath: string
  password: string
  fsId: number
}

export enum ReprocessOption {
  EXCEPTION,
  ALL_FILES,
  TAG,
}

export enum ReprocessType {
  PASSWORD_PROTECTED,
  CORRUPTED,
  CRASHED,
  TIME_OUT,
  NOT_PROCESSED,
  CHILD_MISSING,
  PARTIAL_META_EXTRACTED,
  REPLACE_ANY,
  ANY_FILES,
  NSF_ID,
  DEFAULT,
}

export interface ReprocessTag {
  id: string

  tagId: number

  tagName: string

  parentId: string

  totalTagCount: number
}

export enum SettingsType {
  BULK_SETTINGS,
  CHANGE_SETTINGS,
  IGNORE,
  DEFAULT,
}

export enum ExtractorOperation {
  META_EXTRACTION = 0,
  FULLTEXT_EXTRACTION = 1,
  CHILD_EXTRACTION = 2,
}

export interface MetaExtractor {
  operation: ExtractorOperation
  extractors: Extractor[]
}

export interface Extractor {
  id: number
  name: string
}

export interface EdocField {
  text: string
}

export interface EmailField {
  text: string
}

export interface ExceptionType {
  id: number
  exceptionType: string
  value: string
}

export interface ReprocessStatusModel {
  custodianName: string
  mediaName: string
  createdDate: Date
  queuedOn: Date
  currentlyInProgressJob: JobDetails
  JobDetails: JobDetails[]
}

export interface JobDetails {
  taskName: string
  totalCount: string
  completedCount: string
  timeTaken: string
  percentage: string
  status: string
  postProcessingStatus: string
}

export interface RepositoryModel {
  fsid: number
  fsDisplayName: string
  fsUserName: string
  fsPassword: string
  fsDomain: string
  authenticate: boolean
  sharedFolder: string
  testConnection: string
  testConnectionError: string
  repositoryType: string
}

export interface RepositoryHierarchyModel {
  fsid: string | number
  id: string
  parentId?: string
  type: string
  name: string
  repositoryDisplayName: string
  repositoryRootFolderName: string
  relativePath: string
  isLoading: boolean
  isLoaded: boolean
  isLoadedAll: boolean
  isExpanded: boolean
  isCancelling: boolean
  showNodeMenuOptions: boolean
  hasChildren?: boolean
  hasSuccesfulConnection: boolean
  errorMessage: string
  fullPath: string
}
