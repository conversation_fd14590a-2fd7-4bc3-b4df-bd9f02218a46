import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkRedactionGridComponent } from './bulk-redaction-grid.component'
import { ActivatedRoute } from '@angular/router'
import { BulkRedactFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'

describe('BulkRedactionGridComponent', () => {
  let component: BulkRedactionGridComponent
  let fixture: ComponentFixture<BulkRedactionGridComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkRedactionGridComponent, NoopAnimationsModule],
      providers: [
        provideMockStore({}),
        provideHttpClient(withInterceptorsFromDi()),
        BulkRedactFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkRedactionGridComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
