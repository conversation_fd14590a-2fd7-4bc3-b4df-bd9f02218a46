import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  signal,
  TrackByFunction,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentMenuType,
  FolderDocumentSelectionType,
  FolderType,
  Module,
} from '@venio/shared/models/constants'
import {
  Subject,
  combineLatest,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  takeUntil,
} from 'rxjs'
import {
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
} from '@angular/forms'
import { ActivatedRoute } from '@angular/router'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import {
  NotificationModule,
  NotificationService,
  Type,
} from '@progress/kendo-angular-notification'
import { DataAccessDocumentUtilityModule } from '@venio/data-access/document-utility'
import {
  FolderTypeModel,
  FolderFacade,
  FolderViewModel,
  FolderModel,
  DocumentsFacade,
  FolderGroupPermissionModel,
  FolderActionPayloadModel,
  SearchFacade,
  DocumentsService,
  SearchResultFacade,
} from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  SelectableSettings,
  SelectionChangeItem,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { SamplingModel } from '@venio/shared/models/interfaces'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-bulk-folder-aciton-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonsModule,
    LabelModule,
    InputsModule,
    DropDownListModule,
    TreeListModule,
    DialogModule,
    NotificationModule,
    SvgLoaderDirective,
    DataAccessDocumentUtilityModule,
    IndicatorsModule,
  ],
  templateUrl: './bulk-Folder-aciton-dialog.component.html',
  styleUrl: './bulk-Folder-aciton-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkFolderAcitonDialogComponent implements OnInit, OnDestroy {
  @Input() public MenuType: DocumentMenuType

  public readonly toDestroy$ = new Subject<void>()

  public opened = false

  public dialogTitle = signal('Send Or Remove Documents')

  public confidence: number

  public marginError: number

  public currentFolderType = ''

  public searchTempTable = ''

  public isSelectedCopyFolder = false

  public isSelectedRemoveFolder = false

  public isSelectedMoveFolder = false

  public isFolderVisible = false

  public isBatchSelected: boolean

  public allDocCount = 0

  public selectedSize: number

  public allFileIds: number[]

  public selectedFileIds: number[]

  public selectedDocuments: number[]

  public unselectedDocuments: number[]

  public folders: FolderViewModel[]

  public foldersGroupPermission: FolderGroupPermissionModel[]

  public documentSelectionType: FolderDocumentSelectionType

  public sampling: SamplingModel

  public folderingForm: FormGroup

  public showValidationMessage = false

  public defaultItemFolder: FolderTypeModel = {
    folderTypeId: -1,
    folderType: 'Select Type',
    defaultValue: null,
  }

  public folderTypeList: FolderTypeModel[] = [
    {
      folderTypeId: 1,
      folderType: FolderType.CopyToFolder,
      defaultValue: true,
    },
    {
      folderTypeId: 2,
      folderType: FolderType.RemoveFromFolder,
      defaultValue: false,
    },
    {
      folderTypeId: 3,
      folderType: FolderType.MoveToFolder,
      defaultValue: true,
    },
  ]

  public defaultFolderType: FolderTypeModel

  public settings: SelectableSettings = {
    mode: 'row',
    multiple: false,
    drag: false,
  }

  /**
   * Validation message to show
   */
  public formMessage = ''

  public selected: any[] = []

  constructor(
    private formBuilder: FormBuilder,
    private searchFacade: SearchFacade,
    private folderFacade: FolderFacade,
    private documentsFacade: DocumentsFacade,
    private searchResultsFacade: SearchResultFacade,
    private documentsService: DocumentsService,
    private notificationService: NotificationService,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#selectAllFileIdsFromCurrentPage()
    this.#getDocumentFileIds()
    this.#fetchSearchTempTable()
    this.#setFolderType()
    this.#initForm()
    this.#fetchFolders()
    this.#selectFolders()
    this.#fetchFolderGroupPermission()
    this.#selectFolderGroupPermission()
    this.#selectSendFolderResponses()
    this.#selectRemoveFolderResponses()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public trackByFolderFn = (
    _: number,
    item: TreeListItem
  ): TrackByFunction<TreeListItem> => item.data['folderId']

  #selectSendFolderResponses(): void {
    combineLatest([
      this.folderFacade.getSendToFolderSuccessResponse$,
      this.folderFacade.getSendToFolderErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return
        this.#notificationMessage(success, error)
        this.folderFacade.fetchAutoFolders(this.projectId)
        this.folderFacade.fetchStaticFolders(this.projectId)
      })
  }

  #selectRemoveFolderResponses(): void {
    combineLatest([
      this.folderFacade.getRemoveFolderSuccessResponse$,
      this.folderFacade.getRemoveFolderErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return
        this.#notificationMessage(success, error)
        this.folderFacade.fetchAutoFolders(this.projectId)
        this.folderFacade.fetchStaticFolders(this.projectId)
      })
  }

  #notificationMessage(success, error): void {
    this.#showMessage(success ? success.message : error.message, {
      style: success ? 'success' : 'error',
    })
    this.#resetFolderResponseState()
    if (success) {
      this.#closeDialog()
    }
    this.changeDetectorRef.markForCheck()
  }

  #fetchSelectedFileDocumentTableFolders(): void {
    this.documentsFacade.fetchSelectedFileDocumentTableFolders(this.allFileIds)
  }

  #resetFolderResponseState(): void {
    this.folderFacade.resetFolderState([
      'sendToFolderSuccessResponse',
      'sendToFolderErrorResponse',
      'removeFolderSuccessResponse',
      'removeFolderErrorResponse',
    ])
  }

  #resetDocumentState(): void {
    this.documentsFacade.resetDocumentState([
      'isBatchSelected',
      'currentDocument',
      'currentDocumentName',
      'currentFileName',
      'selectedDocuments',
      'unselectedDocuments',
    ])
  }

  #closeDialog(): void {
    this.#fetchSelectedFileDocumentTableFolders()
    //this.#resetDocumentState()
    this.documentsService.updateDocumentSelection$.next()
    this.dialogRef.close()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
  }

  #setFolderType(): void {
    if (this.MenuType === DocumentMenuType.SEND_REMOVE) {
      this.folderTypeList = this.folderTypeList.filter(
        (item) => item.folderTypeId !== 3
      )
    } else {
      this.folderTypeList = this.folderTypeList.filter(
        (item) => item.folderTypeId === 3
      )
    }
  }

  #initForm(): void {
    this.changeDetectorRef.markForCheck()
    this.folderingForm = this.formBuilder.group({
      documentSource: [null, Validators.compose([Validators.required])],
      documentFamilies: true,
      documentThreads: false,
      samplePercentage: ['100', Validators.compose([Validators.required])],
      documentPercentage: [null, Validators.compose([Validators.required])],
      random: false,
      eventComments: '',
      folderType: [null, Validators.compose([Validators.required])],
      includeSubFolders: true,
      selectedFolderType: 0,
      selectedFolder: '',
      fromFolder: '',
      toFolder: '',
      fromFolderId: null,
      toFolderId: null,
    })
    this.confidence = 95
    this.marginError = 1
    this.currentFolderType = 'FROM'
    this.#onChangeFolderType()
    //this.#setDefautlValueFolerType()
    this.#formValueChangeEvent()
  }

  #fetchSelectedDocument(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((document) => {
        this.selectedDocuments = document
        this.#setTitle(document.length)
      })
  }

  #fetchSearchTempTable(): void {
    this.searchFacade.getSearchTempTables$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((tempTables) => {
        this.searchTempTable = tempTables?.searchResultTempTable
      })
  }

  #setTitle(documentCount: number): void {
    const title =
      this.MenuType === DocumentMenuType.SEND_REMOVE ? 'Send Or Remove' : 'Move'
    this.dialogTitle.set(`${title} ${documentCount} Documents`)
  }

  #setDefautlValueFolerType(): void {
    this.defaultFolderType = this.folderTypeList.find(
      (c) => c.defaultValue === true
    )
    this.folderingForm.get('folderType').setValue(this.defaultFolderType)
  }

  #fetchFolders(): void {
    this.folderFacade.fetchAllFolders(this.projectId)
  }

  #selectFolders(): void {
    this.folderFacade.getAllFolders$
      .pipe(
        filter((folders) => !!folders && folders.length > 0),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((folders) => {
        this.#extendFolderTree(folders)
        this.changeDetectorRef.markForCheck()
      })
  }

  #fetchFolderGroupPermission(): void {
    this.folderFacade.fetchFolderGroupPermission(this.projectId)
  }

  #selectFolderGroupPermission(): void {
    this.folderFacade.getFolderGroupPermission$
      .pipe(
        filter(
          (foldersGroupPermission) =>
            !!foldersGroupPermission && foldersGroupPermission.length > 0
        ),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((foldersGroupPermission) => {
        this.foldersGroupPermission = foldersGroupPermission
      })
  }

  #extendFolderTree(folders: FolderModel[]): void {
    this.folders = folders.map((item) => ({
      ...item,
      id: item.folderId,
      parentId: item.parentFolderId === -1 ? null : item.parentFolderId,
    }))
  }

  #onChangeFolderType(): void {
    this.folderingForm
      .get('folderType')
      .valueChanges.pipe(
        // Do not emit if we have the same value after previous emission
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((selectedValue) => {
        const folderTypeId = selectedValue.folderTypeId

        this.folderingForm.get('selectedFolderType').setValue(folderTypeId)
        this.folderingForm.get('selectedFolder').setValue('')
        this.folderingForm.get('fromFolder').setValue('')
        this.folderingForm.get('toFolder').setValue('')
        this.folderingForm.get('fromFolderId').setValue(null)
        this.folderingForm.get('toFolderId').setValue(null)
        this.selected = []

        if (folderTypeId === 1) {
          // Copy Folder
          this.isSelectedCopyFolder = true
          this.isSelectedRemoveFolder = false
          this.isSelectedMoveFolder = false
        } else if (folderTypeId === 2) {
          // Remove Folder
          this.isSelectedCopyFolder = false
          this.isSelectedRemoveFolder = true
          this.isSelectedMoveFolder = false
        } else if (folderTypeId === 3) {
          // Move Folder
          this.isSelectedCopyFolder = false
          this.isSelectedMoveFolder = true
          this.isSelectedRemoveFolder = false
        } else {
          this.isSelectedCopyFolder = false
          this.isSelectedMoveFolder = false
          this.isSelectedRemoveFolder = false
          this.showValidationMessage = true
          this.formMessage = ''
        }
      })
  }

  #formValueChangeEvent(): void {
    this.folderingForm
      .get('documentFamilies')
      .valueChanges.pipe(takeUntil(this.toDestroy$))
      .subscribe((checked) => {
        if (!checked && this.folderingForm.get('documentThreads').value) {
          this.folderingForm.get('documentThreads').setValue(false)
        }
      })

    this.folderingForm
      .get('documentThreads')
      .valueChanges.pipe(
        filter((c) => !!c),
        takeUntil(this.toDestroy$)
      )
      .subscribe((checked) => {
        if (checked && !this.folderingForm.get('documentFamilies').value) {
          this.folderingForm.get('documentFamilies').setValue(true)
        }
      })
  }

  public onSelectionChange(e): void {
    const items: SelectionChangeItem[] = e.items
    const folder = items[0].dataItem

    const selectedFolderType =
      this.folderingForm.get('selectedFolderType').value
    if (selectedFolderType === 1 || selectedFolderType === 2) {
      this.folderingForm.get('selectedFolder').setValue(folder.folderName)
      this.folderingForm.get('fromFolderId').setValue(folder.folderId)
    } else {
      if (
        this.folderingForm.get('fromFolder').value === '' ||
        this.currentFolderType === 'FROM'
      ) {
        this.folderingForm.get('fromFolder').setValue(folder.folderName)
        this.folderingForm.get('fromFolderId').setValue(folder.folderId)
        this.currentFolderType = 'TO'
      } else {
        this.folderingForm.get('toFolder').setValue(folder.folderName)
        this.folderingForm.get('toFolderId').setValue(folder.folderId)
      }
    }
    if (folder.folderId > 0) this.formMessage = ''
  }

  public onFocusFromFolder(): void {
    this.currentFolderType = 'FROM'
  }

  public onFocusToFolder(): void {
    this.currentFolderType = 'TO'
  }

  public close(status: string): void {
    this.opened = false
    this.dialogRef.close()
  }

  public openDialog(): void {
    this.opened = true
  }

  async #getDocumentFileIds(): Promise<void> {
    let documentCount = 0
    this.isBatchSelected = await firstValueFrom(
      this.documentsFacade.getIsBatchSelected$
    )

    this.selectedFileIds = this.selectedDocuments = await firstValueFrom(
      this.documentsFacade.getSelectedDocuments$
    )

    this.unselectedDocuments = await firstValueFrom(
      this.documentsFacade.getUnselectedDocuments$
    )

    if (this.selectedFileIds.length === 0) {
      const errorMsg = 'Please select a document to apply folder.'
      this.#canEnableSaveButton(errorMsg, false)
    } else if (this.isBatchSelected) {
      this.allDocCount = documentCount = await firstValueFrom(
        this.searchFacade.getTotalHitCount$
      )
      this.documentSelectionType = FolderDocumentSelectionType.All
      this.#canEnableSaveButton('', true)
    } else {
      documentCount = this.selectedFileIds.length
      this.documentSelectionType = FolderDocumentSelectionType.Selected
      this.#canEnableSaveButton('', true)
    }
    this.selectedSize = documentCount
    this.#setTitle(documentCount)
  }

  #canEnableSaveButton(formMessage: string, isFolderVisible: boolean): void {
    this.formMessage = formMessage
    this.isFolderVisible = isFolderVisible
  }

  #getFolderType(): FolderType {
    const folderType = this.folderingForm.get('folderType').value
    return folderType?.folderType
  }

  #selectAllFileIdsFromCurrentPage(): void {
    this.searchResultsFacade.getSearchResultFileIds
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fileIds: number[]) => {
        this.allFileIds = fileIds
      })
  }

  #validateFolderType(): boolean {
    const folderType = this.folderingForm.get('folderType').value
    if (!folderType || folderType?.folderTypeId === -1) {
      this.showValidationMessage = true
      return false
    }
    return true
  }

  #validateFolder(
    selectedFolderId: number,
    actionType: FolderType,
    folderType: string
  ): boolean {
    if (this.selectedDocuments?.length === 0 && this.allDocCount === 0) {
      this.formMessage =
        actionType === FolderType.CopyToFolder
          ? 'No documents have been selected to add to folder.'
          : 'No documents have been selected to remove from folder.'
      return false
    }
    if (!selectedFolderId || selectedFolderId === null) {
      this.formMessage = `Please select the folder`
      return false
    } else if (selectedFolderId && selectedFolderId < 0) {
      this.formMessage = 'Root folder is not a valid folder for this operation.'
      return false
    }
    const folderPermissions = this.foldersGroupPermission.filter(
      (item) => item.folderId === selectedFolderId
    )

    if (folderPermissions.some((f) => f.permission === 'READ_ONLY')) {
      this.formMessage =
        'Selected ' +
        (actionType === FolderType.MoveToFolder ? folderType : '') +
        ' folder is a readonly folder.'
      return false
    }
    return true
  }

  #checkFolderValidation(
    fromFolderId: number,
    toFolderId: number,
    actionType: FolderType
  ): boolean {
    let isValidFromFolder = false
    let isValidToFolder = false
    let isSameFolder = false

    if (
      actionType === FolderType.CopyToFolder ||
      actionType === FolderType.RemoveFromFolder ||
      actionType === FolderType.MoveToFolder
    ) {
      isValidFromFolder = this.#validateFolder(fromFolderId, actionType, 'From')
      if (actionType === FolderType.MoveToFolder && isValidFromFolder) {
        isValidToFolder = this.#validateFolder(toFolderId, actionType, 'To')
        if (isValidFromFolder && isValidToFolder) {
          isSameFolder = fromFolderId[0] === toFolderId[0]
          if (isSameFolder)
            this.formMessage =
              'Source and destination folders cannot be the same'
        }
        return isValidFromFolder && isValidToFolder && !isSameFolder
      }
      return isValidFromFolder
    }
  }

  #validations(
    fromFolderId: number,
    toFolderId: number,
    actionType: FolderType
  ): boolean {
    this.formMessage = ''
    // Validate Folder Type
    if (!this.#validateFolderType()) {
      return false
    }

    // Validate Folder
    if (!this.#checkFolderValidation(fromFolderId, toFolderId, actionType)) {
      return false
    }
    return true
  }

  #prepareSamplingPayload(isSaveFolder = false): void {
    const formValue = this.folderingForm.value
    if (
      formValue?.samplePercentage > 100 &&
      formValue?.documentPercentage === 2
    ) {
      formValue.samplePercentage = 100
    }

    this.sampling = {
      sampleId: 0,
      sampleName: null,
      samplePurpose: null,
      samplePopulation: this.#getDocumentSelectionName(),
      sampleTagCondition: 'Non_Selected_Tags',
      stratifiedBy: null,
      samplingMethod: !isSaveFolder
        ? 'StandardNormalDistribution'
        : formValue?.documentPercentage % 2 === 0
        ? 'Percentage'
        : 'NumberOfDocuments',
      confidenceLevel: this.confidence,
      confidenceInterval: this.marginError,
      samplePercent: formValue?.samplePercentage,
      sampleNumber:
        formValue?.documentPercentage % 2 !== 0 && isSaveFolder
          ? formValue?.samplePercentage
          : this.selectedSize,
      tagIds: null,
      excludeTagIds: null,
      applyTagId: 0,
      searchId: 0,
      excludeNoText: false,
      excludeTags: false,
      sampleAction: null,
      sampleDate: null,
      populationSize: this.selectedSize,
      sampleSize: 0,
      searchResultTempTable: this.searchTempTable,
      selectedFileIds: this.selectedFileIds,
      unselectedFileID: this.unselectedDocuments,
      isBatchSelection: this.isBatchSelected,
      isAllDocuments: formValue?.documentSource === 2,
      isParent: formValue?.documentFamilies,
      module: 'DOCUMENTS', // This is hard coded for now. We will implement this in future
    }
  }

  #prepareFolderPayload(): FolderActionPayloadModel {
    const formValue = this.folderingForm?.value

    const payload: FolderActionPayloadModel = {
      projectId: this.projectId,
      folderActionType: formValue?.folderType.folderTypeId,
      docSelectionType: this.documentSelectionType,
      mainTempTable: this.searchTempTable,
      sourceModule: Module?.Review2,
      folderIds: [formValue?.fromFolderId],
      useInjectedFileIds: undefined,
      selectedFileIds: this.selectedFileIds,
      isBatchSelection: this.isBatchSelected,
      folderEventComment: formValue.eventComments.trim(),
      samplingInfo: this.sampling,
      isRandom: formValue?.random,
      isIncludeSubFolder: formValue?.includeSubFolders,
      toFolderIds: formValue?.toFolderId ? [formValue?.toFolderId] : [],
      module: Module.Review2, // This is hard coded for now. We will implement this in future
      moduleName: this.#getModuleName(Module.Review2),
      propagateDocumentThread: formValue?.documentThreads,
    }
    return payload
  }

  #getModuleName(module: Module): string {
    const moduleName =
      module === Module.Discussion
        ? 'DISCUSSION'
        : module === Module.Discussion_Senders
        ? 'DISCUSSION_SENDERS'
        : module === Module.Discussion_Conversations
        ? 'DISCUSSION_CONVERSATIONS'
        : Module.ParticipantMessages
        ? 'PARTICIPANT_MESSAGES'
        : Module.Review2
        ? 'DOCUMENTS'
        : 'REVIEW'
    return moduleName
  }

  #getDocumentSelectionName(): string {
    const documentSelectionName =
      this.documentSelectionType === FolderDocumentSelectionType.All
        ? 'All_Documents'
        : this.documentSelectionType === FolderDocumentSelectionType.Selected
        ? 'SelectedDocuments'
        : this.documentSelectionType === FolderDocumentSelectionType.Matching
        ? 'DirectHitDocuments'
        : this.documentSelectionType === FolderDocumentSelectionType.Duplicate
        ? 'DuplicateDocuments'
        : 'All_Documents'

    return documentSelectionName
  }

  public save(): void {
    const fromFolderId = this.folderingForm.get('fromFolderId')?.value
    const toFolderId = this.folderingForm.get('toFolderId')?.value
    const folderType = this.#getFolderType()
    if (!this.#validations(fromFolderId, toFolderId, folderType)) {
      return
    }

    this.#prepareSamplingPayload(true)
    const payload = this.#prepareFolderPayload()

    if (folderType === FolderType.CopyToFolder) {
      this.folderFacade.saveSendToFolder(this.projectId, payload)
    }
    if (folderType === FolderType.RemoveFromFolder) {
      this.folderFacade.saveRemoveFolder(this.projectId, payload)
    }
  }
}
