<kendo-dialog-titlebar (close)="close()">
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-block t-w-[14%]">
      <span
        class="t-w-10 t-h-10 t-p-3 t-mr-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
        <img src="assets/svg/icon-person-outline.svg" alt="upload icon" />
      </span>
      Invite Users to Upload
    </div>
    <div class="t-w-[72%] t-t-px-2">
      <div class="t-block">
        <div
          #appendNotification
          class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-14 t-w-full"></div>
      </div>
    </div>
    <div class="t-w-[14%] t-px-2"></div>
  </div>
</kendo-dialog-titlebar>
@defer{
<venio-invite-upload-form
  [inviteUploadForm]="inviteUploadForm"
  [selectedProjectId]="selectedProjectId()" />
} @placeholder {
<div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
  <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
  <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
  <kendo-skeleton [height]="30" shape="rectangle" [width]="'33%'" />
</div>
}
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="sendInvite()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button"
      [disabled]="invitationInProgress()">
      <kendo-loader
        *ngIf="invitationInProgress()"
        size="small"
        type="pulsing"
        themeColor="secondary"
        class="t-pl-[0.5rem]"></kendo-loader>
      SEND INVITE
    </button>
    <button
      kendoButton
      (click)="close()"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
