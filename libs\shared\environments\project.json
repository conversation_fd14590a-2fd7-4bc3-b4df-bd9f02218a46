{"name": "shared-environments", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared/environments/src", "prefix": "venio", "tags": ["shared"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/shared/environments"], "options": {"tsConfig": "libs/shared/environments/tsconfig.lib.json", "project": "libs/shared/environments/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared/environments/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared/environments/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/shared/environments"], "options": {"jestConfig": "libs/shared/environments/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}