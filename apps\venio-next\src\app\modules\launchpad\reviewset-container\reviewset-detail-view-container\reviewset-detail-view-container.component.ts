import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ReviewSetEntry,
  ReviewSetViewDetailViewTypes,
} from '@venio/shared/models/interfaces'

import { ReviewsetDetailViewDocumentsComponent } from '../reviewset-detail-view-documents/reviewset-detail-view-documents.component'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { ReviewsetDetailViewBatchesComponent } from '../reviewset-detail-view-batches/reviewset-detail-view-batches.component'

@Component({
  selector: 'venio-reviewset-detail-view-container',
  standalone: true,
  imports: [
    CommonModule,
    ReviewsetDetailViewDocumentsComponent,
    ReviewsetDetailViewBatchesComponent,
    SkeletonComponent,
  ],
  templateUrl: './reviewset-detail-view-container.component.html',
  styleUrl: './reviewset-detail-view-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewContainerComponent {
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly selectedViewType =
    input.required<ReviewSetViewDetailViewTypes>()

  public readonly batchDeletionStatus = output<boolean>()

  public readonly reviewSetViewDetailViewTypes = ReviewSetViewDetailViewTypes

  public forwardBatchDeletionStatus(status: boolean): void {
    this.batchDeletionStatus.emit(status)
  }
}
