import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { xIcon } from '@progress/kendo-svg-icons'
import { Observable, of } from 'rxjs'

@Component({
  selector: 'venio-set-one-batch-reassign',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    TreeViewModule,
  ],
  templateUrl: './set-one-batch-reassign.component.html',
  styleUrl: './set-one-batch-reassign.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetOneBatchReassignComponent implements OnInit {
  public selectedRadio = ''

  public expandedKeys: any[] = []

  public treeViewData: any[] = [
    {
      text: 'Master Demo Group',
      id: '0',
      items: [
        { text: 'John Doe', id: '1' },
        { text: 'Robert C. Willis', id: '2' },
        { text: 'Ernest T. Johnson', id: '3' },
        { text: 'Linda R. Warner', id: '4' },
      ],
    },
    {
      text: 'Demo Group Reviewers',
      id: '5',
      items: [{ text: 'Randy M. Rees', id: '6' }],
    },
  ]

  constructor() {}

  public ngOnInit(): void {
    this.expandedKeys = this.getAllNodeIds(this.treeViewData)
  }

  public icons = {
    closeIcon: xIcon,
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  private getAllNodeIds(nodes: any[]): string[] {
    let keys: string[] = []

    for (const node of nodes) {
      if (node.id) {
        keys.push(node.id) // Add current node ID
      }

      if (node.items) {
        keys = keys.concat(this.getAllNodeIds(node.items)) // Add children IDs recursively
      }
    }

    return keys
  }
}
