import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiSearchMessageBoxComponent } from './ai-search-message-box.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'

describe('AiSearchMessageBoxComponent', () => {
  let component: AiSearchMessageBoxComponent
  let fixture: ComponentFixture<AiSearchMessageBoxComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiSearchMessageBoxComponent],
      providers: [provideMockStore({}), provideHttpClient()],
    }).compileComponents()

    fixture = TestBed.createComponent(AiSearchMessageBoxComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
