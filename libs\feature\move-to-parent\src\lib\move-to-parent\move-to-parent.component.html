<kendo-dialog-titlebar (close)="onCancelAction()">
  <div class="t-flex t-flex-row t-gap-2">
    <div
      class="t-bg-[#F2F2F2] t-w-10 t-h-10 t-flex t-items-center t-justify-center t-rounded-full">
      <span
        venioSvgLoader
        color="#B8B8B8"
        svgUrl="assets/svg/icon-move.svg"
        width="1.3rem"
        height="1.3rem">
      </span>
    </div>
    <div
      class="t-flex t-text-[#2F3080] t-opacity-87 t-text-base t-font-medium t-relative t-top-2.5 t-ml-2">
      Move To Parent
    </div>
  </div>
</kendo-dialog-titlebar>
<div>
  <kendo-loader *ngIf="isLoading()" size="medium" type="pulsing"></kendo-loader>
</div>
<div class="t-mt-2 v-custom-grey-bg">
  <div class="t-flex t-flex-row t-items-center">
    <kendo-label class="t-mr-2">Destination Parent File Id:</kendo-label>
    <kendo-numerictextbox
      class="!t-border-[#ccc] t-w-36"
      format="#"
      [step]="1"
      [autoCorrect]="true"
      [min]="1"
      [max]="99999999999"
      (valueChange)="onParentIdChanged($event)"
      [disabled]="isMoveToParentInProgress()">
    </kendo-numerictextbox>
  </div>
  <div class="t-flex t-flex-col">
    <kendo-label class="t-text-error t-mt-2">
      Selected document and its family set will be moved as child documents to
      destination parent file.</kendo-label
    >

    <div class="t-mt-4">
      <kendo-label
        text="Total parent document(s) selected to move: "></kendo-label>
      <kendo-label
        class="t-font-medium"
        text="{{ totalParentSelectedToMove() }}"></kendo-label>
    </div>
    <kendo-label
      class="t-text-error t-mt-2"
      *ngIf="totalParentSelectedToMove() === 0"
      >Please select parent document(s) to move</kendo-label
    >
  </div>
</div>
<kendo-expansionpanel
  class="t-mt-4 t-pb-1 v-custom-expansion-panel"
  [expanded]="false">
  <ng-template kendoExpansionPanelTitleDirective>
    <div class="header-content">
      <span>Move to Parent Progress Status</span>
    </div>
  </ng-template>
  <kendo-textarea
    [value]="moveProgressStatus()"
    class="t-border-none"
    [rows]="9"
    resizable="none"
    size="small"
    fillMode="none"
    [readonly]="true">
  </kendo-textarea>
</kendo-expansionpanel>
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="moveClicked()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="move-button"
      [disabled]="!enableMoveOption()">
      Move
      <kendo-loader
        *ngIf="isMoveToParentInProgress()"
        size="small"
        type="pulsing"
        class="t-pl-[0.5rem]"></kendo-loader>
    </button>
    <button
      kendoButton
      (click)="onCancelAction()"
      themeColor="dark"
      fillMode="outline"
      data-qa="close-move-dialog-button">
      Close
    </button>
  </div>
</kendo-dialog-actions>
