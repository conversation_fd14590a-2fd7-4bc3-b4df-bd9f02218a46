export enum UtilityPanelType {
  TAG_CODING = 'TagCoding',
  METADATA = 'Metadata',
  FAMILY = 'Family',
  NOTES = 'Notes',
  DOCUMENT_HISTROY = 'DocumentHistory',
  DUPLICATE = 'Duplicate',
  SIMILAR_DOCUMENTS = 'SimilarDocuments',
  EMAIL_THREAD = 'EmailThread',
  NEAR_DUPLICATE = 'NearDuplicate',
  EDAI_AI_RELEVANCE = 'EdaiAiRelevance',
  EDAI_AI_Privilege = 'EdaiAiPrivilege',
  EDAI_AI_PII_DETECT = 'EdaiAiPIIDetect',
  EDAI_AI_PII_EXTRACT = 'EdaiAiPIIExtract',
}

export enum UtilityPanelTitle {
  TAG_CODING = 'Tags & Coding',
  METADATA = 'Metadata',
  FAMILY = 'Family',
  NOTES = 'Notes',
  DOCUMENT_HISTROY = 'Document History',
  DUPLICATE = 'Duplicate',
  SIMILAR_DOCUMENTS = 'Similar Documents',
  EMAIL_THREAD = 'Email Thread',
  NEAR_DUPLICATE = 'Near Duplicate',
  EDAI_AI_RELEVANCE = 'AI Relevance',
  EDAI_AI_Privilege = 'AI Privilege',
  EDAI_AI_PII_DETECT = 'AI PII Detect',
  EDAI_AI_PII_EXTRACT = 'AI PII Extract',
  CODING = 'Coding',
}
