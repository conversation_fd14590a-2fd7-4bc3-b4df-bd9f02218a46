<ng-container
  *ngFor="
    let stack of conditionStacks();
    trackBy: trackByGroupFn;
    let index = index
  ">
  <div
    @listAnimation
    *ngIf="stack.conditionType === 'group'"
    [ngClass]="{
      't-mt-5': conditionUiType === conditionUiTypes.VIEW_CONDITION
    }"
    class="t-flex t-border t-border-[#cccccc] t-rounded t-overflow-hidden t-relative t-min-h-[4rem]">
    <button
      *ngIf="conditionStacks()?.length > 1"
      title="Remove this condition group"
      (click)="removeGroup(stack.id)"
      kendoButton
      data-qa="iconClose4"
      fillMode="clear"
      class="v-dialog-close t-absolute t--right-0 t-top-0 !t-flex t-items-start t-z-50 t-rounded-bl-lg t-w-6 t-h-6 t-p-0 t-pt-0.5 !t-pl-0.5">
      <kendo-svg-icon
        [icon]="icons.closeIcon"
        [size]="'small'"></kendo-svg-icon>
    </button>
    <div class="v-custom-label t-text-white t-w-[25px] t-text-center">
      <kendo-dropdownbutton
        data-qa="not4"
        [data]="groupOperators"
        (itemClick)="changeGroupOperator(stack.id, $event)"
        fillMode="none"
        class="v-custom-operator-menu">
        {{ stack.operator }}
      </kendo-dropdownbutton>
    </div>
    <div
      *ngIf="stack.conditions.length"
      class="t-flex t-flex-col t-w-full t-gap-2 t-p-2 t-relative"
      [@listAnimation]="stack.conditions.length">
      <ng-container
        *ngFor="let condition of stack.conditions; trackBy: trackByConditionFn">
        <venio-document-view-designer-conditions-row
          [condition]="condition"
          [fields]="fields()"
          [searchField]="searchField()"
          (conditionRowChange)="
            onConditionRowChange(stack.id, condition.id, $event)
          " />
      </ng-container>
    </div>
    <div
      *ngIf="!stack.conditions.length"
      class="t-flex t-flex-col t-w-full t-justify-center t-items-center">
      <div class="t-flex t-items-center">
        <span
          (click)="addNewCondition(stack.id)"
          class="t-flex t-items-center t-cursor-pointer v-custom-secondary-button t-text-[--tb-kendo-custom-secondary-100]">
          <kendo-svg-icon [icon]="icons.plusIconSvg"></kendo-svg-icon>
          Click here </span
        ><span class="t-pl-1"> to add field selection</span>
      </div>
    </div>
  </div>
  <div
    class="t-grid t-place-content-center t-py-4 t-w-full"
    *ngIf="stack.conditionType === 'operator'">
    <kendo-buttongroup
      selection="single"
      class="t-block t-justify-center t-border t-border-[#cccccc] t-rounded-[4px]">
      <button
        class="!t-border-0"
        themeColor="success"
        fillMode="outline"
        *ngFor="let operator of groupCombinatorOperator"
        kendoButton
        [toggleable]="true"
        [svgIcon]="stack.operator === operator ? iconCheck : null"
        [selected]="stack.operator === operator"
        (selectedChange)="updateConditionGroup(stack, operator)">
        {{ operator }}
      </button>
    </kendo-buttongroup>
  </div>
</ng-container>
