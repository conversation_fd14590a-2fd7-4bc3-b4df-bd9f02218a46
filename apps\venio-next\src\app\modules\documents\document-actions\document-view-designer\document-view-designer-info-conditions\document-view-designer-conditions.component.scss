@layer {
  .v-custom-label {
    background: transparent linear-gradient(224deg, #1ebadc 0%, #73c8bf 100%) 0
      0 no-repeat;
    writing-mode: vertical-lr;
    rotate: -180deg;
    position: relative;
    height: inherit;
  }

  .v-custom-condition {
    &__action-container {
      @apply t-flex-wrap t-transition-opacity t-duration-500 t-gap-2 t-rounded t-p-1;
    }

    &__action-container:not(.v-open-active) {
      @apply t-opacity-0  t-invisible  #{!important};
    }

    &:hover {
      .v-custom-condition__action-container {
        @apply t-visible t-opacity-100 #{!important};
      }
    }
  }
}
