import { DirectExportDetailSummaryComponent } from './direct-export-detail-summary/direct-export-detail-summary.component'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  ViewChild,
  OnInit,
  AfterViewInit,
  On<PERSON><PERSON>roy,
  effect,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormGroup,
  FormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms'
import { filter, map, Observable, of, Subject, take, takeUntil } from 'rxjs'
import {
  CaseDetailModel,
  ControlNumberEndorsement,
  ControlNumberSetting,
  FilterOptions,
  ImageConversionOption,
  PDFServiceOption,
  PrintBinding,
  PrintServiceOption,
  PrintSet,
  ProductionOptions,
  ServiceTypeConstants,
  ServiceRequestType,
  ServiceType,
  ServiceTypeDescription,
  SettingsInfo,
  VODRSettings,
  ProductionFieldTemplateModel,
} from '@venio/shared/models/interfaces'
import { ProjectFacade, DirectExportFacade } from '@venio/data-access/common'
import { StringUtils } from '@venio/util/utilities'
import _ from 'lodash'
import {
  DialogModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { DirectExportDetailComponent } from './direct-export-detail/direct-export-detail.component'
import { toSignal } from '@angular/core/rxjs-interop'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { LocalStorage } from '@venio/shared/storage'

@Component({
  selector: 'venio-direct-export-container',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    FormsModule,
    DirectExportDetailComponent,
    DirectExportDetailSummaryComponent,
    ButtonModule,
    LoaderComponent,
  ],
  templateUrl: './direct-export-container.component.html',
  styleUrl: './direct-export-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectExportContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild(DirectExportDetailComponent)
  private directExportCmp: DirectExportDetailComponent

  @Input() public data: {
    isCaseCreationFlow: boolean
    existingCaseId: number
    canCreateCase: boolean
    allowCaseSelection: boolean
  }

  public currentStep = 1

  private readonly projectFacade = inject(ProjectFacade)

  private readonly directExportFacade = inject(DirectExportFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly notificationService = inject(NotificationService)

  private readonly dialogService = inject(DialogService)

  private readonly fb = inject(FormBuilder)

  private readonly cdr = inject(ChangeDetectorRef)

  @ViewChild('content', { read: ViewContainerRef })
  public content: ViewContainerRef

  public settingsForm: FormGroup

  public serviceTypeList: any[]

  public settings: SettingsInfo

  public continueFromPreviousControlNumber = false

  private confirmationDialogRef: DialogRef

  public selectControlNumberConflict = toSignal(
    this.directExportFacade.selectControlNumberConflictResponse$
  )

  private unsubscribed$ = new Subject<void>()

  public serviceInfoData: SettingsInfo

  public defaultSettingsInfo: any

  public caseList: CaseDetailModel[]

  public existingCaseId: number

  public existingCase = false

  public canCreateCase = false

  public allowCaseSelection = true

  public selectedServiceTypeName: ServiceTypeConstants | null = null

  private EXISTING_CASE_SERVICE_TYPE = {
    serviceTypeDisplayName: 'Add data to an existing case',
    serviceTypeId: ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT,
    serviceTypeName: ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT,
  }

  public isCaseCreationFlow: boolean

  public timeZone$: any

  public exportTemplatesList: ProductionFieldTemplateModel[]

  public isServiceTypeCaseLoading = false

  public selectedBaseAPIUrl: string

  private isContinuedAfterControlNumberConflict = false

  private clientId: number

  constructor() {
    // receive response from API to check if there is conflict in control number
    effect(
      () => {
        const controlNumberConflict = this.selectControlNumberConflict()
        this.isServiceTypeCaseLoading = false
        if (
          controlNumberConflict === undefined ||
          controlNumberConflict === null
        ) {
          return
        }

        this.directExportFacade.clearControlNumberConflictResponse()

        // show error message if API throws an error
        if (controlNumberConflict.status?.toLowerCase() === 'error') {
          this.showNotification(controlNumberConflict.message, {
            style: 'error',
          })
        }
        // if there is conflict in control number then show confirmation dialog to continue with existing control number
        else if (
          typeof controlNumberConflict.data === 'boolean' &&
          controlNumberConflict.data === true
        ) {
          this.#confirmControlNumberContinuation()
        }
        // if there is no control number conflict then continue to next step
        else {
          this.progressToNextStep()
        }
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnInit(): void {
    this.directExportFacade.clearControlNumberConflictResponse()
    this.clientId = +LocalStorage.get('ClientId') || 1

    this.projectFacade.fetchCaseDetail()
    this.directExportFacade.fetchTimeZones()
    this.directExportFacade.fetchServiceTypeList()
    this.directExportFacade.fetchExportTemplates(-1)
    this.#fetchConnectorEnvirnments()
    this.#fetchRelativityTemplatesList()
    this.initializeData()
    this.initializeForm()
  }

  // Fetches connector environments
  #fetchConnectorEnvirnments(): void {
    this.directExportFacade.fetchConnectorEnvironments(this.clientId)
  }

  // Fetches Relativity templates list
  #fetchRelativityTemplatesList(): void {
    this.directExportFacade.fetchRelativityTemplates(this.clientId)
  }

  private initializeData(): void {
    this.directExportFacade.selectTimeZones$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res: any) => {
        this.timeZone$ = res?.data
      })

    this.directExportFacade.selectServiceTypeList$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res?.data) {
          const serviceTypeData = res?.data
          this.serviceTypeList = _.cloneDeep(
            this.getServiceTypeDescriptions(serviceTypeData?.serviceTypes)
          )
          this.serviceTypeList.push(this.EXISTING_CASE_SERVICE_TYPE)
          this.directExportCmp?.isServiceTypeRequestLoading?.set(false)
        }
      })

    this.directExportFacade.selectExportFieldTemplates$
      .pipe(
        filter((t) => !!t),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((data) => {
        this.exportTemplatesList = data.filter(
          (item) => !item.IsExportSpecificTemplate
        )
      })

    this.projectFacade.selectCaseDetail$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((data) => {
        if (data) {
          this.caseList = data?.caseDetailEntries || []
        }
      })

    this.directExportFacade.selectDefaultData$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res) {
          this.defaultSettingsInfo = res?.data
        }
      })

    this.handleErrorResponses()
  }

  private handleErrorResponses(): void {
    // Handle error in fetching time zones
    this.directExportFacade.selectTimeZonesError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearTimeZonesError()
        const error = 'An error occurred while fetching time zones.'
        this.showNotification(error, { style: 'error' })
      })

    // Handle error in fetching service type list
    this.directExportFacade.selectServiceTypeListError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearServiceTypeListFailure()
        this.directExportCmp?.isServiceTypeRequestLoading?.set(false)
        const error =
          'An error occurred while fetching list of available services.'
        this.showNotification(error, { style: 'error' })
      })

    // Handle error in fetching export field templates
    this.directExportFacade.selectExportTemplatesError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearExportTemplatesError()
        const error =
          'An error occurred while fetching production field templates.'
        this.showNotification(error, { style: 'error' })
      })

    // Handle error in fetching connector environments
    this.directExportFacade.selectConnctorEnvironmentError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearConnectorEnvironmentsFailure()
        const error =
          e?.message ??
          'An error occurred while fetching relativity connector environments.'
        this.showNotification(error, { style: 'error' })
      })

    // Handle error in fetching relativity workspaces
    this.directExportFacade.selectRelativityWorkspaceError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearRelativityWorkspacesFailure()
        const error =
          e?.message ??
          'An error occurred while fetching relativity workspaces.'
        this.showNotification(error, { style: 'error' })
      })

    // Handle error in fetching relativity workspace fileshares
    this.directExportFacade.selectRelativityWorkspaceFileshareError$
      .pipe(
        filter((e) => !!e),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((e) => {
        this.directExportFacade.clearRelativityWorkspaceFilesharesFailure()
        const error =
          e?.message ??
          'An error occurred while fetching relativity workspaces fileshares.'
        this.showNotification(error, { style: 'error' })
      })
  }

  public ngAfterViewInit(): void {
    this.isCaseCreationFlow = this.data?.isCaseCreationFlow ?? true
    this.existingCaseId = this.data?.existingCaseId
    this.canCreateCase = this.data?.canCreateCase ?? false
    this.allowCaseSelection = this.data?.allowCaseSelection ?? true

    // if user doesn't have permission to create case then set the service request type to 'Add data to an existing project'
    if (!this.canCreateCase) {
      this.settingsForm
        .get('ServiceRequestType')
        .setValue(ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT)
    }
    this.cdr.detectChanges()
  }

  public setSelectedServiceType(event: any): void {
    this.selectedServiceTypeName = event
    const serviceTypeName: ServiceTypeConstants = this.selectedServiceTypeName
    if (serviceTypeName !== this.EXISTING_CASE_SERVICE_TYPE.serviceTypeName) {
      this.existingCase = false
      this.existingCaseId = null
      this.isCaseCreationFlow = true
    }

    const caseNameControl = this.settingsForm.get('caseName')
    if (
      serviceTypeName !== ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT
    ) {
      caseNameControl.setValidators(Validators.required)
      caseNameControl.setAsyncValidators(this.uniqueProjectNameValidator())
    } else {
      caseNameControl.clearValidators()
      caseNameControl.clearAsyncValidators()
    }
    caseNameControl.updateValueAndValidity()
  }

  private initializeForm(): void {
    this.settingsForm = this.fb.group({
      caseName: [
        '',
        {
          validators: Validators.required,
          asyncValidators: [this.uniqueProjectNameValidator()],
          updateOn: 'blur',
        },
      ],
      selectedCase: [],
      ServiceRequestType: [
        null,
        {
          validators: Validators.required,
          asyncValidators: [this.serviceRequestTypeValidator()],
          updateOn: 'blur',
        },
      ],
      serviceRequestTypeExisting: [null],
      serviceTypeName: [null],
      exportTemplateName: [],
      overrideSetting: [false],
      approvePreProcessPage_CostEstimate: [false],
      generalSettings: this.fb.group(
        {
          deduplicationOption: [2],
          timeZone: [188],
          csvExcelHandling: [0],
          discoveryExceptionHandling: [true],
          autoGenerateImagesAfterIngestion: [true],
          ignoreAutoTiffJobsForMediaProcessingStatus: [false],
        },
        { validator: this.validateGeneralSettingsForm }
      ),
      imageConversionSettings: this.fb.group({
        imageType: [2],
        imageColorConversion: this.fb.group({
          imageFileType: [1],
          pdfFiles: [1],
          powerpoint: [1],
        }),
        passwordList: [''],
      }),
      controlNumberAndEndorsementSettings: this.fb.group({
        sortOrder: ['RELATIVE_FILE_PATH'],
        exportLocation: [1],
        ControlNumberSetting: this.fb.group(
          {
            controlNumberPrefix: [''],
            controlNumberDelimiter: [0],
            controlNumberStartingNumber: [1],
            endorseControlNumber: [false],
            controlNumberLocation: [5],
            endorseOptionalMessage: [false],
            messageText: [''],
            messageTextLocation: [3],
            volumeId: ['', [Validators.required]],
            paddingLength: [8],
            continueFromPreviousControlNumber: [false],
            advancedEndorsementSetting: [null],
            prefixDelimiterValue: [''],
          },
          { validator: this.validateControlNumberAndEndorsementSettings }
        ),
      }),
      productionSettings: this.fb.group({
        fieldTemplateId: [4],
        filterOptions: this.fb.group({
          excludeProducedDocuments: [false],
          excludeNativeForRedactedDocuments: [false],
        }),
        savedSearchesForExpressions: [null],
        relativityFieldMappingTemplateId: [1, Validators.required],
        relativityFieldMappingTemplateName: [''],

        connector: this.fb.group({
          id: [null, Validators.required],
          name: ['', Validators.required],
          connectorPlatform: ['', Validators.required],
          userEnvironmentId: [null, Validators.required],
          workspaceId: [null, Validators.required],
          workspaceName: ['', Validators.required],
          baseAPIUrl: [''],
          connectorFileSharePath: [null, Validators.required],
        }),
      }),
      pdfServiceSettings: this.fb.group({
        pdfType: [0],
        pdfFamilyFileHandling: [0],
        pdfFileNamingConvention: [0],
      }),
      printServiceSettings: this.fb.group({
        binding: [''],
        threeRingBinderSize: [''],
        threeRingBinderColor: [''],
        printSet: [''],
        numberOfSets: [1],
        paperType: [''],
        paperSide: [''],
        documentSeparator: [false],
        printFamilyFileHandling: [''],
      }),
      thirdPartyBillingOption: this.fb.group({
        thirdPartyBillingEnabled: [false],
        company: [null],
        billingAddress: [null],
        billingCaseName: [null],
        contactPerson: [null],
        contactPhone: [null],
        contactEmail: [null],
      }),
      webURL: ['http://localhost/VenioWeb'],
      clientMatterNo: [''],
      createImage: [true],
      dataRetentionRequest: [0],
      editableCustomFieldList: [null],
      productionSourceId: [''],
      enableDiscoveryExceptionHandling: [true],
      autoQueueForEntityExtraction: [false],
    })
  }

  public nextStep(): void {
    if (!this.validateSettingsForm()) {
      return
    }
    // if creating new case or not overriding settings then no need to check for control number conflict
    if (
      !this.existingCase ||
      !this.settingsForm.get('overrideSetting').value ||
      this.currentStep !== 1
    ) {
      this.progressToNextStep()
    }
    // else call API to check for control number conflict
    else {
      this.isServiceTypeCaseLoading = true
      this.directExportFacade.checkIfControlNumberHasConflict(
        this.existingCaseId,
        this.getSettingsFromTheForm()
      )
    }
  }

  private progressToNextStep(): void {
    this.updateCurrentStep()
    const settingsInfo: SettingsInfo = _.cloneDeep(
      this.getSettingsFromTheForm()
    )
    if (this.existingCase) {
      settingsInfo.serviceRequestType =
        this.settingsForm?.value?.serviceRequestTypeExisting
    }
    this.processServiceInfoData(settingsInfo)
    this.directExportFacade.updateDefaultDataFromForm(settingsInfo)

    if (this.currentStep === 3) {
      this.finalizeStep(settingsInfo)
    }
    this.cdr.detectChanges()
  }

  private validateSettingsForm(): boolean {
    if (this.currentStep === 1) {
      const control = this.settingsForm.get('ServiceRequestType')
      control?.markAsTouched()
      control?.updateValueAndValidity({ onlySelf: true, emitEvent: true })
      if (this.settingsForm.invalid) {
        this.settingsForm.markAllAsTouched()
        this.expandPanelWithValidationError()
        this.cdr.detectChanges()
        return false
      }
    }
    return true
  }

  // Expands the first panel with validation errors
  private expandPanelWithValidationError(): void {
    // Check which panels have validation errors
    const panelsWithErrors = []

    // check if image conversion panel has validation errors
    if (!this.settingsForm['controls']?.imageConversionSettings?.valid) {
      panelsWithErrors.push('image-conversion')
    }

    // check if Control Number & Endorsement panel has validation errors
    if (
      !this.settingsForm['controls']?.controlNumberAndEndorsementSettings?.valid
    ) {
      panelsWithErrors.push('control-number-endorsement')
    }

    // check if Relativity Services panel has validation errors
    if (
      !this.settingsForm['controls']?.productionSettings['controls']?.connector
        ?.valid
    ) {
      panelsWithErrors.push('relativity-services')
    }

    const currentPanel = this.directExportCmp.currentExpandedPanel()
    // If the current panel has errors, do nothing (stay on the same panel)
    if (panelsWithErrors.includes(currentPanel)) {
      return
    }
    // If there are no errors in the current panel, expand the first panel with errors
    else if (panelsWithErrors.length > 0) {
      this.directExportCmp.onPanelToggle(panelsWithErrors[0], true)
      return
    }
  }

  #confirmControlNumberContinuation(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
      appendTo: this.content,
    })
    this.confirmationDialogRef.content.instance.title =
      'Control number conflict'
    this.confirmationDialogRef.content.instance.message =
      'There is conflict in control number. Do you want to continue with existing control number?'

    this.confirmationDialogRef.result
      .pipe(take(1), takeUntil(this.unsubscribed$))
      .subscribe((result) => {
        if (typeof result === 'boolean' && result === true) {
          this.isContinuedAfterControlNumberConflict = true
          this.progressToNextStep()
        }
      })
  }

  private showNotification(content: string, type: Type): void {
    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  private updateCurrentStep(): void {
    this.currentStep =
      this.currentStep < 3 ? this.currentStep + 1 : this.currentStep
  }

  private processServiceInfoData(settingsInfo: SettingsInfo): void {
    this.serviceInfoData = settingsInfo
    this.serviceInfoData.clientMatterNo = ''
  }

  private finalizeStep(settingsInfo: SettingsInfo): void {
    this.cdr.detectChanges()
    if (this.existingCase) {
      const payload = {
        projectId: this.existingCaseId,
        isExistingCase: true,
        overrideSettings: !!this.settingsForm.get('overrideSetting').value,
        settingId: -1,
        settingsInfo: this.removeEnsureDefaultKey(settingsInfo),
        afterConflict: this.isContinuedAfterControlNumberConflict,
      }

      const content = {
        selectedCase: payload,
        actionType: CommonActionTypes.DIRECT_EXPORT,
        isCaseActionClick: true,
      }

      this.#notifyParentApp(content)
    } else {
      this.handleNewCase(this.removeEnsureDefaultKey(settingsInfo))
    }
  }

  /**
   * Utility function to remove the `ensureDefault` key from an object recursively
   */
  public removeEnsureDefaultKey(data: any): any {
    if (Array.isArray(data)) {
      return data.map(this.removeEnsureDefaultKey)
    } else if (typeof data === 'object' && data !== null) {
      return Object.entries(data).reduce(
        (acc: Record<string, any>, [key, value]) => {
          if (key !== 'ensureDefaults') {
            acc[key] = this.removeEnsureDefaultKey(value) // Recursively process values
          }
          return acc
        },
        {}
      )
    }
    return data
  }

  #notifyParentApp(content: unknown): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content,
      } as MessageContent,
    })
  }

  private handleNewCase(settingsInfo: SettingsInfo): void {
    this.directExportFacade.createServiceTypeCase(settingsInfo)
    this.directExportFacade.isServiceTypeCreationLoading$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe({
        next: (res: boolean) => {
          this.isServiceTypeCaseLoading = res
          this.cdr.detectChanges()
        },
      })

    this.directExportFacade.selectServiceTypeCaseSuccess$
      .pipe(
        filter(
          (p) =>
            Array.isArray(p) &&
            p[0]?.ProjectId !== undefined &&
            p[0]?.ProjectId > 0
        ),
        map((p) => ({ project: p[0], serviceId: p[1] })),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: ({ project, serviceId }) => {
          this.showNotification('Service Type Case Created', {
            style: 'success',
          })
          const payload = {
            projectId: project.ProjectId,
            isExistingCase: false,
            overrideSettings: false,
            settingId: serviceId,
            settingsInfo: settingsInfo,
          }
          const content = {
            selectedCase: payload,
            actionType: CommonActionTypes.DIRECT_EXPORT,
            isCaseActionClick: true,
          }
          this.#notifyParentApp(content)
        },
      })
  }

  public previousStep(): void {
    this.currentStep =
      this.currentStep > 1 ? this.currentStep - 1 : this.currentStep
  }

  public setIsExistingCase(event: boolean): void {
    this.existingCase = event
    if (this.settingsForm && this.settingsForm.get('caseName')) {
      this.settingsForm.get('caseName').updateValueAndValidity() // Check if form and control exist
    }
  }

  /**
   * Validator to ensure project names are unique
   */
  private uniqueProjectNameValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null)
      }
      if (!this.existingCase) {
        const isNotUnique = this.caseList?.some(
          (project) =>
            project.projectName.toLowerCase() === control.value.toLowerCase()
        )
        return of(isNotUnique).pipe(
          map((result: boolean) =>
            result ? { notUniqueName: { value: control.value } } : null
          )
        )
      }
      return of(null)
    }
  }

  // Validator to check if the service request type is valid.
  private serviceRequestTypeValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (control.value === null || control.value === undefined) {
        return of({ invalidServiceRequestType: true })
      }

      const serviceType = this.serviceTypeList?.find(
        (val) => val?.serviceTypeId === control.value
      )
      return of(serviceType).pipe(
        map((result: any) =>
          !result
            ? { invalidServiceRequestType: { value: control.value } }
            : null
        )
      )
    }
  }

  private updateDefaultDataFromServerData(
    settingsInfo: SettingsInfo,
    addDataToExistingCase: any,
    overrideSettingsInfo: any,
    isContinuedAfterControlNumberConflict: any,
    serverData: any
  ): void {
    settingsInfo.searchTerm = serverData?.searchTerm ?? settingsInfo.searchTerm
    settingsInfo.tzTimeZone = serverData?.tzTimeZone ?? settingsInfo.tzTimeZone
    settingsInfo.enableNativeFileHandling =
      serverData?.enableNativeFileHandling ??
      settingsInfo.enableNativeFileHandling
    settingsInfo.serviceRequestType =
      serverData?.serviceRequestType ?? settingsInfo.serviceRequestType
    settingsInfo.exportTemplateName =
      serverData?.exportTemplateName ?? settingsInfo.exportTemplateName

    // Update nested objects in imageConversionOption
    const imageConversion =
      serverData?.imageConversionOption?.imageColorConversion
    settingsInfo.imageConversionOption.imageColorConversion.imageFileType =
      imageConversion?.imageFileType ??
      settingsInfo.imageConversionOption.imageColorConversion.imageFileType
    settingsInfo.imageConversionOption.imageColorConversion.pdfFiles =
      imageConversion?.pdfFiles ??
      settingsInfo.imageConversionOption.imageColorConversion.pdfFiles
    settingsInfo.imageConversionOption.imageColorConversion.powerpoint =
      imageConversion?.powerpoint ??
      settingsInfo.imageConversionOption.imageColorConversion.powerpoint

    settingsInfo.imageConversionOption.passwordList =
      serverData?.imageConversionOption?.passwordList ??
      settingsInfo.imageConversionOption.passwordList
    settingsInfo.imageConversionOption.deduplicationOption =
      serverData?.imageConversionOption?.deduplicationOption ??
      settingsInfo.imageConversionOption.deduplicationOption

    settingsInfo.imageConversionOption.csV_Excel_option =
      serverData?.imageConversionOption?.csV_Excel_option ??
      settingsInfo.imageConversionOption.csV_Excel_option
    settingsInfo.imageConversionOption.autoGenerateImagesAfterIngestion =
      serverData?.imageConversionOption?.autoGenerateImagesAfterIngestion ??
      settingsInfo.imageConversionOption.autoGenerateImagesAfterIngestion

    // Update controlNumberEndorsement with fallback
    settingsInfo.controlNumber_Endorsement.sortOrder =
      serverData?.controlNumber_Endorsement?.sortOrder ??
      settingsInfo.controlNumber_Endorsement.sortOrder
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.prefix =
      serverData?.controlNumber_Endorsement?.controlNumberSetting?.prefix ??
      settingsInfo.controlNumber_Endorsement.controlNumberSetting.prefix
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.prefixDelimiter =
      serverData?.controlNumber_Endorsement?.controlNumberSetting
        ?.prefixDelimiter ??
      settingsInfo.controlNumber_Endorsement.controlNumberSetting
        .prefixDelimiter
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.startNumber =
      serverData?.controlNumber_Endorsement?.controlNumberSetting
        ?.startNumber ??
      settingsInfo.controlNumber_Endorsement.controlNumberSetting.startNumber

    // Update additional fields based on server data or fall back to existing settingsInfo
    settingsInfo.productionOptions = {
      fieldTemplateId:
        serverData?.productionOptions?.fieldTemplateId ??
        settingsInfo.productionOptions?.fieldTemplateId,
      savedSearchesForExpressions:
        serverData?.productionOptions?.savedSearchesForExpressions ??
        settingsInfo.productionOptions?.savedSearchesForExpressions,
      filterOptions: {
        excludeProducedDocuments:
          serverData?.productionOptions?.filterOptions
            ?.excludeProducedDocuments ??
          settingsInfo.productionOptions?.filterOptions
            ?.excludeProducedDocuments,
        excludeNativeForRedactedDocuments:
          serverData?.productionOptions?.filterOptions
            ?.excludeNativeForRedactedDocuments ??
          settingsInfo.productionOptions?.filterOptions
            ?.excludeNativeForRedactedDocuments,
        ensureDefaults:
          settingsInfo.productionOptions?.filterOptions?.ensureDefaults,
      },
      ensureDefaults: settingsInfo.productionOptions?.ensureDefaults,
    }

    // Update top-level fields or retain existing values
    addDataToExistingCase =
      serverData?.addDataToExistingCase ?? addDataToExistingCase
    overrideSettingsInfo =
      serverData?.overrideSettingsInfo ?? overrideSettingsInfo
    isContinuedAfterControlNumberConflict =
      serverData?.isContinuedAfterControlNumberConflict ??
      isContinuedAfterControlNumberConflict
  }

  /**
   * Creates and returns the VODR SettingsInfo object from the user-selected values
   */
  private getSettingsFromTheForm(): SettingsInfo {
    const settingsInfo = new SettingsInfo()
    const vodSettings = new VODRSettings()

    this.updateDefaultDataFromServerData(
      settingsInfo,
      vodSettings.addDataToExistingCase,
      vodSettings.overrideSettingsInfo,
      this.isContinuedAfterControlNumberConflict,
      this.defaultSettingsInfo
    )
    this.getServiceSelectionSettingsFromTheForm(settingsInfo)
    this.getGeneralSettingsFromTheForm(settingsInfo)
    this.getImageConversionSettingsFromTheForm(settingsInfo)
    this.getControlSettingsFromTheForm(settingsInfo)
    this.getPdfSettingsFromTheForm(settingsInfo)
    this.getPrintSettingsFromTheForm(settingsInfo)
    this.getProductionSettingsFromTheForm(settingsInfo)
    return settingsInfo
  }

  /**
   * Prepares service selection page related options in the SettingsInfo from the user-selected form values.
   */
  private getServiceSelectionSettingsFromTheForm(
    settingsInfo: SettingsInfo
  ): void {
    const formValue = this.settingsForm.value

    settingsInfo.caseName = formValue?.caseName || settingsInfo.caseName

    const defaultServiceRequestType: number =
      typeof settingsInfo.serviceRequestType === 'number'
        ? settingsInfo.serviceRequestType
        : 0

    settingsInfo.serviceRequestType =
      formValue?.ServiceRequestType !==
      ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT
        ? Number(formValue?.ServiceRequestType)
        : this.settings && !_.isEmpty(this.settings)
        ? Number(this.settings.serviceRequestType)
        : defaultServiceRequestType

    settingsInfo.exportTemplateName =
      formValue?.ServiceRequestType !==
      ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT
        ? formValue?.exportTemplateName
        : this.settings && !_.isEmpty(this.settings)
        ? this.settings.exportTemplateName
        : settingsInfo.exportTemplateName

    settingsInfo.exportTemplateName =
      formValue?.exportTemplateName || settingsInfo.exportTemplateName

    settingsInfo.dataRetentionRequest =
      formValue?.dataRetentionType || settingsInfo.dataRetentionRequest

    settingsInfo.createImage =
      formValue?.createImages !== null
        ? formValue.createImages
        : settingsInfo.createImage
  }

  /**
   * Prepares general options in the SettingsInfo from the user-selected form values.
   */
  private getGeneralSettingsFromTheForm(settingsInfo: SettingsInfo): void {
    const generalFormValue: any = (<FormGroup>(
      this.settingsForm.controls.generalSettings
    ))?.getRawValue()

    settingsInfo.enableDiscoveryExceptionHandling =
      generalFormValue?.discoveryExceptionHandling ??
      settingsInfo.enableDiscoveryExceptionHandling

    settingsInfo.indexOnlyCase =
      generalFormValue?.nativeFileHandling ?? settingsInfo.indexOnlyCase

    settingsInfo.editableCustomFieldList =
      this.settings && !_.isEmpty(this.settings)
        ? this.settings.editableCustomFieldList
        : settingsInfo.editableCustomFieldList || []
  }

  /**
   * Prepares image conversion related options.
   */
  private getImageConversionSettingsFromTheForm(
    settingsInfo: SettingsInfo
  ): void {
    const generalFormValue: any = (<FormGroup>(
      this.settingsForm.controls.generalSettings
    )).getRawValue()
    const imageFormValue: any = (<FormGroup>(
      this.settingsForm.controls.imageConversionSettings
    )).getRawValue()

    settingsInfo.imageConversionOption = {
      imageType:
        imageFormValue?.imageType ??
        settingsInfo.imageConversionOption?.imageType ??
        0,
      deduplicationOption:
        generalFormValue?.deduplicationOption ??
        settingsInfo.imageConversionOption?.deduplicationOption ??
        new ImageConversionOption().deduplicationOption,
      csV_Excel_option:
        generalFormValue?.csvExcelHandling ??
        settingsInfo.imageConversionOption?.csV_Excel_option ??
        new ImageConversionOption().csV_Excel_option,
      autoGenerateImagesAfterIngestion:
        generalFormValue?.autoGenerateImagesAfterIngestion ??
        settingsInfo.imageConversionOption?.autoGenerateImagesAfterIngestion ??
        new ImageConversionOption().autoGenerateImagesAfterIngestion,

      imageColorConversion: {
        imageFileType:
          imageFormValue?.imageColorConversion?.imageFileType ??
          settingsInfo.imageConversionOption?.imageColorConversion
            ?.imageFileType,
        pdfFiles:
          imageFormValue?.imageColorConversion?.pdfFiles ??
          settingsInfo.imageConversionOption?.imageColorConversion?.pdfFiles,
        powerpoint:
          imageFormValue?.imageColorConversion?.powerpoint ??
          settingsInfo.imageConversionOption?.imageColorConversion?.powerpoint,
        ensureDefaults:
          settingsInfo.imageConversionOption?.imageColorConversion
            ?.ensureDefaults,
      },
      passwordList:
        this.getPasswordsFromTheForm(imageFormValue?.passwordList) ||
        settingsInfo.imageConversionOption?.passwordList ||
        new ImageConversionOption().passwordList,
      ensureDefaults: settingsInfo.imageConversionOption?.ensureDefaults,
    }

    settingsInfo.tzTimeZone =
      generalFormValue?.timeZone || settingsInfo.tzTimeZone
  }

  /**
   * Prepares Control Settings and Endorsement related options in the SettingsInfo from the user-selected form values.
   */
  private getControlSettingsFromTheForm(settingsInfo: SettingsInfo): void {
    const formValue: any = (<FormGroup>(
      this.settingsForm.controls.controlNumberAndEndorsementSettings
    ))?.getRawValue()

    const defaultControlNumberEndorsement = new ControlNumberEndorsement()
    const defaultControlNumberSetting = new ControlNumberSetting()

    settingsInfo.controlNumber_Endorsement = {
      sortOrder:
        formValue?.sortOrder ??
        settingsInfo.controlNumber_Endorsement?.sortOrder ??
        defaultControlNumberEndorsement.sortOrder,
      exportLocation:
        formValue?.exportLocation ??
        settingsInfo.controlNumber_Endorsement?.exportLocation ??
        defaultControlNumberEndorsement.exportLocation,
      controlNumberSetting: {
        prefix:
          formValue?.ControlNumberSetting?.controlNumberPrefix ??
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.prefix ??
          defaultControlNumberSetting.prefix,
        paddingLength:
          formValue?.ControlNumberSetting?.paddingLength ??
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.paddingLength ??
          defaultControlNumberSetting.paddingLength,
        advancedEndorsementSetting:
          formValue?.advancedEndorsementSetting ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.advancedEndorsementSetting ||
          defaultControlNumberSetting.advancedEndorsementSetting,
        controlNumberLocation:
          formValue?.ControlNumberSetting?.controlNumberLocation ??
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.controlNumberLocation ??
          defaultControlNumberSetting.controlNumberLocation,
        prefixDelimiter:
          formValue?.ControlNumberSetting?.controlNumberDelimiter ??
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.prefixDelimiter ??
          defaultControlNumberSetting.prefixDelimiter,
        startNumber:
          +formValue?.ControlNumberSetting?.controlNumberStartingNumber ||
          +settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.startNumber ||
          +defaultControlNumberSetting.startNumber,
        endorseControlNumber:
          formValue?.ControlNumberSetting?.endorseControlNumber ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.endorseControlNumber ||
          defaultControlNumberSetting.endorseControlNumber,
        endorseOptionalMessage:
          formValue?.ControlNumberSetting?.endorseOptionalMessage ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.endorseOptionalMessage ||
          defaultControlNumberSetting.endorseOptionalMessage,
        messageText:
          formValue?.ControlNumberSetting?.messageText ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.messageText ||
          defaultControlNumberSetting.messageText,
        optionalMessageLocation:
          formValue?.ControlNumberSetting?.messageTextLocation ??
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.optionalMessageLocation ??
          defaultControlNumberSetting.optionalMessageLocation,
        volumnId:
          formValue?.ControlNumberSetting?.volumeId ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.volumnId ||
          defaultControlNumberSetting.volumnId,
        continueFromPreviousControlNumber:
          formValue?.ControlNumberSetting?.continueFromPreviousControlNumber ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.continueFromPreviousControlNumber ||
          defaultControlNumberSetting.continueFromPreviousControlNumber,
        prefixDelimiterValue:
          formValue?.ControlNumberSetting?.prefixDelimiterValue ||
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.prefixDelimiterValue ||
          defaultControlNumberSetting.prefixDelimiterValue,
        ensureDefaults:
          settingsInfo.controlNumber_Endorsement?.controlNumberSetting
            ?.ensureDefaults,
      },
      ensureDefaults: settingsInfo.controlNumber_Endorsement?.ensureDefaults,
    }
  }

  /**
   * Prepares PDF service-related options in the SettingsInfo from the user-selected form values.
   */
  private getPdfSettingsFromTheForm(settingsInfo: SettingsInfo): void {
    const formValue: any = (<FormGroup>(
      this.settingsForm.controls.pdfServiceSettings
    ))?.getRawValue()

    const defaultPdfServiceOption = new PDFServiceOption()

    settingsInfo.pdfServiceOption = {
      pdfType:
        formValue?.pdfType ||
        settingsInfo.pdfServiceOption?.pdfType ||
        defaultPdfServiceOption.pdfType,
      pdfFamilyFileHandling:
        formValue?.pdfFamilyFileHandling ||
        settingsInfo.pdfServiceOption?.pdfFamilyFileHandling ||
        defaultPdfServiceOption.pdfFamilyFileHandling,
      pdfFileNamingConvention:
        formValue?.pdfFileNamingConvention ||
        settingsInfo.pdfServiceOption?.pdfFileNamingConvention ||
        defaultPdfServiceOption.pdfFileNamingConvention,
      ensureDefaults: settingsInfo.pdfServiceOption?.ensureDefaults,
    }
  }

  /**
   * Prepares PRINT service-related options in the SettingsInfo from the user-selected form values.
   */
  private getPrintSettingsFromTheForm(settingsInfo: SettingsInfo): void {
    const formValue: any = (<FormGroup>(
      this.settingsForm.controls.printServiceSettings
    ))?.getRawValue()

    const defaultPrintServiceOption = new PrintServiceOption()
    const defaultPrintBinding = new PrintBinding()
    const defaultPrintSet = new PrintSet()

    settingsInfo.printServiceOption = {
      documentSeparator:
        formValue?.documentSeparator ||
        settingsInfo.printServiceOption?.documentSeparator ||
        defaultPrintServiceOption.documentSeparator,
      familyFileHandling:
        formValue?.printFamilyFileHandling ||
        settingsInfo.printServiceOption?.familyFileHandling ||
        defaultPrintServiceOption.familyFileHandling,
      paperSide:
        formValue?.paperSide ||
        settingsInfo.printServiceOption?.paperSide ||
        defaultPrintServiceOption.paperSide,
      paperType:
        formValue?.paperType ||
        settingsInfo.printServiceOption?.paperType ||
        defaultPrintServiceOption.paperType,
      printBinding: {
        bindingType:
          formValue?.binding ||
          settingsInfo.printServiceOption?.printBinding?.bindingType ||
          defaultPrintBinding.bindingType,
        binderColor:
          formValue?.threeRingBinderColor ||
          settingsInfo.printServiceOption?.printBinding?.binderColor ||
          defaultPrintBinding.binderColor,
        binderSize:
          formValue?.threeRingBinderSize ||
          settingsInfo.printServiceOption?.printBinding?.binderSize ||
          defaultPrintBinding.binderSize,
        ensureDefaults:
          settingsInfo.printServiceOption?.printBinding?.ensureDefaults,
      },
      printSet: {
        numberOfSetValue:
          +formValue?.numberOfSets ||
          +settingsInfo.printServiceOption?.printSet?.numberOfSetValue ||
          +defaultPrintSet.numberOfSetValue,
        printSetOption:
          formValue?.printSet ||
          settingsInfo.printServiceOption?.printSet?.printSetOption ||
          defaultPrintSet.printSetOption,
        ensureDefaults:
          settingsInfo.printServiceOption?.printSet?.ensureDefaults,
      },
      ensureDefaults: settingsInfo.printServiceOption?.ensureDefaults,
    }
  }

  /**
   * Prepares Production related options in the SettingsInfo from the user-selected form values.
   */
  private getProductionSettingsFromTheForm(settingsInfo: SettingsInfo): void {
    const formValue = (<FormGroup>(
      this.settingsForm.controls.productionSettings
    ))?.getRawValue()

    const defaultProductionOptions = new ProductionOptions()
    const defaultFilterOptions = new FilterOptions()

    settingsInfo.productionOptions = {
      fieldTemplateId:
        formValue?.fieldTemplateId ||
        settingsInfo.productionOptions?.fieldTemplateId ||
        defaultProductionOptions.fieldTemplateId,
      savedSearchesForExpressions:
        formValue?.savedSearches ||
        settingsInfo.productionOptions?.savedSearchesForExpressions ||
        defaultProductionOptions.savedSearchesForExpressions,
      relativityFieldMappingTemplateId:
        formValue?.relativityFieldMappingTemplateId ||
        settingsInfo.productionOptions?.relativityFieldMappingTemplateId ||
        defaultProductionOptions.relativityFieldMappingTemplateId,
      connector: {
        id:
          formValue?.connector?.id ||
          settingsInfo.productionOptions?.connector?.id ||
          defaultProductionOptions.connector?.id,
        name:
          formValue?.connector?.name ||
          settingsInfo.productionOptions?.connector?.name ||
          defaultProductionOptions.connector?.name,
        connectorPlatform:
          formValue?.connector?.connectorPlatform ||
          settingsInfo.productionOptions?.connector?.connectorPlatform ||
          defaultProductionOptions.connector?.connectorPlatform,
        userEnvironmentId:
          formValue?.connector?.userEnvironmentId ||
          settingsInfo.productionOptions?.connector?.userEnvironmentId ||
          defaultProductionOptions.connector?.userEnvironmentId,
        workspaceId:
          formValue?.connector?.workspaceId ||
          settingsInfo.productionOptions?.connector?.workspaceId ||
          defaultProductionOptions.connector?.workspaceId,
        workspaceName:
          formValue?.connector?.workspaceName ||
          settingsInfo.productionOptions?.connector?.workspaceName ||
          defaultProductionOptions.connector?.workspaceName,
        connectorFileSharePath:
          formValue?.connector?.connectorFileSharePath ||
          settingsInfo.productionOptions?.connector?.connectorFileSharePath ||
          defaultProductionOptions.connector?.connectorFileSharePath,
        baseAPIUrl:
          formValue?.connector?.baseAPIUrl ||
          settingsInfo.productionOptions?.connector?.baseAPIUrl ||
          defaultProductionOptions.connector?.baseAPIUrl,
      },
      filterOptions: {
        excludeProducedDocuments:
          formValue?.excludeProducedDocuments ||
          settingsInfo.productionOptions?.filterOptions
            ?.excludeProducedDocuments ||
          defaultFilterOptions.excludeProducedDocuments,
        excludeNativeForRedactedDocuments:
          formValue?.excludeNativeForRedactedDocuments ||
          settingsInfo.productionOptions?.filterOptions
            ?.excludeNativeForRedactedDocuments ||
          defaultFilterOptions.excludeNativeForRedactedDocuments,
        ensureDefaults:
          settingsInfo.productionOptions?.filterOptions?.ensureDefaults,
      },
      ensureDefaults: settingsInfo.productionOptions?.ensureDefaults,
    }
  }

  /**
   * Gets the service type value from the form and converts it into the format that can be used to set into SettingsInfo object.
   * @param serviceRequestType ServiceRequestType
   */
  private getServiceTypeFromTheForm(
    serviceRequestType: ServiceRequestType
  ): ServiceType {
    let serviceType = null
    if (
      serviceRequestType &&
      !StringUtils.isNullOrEmpty(serviceRequestType.serviceType)
    ) {
      for (const [key, value] of ServiceTypeDescription.entries()) {
        if (value === serviceRequestType.serviceType) {
          serviceType = key
          break
        }
      }
    }
    return serviceType
  }

  /**
   * Form contains passwords separated by newline character. This function converts it into an array of password strings.
   * @param formValue
   */
  private getPasswordsFromTheForm(formValue: string): string[] {
    if (StringUtils.isNullOrEmpty(formValue)) {
      return []
    }
    return formValue.split('\n')
  }

  private getServiceTypeDescriptions(
    serviceTypeNames: {
      serviceTypeDisplayName: string
      serviceTypeName: string
    }[]
  ): {
    serviceTypeId: number
    serviceTypeDisplayName: string
    serviceTypeName: string
  }[] {
    const descriptions: {
      serviceTypeId: number
      serviceTypeDisplayName: string
      serviceTypeName: string
    }[] = []

    serviceTypeNames.forEach((serviceType) => {
      const matchingServiceConstantKey = Object.keys(ServiceTypeConstants).find(
        (key) =>
          ServiceTypeConstants[key as keyof typeof ServiceTypeConstants] ===
          serviceType?.serviceTypeName
      )
      if (matchingServiceConstantKey) {
        const serviceTypeEnum =
          ServiceType[matchingServiceConstantKey as keyof typeof ServiceType]
        if (serviceTypeEnum !== undefined) {
          const serviceTypeDescription =
            ServiceTypeDescription.get(serviceTypeEnum)
          if (serviceTypeDescription) {
            descriptions.push({
              serviceTypeId: serviceTypeEnum,
              serviceTypeDisplayName: serviceType.serviceTypeDisplayName,
              serviceTypeName: serviceType.serviceTypeName,
            })
          }
        }
      }
    })
    return descriptions
  }

  /**
   * Validates general settings form.
   * @param fg Form Group - serviceSettingFormGroup > generalSettings
   */
  private validateGeneralSettingsForm: ValidatorFn = (
    fg: FormGroup
  ): ValidationErrors | null => {
    const validationErrors: Record<string, boolean> = {}

    if (Object.keys(validationErrors).length === 0) {
      return null
    }
    return validationErrors
  }

  /**
   * Validates control number and endorsement settings form.
   * @param fg Form Group - serviceSettingFormGroup > controlNumberAndEndorsementSettings
   */
  private validateControlNumberAndEndorsementSettings: ValidatorFn = (
    fg: FormGroup
  ): ValidationErrors | null => {
    const pattern = /^[^\\\\/:*?"<>|]+$/

    const prefixControl = fg.get('controlNumberPrefix')
    const volumeIdControl = fg.get('volumeId')
    const startingNumberControl = fg.get('controlNumberStartingNumber')

    if (!prefixControl || !volumeIdControl || !startingNumberControl) {
      return null
    }

    // Prefix Validation
    const prefix = prefixControl.value
    if (prefix && (prefix.trim() === '' || !pattern.test(prefix))) {
      prefixControl.setErrors({ invalidPrefix: true })
    } else if (prefixControl.hasError('invalidPrefix')) {
      prefixControl.setErrors(null) // Clear only if this was previously invalid
    }
    // Volume ID Validation
    let volumeId = volumeIdControl.value
    if (!volumeId || volumeId.trim() === '') {
      volumeIdControl.setErrors({ requiredVolumeId: true })
    } else {
      volumeId = volumeId.replace(/\.+$/, '')
      if (!pattern.test(volumeId)) {
        volumeIdControl.setErrors({ invalidVolumeId: true })
      } else if (
        volumeIdControl.hasError('invalidVolumeId') ||
        volumeIdControl.hasError('requiredVolumeId')
      ) {
        volumeIdControl.setErrors(null) // Clear if previously had errors
      }
    }

    // Starting Number Validation
    const startingNumber = startingNumberControl.value
    if (
      !this.settings?.controlNumber_Endorsement?.controlNumberSetting
        ?.continueFromPreviousControlNumber
    ) {
      if (
        startingNumber === undefined ||
        startingNumber === null ||
        startingNumber.toString().trim() === ''
      ) {
        startingNumberControl.setErrors({ requiredStartingNumber: true })
      } else if (isNaN(startingNumber) || startingNumber < 0) {
        startingNumberControl.setErrors({ invalidStartingNumber: true })
      } else {
        startingNumberControl.setErrors(null)
      }
    }

    // Return null if no errors are set at the group level
    const anyChildInvalid =
      prefixControl.invalid ||
      volumeIdControl.invalid ||
      (!this.settings?.controlNumber_Endorsement?.controlNumberSetting
        ?.continueFromPreviousControlNumber &&
        startingNumberControl.invalid)

    return anyChildInvalid ? { groupInvalid: true } : null
  }

  public ngOnDestroy(): void {
    this.directExportFacade.resetDirectExportState('defaultData')
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
