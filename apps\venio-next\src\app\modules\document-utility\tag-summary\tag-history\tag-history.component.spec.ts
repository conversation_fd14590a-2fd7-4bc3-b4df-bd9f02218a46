import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagHistoryComponent } from './tag-history.component'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagHistoryComponent', () => {
  let component: TagHistoryComponent
  let fixture: ComponentFixture<TagHistoryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagHistoryComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagHistoryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
