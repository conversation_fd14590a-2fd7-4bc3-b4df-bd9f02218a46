import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ActivatedRoute } from '@angular/router'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  BreadCrumb,
  DocumentsFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { filter, Subject, takeUntil } from 'rxjs'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

@Component({
  selector: 'venio-send-to-analyze',
  standalone: true,
  imports: [CommonModule],
  template: '',
  styles: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SendToAnalyzeComponent implements OnInit, OnD<PERSON>roy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly documentsFacade = inject(DocumentsFacade)

  private readonly searchFacade = inject(SearchFacade)

  private readonly startupFacade = inject(StartupsFacade)

  private readonly breadCrumbFacade = inject(BreadcrumbFacade)

  private readonly searchDupOption = toSignal(
    this.searchFacade.getSearchDupOption$
  )

  private readonly searchTempTable = toSignal(
    this.searchFacade.getSearchTempTables$
  )

  private readonly getSearchInitialParameters = toSignal(
    this.searchFacade.getSearchInitialParameters$
  )

  private readonly breadCrumbStacks = toSignal(
    this.breadCrumbFacade.selectBreadcrumbStack$
  )

  private readonly completeBreadcrumbSyntax = toSignal(
    this.breadCrumbFacade.selectCompleteBreadcrumbSyntax$
  )

  private readonly selectedMediaIds = toSignal(
    this.startupFacade.getSelectedMediaScope$
  )

  private readonly includeFamilySearch = toSignal(
    this.searchFacade.getIncludePC$
  )

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    // setup menu select event
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.SEND_TO_ANALYZE),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#sendToAnalyze()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetEvents()
  }

  #sendToAnalyze(): void {
    const breadcrumb = this.#getBreadcrumbsInOldVodFormat()
    const searchFormValues = this.getSearchInitialParameters()

    // payload sent to old venio with parameters isSendToAnalyze, projectId, searchQueryModel
    const payload = {
      isSendToAnalyze: true,
      projectId: this.projectId,
      searchQueryModel: {
        searchQuery:
          this.completeBreadcrumbSyntax() || searchFormValues.searchExpression,
        sourceModule: 1, // 1 is for review module
        searchInputs: breadcrumb.map((q, index) => ({
          expression: q.query,
          displayText: q.filterText ?? q.query,
          documentCounts:
            breadcrumb.length === index + 1
              ? this.getSearchInitialParameters()?.totalHitCount
              : q.docCount ?? 0,
          isInitialSearch: q.isInitialSearch,
          isFilterSearch: q.isFilterSearch,
        })),
        searchParameters: {
          error: null,
          searchResultIntialParameters: null,
          tempTables: this.searchTempTable(),
        },
        searchDuplicateOption: this.searchDupOption(),
        mediaList: this.selectedMediaIds()?.map((mid) => mid.toString()),
        includeFamilySearch: this.includeFamilySearch(),
      },
    }

    this.#notifyParentApp(payload)

    // stop showing search loading indicator
    this.searchFacade.IsSearchLoading(false)
    this.#resetEvents()
  }

  // transforms the breadcrumb to old vod format.
  // only to be used in old analyze page.
  // will be unnecessary in new analyze page and can be removed
  #getBreadcrumbsInOldVodFormat(): BreadCrumb[] {
    const stack = this.breadCrumbStacks()
    const breadcrumbs: BreadCrumb[] = []
    let index = 0
    stack.forEach((s) => {
      if (
        s.conditionType === 'group' && //the actual condition is present in group conditionType
        s.checked && //check if enabled in breadcrumbs
        s.groupStackType &&
        s.groupStackType.toLocaleLowerCase().trim() !== 'view search' //check if it is initial search and exclude it
      ) {
        index++
        let mergedQueryCondition: string

        //check if the condition is in children (usually for field search)
        if (s.children?.length > 0) {
          mergedQueryCondition = s.children[0].conditions
            .map((c) => c.conditionSyntax)
            .join(' AND ')
        } else {
          mergedQueryCondition = s.conditions
            .map((c) => c.conditionSyntax)
            .join(' AND ')
        }

        breadcrumbs.push({
          query: mergedQueryCondition,
          filterText: mergedQueryCondition,
          itemIndex: index,
          isFilterSearch: true,
        })
      }
    })
    return breadcrumbs
  }

  #notifyParentApp(content: unknown): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content,
      } as MessageContent,
    })
  }

  #resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }
}
