import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule, ListItemModel } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import {
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'
import { CaseLandingUiGraphComponent } from '../case-landing-ui-graph/case-landing-ui-graph.component'
import { CasemulticheckFilterComponent } from '../case-multicheck-filter/casemulticheck-filter.component'
import { chevronDownIcon } from '@progress/kendo-svg-icons'

export interface Batch {
  id: number
  name: string
  client: string
  batchName: string
  status: string
  progress: number
  createdBy: string
  createdOn: Date
}

interface CustomListItemModel extends ListItemModel {
  svgPath: string
}

@Component({
  selector: 'venio-case-landing-review-set',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    UiPaginationModule,
    DropDownsModule,
    SvgLoaderDirective,
    ProgressBarModule,
    DynamicHeightDirective,
    CaseLandingUiGraphComponent,
    LoaderModule,
    CasemulticheckFilterComponent,
    DialogsModule,
  ],
  templateUrl: './case-landing-review-set.component.html',
  styleUrl: './case-landing-review-set.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseLandingReviewSetComponent implements OnInit {
  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public range = { start: null, end: null }

  public dummyDropdownData = [
    { text: 'Item 1' },
    { text: 'Item 2' },
    { text: 'Item 3' },
  ]

  public batches: Batch[] = []

  public label: LabelSettings = {
    visible: true,
    format: 'percent',
    position: 'end',
  }

  public batchDropdown: CustomListItemModel[] = [
    {
      text: 'Reassign',
      svgPath: 'assets/svg/icon-reassign-shuffle.svg',
    },
    {
      text: 'Lock',
      svgPath: 'assets/svg/icon-lock-style.svg',
    },
    {
      text: 'View',
      svgPath: 'assets/svg/icon-dark-eye.svg',
    },
  ]

  public icons = {
    chevronDownIcon: chevronDownIcon,
  }

  public ngOnInit(): void {
    this.generateDummyData()
  }

  private generateDummyData(): void {
    const clients = [
      'John Vs State Of Florida',
      'Apple Vs Samsung',
      'Case Related Melissa',
      'Case 4',
      'Case 5',
    ]
    const statuses = ['INPROGRESS', 'COMPLETED', 'PENDING']
    const createdBy = 'Admin'

    for (let i = 1; i <= 45; i++) {
      this.batches.push({
        id: i,
        name: `Set ${i}`,
        client: clients[i % clients.length],
        batchName: `Batch ${i.toString().padStart(4, '0')}`,
        status: statuses[i % statuses.length],
        progress: Math.floor(Math.random() * 100),
        createdBy: createdBy,
        createdOn: new Date('2024-07-06T10:47:53'),
      })
    }
  }
}
