import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { WindowCloseActionDirective } from '@progress/kendo-angular-dialog'
import { ReviewSetEntry } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-reviewset-detail-view-header',
  standalone: true,
  imports: [CommonModule, ButtonComponent, WindowCloseActionDirective],
  templateUrl: './reviewset-detail-view-header.component.html',
  styleUrl: './reviewset-detail-view-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewHeaderComponent {
  public readonly selectedReviewSetEntry = input.required<ReviewSetEntry>()

  public readonly dialogCloseAction = output()
}
