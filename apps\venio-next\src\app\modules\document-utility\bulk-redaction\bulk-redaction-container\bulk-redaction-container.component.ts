import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { DocumentsFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { filter, Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-bulk-redaction-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './bulk-redaction-container.component.html',
  styleUrl: './bulk-redaction-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkRedactionContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.BULK_REDACT),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('../bulk-redaction-dialog/bulk-redaction-dialog.component').then(
      (d) => {
        // reset the loading indicator
        this.#resetMenuLoadingState()

        // launch the dialog
        this.#launchDialogContent(d.BulkRedactionDialogComponent)

        // once the dialogRef instance is created
        this.#handleEditDialogCloseEvent()
      }
    )
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      height: '90vh',
      width: '80%',
      maxWidth: '1100px',
      maxHeight: '1100px',
      minHeight: '500px',
      minWidth: '90vw',
    })
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
