import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchLogHistoryComponent } from './search-log-history.component'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchLogHistoryComponent', () => {
  let component: SearchLogHistoryComponent
  let fixture: ComponentFixture<SearchLogHistoryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchLogHistoryComponent, BrowserAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchLogHistoryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
