import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ProgressBarModule,
  LabelSettings,
  ProgressColor,
  LabelFn,
} from '@progress/kendo-angular-progressbar'
import { ChartsModule } from '@progress/kendo-angular-charts'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-reprocess-status',
  standalone: true,
  imports: [
    CommonModule,
    ProgressBarModule,
    ChartsModule,
    LayoutModule,
    ButtonsModule,
    IconsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './reprocess-status.component.html',
  styleUrl: './reprocess-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReprocessStatusComponent {
  public progressData = [
    { value: 50, label: 'Ingestion' },
    { value: 100, label: 'Indexing' },
    { value: 70, label: 'Email Analysis' },
    { value: 90, label: 'Language Identification' },
  ]

  public colors: ProgressColor[] = [
    {
      from: 0,
      to: 64,
      color: '#FFBC3E',
    },
    {
      from: 65,
      to: 99,
      color: '#1DBADC',
    },
    {
      from: 100,
      to: 100,
      color: '#9BD2A7',
    },
  ]

  public showCharts = false

  public progressValue1 = 100

  public progressValue2 = 40

  public progressMax = 100

  public progressLabel: LabelSettings = {}

  public progressStyles: { [key: string]: string } = {
    background: '',
  }

  constructor() {
    this.progressLabel = {
      format: this.formatter,
      position: 'end',
    }
  }

  public formatter: LabelFn = (value: number): string => {
    return `${value}/${this.progressMax}`
  }

  public getProgressBarStyle(value: number): { [key: string]: string } {
    let color = ''
    if (value <= 50) {
      color = '#FFBB12'
    } else if (value <= 75) {
      color = '#1DBADC'
    } else {
      color = '#9BD2A7'
    }
    return { background: color }
  }
}
