import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagSummaryContainerComponent } from './tag-summary-container.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagSummaryContainerComponent', () => {
  let component: TagSummaryContainerComponent
  let fixture: ComponentFixture<TagSummaryContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, TagSummaryContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagSummaryContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
