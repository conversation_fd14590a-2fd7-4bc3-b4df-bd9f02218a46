import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  runInInjectionContext,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { chevronDownIcon, eyeIcon } from '@progress/kendo-svg-icons'
import {
  combineLatest,
  filter,
  from,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs'
import { UserFacade } from '@venio/data-access/common'
import { ReportsFacade } from '@venio/data-access/reports'
import { SelectionRange } from '@progress/kendo-angular-dateinputs'
import dayjs from 'dayjs'
import { UserModel } from '@venio/shared/models/interfaces'
import { AiFacade, EdaiStatusFilterCriteria } from '@venio/data-access/ai'
import { ActivatedRoute } from '@angular/router'
import {
  FilterDirective,
  ItemTemplateDirective,
  MultiSelectComponent,
} from '@progress/kendo-angular-dropdowns'
import { KENDO_COMMON } from '@progress/kendo-angular-common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { ReportDatePickerComponent } from '../../reports/tabular/report-date-picker/report-date-picker.component'
import { EdaiStatusGridComponent } from '../edai-status-grid/edai-status-grid.component'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CaseConvertorService, DebounceTimer } from '@venio/util/utilities'
import { PageArgs } from '@venio/ui/pagination'
import { debounce } from 'lodash'
import { map } from 'rxjs/operators'
function equality(a: any, b: any): boolean {
  return JSON.stringify(a) === JSON.stringify(b)
}
@Component({
  selector: 'venio-edai-status',
  standalone: true,
  imports: [
    CommonModule,
    MultiSelectComponent,
    FilterDirective,
    KENDO_COMMON,
    ButtonComponent,
    ItemTemplateDirective,
    TooltipDirective,
    ReportDatePickerComponent,
    EdaiStatusGridComponent,
    SvgLoaderDirective,
  ],
  templateUrl: './edai-status.component.html',
  styleUrl: './edai-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiStatusComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  protected readonly downIcon = chevronDownIcon

  public readonly eyeIcon = eyeIcon

  public readonly pagingInfo = signal<PageArgs>(
    {
      pageSize: 100,
      pageNumber: 1,
    },
    { equal: equality }
  )

  private readonly userFacade = inject(UserFacade)

  private readonly reportFacade = inject(ReportsFacade)

  private readonly aiFacade = inject(AiFacade)

  private readonly injector = inject(Injector)

  private activatedRoute = inject(ActivatedRoute)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public selectedDateRange = signal<SelectionRange>(
    {
      end: undefined,
      start: undefined,
    },
    { equal: equality }
  )

  public readonly selectedUser = signal<number[]>(undefined, {
    equal: equality,
  })

  public readonly selectedStatus = signal<number[]>([0, 1, 2], {
    equal: equality,
  })

  public readonly userList = signal<UserModel[]>([])

  public readonly statusList = signal<Array<{ label: string; value: number }>>([
    {
      label: 'Not Started',
      value: 0,
    },
    {
      label: 'In Progress',
      value: 1,
    },
    {
      label: 'Completed',
      value: 2,
    },
  ])

  private getFormattedDate(): { start: string; end: string } {
    const { start, end } = this.selectedDateRange()
    return {
      start: start ? dayjs(start).format('MM-DD-YYYY') : null,
      end: end ? dayjs(end).format('MM-DD-YYYY') : null,
    }
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public ngOnInit(): void {
    this.#selectUserList()
    this.#fetchUserList()
    this.#setSelectedDateRange()
  }

  public ngAfterViewInit(): void {
    this.#handleFilterCriteriaChange()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetDateRange()
  }

  /**
   * Handles the page change event see `#handleFilterCriteriaChange` which triggers the fetch
   * @param {PageArgs} args - page arguments
   * @returns {void}
   */
  public pageChange(args: PageArgs): void {
    this.pagingInfo.update((prev) => ({
      ...prev,
      pageSize: args.pageSize,
      pageNumber: args.pageNumber,
    }))
  }

  @DebounceTimer(100)
  public refreshStatusList(): void {
    const { pageSize, pageNumber } = this.pagingInfo()
    this.#fetchEdaiStatusResults({
      pageNumber,
      pageSize,
      userIds: this.selectedUser() || [],
      statuses: this.selectedStatus() || [],
      startDate: this.getFormattedDate().start,
      endDate: this.getFormattedDate().end,
    })
  }

  #resetDateRange(): void {
    this.reportFacade.resetReportState(['dateRange'])
  }

  #handleFilterCriteriaChange(): void {
    runInInjectionContext(this.injector, () => {
      const debouncedFetch = debounce((criteria: any) => {
        const { pagingInfo, dateRange, selectedUser, statuses } = criteria
        const { pageSize, pageNumber } = pagingInfo
        const { start, end } = dateRange
        console.log(this.userList())
        const userIds =
          selectedUser?.length > 0
            ? selectedUser
            : this.userList()
                .filter((u) => u.userId > 0)
                .map((u) => u.userId)

        this.#fetchEdaiStatusResults({
          pageNumber,
          pageSize,
          startDate: start,
          endDate: end,
          userIds,
          statuses,
        })
      }, 300)

      return effect(() => {
        const pagingInfo = this.pagingInfo()
        const dateRange = this.getFormattedDate()
        const selectedUser = this.selectedUser()
        const statuses = this.selectedStatus()

        const criteria = {
          pagingInfo,
          dateRange,
          selectedUser,
          statuses,
        }
        debouncedFetch(criteria)
      })
    })
  }

  #fetchEdaiStatusResults(filterCriteria?: EdaiStatusFilterCriteria): void {
    this.aiFacade.fetchEdaiStatusResults(this.projectId, {
      ...filterCriteria,
    })
  }

  #fetchUserList(): void {
    this.userFacade.fetchUserList()
  }

  #setSelectedDateRange(): void {
    this.reportFacade.selectSelectedDateRange$
      .pipe(
        filter((range) => Boolean(range)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((range) =>
        this.selectedDateRange.update((prev) => ({
          ...prev,
          start: range.start,
          end: range.end,
        }))
      )
  }

  #selectUserList(): void {
    combineLatest([
      this.userFacade.selectUserListSuccessResponse$,
      this.userFacade.selectUserListErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        switchMap(([success, error]) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData = camelCaseConvertorService.convertToCase<any>(
            success,
            'camelCase'
          )
          return from(convertedData).pipe(map((data) => ({ data, error })))
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(({ data, error }) => {
        this.userList.set(data?.data || [])
      })
  }
}
