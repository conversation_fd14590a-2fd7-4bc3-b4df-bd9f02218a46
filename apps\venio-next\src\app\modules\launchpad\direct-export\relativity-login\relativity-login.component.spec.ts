import { ComponentFixture, TestBed } from '@angular/core/testing'
import { RelativityLoginComponent } from './relativity-login.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { DirectExportFacade } from '@venio/data-access/common'
import { RelativitySettingsDataModel } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'

describe('RelativityLoginComponent', () => {
  let component: RelativityLoginComponent
  let fixture: ComponentFixture<RelativityLoginComponent>

  const mockRelativityData: RelativitySettingsDataModel = {
    isRelativityOne: false,
    id: 123,
    environmentId: 123,
    relativityOneClientId: 'client-123',
    relativityOneClientSecret: 'secret-123',
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RelativityLoginComponent,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideMockStore({}),
        provideAnimations(),
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: DirectExportFacade,
          useValue: {
            selectUpdateUserEnvironmentSuccess$: of({}),
            selectUpdateUserEnvironmentError$: of({}),
            selectCreateUserEnvironmentSuccess$: of({}),
            selectCreateUserEnvironmentError$: of({}),
            updateUserEnvironment: jest.fn(),
            createUserEnvironment: jest.fn(),
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(RelativityLoginComponent)
    component = fixture.componentInstance

    // Set the mock input value
    component.relativityData = mockRelativityData

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
