import { CommonModule } from '@angular/common'
import { Component, effect, input } from '@angular/core'
import { ChartsModule } from '@progress/kendo-angular-charts'
import {
  ChartDataModel,
  ProductionGraphDataModel,
} from '@venio/data-access/review'

@Component({
  selector: 'venio-production-status-graph',
  standalone: true,
  imports: [CommonModule, ChartsModule],
  templateUrl: './production-status-graph.component.html',
  styleUrl: './production-status-graph.component.scss',
})
export class ProductionStatusGraphComponent {
  public graphId = input<number>()

  public graphData = input<ProductionGraphDataModel>()

  public theme: string

  public chartData: ChartDataModel[] = []

  constructor() {
    effect(() => {
      this.chartData = this.graphData()?.chartData
      if (this.graphData()?.progress === 0) {
        this.theme = 'NOT STARTED'
      } else if (
        this.graphData()?.progress > 0 &&
        this.graphData()?.progress < 100
      ) {
        this.theme = 'INPROGRESS'
      } else {
        this.theme = 'COMPLETED'
      }
    })
  }

  public getGraphTitle(graphId: number): string {
    switch (graphId) {
      case 1:
        return 'Image'
      case 2:
        return 'Fulltext'
      case 3:
        return 'Native'
      case 4:
        return 'Load file'
      case 5:
        return 'Relativity Image'
      case 6:
        return 'Relativity Fulltext'
      case 7:
        return 'Relativity Load file'
      default:
        return 'Unknown'
    }
  }

  public calculateLabelMargin(value: number): number {
    const length = value.toString().length
    if (length <= 2) return -15
    if (length <= 4) return -25
    return -35
  }
}
