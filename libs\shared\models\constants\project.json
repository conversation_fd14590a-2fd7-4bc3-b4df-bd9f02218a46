{"name": "shared-models-constants", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared/models/constants/src", "prefix": "venio", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/shared/models/constants"], "options": {"tsConfig": "libs/shared/models/constants/tsconfig.lib.json", "project": "libs/shared/models/constants/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared/models/constants/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared/models/constants/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"executor": "@nx/eslint:lint"}}}