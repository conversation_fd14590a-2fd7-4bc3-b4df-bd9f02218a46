import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetBatchesViewDashboardComponent } from './reviewset-batches-view-dashboard.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { ReportsFacade } from '@venio/data-access/reports'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'

describe('ReviewsetBatchesViewDashboardComponent', () => {
  let component: ReviewsetBatchesViewDashboardComponent
  let fixture: ComponentFixture<ReviewsetBatchesViewDashboardComponent>

  const mockReviewSetFacade = {
    selectReviewSetBatchSummary$: of(undefined),
    selectReviewSetBatchSummaryRate$: of(undefined),
    fetchReviewSetBatchSummary: jest.fn(),
    fetchReviewSetBatchSummaryRate: jest.fn(),
    clearReviewSetState: jest.fn(),
  }

  const mockReportsFacade = {
    resetReportState: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetBatchesViewDashboardComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: ReviewSetFacade, useValue: mockReviewSetFacade },
        {
          provide: ReportsFacade,
          useValue: mockReportsFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetBatchesViewDashboardComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
