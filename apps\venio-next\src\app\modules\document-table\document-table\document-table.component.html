<!-- <button (click)="treelist.autoFitColumns()">Auto Fit</button>  -->
<div
  #main
  class="t-inline-block t-relative t-h-full t-w-full t-bg-white t-select">
  <div
    #toolbar
    class="t-flex t-justify-between t-items-center t-relative t-flex-wrap t-px-2 t-py-2">
    <div class="t-flex t-gap-2">
      <ng-container
        *ngIf="documentMenuLazy | async; else loadingMenu"
        [ngComponentOutlet]="documentMenuLazy | async" />

      <div
        class="t-flex t-rounded t-items-center t-bg-[#f6f6f6] t-border t-border-[#BEBEBE] k-rounded-md t-pl-2 t-pr-1"
        *ngIf="isEmailThreadView()">
        <span class="t-mr-1 t-text-sm">Email Thread View</span>
        <button
          kendoButton
          #switchViewBtn
          fillMode="clear"
          size="none"
          class="t-pb-[0.125rem]"
          (click)="closeEmailThreadView()">
          <span
            venioSvgLoader
            [parentElement]="switchViewBtn.element"
            applyEffectsTo="fill"
            color="#CDCDCD"
            hoverColor="#ED7425"
            svgUrl="assets/svg/Icon-ionic-md-close-circle.svg"
            title="Switch To Normal View"
            height="1rem"
            width="1rem"></span>
        </button>
      </div>
    </div>
    <ng-template #loadingMenu>
      <div class="t-flex">
        <kendo-skeleton
          shape="text"
          animation="pulse"
          [width]="40"
          [height]="40"
          class="t-mr-3"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          animation="pulse"
          width="10rem"></kendo-skeleton>
      </div>
    </ng-template>
    <!-- Container for displaying selected documents count -->
    <div class="t-items-start" *ngIf="selectedDocumentCount()">
      <!-- Kendo label for "Selected Documents" -->
      <kendo-label>
        Selected Documents
        <!-- Span for displaying selected document count with dynamic value -->
        <!-- .. with attribute for identification -->
        <span
          class="t-text-[#1EBADC]"
          data-qa="document-table-selected-document-count"
          >{{ selectedDocumentCount() }}
        </span>
      </kendo-label>
    </div>
    <venio-pagination
      [disabled]="totalRecords === 0"
      [totalRecords]="totalRecords"
      [pageSize]="pageSize"
      [currentPage]="currentPage"
      [showPageJumper]="false"
      [showPageSize]="true"
      (pageChanged)="pageChanged($event)"
      (pageSizeChanged)="pageSizeChanged($event)"
      class="t-block t-py-2">
    </venio-pagination>
  </div>
  <!-- <venio-create-redaction-set></venio-create-redaction-set> -->
  <kendo-treelist
    kendoTreeListSelectable
    kendoTreeListExpandable
    kendoTooltip
    [kendoTreeListFlatBinding]="tableData()"
    [(selectedItems)]="currentPageSelectedDocuments"
    [idField]="idField()"
    [parentIdField]="parentIdField"
    [selectable]="selectableSettings"
    [filterable]="'menu'"
    [resizable]="true"
    (selectionChange)="onSelectionChange($event)"
    [isSelected]="isSelected"
    [(expandedKeys)]="expandedKeys"
    (expandedKeysChange)="onExpandedIdsChange($event)"
    scrollable="virtual"
    [virtualColumns]="true"
    [rowHeight]="20"
    [pageSize]="isEmailThreadView() ? numberOfEmailInCurrentPage : pageSize"
    [loading]="loadingDocuments()"
    [height]="main.clientHeight - toolbar.clientHeight - 50"
    [trackBy]="documentTrackByFn"
    [ngClass]="{ 'v-no-data': tableData().length === 0 }"
    class="t-w-full t-overflow-y-visible t-relative v-custom-even-treelist v-ignore-global-overrides">
    <kendo-treelist-column
      headerClass="!t-py-2 !t-text-center !t-pl-3 !t-pr-1 !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
      [field]="'seq_no'"
      [resizable]="true"
      [autoSize]="true"
      [minResizableWidth]="32"
      [width]="32"
      title="#"
      [filterable]="false"
      *ngIf="!isEmailThreadView() && !loadingDocuments()"
      class="!t-py-1 !t-pl-3 !t-pr-1 !t-text-center">
      <ng-template kendoTreeListHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Row Number"
          >#</span
        >
      </ng-template>
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
        <span *ngIf="!loadingDocuments()">{{ dataItem['seq_no'] }}</span>
      </ng-template>
    </kendo-treelist-column>

    <!-- checkbox column for normal view (old)-->
    <kendo-treelist-checkbox-column
      [width]="45"
      [autoSize]="true"
      [resizable]="false"
      [showSelectAll]="false"
      headerClass="!t-pl-3 !t-py-2 !t-align-middle !t-pr-1"
      [class]="{
        '!t-py-[0.40rem]': isEmailThreadView(),
        '!t-relative !t-pt-2 !t-pb-1 !t-pl-3 !t-pr-1 !t-overflow-hidden':
          !isEmailThreadView()
      }"
      [minResizableWidth]="45"
      *ngIf="!isEmailThreadView() && !loadingDocuments()">
      <ng-template
        kendoTreeListHeaderTemplate
        let-column
        let-columnIndex="columnIndex">
        <venio-document-select-header-renderer />
      </ng-template>
    </kendo-treelist-checkbox-column>

    <!--column for expand button in email thread view-->
    <kendo-treelist-column
      [resizable]="false"
      [width]="30"
      [minResizableWidth]="30"
      [filterable]="false"
      [expandable]="true"
      class="!t-py-1"
      *ngIf="isEmailThreadView() && !loadingDocuments()" />

    <!--custom checkbox column for email thread view-->
    <kendo-treelist-column
      class="!t-py-1"
      headerClass="!t-py-2 !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
      field="'__FileId'"
      [resizable]="true"
      [width]="440"
      [minResizableWidth]="100"
      [filterable]="false"
      *ngIf="isEmailThreadView() && !loadingDocuments()">
      <ng-template
        kendoTreeListHeaderTemplate
        let-column
        let-columnIndex="columnIndex">
        <div class="t-flex t-w-1 t-gap-2">
          <venio-document-select-header-renderer />
          <div class="t-flex t-items-center">Subject</div>
        </div>
      </ng-template>
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <!-- t-ml-6 for indenting child emails (__treelistParentId is null for only parent) -->
        <div
          class="t-flex t-gap-2 t-w-full"
          *ngIf="!loadingDocuments()"
          [ngClass]="{ 't-ml-6': dataItem['__treelistParentId'] !== null }">
          <input
            type="checkbox"
            class="!t-mt-0.5 k-checkbox k-checkbox-md k-rounded-md"
            [disabled]="isGeneratedOrMissingEmail(dataItem)"
            [checked]="isSelected(dataItem)"
            (change)="itemSelected($event, dataItem)" />
          <span
            [ngClass]="{ 't-ml-4': !dataItem['__treelistParentId'] }"
            venioSvgLoader
            applyEffectsTo="fill"
            [color]="getIconColor(dataItem)"
            [svgUrl]="getEmailIcon(dataItem)"
            [title]="getIconTitle(dataItem)"
            height="1rem"
            width="1rem"></span>

          <!-- element that wil be displayed for parent email items. it will show email summary popup on hover.  -->
          <span
            *ngIf="dataItem.__treelistParentId === null"
            kendoPopoverAnchor
            [popover]="emailThreadSummaryPopover"
            [showOn]="'hover'"
            class="k-text-ellipsis t-w-full"
            (mouseenter)="showPopover(dataItem)"
            [ngStyle]="{ color: getSubjectTextColor(dataItem) }"
            >{{ dataItem.__emailSubject }}</span
          >

          <!-- element that will be displayed for child email items. summary popup is not set here. -->
          <span
            *ngIf="dataItem['__treelistParentId'] !== null"
            class="k-text-ellipsis t-w-full"
            [ngStyle]="{ color: getSubjectTextColor(dataItem) }"
            >{{ dataItem.__emailSubject }}</span
          >
        </div>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
      </ng-template>
    </kendo-treelist-column>

    <kendo-treelist-column
      headerClass="!t-py-2 !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
      title="Conflict"
      [width]="145"
      [minResizableWidth]="100"
      [resizable]="false"
      [filterable]="false"
      [autoSize]="false"
      *ngIf="isReviewForTagRuleConflicted() && !loadingDocuments()">
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <!-- Template for the tag rule conflict column -->
        <button
          kendoButton
          #resolveBtn
          fillMode="clear"
          size="none"
          class="!t-py-0 t-ml-[-9px] t-mt-[-11px] t-absolute t-text-blue-900-temp"
          *ngIf="!isEmailThreadView() || !isGeneratedOrMissingEmail(dataItem)"
          (click)="resolveTagConfllict(dataItem)">
          <span>Resolve conflict </span>
        </button>
      </ng-template>
    </kendo-treelist-column>

    <kendo-treelist-column
      [width]="35"
      headerClass="!t-py-2 !t-pl-0 !t-pr-1"
      [minResizableWidth]="35"
      [resizable]="false"
      [filterable]="false"
      [autoSize]="false"
      class="t-text-center !t-py-1 !t-pl-0 !t-pr-1"
      *ngIf="!isReviewPanelPopout">
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
        <!-- Template for the first column -->
        <button
          kendoButton
          #editBtn
          fillMode="clear"
          size="none"
          class="!t-py-0 t-mx-auto t-mt-[-11px] t-absolute"
          *ngIf="
            (!isEmailThreadView() || !isGeneratedOrMissingEmail(dataItem)) &&
            !loadingDocuments()
          "
          (click)="showDocumentViewer(dataItem)">
          <span
            [parentElement]="editBtn.element"
            venioSvgLoader
            applyEffectsTo="fill"
            color="#ADADAD"
            hoverColor="#FFBB12"
            svgUrl="assets/svg/icon-action-grid-pencil.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </ng-template>
    </kendo-treelist-column>

    <kendo-treelist-column
      headerClass="!t-py-2 !t-px-1"
      [width]="35"
      [minResizableWidth]="35"
      [resizable]="false"
      [filterable]="false"
      class="!t-py-1 !t-px-1"
      [autoSize]="false">
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
        <!-- Template for the document type column -->
        <button
          *ngIf="!loadingDocuments()"
          kendoButton
          #doctype
          fillMode="clear"
          size="none"
          class="!t-py-0 t-mx-auto t-mt-[-11px] t-absolute">
          <span
            [parentElement]="doctype.element"
            venioSvgLoader
            applyEffectsTo="fill"
            color="#ADADAD"
            hoverColor="#FFBB12"
            [svgUrl]="getDocTypeIcon(dataItem).iconPath"
            [title]="getDocTypeIcon(dataItem).tooltip"
            height="1rem"
            width="1rem">
          </span>
        </button>
      </ng-template>
    </kendo-treelist-column>
    <!-- Column for showing if document is Reviewed  -->
    <kendo-treelist-column
      *ngIf="reviewSetState.isBatchReview()"
      headerClass="!t-px-1 !t-py-2"
      [width]="45"
      [minResizableWidth]="45"
      [resizable]="false"
      [filterable]="false"
      [autoSize]="false">
      <ng-template kendoTreeListHeaderTemplate>
        <span
          venioSvgLoader
          applyEffectsTo="fill"
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/icon-document-unreviewed.svg'"
          [title]="'Is Reviewed'"
          height="1rem"
          width="1rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </ng-template>
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
        <button
          *ngIf="!loadingDocuments()"
          kendoButton
          field="__isReviewed"
          #isReviewed
          fillMode="clear"
          size="none"
          class="!t-py-0 t-ml-[-9px] t-mt-[-11px] t-absolute">
          <span
            [parentElement]="isReviewed.element"
            venioSvgLoader
            applyEffectsTo="fill"
            hoverColor="#FFBB12"
            [svgUrl]="getIsReviewedIcon(dataItem).iconPath"
            [title]="getIsReviewedIcon(dataItem).tooltip"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </ng-template>
    </kendo-treelist-column>

    <kendo-treelist-column
      headerClass="!t-py-2 !t-pl-2 !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
      field="__tagicon"
      title="Tag Color"
      [filterable]="false"
      [width]="80"
      [minResizableWidth]="60"
      [autoSize]="true"
      class="!t-py-1 !t-pl-2">
      <ng-template kendoTreeListHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Tag Color"
          >Tag Color</span
        >
      </ng-template>
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <kendo-skeleton
          *ngIf="loadingDocuments()"
          shape="text"
          animation="pulse"
          height="20px"></kendo-skeleton>
        <div
          *ngIf="!loadingDocuments()"
          class="t-flex t-flex-1 t-w-fit t-overflow-hidden">
          <ng-container *ngFor="let icon of svgIconForTagSummary()">
            <ng-container
              *ngIf="
                (dataItem.__notes || dataItem.__tags) &&
                !dataItem.__folderLineage &&
                icon.actionType === 'TAG'
              "
              [ngTemplateOutlet]="tagSummaryIconTemplate"
              [ngTemplateOutletContext]="{ icon: icon, dataItem: dataItem }">
            </ng-container>
            <ng-container
              *ngIf="
                !dataItem.__notes &&
                !dataItem.__tags &&
                dataItem.__folderLineage &&
                icon.actionType === 'FOLDER'
              "
              [ngTemplateOutlet]="tagSummaryIconTemplate"
              [ngTemplateOutletContext]="{ icon: icon, dataItem: dataItem }">
            </ng-container>
            <ng-container
              *ngIf="
                (dataItem.__notes || dataItem.__tags) &&
                dataItem.__folderLineage &&
                icon.actionType === 'TAGFOLDER'
              "
              [ngTemplateOutlet]="tagSummaryIconTemplate"
              [ngTemplateOutletContext]="{ icon: icon, dataItem: dataItem }">
            </ng-container>
          </ng-container>

          <span
            *ngFor="let tag of dataItem.__tagicon.split(',')"
            class="t-flex t-items-center">
            <div
              class="t-h-2 t-w-2 t-float-left t-m-0.5 t-rounded-full"
              kendoTooltip
              [title]="getTagInfo(tag).name"
              [style.background-color]="getTagInfo(tag).color"></div>
          </span>
        </div>
      </ng-template>
    </kendo-treelist-column>
    @for(col of columns(); track col) {
    <kendo-treelist-column
      headerClass="!t-py-2 !t-px-0 !t-text-primary !t-tracking-tight !t-capitalize !t-align-middle"
      [field]="col"
      [title]="col"
      [width]="100"
      [minResizableWidth]="100"
      [autoSize]="false"
      class="!t-py-1 !t-px-0">
      <ng-template
        kendoTreeListHeaderTemplate
        let-column
        let-columnIndex="columnIndex">
        <div
          class="t-flex t-flex-1 t-whitespace-nowrap t-cursor-pointer"
          (click)="openTallyDialog(col)"
          [title]="col">
          {{ col }}
        </div>
        <ng-template kendoTreeListCellTemplate let-dataItem>
          <kendo-skeleton
            *ngIf="loadingDocuments()"
            shape="text"
            animation="pulse"
            height="20px"></kendo-skeleton>
          <span *ngIf="!loadingDocuments()">{{ dataItem[col] }}</span>
        </ng-template>
      </ng-template>
      <!-- <ng-template kendoTreeListFilterCellTemplate let-filter>
            <venio-row-filter [displayFieldName]="col"></venio-row-filter>
          </ng-template>

          <ng-template kendoTreeListCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
            <ng-container [ngSwitch]="col">
              <ng-container *ngSwitchCase="'Hash Value'">
                <span (click)="cellClick1(col)"> Row: {{rowIndex}}</span>
              </ng-container>
              <ng-container *ngSwitchDefault>
                <span (click)="cellClick(dataItem,col)">{{dataItem[col]}}</span>
              </ng-container>
            </ng-container>
          </ng-template> -->
    </kendo-treelist-column>
    }
    <ng-template kendoTreeListNoRecordsTemplate
      >@if(noRecordsAvailable()) {
      <div class="t-flex t-place-content-center">No records available.</div>
      }
    </ng-template>
  </kendo-treelist>
</div>
<ng-template #tagSummaryIconTemplate let-icon="icon" let-dataItem="dataItem">
  <button
    kendoButton
    kendoTooltip
    [title]="'Document Summary'"
    #parentElTag
    class="t-bg-inherit !t-border-none !t-p-0 t-h-fit"
    data-qa="btn-tag-summary"
    (click)="setCurrentDocument(dataItem)"
    kendoPopoverAnchor
    [popover]="summaryPopover"
    size="none"
    fillMode="clear"
    rounded="none">
    <span
      [parentElement]="parentElTag.element"
      venioSvgLoader
      [svgUrl]="icon.iconPath"
      height="1rem"
      width="1.4rem">
      <kendo-loader size="small"></kendo-loader>
    </span>
  </button>
</ng-template>

<kendo-popover #summaryPopover position="right">
  <ng-template kendoPopoverTitleTemplate>
    <ng-container
      *ngComponentOutlet="tagSummaryComponent | async"></ng-container>
  </ng-template>
</kendo-popover>

<!-- Popup that will show email thread summary -->
<kendo-popover #emailThreadSummaryPopover position="top" [callout]="true">
  <ng-template kendoPopoverBodyTemplate>
    <div style="width: 220px">
      <div class="t-flex t-width-full">
        <div class="t-w-4/5">Total Email</div>
        <div
          [ngStyle]="{ color: emailThreadColorConstant.defaultEmailIconColor }">
          {{ childEmailCount }}
        </div>
      </div>
      <div class="t-flex t-width-full">
        <div class="t-w-4/5">Total Inclusive</div>
        <div
          [ngStyle]="{
            color: emailThreadColorConstant.inclusiveEmailIconColor
          }">
          {{ inclusiveEmailCount }}
        </div>
      </div>
      <div class="t-flex t-width-full">
        <div class="t-w-4/5">Total Missing Emails</div>
        <div
          [ngStyle]="{ color: emailThreadColorConstant.missingEmailIconColor }">
          {{ missingEmailCount }}
        </div>
      </div>
      <div class="t-flex t-width-full">
        <div class="t-w-4/5">Total Generated Emails</div>
        <div
          [ngStyle]="{
            color: emailThreadColorConstant.generatedEmailIconColor
          }">
          {{ generatedEmailCount }}
        </div>
      </div>
    </div>
  </ng-template>
</kendo-popover>
