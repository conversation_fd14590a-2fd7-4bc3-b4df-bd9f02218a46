<kendo-dialog-titlebar>
  <button class="t-flex t-items-center" (click)="closeDialog()">
    <kendo-svgicon
      [icon]="iconLeft"
      class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
    <kendo-label class="t-text-xs t-font-black t-text-[#000000]"
      >BACK</kendo-label
    >
  </button>
</kendo-dialog-titlebar>
<div class="t-flex t-flex-col t-items-center t-justify-center t-p-4">
  <div class="t-flex t-flex-row t-w-full t-items-start">
    <div class="t-flex t-flex-col t-w-full t-items-start t-justify-center">
      <p
        class="t-text-[#030303] t-sticky t-text-[1.75rem]/8 t-font-black t-mb-2">
        TERMS OF USE
      </p>
      @if(isEndUserLicenseAgreementLoading()){
      <div class="t-w-full t-flex t-flex-row t-gap-2">
        <kendo-skeleton height="10" shape="text" width="33.66%" />
        <kendo-skeleton height="10" shape="text" width="33.66%" />
        <kendo-skeleton height="10" shape="text" width="33.66%" />
      </div>
      <div class="t-w-full t-flex t-flex-row t-gap-2">
        <kendo-skeleton height="10" shape="text" width="20%" />
        <kendo-skeleton height="10" shape="text" width="20%" />
        <kendo-skeleton height="10" shape="text" width="20%" />
        <kendo-skeleton height="10" shape="text" width="20%" />
        <kendo-skeleton height="10" shape="text" width="20%" />
      </div>
      <div class="t-w-full t-flex t-flex-row t-gap-2">
        @for(n of [1,2,3,4,5,6,7,8]; track n) {
        <kendo-skeleton height="10" shape="text" width="10%" />
        }
      </div>
      } @if(endUserLicenseAgreement() && !isEndUserLicenseAgreementLoading()) {
      <!-- <pre
        class="t-text-xs/4 t-p-0 t-mt-4 t-font-normal !t-font-['Roboto'] t-mb-4 t-align-left t-text-left t-text-[#000000] t-whitespace-pre-wrap"
        >{{ endUserLicenseAgreement() }}
      </pre> -->
      <div
        class="ql-editor t-w-full t-h-full"
        [innerHTML]="endUserLicenseAgreement()"></div>
      }
    </div>
  </div>
</div>
