import { GroupModel } from '../project/group.model'

export enum InputControlTypes {
  Boolean = 'Boolean',
  Date = 'Date',
  DateTime = 'DateTime',
  Numeric = 'Numeric',
  Paragraph = 'Paragraph',
  Text = 'Text',
  UnicodeParagraph = 'Unicode Paragraph',
  UnicodeText = 'Unicode Text',
}

export interface CustomFieldsModel {
  projectId: number
  customFieldId: number
  customFieldInfoId: number
  fieldName: string
  displayName: string
  uiInputType: InputControlTypes
  fieldCodingValues: string[]
  length: number
  precision: number
  scale: number
  description: string
  defaultValue: string
  allowCoding: boolean
  allowEmptyValues: boolean
  allowPredefinedCodingValuesOnly: boolean
  multiValuedCodingOptions: number
  allowMultipleCodingValues: boolean
  delimiterForCodingValues: string
  codingValues: string
  isSearchable: boolean
  isEntityField: boolean
  groupAccess: GroupModel[]
  allowToUpdate: boolean

  // extra
  fieldValues: Array<{ count: number; fieldValue: string; seqId: number }>
  venioFieldId: number
  id: string
  displayOrder: number
  isDisplayed: boolean
  allowNullSearch: boolean
  isCustomField: boolean
  isNullable: boolean
  venioDBRef: string
  internalFieldName: string
  displayFieldName: string
  fieldDescription: string
  fieldDataType: string
  fieldGroup: string
  searchDataType: string
  filterExpression: string
  uniqueDataIdentifier: string
}

export interface CustomFieldType {
  value: string
  displayName: string
}

export interface CodingReorderModel {
  codingOrderXml: string
}
