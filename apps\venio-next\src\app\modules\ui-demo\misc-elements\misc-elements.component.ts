import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-misc-elements',
  standalone: true,
  imports: [CommonModule, ButtonsModule],
  templateUrl: './misc-elements.component.html',
  styleUrls: ['./misc-elements.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MiscElementsComponent {}
