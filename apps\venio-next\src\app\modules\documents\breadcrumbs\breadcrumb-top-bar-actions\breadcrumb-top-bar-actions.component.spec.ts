import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbTopBarActionsComponent } from './breadcrumb-top-bar-actions.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DialogService } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

jest.mock('@progress/kendo-angular-dialog')
describe('BreadcrumbActionControlsComponent', () => {
  let component: BreadcrumbTopBarActionsComponent
  let fixture: ComponentFixture<BreadcrumbTopBarActionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbTopBarActionsComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DialogService,
        provideMockStore({}),
        SearchFacade,
        FieldFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbTopBarActionsComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
