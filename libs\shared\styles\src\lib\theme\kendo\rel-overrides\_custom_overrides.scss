$scrollbar-width: 10px;
$scollbar-height: 10px;
$scrollbar-radius: 4px;
$scrollbar-color: rgba(200, 200, 200, 0.6);
$scrollbar-hover-color: rgba(180, 180, 180, 0.8);
$scrollbar-bg-color: rgba(220, 220, 220, 0.2);
$scrollbar-hover-bg-color: rgba(210, 210, 210, 0.4);

@mixin scrollbar(
  $width,
  $height,
  $radius,
  $thumb-color,
  $track-color,
  $hover-track-color
) {
  /* WebKit (Chrome, Safari, Edge) styles */
  &::-webkit-scrollbar {
    width: $width;
    height: $height;
  }

  &::-webkit-scrollbar-track {
    border-radius: $radius;
    background: $track-color;
    cursor: default;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: $radius;
    background: $thumb-color;
    transition: background 0.3s ease-in-out;
    cursor: default;

    &:hover {
      background: $scrollbar-hover-color;
    }
  }

  &:hover::-webkit-scrollbar-track {
    background: $hover-track-color;
  }
}

* {
  @include scrollbar(
    $scrollbar-width,
    $scollbar-height,
    $scrollbar-radius,
    $scrollbar-color,
    $scrollbar-bg-color,
    $scrollbar-hover-bg-color
  );

  /* Firefox specific styles */
  @supports (-moz-appearance: none) {
    scrollbar-width: thin;
  }
}

.k-focus {
  outline: none;
}

.k-focus > .k-link {
  outline-color: var(--kendo-neutral-40) !important;
}

.k-input-solid {
  --kendo-input-focus-border: var(--kendo-neutral-40);
}

.k-picker-solid {
  --kendo-picker-focus-border: var(--kendo-neutral-40);
}
// seperate layout for the document coding form
.v-parent-container {
  venio-document-coding {
    width: 100%;
  }
  form {
    flex-direction: row;
    gap: 2%;
    grid-row-gap: 1.1rem;
    .v-parent-field {
      width: 32%;
    }
  }
}
@layer {
  .v-custom-metadata-viewport {
    @apply t-h-[calc(100vh-18rem)] t-grow #{!important};
  }
  // Mixin for scrollbar styles
  @mixin scrollbar-styles($width, $height) {
    scrollbar-width: $width;
    -ms-overflow-style: $width;

    &::-webkit-scrollbar {
      width: $height;
      height: $height;
    }
  }

  // Custom chat styles
  .k-chat {
    &.v-custom-chat {
      .k-chat-bubble {
        @apply t-bg-[#ECECEC] t-border-0 t-text-[#000000] #{!important};
        &::before {
          content: ' ';
          border-top: 15px solid transparent !important;
          border-right: 18px solid #ececec !important;
          border-bottom: 15px solid transparent !important;
          transform: translateY(-50%);
          position: absolute;
          top: 50%;
          left: -10px;
          width: 0;
          height: 0;
        }
      }
      .k-alt {
        .k-chat-bubble {
          @apply t-bg-[#ECECEC] #{!important};

          &::before {
            content: ' ';
            border-top: 15px solid transparent !important;
            border-right: 18px solid #ececec !important;
            border-bottom: 15px solid transparent !important;
            transform: translateY(-50%) rotate(180deg);
            top: 50%;
            right: -10px;
            left: inherit;
          }
        }
      }

      .k-message-group:not(.k-alt) .k-only .k-chat-bubble {
        border-end-start-radius: 0.75rem !important;
      }

      .k-avatars .k-message-group:not(.k-alt, .k-no-avatar) {
        padding-inline-start: calc(
          40px + var(--kendo-spacing-2, 0.5rem)
        ) !important;
      }
      .k-avatars .k-message-group.k-alt:not(.k-no-avatar) {
        padding-inline-end: calc(
          40px + var(--kendo-spacing-2, 0.5rem)
        ) !important;
      }
      .k-message-group.k-alt .k-only .k-chat-bubble {
        border-end-end-radius: 0.75rem !important;
      }

      .k-timestamp {
        border-radius: 4px;
        background-color: #ececec !important;
        @apply t-p-[0.3rem] t-self-center #{!important};
      }
    }
  }

  // Mixin for hover styles
  @mixin hover-scrollbar-styles {
    &:hover {
      @include scrollbar-styles(auto, inherit);
    }
  }

  // Toggle the scrollbar visibility on hover
  .v-hide-scrollbar {
    @apply t-overflow-y-auto;
    @include scrollbar-styles(none, 0);

    @include hover-scrollbar-styles;

    .k-virtual-content,
    .k-listview-content {
      @include scrollbar-styles(none, 0);

      @include hover-scrollbar-styles;
    }
  }

  .v-dashed-sperator {
    background-image: url('~apps/venio-next/src/assets/svg/icon-dashed-border.svg');
  }

  .v-dashed-horizontal-sperator {
    background-image: url('~apps/venio-next/src/assets/svg/icon-horizontal-border.svg');
  }

  // Dashed svg custom border
  .v-custom-dashed-border{
    background-image: url('~apps/venio-next/src/assets/svg/icon-custom-line-border.svg');
  }
}

// override the host styles for breadcrumb stack container
venio-breadcrumb-stack-container{
  min-width: auto !important;
}
