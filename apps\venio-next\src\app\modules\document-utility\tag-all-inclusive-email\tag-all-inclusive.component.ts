import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  InitialSearchResultParameter,
  SearchFacade,
} from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { TagActionType } from '@venio/data-access/document-utility'
import { VenioNotificationService } from '@venio/feature/notification'

@Component({
  selector: 'venio-tag-all-inclusive',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tag-all-inclusive.component.html',
  styleUrl: './tag-all-inclusive.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagAllInclusiveComponent implements OnInit, OnD<PERSON>roy {
  private readonly toD<PERSON>roy$ = new Subject<void>()

  private dialogRef: DialogRef

  private event: DocumentMenuType

  constructor(
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private dialogService: DialogService,
    private notification: VenioNotificationService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      width: '50%',
      height: '75vh',
      cssClass: 't-mt-2',
    })
    const dialogInstance = this.dialogRef.content.instance
    dialogInstance.tagActionType =
      this.event === DocumentMenuType.TAG_ALL_INCLUSIVE_EMAIL
        ? TagActionType.TagAllInclusiveEmails
        : TagActionType.TagWholeThreadOfSelectedDocuments
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('@venio/feature/tag-email-thread').then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.TagEmailThreadComponent)

      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    combineLatest([
      this.documentsFacade.selectDocumentMenuEvent$,
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchInitialParameters$,
    ])
      .pipe(
        filter(
          ([event]) =>
            event === DocumentMenuType.TAG_ALL_INCLUSIVE_EMAIL ||
            event === DocumentMenuType.TAG_WHOLE_THREAD
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([
          event,
          isBatchSelected,
          selectedDocs,
          unselectedDocs,
          searchParams,
        ]) => {
          // launch the dialog
          this.event = event

          let selectedDocuments = 0
          selectedDocuments = this.getSelectedDocumentCount(
            isBatchSelected,
            selectedDocuments,
            searchParams,
            unselectedDocs,
            selectedDocs
          )

          if (
            selectedDocuments === 0 &&
            event === DocumentMenuType.TAG_WHOLE_THREAD
          ) {
            this.notification.showError(
              'Please select at least one document to tag.'
            )
            this.#resetMenuLoadingState()
            this.#resetMenuEventState()
          } else {
            this.#handleLazyLoadedDialog()
          }
        }
      )
  }

  private getSelectedDocumentCount(
    isBatchSelected: boolean,
    selectedDocuments: number,
    searchParams: InitialSearchResultParameter,
    unselectedDocs: number[],
    selectedDocs: number[]
  ): number {
    if (isBatchSelected) {
      selectedDocuments = searchParams.totalHitCount - unselectedDocs.length
    } else {
      selectedDocuments = selectedDocs.length
    }
    return selectedDocuments
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
