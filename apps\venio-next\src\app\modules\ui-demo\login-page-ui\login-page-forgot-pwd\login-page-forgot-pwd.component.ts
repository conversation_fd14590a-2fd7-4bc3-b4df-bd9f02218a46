import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-login-page-forgot-pwd',
  standalone: true,
  imports: [
    CommonModule,
    CommonModule,
    ReactiveFormsModule,
    InputsModule,
    LabelModule,
    IconsModule,
    PopoverModule,
  ],
  templateUrl: './login-page-forgot-pwd.component.html',
  styleUrl: './login-page-forgot-pwd.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginPageForgotPwdComponent implements OnInit {
  public resetPasswordForm: FormGroup

  public showNewPassword = false

  public showConfirmPassword = false

  public icons = {
    eyeIcon: eyeIcon,
    slashIcon: eyeSlashIcon,
  }

  public passwordStrengthMessage =
    'Password needs to be at least 8 characters, one uppercase, one number & one special character'

  public passwordStrengthClass = 'v-error-popover'

  constructor(private fb: FormBuilder) {}

  public ngOnInit(): void {
    this.resetPasswordForm = this.fb.group({
      newPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(
            /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/
          ),
        ],
      ],
      confirmPassword: ['', Validators.required],
    })

    this.resetPasswordForm
      .get('newPassword')
      .valueChanges.subscribe((password) => {
        this.updatePasswordStrength(password)
      })
  }

  public get passwordMismatch(): boolean {
    const newPassword = this.resetPasswordForm.get('newPassword').value
    const confirmPassword = this.resetPasswordForm.get('confirmPassword').value
    return confirmPassword && newPassword !== confirmPassword
  }

  public toggleNewPasswordVisibility(): void {
    this.showNewPassword = !this.showNewPassword
  }

  public toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword
  }

  public updatePasswordStrength(password: string): void {
    const strongPasswordPattern =
      /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/

    if (strongPasswordPattern.test(password)) {
      this.passwordStrengthMessage = 'Your password is strong'
      this.passwordStrengthClass = 'v-success-popover'
    } else {
      this.passwordStrengthMessage =
        'Password needs to be at least 8 characters, one uppercase, one number & one special character'
      this.passwordStrengthClass = 'v-error-popover'
    }
  }

  public onSubmit(): void {
    if (this.resetPasswordForm.valid && !this.passwordMismatch) {
      // Handle successful password reset
      console.log('Password Reset Successful!', this.resetPasswordForm.value)
    } else {
      // Handle form errors
      console.error('Form is invalid or passwords do not match!')
    }
  }
}
