<div class="t-bg-[#FBFBFB] t-p-4 t-rounded-sm t-h-full t-overflow-hidden">
  <div class="t-overflow-y-auto" venioDynamicHeight [extraSpacing]="80">
    <div class="t-flex">
      <h3 class="t-font-semibold t-text-base">Select a Case</h3>
    </div>

    <div class="t-flex t-gap-2 t-mt-4">
      <kendo-dropdownlist
        class="t-w-48"
        defaultItem="Demo Master"
        [data]="listItems"></kendo-dropdownlist>

      <kendo-dropdownlist
        class="t-w-48"
        defaultItem="Clone from template"
        [data]="listItems"></kendo-dropdownlist>
    </div>

    <div class="t-block t-mt-4">
      <kendo-expansionpanel
        *ngFor="let panel of panelsData"
        [title]="panel.title"
        [(expanded)]="panel.expanded"
        (stateChange)="onPanelExpand(panel)"
        class="t-mb-4 t-w-2/3 t-relative t-overflow-visible t-rounded-md v-custom-expansion-case"
        [svgExpandIcon]="icons.downIcon"
        [svgCollapseIcon]="icons.upIcon">
        <ng-template kendoExpansionPanelTitleDirective>
          <span
            class="t-w-full t-text-[#707070] t-font-medium t-text-[15px]"
            [ngClass]="{ 't-text-info': panel.expanded }"
            >{{ panel.title }}</span
          >
        </ng-template>

        @defer { @if(panel.name === 'general'){
        <venio-review-create-case-general></venio-review-create-case-general>
        } @else if(panel.name === 'source'){
        <venio-review-create-case-source></venio-review-create-case-source>
        } @else if(panel.name === 'reviewers') {
        <venio-review-create-case-reviewer></venio-review-create-case-reviewer>
        } @else if(panel.name === 'documentSort'){
        <venio-review-create-case-sort></venio-review-create-case-sort>
        } @else if(panel.name === 'advance'){
        <venio-review-create-case-advance></venio-review-create-case-advance>
        }} @placeholder {
        <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
          <kendo-skeleton [height]="10" shape="circle" [width]="'2%'" />
          <kendo-skeleton [height]="10" shape="rectangle" [width]="'97%'" />
        </div>
        }
      </kendo-expansionpanel>
    </div>
  </div>
</div>

<!-- Footer -->
<div
  class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4 t-bottom-[0] t-left-[0px] t-bg-[#FFFFFF] t-p-4 t-fixed t-z-10">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="save">
    SAVE
  </button>
  <button data-qa="cancel" kendoButton themeColor="dark" fillMode="outline">
    CANCEL
  </button>
</div>
