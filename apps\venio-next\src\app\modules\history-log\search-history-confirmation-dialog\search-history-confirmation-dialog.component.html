<kendo-dialog
  (close)="searchAction(false)"
  [maxHeight]="'40vh'"
  [height]="'40vh'"
  [minWidth]="'50vw'"
  [width]="'50vw'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
      <div class="t-flex t-w-full t-flex-wrap t-gap-3">
        <div showHints="always" class="t-flex t-flex-1 t-gap-1 t-flex-col">
          <div class="t-flex t-gap-3 t-mt-2 t-flex-col t-gap-4">
            <div class="t-flex">
              <input
                type="radio"
                #enableYes
                [(ngModel)]="selectedScope"
                value="SAME_ORIGINAL"
                kendoRadioButton
                name="enableYesNo"
                [checked]="true" />
              <kendo-label
                class="t-ml-2"
                [for]="enableYes"
                text="Search in same scope as in original search"></kendo-label>
            </div>

            <div class="t-flex">
              <input
                type="radio"
                #enableNo
                [(ngModel)]="selectedScope"
                value="CURRENT"
                kendoRadioButton
                name="enableYesNo" />
              <kendo-label
                class="t-ml-2"
                [for]="enableNo"
                text="Run Search in the current scope"></kendo-label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="searchAction(true)"
        class="v-custom-secondary-button"
        themeColor="secondary"
        data-qa="save-button">
        OK
      </button>
      <button
        kendoButton
        (click)="searchAction(false)"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
