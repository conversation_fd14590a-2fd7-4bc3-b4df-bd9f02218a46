import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormReviewersComponent } from './reviewset-form-reviewers.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ReviewSetPayloadService } from '../reviewset-payload.service'
import { createMockReviewSetForm } from '../review-set-form.mock'
import { FormBuilder } from '@angular/forms'

describe('ReviewsetFormReviewersComponent', () => {
  let component: ReviewsetFormReviewersComponent
  let fixture: ComponentFixture<ReviewsetFormReviewersComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormReviewersComponent],
      providers: [
        provideNoopAnimations(),
        provideMockStore(),
        ReviewsetFormService,
        ReviewSetPayloadService,
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormReviewersComponent)
    component = fixture.componentInstance
    const formBuilder = TestBed.inject(FormBuilder)
    fixture.componentRef.setInput(
      'reviewSetForm',
      createMockReviewSetForm(formBuilder)
    )
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
