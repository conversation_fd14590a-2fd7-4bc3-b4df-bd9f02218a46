import { ComponentFixture, TestBed } from '@angular/core/testing'

import { PaginationComponent } from './pagination.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'

jest.doMock('@progress/kendo-angular-buttons')

describe('PaginationComponent', () => {
  let component: PaginationComponent
  let fixture: ComponentFixture<PaginationComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PaginationComponent],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(PaginationComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
