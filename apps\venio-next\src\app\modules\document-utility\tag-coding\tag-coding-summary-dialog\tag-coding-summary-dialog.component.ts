import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { SaveTagResponseModel } from '@venio/data-access/document-utility'
import { CodingSummary } from '@venio/shared/models/interfaces'
import {
  PopoverContainerDirective,
  PopoverModule,
  TooltipModule,
} from '@progress/kendo-angular-tooltip'
import { eyeIcon, xIcon } from '@progress/kendo-svg-icons'
import { SearchDupOption, StartupsFacade } from '@venio/data-access/review'
import { Subject, takeUntil } from 'rxjs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ProjectFacade } from '@venio/data-access/common'

@Component({
  selector: 'venio-tag-coding-summary-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    LabelModule,
  ],
  templateUrl: './tag-coding-summary-dialog.component.html',
  styleUrl: './tag-coding-summary-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCodingSummaryDialogComponent implements OnInit, OnDestroy {
  private toDestroy$ = new Subject<void>()

  @ViewChild('container', { static: false })
  private container: PopoverContainerDirective

  public popoverPosition = { top: 0, left: 0 }

  public dialogTitle = 'Tagged/UnTagged Document Summary'

  public selectedTabIndex = 0

  public showHiddenColumns = false

  public tagCodingDetails: any

  public taggedItems: SaveTagResponseModel[]

  public untaggedItems: SaveTagResponseModel[]

  public codingItems: CodingSummary[]

  public duplicatePropagateHeaderText: string

  public duplicateTagRuleText: string

  public parentChildTagRuleText: string

  public emailThreadTagRuleText: string

  public nddTagRuleText: string

  public searchDuplicateOption: SearchDupOption

  public searchIncludePCOption: boolean

  public searchDuplicationOptionText: string

  public searchPCOptionText: string

  public icons = { eyeIcon: eyeIcon, closeIcon: xIcon }

  public isBulkTagCoding = false

  constructor(
    private startupsFacade: StartupsFacade,
    private projectFacade: ProjectFacade,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#selectSearchParamForSearchSetting()
    this.#getTagItems()
    this.#getCodingItems()
    this.#setSelectedTabIndex()
  }

  public onSelect(e: any): void {
    if (e.index === 0) {
      this.dialogTitle = 'Tagged/UnTagged Document Summary'
    } else {
      this.dialogTitle = 'Coding Document Summary'
    }
  }

  #getTagItems(): void {
    this.taggedItems = this.tagCodingDetails?.tagResult?.filter(
      (item) => item.isTagOperation
    )
    this.untaggedItems = this.tagCodingDetails?.tagResult?.filter(
      (item) => !item.isTagOperation
    )
  }

  #getCodingItems(): void {
    this.codingItems = this.tagCodingDetails?.codingResult?.updatedCodingList
  }

  #setSelectedTabIndex(): void {
    if (this.taggedItems?.length > 0 || this.untaggedItems?.length > 0) {
      this.selectedTabIndex = 0
      this.dialogTitle = 'Tagged/UnTagged Document Summary'
    } else if (this.codingItems?.length > 0) {
      this.selectedTabIndex = 1
      this.dialogTitle = 'Coding Document Summary'
    }
  }

  public isSelected(tabIndex: number): boolean {
    return this.selectedTabIndex === tabIndex
  }

  public getGridHeight(): number {
    const bothGridsVisible =
      this.taggedItems.length > 0 && this.untaggedItems.length > 0
    return bothGridsVisible ? 180 : 400
  }

  public toggleHiddenColumns(event: Event): void {
    this.showHiddenColumns = (event.target as HTMLInputElement).checked
  }

  public popoverHide(): void {
    this.container.hide()
  }

  public popoverShow(anchor: Element, event: MouseEvent, tagId: number): void {
    this.container.hide()
    const buttonPosition = (event.target as HTMLElement).getBoundingClientRect()
    this.popoverPosition.top = buttonPosition.top + buttonPosition.height - 10
    this.popoverPosition.left = buttonPosition.left - 1
    this.container.show(anchor)
    this.#setTagRuleText(this.getTagPropagationSetting(tagId))
    this.#setCurrentSearchResultOptions()
  }

  public getTagPropagationSetting(tagId: number): any | undefined {
    const tag = this.tagCodingDetails.tagResult.find(
      (item) => item.tagId === tagId
    )
    return tag ? tag.tagPropagationSetting : undefined
  }

  #selectSearchParamForSearchSetting(): void {
    this.startupsFacade.getSearchParams$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((searchParams: any) => {
        this.searchIncludePCOption = searchParams.includePC
        this.searchDuplicateOption =
          searchParams.searchDuplicateOption === SearchDupOption.DEFAULT
            ? SearchDupOption.HIDE_ALL_DUPS_DYNAMIC
            : searchParams.searchDuplicateOption
      })
  }

  #setTagRuleText(tagSettings: any): void {
    if (tagSettings.dupTagOption === 2) {
      this.duplicateTagRuleText = 'Do not propagate tags to duplicates'
    } else if (tagSettings.dupTagOption === 1) {
      this.duplicateTagRuleText = 'Propagate tags within same custodian only'
    } else if (tagSettings.dupTagOption === 3) {
      this.duplicateTagRuleText =
        'Propagate tags to all duplicates in selected media scope'
    } else if (tagSettings.dupTagOption === 0) {
      this.duplicateTagRuleText =
        'Propagate tags to all duplicates in whole case'
    }

    this.parentChildTagRuleText = tagSettings.propagatePCSet
      ? 'Propagate tags to parent/child.'
      : 'Do not propagate tags to parent/child.'

    this.emailThreadTagRuleText = tagSettings.includeEmailThread
      ? 'Propagate tags to all emails of the email thread'
      : 'Do not propagate tags to emails of the email thread'

    this.nddTagRuleText =
      tagSettings.nddTagOption === 0
        ? 'Do no propagate to near duplicates'
        : tagSettings.nddTagOption === 1
        ? 'Propagate to near duplicates in selected media scope'
        : 'Propagate to near duplicates in whole scope'
  }

  #setCurrentSearchResultOptions(): void {
    switch (this.searchDuplicateOption) {
      case SearchDupOption.HIDE_ALL_DUPS_DYNAMIC:
        this.searchDuplicationOptionText =
          'Show only one instance in the selected scope (DynamicDeDupe™)'
        break
      case SearchDupOption.SHOW_ALL_DUPS:
        this.searchDuplicationOptionText =
          'Show all hits in the selected scope (No DeDupe)'
        break
      case SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_DYNAMIC:
        this.searchDuplicationOptionText =
          'Show only one instance per custodian in the selected scope (DynamicDeDupe™)'
        break
      case SearchDupOption.HIDE_PROJECT_LEVEL_DUPS_STATIC:
        this.searchDuplicationOptionText =
          'Hide project level duplicates (StaticDeDupe™)'
        break
      case SearchDupOption.HIDE_CUSTODIAN_LEVEL_DUPS_STATIC:
        this.searchDuplicationOptionText =
          'Hide custodian level duplicates (StaticDeDupe™)'
        break
    }

    this.searchPCOptionText = this.searchIncludePCOption
      ? 'Include parent/child'
      : 'Exclude parent/child'
  }

  public close(status: string): void {
    this.dialogRef.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
