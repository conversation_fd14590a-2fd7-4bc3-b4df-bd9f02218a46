import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule, TextAreaComponent } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { AiFacade } from '@venio/data-access/ai'
import {
  ProjectFacade,
  UserFacade,
  WebSocketService,
} from '@venio/data-access/common'
import { filter, map, Subject, takeUntil } from 'rxjs'
import { CaseModel } from '@venio/data-access/review'
import { UserModel } from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { environment } from '@venio/shared/environments'
import { FormsModule } from '@angular/forms'
import { Tokenizer } from '@venio/util/utilities'

@Component({
  selector: 'venio-ai-search-message-box',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    ButtonsModule,
    SvgLoaderDirective,
    TooltipsModule,
    FormsModule,
  ],
  templateUrl: './ai-search-message-box.component.html',
  styleUrl: './ai-search-message-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiSearchMessageBoxComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  @ViewChild(TextAreaComponent, { static: true })
  public messageBox: TextAreaComponent

  private readonly tokenizer = new Tokenizer({
    minTokenLength: 2,
    maxConsecutiveRepeats: 1,
  })

  private aiFacade = inject(AiFacade)

  private projectFacade = inject(ProjectFacade)

  private webSocketService = inject(WebSocketService)

  private userFacade = inject(UserFacade)

  public isConnected = signal<boolean>(false)

  public hasMinimumToken = signal<boolean>(false)

  public hasMaximumToken = signal<boolean>(false)

  private currentUser = signal<UserModel>(undefined)

  public isFocused = signal<boolean>(false)

  public hasSummaryList = signal<boolean>(false)

  private projectInfo = signal<CaseModel>(undefined)

  public get cleanMessage(): string {
    let text = this.messageBox?.value || ''
    // Remove leading and trailing whitespace
    text = text.trim()

    // Normalize case
    text = text.toLowerCase()

    // Normalize Unicode characters to ASCII
    text = text.normalize('NFD').replace(/[\u0300-\u036f]/g, '')

    // Correct repeated punctuation marks and special characters
    text = text.replace(/([!?.]){2,}/g, '$1')

    // Normalize whitespace
    text = text.replace(/\s+/g, ' ').trim()

    return text
  }

  public get lines(): number {
    const line = this.messageBox?.value?.split(/\r?\n|\r/g).length || 1
    return line >= 5 || line <= 5 ? line : 1
  }

  public readonly isAiSearchLoading$ = this.aiFacade.selectIsAiSearchLoading$

  public ngOnInit(): void {
    this.#selectCurrentUser()
    this.#selectResetTrigger()
  }

  public ngAfterViewInit(): void {
    this.#selectWsConnectionState()
    this.#selectProject()
    this.#focusMessageBox()
    this.#selectSummaryList()
    this.#connectWebSocket()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.webSocketService.closeConnection()
  }

  public validateInputFocus(): void {
    this.validateMinimumToken()
    this.isFocused.set(this.messageBox.isFocused)
  }

  private validateMinimumToken(): void {
    const message = this.messageBox.value || ''
    const tokens = this.tokenizer.process(message)
    if (tokens.length === 0) {
      this.hasMinimumToken.set(false)
      this.hasMaximumToken.set(false)
      return
    }

    const hasMinimumToken = tokens.length < 2
    const hasMaximumToken = tokens.length > 200

    this.hasMaximumToken.set(hasMaximumToken)
    this.hasMinimumToken.set(hasMinimumToken)
  }

  public search(event: KeyboardEvent | Event): void {
    this.validateMinimumToken()
    if (
      !this.isConnected() ||
      this.hasMaximumToken() ||
      this.hasMinimumToken()
    ) {
      event.preventDefault()
      return
    }

    if (
      event instanceof KeyboardEvent &&
      event.key === 'Enter' &&
      event.shiftKey
    ) {
      return
    }

    if (!this.cleanMessage) {
      return
    }

    const project = this.projectInfo()

    if (!project) {
      return
    }

    if (
      (event instanceof KeyboardEvent && event.key === 'Enter') ||
      event instanceof Event
    ) {
      const userName = this.currentUser()?.userName

      const collection_name = `collection_${project.databaseInstanceName}`
      const uuid = UuidGenerator.uuid
      this.aiFacade.performAiSearch(
        {
          collection_name,
          query: this.cleanMessage,
        },
        userName,
        uuid
      )
      if (event instanceof KeyboardEvent) {
        event.preventDefault()
      }

      this.messageBox.updateValue('')
    }
  }

  public resetALl(): void {
    this.aiFacade.resetAiState([
      'searchSummaryList',
      'selectedSearchTab',
      'activeUuid',
      'selectedDocumentSummary',
      'allFileIds',
    ])
    this.aiFacade.triggerReset(true)
    this.hasSummaryList.set(false)
  }

  #selectWsConnectionState(): void {
    this.webSocketService.connectionState$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isConnected) => {
        this.isConnected.set(isConnected === 'connected')
      })
  }

  #connectWebSocket(): void {
    const userName = this.currentUser()?.userName

    this.webSocketService.connect(
      environment.chatApiUrl + '/api/v1/chat/ws/' + userName
    )
  }

  #focusMessageBox(): void {
    setTimeout(() => this.messageBox.focus(), 500)
  }

  #selectProject(): void {
    this.projectFacade.selectProjectInfoSuccessResponse$
      .pipe(
        filter((projectInfo) => Boolean(projectInfo)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.projectInfo.set(res.data)
      })
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserDetails$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((user) => {
        this.currentUser.set(user)
      })
  }

  #selectResetTrigger(): void {
    this.aiFacade.selectIsResetTriggered$
      .pipe(
        filter((isReset) => isReset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.messageBox.blur()
        this.messageBox.updateValue('')
        this.messageBox.focus()
        this.aiFacade.resetAiState('isResetTriggered')
      })
  }

  #selectSummaryList(): void {
    this.aiFacade.selectAiSearchAiSummaryList$
      .pipe(
        filter((summaryList) => typeof summaryList !== 'undefined'),
        map((summaryList) =>
          Object.values(summaryList).some((s) =>
            Boolean(s.original_query.trim())
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((hasSummaryList) => {
        this.hasSummaryList.set(hasSummaryList)
      })
  }
}
