import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'
import { LayoutModule, SelectEvent } from '@progress/kendo-angular-layout'
import { PageControlActionType } from '../shared/enum/page-control-action-type.enum'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule, SVGIcon } from '@progress/kendo-angular-icons'
import {
  arrowRotateCwIcon,
  chevronLeftIcon,
  plusIcon,
} from '@progress/kendo-svg-icons'
import { employees } from '../launchpad-caseui/employees'

import { Employee, employeestree } from './employeetree'
import {
  FlatBindingDirective,
  SelectableSettings,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { CommonActionButtonsComponent } from '../shared/common/common-action-buttons/common-action-buttons.component'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-edit-tags-coding',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    GridModule,
    InputsModule,
    SvgLoaderDirective,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    IconsModule,
    TreeListModule,
    CommonActionButtonsComponent,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './edit-tags-coding.component.html',
  styleUrls: ['./edit-tags-coding.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditTagsCodingComponent implements OnInit {
  public opened = false

  public addStatus = false

  public customFields = false

  public dialogTitle = 'Edit - Tags & Coding'

  public refreshSvg: SVGIcon = arrowRotateCwIcon

  public plusSvg: SVGIcon = plusIcon

  public leftSvg: SVGIcon = chevronLeftIcon

  @ViewChild(FlatBindingDirective) public dataBinding: FlatBindingDirective

  public data: Employee[] = employeestree

  public form: FormGroup

  constructor(private fb: FormBuilder) {}

  public settings: SelectableSettings = {
    mode: 'row',
    multiple: true,
    drag: false,
  }

  public selected: any[] = []

  public sampleCustomers: any[] = [
    {
      Tag: 1,
      ProductName: 'Responsive',
      SupplierID: 11,
    },
    {
      Tag: 2,
      ProductName: 'Privilege',
      SupplierID: 21,
    },
    {
      Tag: 3,
      ProductName: 'Potentially Privilege',
      SupplierID: 31,
    },
    {
      Tag: 4,
      ProductName: 'Hot',
      SupplierID: 41,
    },
    {
      Tag: 5,
      ProductName: 'Review Complete',
      SupplierID: 51,
    },
  ]

  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'Tag Group Name',
    value: null,
  }

  // color picker value
  public selectedColor = '#E21E36'

  public svgIconForTagControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-tagedit-delete.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-action-grid-pencil.svg',
    },
    {
      actionType: PageControlActionType.VIEW_DETAIL,
      iconPath: 'assets/svg/icon-tagedit-copy.svg',
    },
  ]

  public sampleData = [
    {
      Id: 'ALFKI',
      CompanyName: 'Demo_master-Site Admin Group',
      ContactName: 'Maria Anders',
      ContactTitle: 'Sales Representative',
      City: 'Berlin',
    },
    {
      Id: 'ANATR',
      CompanyName: 'Demo_master-Project Admin Group',
      ContactName: 'Ana Trujillo',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'ANTON',
      CompanyName: 'Demo_master-User Group',
      ContactName: 'Antonio Moreno',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'AROUT',
      CompanyName: 'Demo_master-Viewer Group',
      ContactName: 'Thomas Hardy',
      ContactTitle: 'Sales Representative',
      City: 'London',
    },
  ]

  public svgIconForGridControls = [
    {
      actionType: 'ACTION_REFRESH',
      iconPath: 'assets/svg/icon-grid-action-refresh.svg',
    },
    {
      actionType: 'ACTION_UPLOAD',
      iconPath: 'assets/svg/icon-grid-action-cloud-upload.svg',
    },
    {
      actionType: 'ACTION_GRAPH',
      iconPath: 'assets/svg/icon-grid-action-graph.svg',
    },
    {
      actionType: 'ACTION_DOWNLOAD',
      iconPath: 'assets/svg/icon-grid-action-cloud-download.svg',
    },
  ]

  public gridData: unknown[] = employees

  public tagGridStatus = true

  public ngOnInit(): void {
    this.openDialog()
    this.form = this.fb.group({
      yesNo: ['Yes'], // Default to 'Yes'
      defaultValue: [''],
      enableYesNo: ['Yes'],
      delimiterValue: [''],
      multipleYesNo: ['No'],
    })
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public openDialog(): void {
    this.opened = true
  }

  public openAdd(): void {
    this.addStatus = true
    this.dialogTitle = 'Add - Tags'
  }

  public openEdit(): void {
    this.addStatus = false
    this.dialogTitle = 'Edit - Tags & Coding'
    this.tagGridStatus = true
  }

  public browseActionClicked(
    actionType: any //'FIRST_PAGE' | 'NEXT_PAGE' | 'PREV_PAGE' | 'LAST_PAGE'
  ): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }

  public onColorChange(color: string): void {
    this.selectedColor = color
  }

  public onTabSelect(e: SelectEvent): void {
    e.index === 1 ? (this.tagGridStatus = false) : (this.tagGridStatus = true)
  }

  public addNewCodingField(): void {
    this.customFields = !this.customFields
  }

  // Utility method to check the radio button value
  public isNoSelected(): boolean {
    return this.form.get('yesNo').value === 'No'
  }

  public isEnableCodingSelected(): boolean {
    return this.form.get('enableYesNo').value === 'No'
  }

  public isNoDelimiter(): boolean {
    return this.form.get('multipleYesNo').value === 'No'
  }
}
