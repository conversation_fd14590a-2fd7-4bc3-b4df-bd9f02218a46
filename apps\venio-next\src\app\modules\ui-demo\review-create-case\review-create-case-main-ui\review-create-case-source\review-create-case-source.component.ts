import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { FormsModule } from '@angular/forms'
import { FilterExpandSettings } from '@progress/kendo-angular-treeview'
import {
  IconsModule,
  SVGIcon,
  SVGIconModule,
} from '@progress/kendo-angular-icons'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LabelModule } from '@progress/kendo-angular-label'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'

@Component({
  selector: 'venio-review-create-case-source',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    InputsModule,
    FormsModule,
    IconsModule,
    SVGIconModule,
    ButtonsModule,
    LabelModule,
    DateInputsModule,
  ],
  templateUrl: './review-create-case-source.component.html',
  styleUrl: './review-create-case-source.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseSourceComponent {
  public isDisabled = true

  public downIcon: SVGIcon = chevronDownIcon

  public listItems: Array<string> = ['Item 1', 'Item 2']

  public tagDropdowndata: any[] = [
    {
      text: 'Node One',
      items: [{ text: 'Node A0' }, { text: 'Node A1' }, { text: 'Node A2' }],
    },
    {
      text: 'Node Two',
      items: [{ text: 'Node B0' }, { text: 'Node B1' }, { text: 'Node B2' }],
    },
  ]

  public expandedOnClear: 'none' | 'all' | 'initial' | 'unchanged' = 'none'

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public get filterExpandSettings(): FilterExpandSettings {
    return { expandedOnClear: this.expandedOnClear }
  }
}
