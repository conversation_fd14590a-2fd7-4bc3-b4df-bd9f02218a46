<div class="t-w-full">
  <div class="t-bg-white t-rounded-xl t-p-6 t-flex t-flex-col t-gap-4 t-shadow-sm">
    <div class="t-flex t-items-center t-gap-2">
      <app-title-and-download [title]="'Content Filter'"></app-title-and-download>
      <a href="#" class="t-text-purple-600 t-text-sm t-underline">View Results</a>
    </div>
    <div class="t-w-full t-min-h-[300px]">
      <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"></plotly-plot>
    </div>
  </div>
</div>