<div class="t-flex t-gap-2 t-h-full">
  <div class="t-w-1/3 t-bg-[#FBFBFB] t-p-2 t-pb-4">
    <div class="t-flex t-flex-col t-my-2">
      <span class="t-inline-flex t-items-center t-gap-2 t-pt-[15px]">
        <input
          type="checkbox"
          #reprocessInNewMedia
          kendoCheckBox
          [(ngModel)]="reprocessInNewMediaChecked" />
        <kendo-label
          class="k-radio-label"
          [for]="reprocessInNewMedia"
          text="Reprocess in new media"></kendo-label>
      </span>
      <span class="t-mt-4 t-flex t-items-start">
        <input
          kendoTextBox
          placeholder="Media name ..."
          class="t-ml-[20px]"
          [disabled]="!reprocessInNewMediaChecked" />
      </span>
    </div>
    <div class="t-flex t-gap-3 t-items-center">
      <span class="t-inline-flex t-gap-0">
        <input
          type="radio"
          kendoRadioButton
          name="radioGroup"
          #execptionType
          value="execptionType"
          [(ngModel)]="selectedRadioOption" />
        <kendo-label
          class="k-radio-label"
          [for]="execptionType"
          text="Exception Types"></kendo-label>
      </span>

      <span class="t-inline-flex t-gap-0">
        <input
          type="radio"
          kendoRadioButton
          name="radioGroup"
          #searchTag
          value="searchTag"
          [(ngModel)]="selectedRadioOption" />
        <kendo-label
          class="k-radio-label"
          [for]="searchTag"
          text="Search Tags"></kendo-label>
      </span>
    </div>

    <p class="t-my-4">Show selected type of documents</p>

    <div
      class="t-flex t-h-[80%]"
      *ngIf="selectedRadioOption === 'execptionType'">
      <kendo-grid
        class="t-w-full t-h-[40vh] t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
        [kendoGridBinding]="extractionData"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        kendoGridSelectBy="id">
        <!-- Header Template -->

        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="40"></kendo-grid-column>
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
        </kendo-grid-checkbox-column>
        <kendo-grid-column
          field="status"
          title="Select All"
          headerClass="t-text-primary"
          [width]="170">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{ dataItem.status }}</span>
          </ng-template>
        </kendo-grid-column>

        <!-- Bulk Settings Column -->
        <kendo-grid-column title="" [width]="110" headerClass="t-text-primary">
          <ng-template kendoGridCellTemplate>
            <span class="t-text-[#1CBBDC]">Bulk Settings</span>
          </ng-template>
        </kendo-grid-column>

        <!-- Password Bank Column -->
        <kendo-grid-column title="" [width]="130" headerClass="t-text-primary">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span
              class="t-text-[#1CBBDC] t-cursor-pointer"
              (click)="openPasswordBankDialog()"
              *ngIf="dataItem.status === 'Password Protected'">
              Password Bank
            </span>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex t-flex-col" *ngIf="selectedRadioOption === 'searchTag'">
      <kendo-treelist
        [kendoTreeListFlatBinding]="data"
        idField="id"
        parentIdField="ParentTagId"
        [height]="250"
        kendoTreeListExpandable
        [initiallyExpanded]="true"
        kendoTreeListSelectable
        [ngClass]="'v-custom-tagtree t-mb-5'"
        [columnMenu]="false"
        [sortable]="true"
        [resizable]="true"
        [reorderable]="true"
        [navigable]="true"
        [pageable]="false"
        [pageSize]="100"
        [rowReorderable]="true">
        <kendo-treelist-checkbox-column
          headerClass="t-text-primary v-custom-tagheader"
          [expandable]="true"
          field="name"
          title="Name"
          [width]="140"
          [checkChildren]="true"
          [showSelectAll]="true">
          <ng-template kendoTreeListCellTemplate let-dataItem>
            <div
              class="t-inline-block t-absolute t-ml-7 t-pointer-events-none t-select-none">
              {{ dataItem.TagName }}
              <span> ({{ dataItem.TagCount }}) </span>
            </div>
          </ng-template>
        </kendo-treelist-checkbox-column>
      </kendo-treelist>
      <p class="t-my-2">Tag(s) selected: 0</p>
    </div>
  </div>
  <div class="t-w-2/3 t-p-2">
    <div class="t-h-[70px] t-flex t-items-center">
      <div class="t-flex t-justify-between t-items-center t-w-full t-p-2">
        <span class="t-inline-flex t-items-center t-gap-2">
          <input type="checkbox" #allFiles kendoCheckBox [checked]="true" />
          <kendo-label
            class="k-radio-label"
            [for]="allFiles"
            text="All Files"></kendo-label>
        </span>

        <div class="t-flex t-gap-2">
          <button
            kendoButton
            class="!t-border-[#2F3081] t-text-[#2F3081] hover:t-border-[#2F3081] hover:t-text[#2F3081]"
            fillMode="outline">
            Bulk Settings
          </button>

          <button
            kendoButton
            class="!t-border-[#253238] t-text-[#253238] hover:t-border-[#253238] hover:t-text[#253238]"
            fillMode="outline"
            (click)="openLoadfileDialog()">
            Use Load File
          </button>

          <button
            kendoButton
            class="v-custom-secondary-button"
            themeColor="secondary"
            fillMode="outline">
            Save As Load File
          </button>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-h-[calc(100%-140px)] t-mt-3">
      <kendo-grid
        class="t-w-full t-h-full t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
        [kendoGridBinding]="allFilesData"
        venioDynamicHeight
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        kendoGridSelectBy="id"
        [pageable]="{ type: 'numeric', position: 'top' }"
        [sortable]="true"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true">
        <ng-template kendoPagerTemplate>
          <div class="t-flex t-gap-2">
            <button
              kendoButton
              themeColor="secondary"
              (click)="openOverlay('custodian')"
              class="t-w-9 t-h-9 hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
              fillMode="solid"
              #actionGrid1>
              <span
                venioSvgLoader
                [parentElement]="actionGrid1.element"
                applyEffectsTo="stroke"
                hoverColor="#FFFFFF"
                color="#FFFFFF"
                svgUrl="assets/svg/icon-reprocessing-mashup.svg"
                height="1.1rem"
                width="1.1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </div>
          <kendo-grid-spacer></kendo-grid-spacer>

          <venio-pagination
            [disabled]="allFilesData?.length === 0"
            [totalRecords]="allFilesData?.length"
            [pageSize]="15"
            [showPageJumper]="false"
            [showPageSize]="true"
            [showRowNumberInputBox]="true"
            class="t-px-5 t-block t-py-2">
          </venio-pagination>
        </ng-template>

        <!-- Checkbox Column -->
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
        </kendo-grid-checkbox-column>

        <!-- File ID Column -->
        <kendo-grid-column
          field="fileId"
          title="File ID"
          [width]="100"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="File ID"
              >File ID</span
            >
          </ng-template>
        </kendo-grid-column>

        <!-- Media Name Column -->
        <kendo-grid-column
          field="mediaName"
          title="Media Name"
          [width]="150"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="Media Name"
              >Media Name</span
            >
          </ng-template>
        </kendo-grid-column>

        <!-- File Name Column -->
        <kendo-grid-column
          field="fileName"
          title="File Name"
          [width]="150"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="File Name"
              >File Name</span
            >
          </ng-template>
        </kendo-grid-column>

        <!-- Original File Path Column -->
        <kendo-grid-column
          field="originalFilePath"
          title="Original File Path"
          [width]="200"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="Original File Path"
              >Original File Path</span
            >
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span kendoTooltip title="{{ dataItem.originalFilePath }}">{{
              dataItem.originalFilePath
            }}</span>
          </ng-template>
        </kendo-grid-column>

        <!-- File Type Column -->
        <kendo-grid-column
          field="fileType"
          title="File Type"
          [width]="120"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="File Type"
              >File Type</span
            >
          </ng-template>
        </kendo-grid-column>

        <!-- Exception Type Column -->
        <kendo-grid-column
          field="exceptionType"
          title="Exception Type"
          [width]="160"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="Exception Type"
              >Exception Type</span
            >
          </ng-template>
        </kendo-grid-column>

        <!-- Actions Column -->
        <kendo-grid-column
          title="Actions"
          [width]="165"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span
              kendoTooltip
              class="t-text-ellipsis t-overflow-hidden"
              title="Actions"
              >Actions</span
            >
          </ng-template>
          <ng-template kendoGridCellTemplate>
            <a href="javascript:void(0)" class="bulk-settings">
              <div class="t-flex t-gap-1 t-ml-2">
                <kendo-dropdownlist
                  [(ngModel)]="selectedBulkSetting"
                  [data]="bulkSettingOptions"
                  class="t-w-56">
                </kendo-dropdownlist>
              </div>
            </a>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
    <div class="t-h-[70px] t-flex t-items-center">
      <div
        class="t-flex t-justify-between t-items-center t-p-2 t-mt-[4px] t-w-full">
        <div class="t-flex">
          <span class="t-inline-flex t-items-center t-gap-2">
            <input
              type="checkbox"
              #SelectAllPages
              kendoCheckBox
              [checked]="true" />
            <kendo-label
              class="k-radio-label"
              [for]="SelectAllPages"
              text="Select All from All Pages"></kendo-label>
          </span>
        </div>
        <button
          kendoButton
          #reprocessButton
          themeColor="secondary"
          fillMode="outline"
          class="v-custom-secondary-button t-cursor-pointer">
          <span class="t-flex t-items-center t-gap-[5px]">
            <span
              venioSvgLoader
              [parentElement]="reprocessButton.element"
              color="#9BD2A7"
              hoverColor="#ffffff"
              applyEffectsTo="fill"
              svgUrl="assets/svg/icon-fulltext-refresh.svg"
              height="18px"
              width="18px"></span>
            <span>REPROCESS</span>
          </span>
        </button>
      </div>
    </div>
  </div>
</div>

<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isOverlayActive"
  (click)="isOverlayActive = !isOverlayActive"></div>
<div
  class="t-fixed t-top-[1px] t-w-[56%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isOverlayActive,
    't-right-[-56%]': !isOverlayActive
  }">
  <div class="t-flex t-justify-between t-items-center t-w-full">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="overlayIconUrl"></button>
      {{ overlayTitle }}
    </span>
    <button
      (click)="closeOverlay()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>
  <venio-case-reprocessing-custodian-dialog
    *ngIf="
      activeComponent === 'custodian'
    "></venio-case-reprocessing-custodian-dialog>

  <venio-case-reprocessing-update
    *ngIf="activeComponent === 'update'"></venio-case-reprocessing-update>

  <venio-case-reprocessing-settings
    *ngIf="activeComponent === 'settings'"></venio-case-reprocessing-settings>
</div>

<div kendoDialogContainer></div>

<kendo-dialog
  *ngIf="openLoadFileDialog"
  [maxHeight]="300"
  [width]="'540px'"
  (close)="closeLoadfileDialog()">
  <kendo-dialog-titlebar>
    <kendo-label
      text="Import files for Replacement"
      class="t-text-base t-text-[#2F3080]">
    </kendo-label>
  </kendo-dialog-titlebar>
  <div class="t-flex t-items-start t-space-x-1 t-gap-[5px] t-my-1">
    <div class="t-flex t-flex-col t-gap-[5px]">
      <div class="t-flex t-flex-row t-my-1 t-mx-2">
        <p class="t-font-medium t-pr-[5px]">File Replace Load File</p>
        <span
          class="t-text-center t-cursor-pointer t-rounded-full t-bg-[#2F3080] t-font-black t-text-white t-h-[18px] t-w-[18px]"
          showOn="hover"
          kendoPopoverAnchor
          [popover]="LoadFileHelpPopover"
          >?</span
        >
        <kendo-popover #LoadFileHelpPopover [width]="300" position="right">
          <ng-template kendoPopoverBodyTemplate>
            <div class="t-p-2">
              <kendo-label
                text="Sample Load File For Replacement"
                class="t-text-base t-font-bold t-text-[#2F3080]">
              </kendo-label>
              <div class="t-flex t-flex-col t-items-start t-gap-4 t-mt-3">
                <div>
                  <kendo-label
                    text="Sample Load File"
                    class="t-text-base t-text-[#2F3080]">
                  </kendo-label>
                  <div
                    class="t-text-xs/4 t-my-1 t-font-normal t-align-left t-text-left t-text-[#000000]">
                    <div class="t-pb-[2px]">Field ReplaceFileLocation</div>
                    <div class="t-pb-[2px]">
                      1, "\\ShareServer\ShareFolder\ReplacementFile.txt"
                    </div>
                  </div>
                </div>
                <div>
                  <kendo-label
                    text="Sample Load File with No field Header in first Line"
                    class="t-text-base t-text-[#2F3080]">
                  </kendo-label>
                  <div
                    class="t-text-xs/4 t-my-1 t-font-normal t-align-left t-text-left t-text-[#000000]">
                    <div class="t-pb-[2px]">Field ReplaceFileLocation</div>
                    <div class="t-pb-[2px]">
                      1, "\\ShareServer\ShareFolder\ReplacementFile.txt"
                    </div>
                  </div>
                </div>
                <div>
                  <div>
                    <p class="t-pb-[2px] t-text-base t-text-[#ED7425]">
                      <b>Note:</b>
                    </p>
                    <p class="t-pb-[2px] t-text-[#ED7425]">
                      Please Use File Location of the "To be Replaced File" in
                      the "ReplaceFileLocation
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-popover>
      </div>
      <div class="t-flex t-flex-row t-gap-[5px] t-my-2">
        <div class="t-flex t-items-center t-gap-2">
          <input
            kendoTextBox
            placeholder="Choose a text file (*.txt, *.csv)"
            class="t-w-[250px]"
            [value]="selectedFileName"
            readonly />
          <button
            kendoButton
            class="v-custom-secondary-button"
            themeColor="secondary"
            fillMode="outline"
            (click)="fileInput.click()">
            Browse
          </button>
          <input
            #fileInput
            type="file"
            class="t-hidden"
            (change)="onFileSelected($event)" />
        </div>
        <div>
          <button
            kendoButton
            class="t-rounded-none"
            themeColor="dark"
            [disabled]="true"
            fillMode="outline">
            Validate
          </button>
        </div>
      </div>
      <div class="t-inline-flex t-items-center t-gap-[5px]">
        <kendo-checkbox #LoadingCheckbox></kendo-checkbox>
        <kendo-label
          text="No field Header in first line of Load File"
          for="LoadingCheckbox"></kendo-label>
      </div>
    </div>
  </div>
  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        themeColor="secondary"
        fillMode="outline"
        class="v-custom-secondary-button"
        (click)="closeLoadfileDialog()">
        Save
      </button>
      <button
        kendoButton
        (click)="closeLoadfileDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog
  *ngIf="passwordBankDialog"
  [maxHeight]="780"
  [width]="'500'"
  (close)="closePasswordBankDialog()">
  <kendo-dialog-titlebar>
    <kendo-label text="Password Bank" class="t-text-base t-text-[#2F3080]">
    </kendo-label>
  </kendo-dialog-titlebar>
  <div class="block">
    <kendo-tabstrip
      (tabSelect)="onSelect($event)"
      class="v-custom-tabstrip v-custom-tabstrip-case v-custom-tabstrip-password t-w-[600px] t-h-[600px]">
      <kendo-tabstrip-tab title="Password" [selected]="tabStatus === 0">
        <ng-template kendoTabContent>
          <div
            class="t-flex t-flex-col t-items-start t-my-2 t-gap-[5px] t-mt-[15px] t-p-[15px]">
            <div
              class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
              <span class="t-flex t-pr-[50px]">
                <kendo-label
                  text="Add Password"
                  class="t-text-base t-font-bold t-text-[#2F3080]">
                </kendo-label>
              </span>
              <span class="t-flex">
                <button
                  kendoButton
                  class="v-custom-secondary-button"
                  themeColor="secondary"
                  fillMode="outline"
                  (click)="openImportPasswordDialog()">
                  Import Password List
                </button>
              </span>
            </div>
            <div>
              <p>New Password <span class="t-text-[#DC3545]">*</span></p>
            </div>
            <div class="t-flex t-flex-row t-gap-[5px]">
              <input
                kendoTextBox
                type="password"
                placeholder="Password"
                [(ngModel)]="passwordInput" />
              <button
                kendoButton
                themeColor="secondary"
                fillMode="outline"
                class="v-custom-secondary-button"
                (click)="addPassword()">
                Add
              </button>
            </div>
            <div class="t-flex t-flex-col">
              <p
                class="t-my-1 t-text-base t-font-bold t-text-[#2F3080] t-mt-4 t-mb-2">
                Password List
              </p>
              <kendo-grid [data]="passwordDummyData" [height]="300">
                <kendo-grid-column
                  headerClass="t-text-primary"
                  field="password"
                  title="Password"></kendo-grid-column>
                <kendo-grid-column title="Delete" headerClass="t-text-primary">
                  <ng-template
                    kendoGridCellTemplate
                    let-dataItem
                    let-rowIndex="rowIndex">
                    <button
                      kendoButton
                      (click)="deleteRow(rowIndex)"
                      [look]="'outline'"
                      class="t-text-red-600">
                      Delete
                    </button>
                  </ng-template>
                </kendo-grid-column>
              </kendo-grid>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab
        title="Lotus Notes User Id, Password"
        [selected]="tabStatus === 1">
        <ng-template kendoTabContent>
          <ng-container *ngIf="tabStatus === 1">
            <div
              class="t-flex t-flex-col t-items-start t-my-2 t-gap-[5px] t-mt-[15px] t-p-[15px]">
              <div
                class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
                <span class="t-flex t-pr-[20px]">
                  <kendo-label
                    text="Add NSF User Id, Password"
                    class="t-text-base t-font-bold t-text-[#2F3080]">
                  </kendo-label>
                </span>
                <span class="t-flex">
                  <button
                    kendoButton
                    (click)="openimportNsfUserIdPasswordDialog()"
                    class="v-custom-secondary-button"
                    themeColor="secondary"
                    fillMode="outline">
                    Import User Id File, Password List
                  </button>
                </span>
              </div>
              <div>
                <p>NSF User Id file <span class="t-text-[#DC3545]">*</span></p>
              </div>
              <div class="t-flex t-items-center t-gap-2">
                <input
                  kendoTextBox
                  placeholder="Browse .id file"
                  class="t-w-[250px]"
                  [value]="selectedFileName"
                  readonly />
                <button
                  kendoButton
                  class="v-custom-secondary-button"
                  themeColor="secondary"
                  fillMode="outline"
                  (click)="fileInput.click()">
                  Browse
                </button>
                <input
                  #fileInput
                  type="file"
                  class="t-hidden"
                  (change)="onFileSelected($event)" />
              </div>
              <div class="t-my-1">
                <div
                  class="t-flex t-flex-col t-gap-2 t-items-start t-py-[5px] t-px-[5px] t-my-3">
                  <span class="t-inline-flex t-gap-0">
                    <input
                      type="radio"
                      kendoRadioButton
                      name="lotusRadioGroup"
                      #noAssociatedPassword
                      value="noAssociatedPassword"
                      [(ngModel)]="selectedLotusRadioOption" />
                    <kendo-label
                      class="k-radio-label"
                      [for]="noAssociatedPassword"
                      text="NSF User Id File does not have associated Password
                        "></kendo-label>
                  </span>

                  <span class="t-inline-flex t-gap-0">
                    <input
                      type="radio"
                      kendoRadioButton
                      name="lotusRadioGroup"
                      #AssociatedPassword
                      value="associatedPassword"
                      [(ngModel)]="selectedLotusRadioOption" />
                    <kendo-label
                      class="k-radio-label"
                      [for]="AssociatedPassword"
                      text="NSF User Id File has associated Password"></kendo-label>
                  </span>
                </div>
                <div
                  *ngIf="selectedLotusRadioOption === 'noAssociatedPassword'">
                  <div class="t-flex t-flex-row t-gap-[5px]">
                    <span>
                      <kendo-label for="lotusPasswordTextbox"></kendo-label>
                    </span>
                    <input
                      kendoTextBox
                      #lotusPasswordTextbox
                      type="password"
                      disabled="true"
                      placeholder="Password"
                      [(ngModel)]="passwordInput" />
                    <button
                      kendoButton
                      themeColor="secondary"
                      fillMode="outline"
                      class="v-custom-secondary-button"
                      (click)="addPassword()">
                      Add
                    </button>
                  </div>
                </div>

                <div *ngIf="selectedLotusRadioOption === 'associatedPassword'">
                  <div class="t-flex t-flex-row t-gap-[5px]">
                    <span>
                      <kendo-label for="lotusPasswordTextbox"></kendo-label>
                    </span>
                    <input
                      kendoTextBox
                      #lotusPasswordTextbox
                      type="password"
                      placeholder="Password"
                      [(ngModel)]="passwordInput" />
                    <button
                      kendoButton
                      themeColor="secondary"
                      fillMode="outline"
                      class="v-custom-secondary-button"
                      (click)="addPassword()">
                      Add
                    </button>
                  </div>
                </div>
              </div>
              <div class="t-flex t-flex-col">
                <p
                  class="t-my-1 t-text-base t-text-[#2F3080] t-font-bold t-mt-4 t-mb-2">
                  NSF User Id File Password List
                </p>
                <kendo-grid [data]="passwordDummyData" [height]="240">
                  <kendo-grid-column
                    headerClass="t-text-primary"
                    field="NSFUserIdFile"
                    title="NSF User Id  File"></kendo-grid-column>
                  <kendo-grid-column
                    headerClass="t-text-primary"
                    field="password"
                    title="Password"></kendo-grid-column>
                  <kendo-grid-column
                    title="Delete"
                    headerClass="t-text-primary">
                    <ng-template
                      kendoGridCellTemplate
                      let-dataItem
                      let-rowIndex="rowIndex">
                      <button
                        kendoButton
                        (click)="deleteRow(rowIndex)"
                        [icon]="'trash'"
                        [look]="'outline'"
                        class="t-text-red-600">
                        Delete
                      </button>
                    </ng-template>
                  </kendo-grid-column>
                </kendo-grid>
              </div>
            </div>
          </ng-container>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>
  </div>
  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        (click)="closePasswordBankDialog()">
        Save
      </button>
      <button
        kendoButton
        (click)="closePasswordBankDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog
  [maxHeight]="780"
  [width]="'650'"
  [maxWidth]="656"
  *ngIf="importPasswordDialog"
  (close)="closeImportPasswordDialog()">
  <kendo-dialog-titlebar
    ><kendo-label text="Password List" class="t-text-base t-text-[#2F3080]">
    </kendo-label
  ></kendo-dialog-titlebar>

  <!-- Browse File and Search Field -->
  <div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
    <!-- Browse File -->
    <div class="t-flex t-flex-row t-w-full t-items-center t-gap-[15px]">
      <span>
        <kendo-label text="Browse file"></kendo-label>
      </span>
      <span class="t-w-[40%]">
        <input
          kendoTextBox
          readonly
          placeholder="Choose a text file (*.txt, *.csv)" />
      </span>
      <span>
        <button
          kendoButton
          (click)="fileInput.click()"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline">
          Browse
        </button>
      </span>
      <input
        #fileInput
        type="file"
        class="t-hidden"
        (change)="onFileSelected($event)"
        accept=".txt, .csv"
        multiple />
      <span
        class="t-cursor-pointer t-text-center t-rounded-full t-bg-[#2F3080] t-font-black t-text-white t-h-[24px] t-w-[24px] t-flex t-items-center t-justify-center"
        (click)="openpasswordBankHelpDialog()"
        >?</span
      >
    </div>

    <!-- Search Field -->
    <div class="t-flex t-justify-end t-gap-2 t-h-[45px]">
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[25rem]"
        placeholder="Search"
        [clearButton]="true">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            fillMode="clear"
            class="t-text-[#1EBADC]"
            imageUrl="assets/svg/icon-updated-search.svg"></button>
        </ng-template>
      </kendo-textbox>
    </div>
  </div>

  <!-- Kendo Grid -->
  <kendo-grid
    class="t-grid !t-w-[550px] !t-h-[350px] t-border-b-1 t-relative t-overflow-y-auto"
    [kendoGridBinding]="importPasswordGridData"
    [sortable]="true"
    [scrollable]="'scrollable'"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    kendoGridSelectBy="id"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }">
    <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
    </kendo-grid-checkbox-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="id"
      title="#"
      [autoSize]="true"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="password"
      title="Password"
      [width]="150"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="added"
      title="Added"
      [width]="100"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="error"
      title="Error Message"
      [width]="190"></kendo-grid-column>
  </kendo-grid>
  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        (click)="closeImportPasswordDialog()">
        Import
      </button>
      <button
        kendoButton
        (click)="closeImportPasswordDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog
  [maxHeight]="780"
  [width]="'650'"
  [maxWidth]="656"
  *ngIf="importNsfUserIdPasswordDialog"
  (close)="closeimportNsfUserIdPasswordDialog()">
  <kendo-dialog-titlebar
    ><kendo-label text="Password List" class="t-text-base t-text-[#2F3080]">
    </kendo-label
  ></kendo-dialog-titlebar>

  <!-- Browse File and Search Field -->
  <div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
    <!-- Browse File -->
    <div class="t-flex t-flex-row t-w-full t-items-center t-gap-[15px]">
      <span>
        <kendo-label text="Browse file"></kendo-label>
      </span>
      <span class="t-w-[40%]">
        <input
          kendoTextBox
          readonly
          placeholder="Choose a text file (*.txt, *.csv)" />
      </span>
      <span>
        <kendo-button
          (click)="fileInput.click()"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline">
          Browse
        </kendo-button>
      </span>
      <input
        #fileInput
        type="file"
        class="t-hidden"
        (change)="onFileSelected($event)"
        accept=".txt, .csv"
        multiple />
      <span
        class="t-cursor-pointer t-text-center t-rounded-full t-bg-[#2F3080] t-font-black t-text-white t-h-[24px] t-w-[24px] t-flex t-items-center t-justify-center"
        (click)="openpasswordBankNsfUserIdHelpDialog()"
        >?</span
      >
    </div>

    <!-- Search Field -->
    <div class="t-flex t-justify-end t-gap-2 t-h-[45px]">
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[25rem]"
        placeholder="Search"
        [clearButton]="true">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            fillMode="clear"
            class="t-text-[#1EBADC]"
            imageUrl="assets/svg/icon-updated-search.svg"></button>
        </ng-template>
      </kendo-textbox>
    </div>
  </div>

  <!-- Kendo Grid -->
  <kendo-grid
    class="t-grid t-w-[550px] t-h-[350px] t-border-b-1 t-relative t-overflow-y-auto"
    [kendoGridBinding]="importPasswordGridData"
    [sortable]="true"
    [scrollable]="'scrollable'"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    kendoGridSelectBy="id"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }">
    <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
    </kendo-grid-checkbox-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="id"
      title="#"
      [autoSize]="true"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="nsfuserid"
      title="NSF User Id File"
      [width]="200"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="password"
      title="Password"
      [width]="150"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="added"
      title="Added"
      [width]="100"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="error"
      title="Error Message"
      [width]="190"></kendo-grid-column>
  </kendo-grid>
  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        (click)="closeimportNsfUserIdPasswordDialog()">
        Import
      </button>
      <button
        kendoButton
        (click)="closeimportNsfUserIdPasswordDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog
  [maxHeight]="480"
  [width]="'400'"
  [maxWidth]="416"
  *ngIf="passwordBankHelpDialog"
  (close)="closepasswordBankHelpDialog()">
  <kendo-dialog-titlebar
    ><kendo-label
      text="Password Bank Help"
      class="t-text-base t-text-[#2F3080]">
    </kendo-label
  ></kendo-dialog-titlebar>

  <div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
    <div>
      <kendo-label
        text="Sample Load File (*.txt,*.csv) Encoding UTF8:"
        class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div
        class="t-text-xs/4 t-mt-4 t-font-normal t-mb-4 t-align-left t-text-left t-text-[#000000]">
        <div class="t-pb-[2px]">Password1</div>
        <div class="t-pb-[2px]">Password2</div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">PasswordN</div>
      </div>
    </div>

    <div>
      <kendo-label text="Note:" class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div>
        <p class="t-pb-[2px]">
          Note: - Import file size must be < <b>15 MB</b>.
        </p>
        <p class="t-pb-[2px]">- Import file must be <= <b>25000</b> lines.</p>
        <p class="t-pb-[2px]">- File must be in ".csv" or ".txt" extension.</p>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="closepasswordBankHelpDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog
  [maxHeight]="480"
  [width]="'400'"
  [maxWidth]="416"
  *ngIf="passwordBankNsfUserIdHelpDialog"
  (close)="closepasswordBankNsfUserIdHelpDialog()">
  <kendo-dialog-titlebar
    ><kendo-label
      text="Password Bank Help"
      class="t-text-base t-text-[#2F3080]">
    </kendo-label
  ></kendo-dialog-titlebar>

  <div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
    <div>
      <kendo-label
        text="Sample Load File (*.txt,*.csv) Encoding UTF8:"
        class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div
        class="t-text-xs/4 t-mt-4 t-font-normal t-mb-4 t-align-left t-text-left t-text-[#000000]">
        <div class="t-pb-[2px]">FilePath, Password</div>
        <div class="t-pb-[2px]">"\\ShareServer\ShareFolder\user1.id",</div>
        <div class="t-pb-[2px]">
          "\\ShareServer\ShareFolder\user2.id","password2"
        </div>
        <div class="t-pb-[2px]">
          "\\ShareServer\ShareFolder\userN.id","passwordN"
        </div>
      </div>
    </div>

    <div>
      <kendo-label
        text="Field Name	Descriptions"
        class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <table class="t-w-full t-border-collapse t-border t-rounded-lg">
        <thead>
          <tr class="t-bg-gray-100">
            <th
              class="t-border t-px-4 t-py-2 t-text-left t-text-base t-text-[#2F3080] t-bg-gray-100">
              File Name
            </th>
            <th
              class="t-border t-px-4 t-py-2 t-text-left t-text-base t-text-[#2F3080] t-bg-gray-100">
              Description
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="t-border t-px-4 t-py-2">FilePath</td>
            <td class="t-border t-px-4 t-py-2">
              NSF User Id file path in the share folder
            </td>
          </tr>
          <tr>
            <td class="t-border t-px-4 t-py-2">Password</td>
            <td class="t-border t-px-4 t-py-2">
              NSF User Id File has associated Password
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div>
      <kendo-label text="Note:" class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div>
        <p class="t-pb-[2px]">
          Note: - Import file size must be < <b>15 MB</b>.
        </p>
        <p class="t-pb-[2px]">- Import file must be <= <b>25000</b> lines.</p>
        <p class="t-pb-[2px]">- File must be in ".csv" or ".txt" extension.</p>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="closepasswordBankNsfUserIdHelpDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
