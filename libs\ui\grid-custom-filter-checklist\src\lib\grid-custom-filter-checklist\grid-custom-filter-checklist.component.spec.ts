import { ComponentFixture, TestBed } from '@angular/core/testing'
import { GridCustomFilterChecklistComponent } from './grid-custom-filter-checklist.component'

describe('GridCustomFilterChecklistComponent', () => {
  let component: GridCustomFilterChecklistComponent
  let fixture: ComponentFixture<GridCustomFilterChecklistComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GridCustomFilterChecklistComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(GridCustomFilterChecklistComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
