import { provideMockStore } from '@ngrx/store/testing'
import { ComponentFixture, TestBed } from '@angular/core/testing'
import {
  CaseInfoFacade,
  DocumentsFacade,
  DocumentsService,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
  UIService,
} from '@venio/data-access/review'
import { DocumentTableComponent } from './document-table.component'
import { DocumentTableModule } from './document-table.module'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject } from 'rxjs'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { FieldDateConversionService } from '@venio/util/utilities'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'

jest.doMock('@progress/kendo-angular-treelist')
jest.doMock('@progress/kendo-angular-dialog')

describe('DocumentTableComponent', () => {
  let component: DocumentTableComponent
  let fixture: ComponentFixture<DocumentTableComponent>
  let queryParamsSubject: BehaviorSubject<any>
  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })
    await TestBed.configureTestingModule({
      imports: [DocumentTableModule, NoopAnimationsModule],
      declarations: [DocumentTableComponent],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        SearchFacade,
        FieldFacade,
        SearchResultFacade,
        DocumentsFacade,
        StartupsFacade,
        CaseInfoFacade,
        DocumentsService,
        UIService,
        {
          provide: FieldDateConversionService,
          useValue: {
            convertFieldDates: jest.fn(),
          },
        },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        ReviewFacade,
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin, // Adjust the origin accordingly
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentTableComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
