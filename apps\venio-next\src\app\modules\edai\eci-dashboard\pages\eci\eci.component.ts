import { Component, OnInit } from '@angular/core';
import { KENDO_LAYOUT } from '@progress/kendo-angular-layout';
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons';
import { CommonModule } from '@angular/common';
import { DropDownButtonModule } from '@progress/kendo-angular-buttons';
import { filterIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { SummaryComponent } from '../../shared/summary/summary.component';
import { KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { KENDO_POPUP } from '@progress/kendo-angular-popup';
import { KENDO_LISTVIEW } from '@progress/kendo-angular-listview';
import { custodians } from './mockData';
import { CheckboxListItemComponent } from '../../shared/checkbox-list-item/checkbox-list-item.component';
import { RelevanceComponent } from '../../shared/relevance/relevance.component';
import { SunburstComponent } from '../../shared/sunburst/sunburst.component';
import { InappropriateContentComponent } from '../../shared/inappropriate-content/inappropriate-content.component';
import { WordCloudComponent } from '../../shared/word-cloud/word-cloud.component';
import { FocusedSectionComponent } from '../../shared/focused-section/focused-section.component';
import { DataService } from '../../shared/data.service';
@Component({
  selector: 'app-eci',
  standalone: true,
  imports: [
    KENDO_LAYOUT,
    KENDO_BUTTONS,
    KENDO_POPUP,
    KENDO_INPUTS,
    KENDO_LISTVIEW,
    DropDownButtonModule,
    CommonModule,
    SummaryComponent,
    CheckboxListItemComponent,
    RelevanceComponent,
    SunburstComponent,
    InappropriateContentComponent,
    WordCloudComponent,
    FocusedSectionComponent
  ],
  templateUrl: './eci.component.html',
  styleUrls: ['./eci.component.scss']
})
export class EciComponent implements OnInit {
  isFocusedSectionOpened = false;
  constructor(private dataService: DataService) { }

  public custodians: any[] = custodians;
  public showFocused = true;
  public show = false;
  public showCustodianFilters = false;

  ngOnInit() {
    this.dataService.isFocusedSectionOpened$.subscribe(val => {
      this.isFocusedSectionOpened = val;
      // You can now use this boolean in your template or logic
      console.log('Focused section opened:', val);
    });
  }

  public onToggle(): void {
    this.show = !this.show;
  }
  public svgFilter: SVGIcon = filterIcon;
  public onCustodianClick(): void {
    this.showCustodianFilters = !this.showCustodianFilters;
  }
}
