@import 'variables';

@layer {
  kendo-tabstrip {
    &.k-tabstrip {
      &.k-tabstrip-top {
        & > .k-tabstrip-items-wrapper {
          border-bottom: 1px solid #dbdbdb !important;
        }
      }

      .k-tabstrip-items-wrapper {
        .k-item::after {
          @apply t-hidden #{!important};
        }
      }

      .k-tabstrip-content,
      .k-tabstrip > .k-content {
        @apply t-p-0 #{!important};
      }

      .k-tabstrip-items {
        @apply t-mt-2 #{!important};

        .k-item {
          background-color: #f6f6f6 !important;
          min-width: 100px !important;
          @apply t-h-8 t-rounded-lg t-rounded-bl-none t-rounded-br-none t-border-0 #{!important};

          &:active,
          &.k-active,
          &.k-selected {
            background: $hover-bg-color !important;
            color: $hover-text-color !important;
            @apply t-font-normal #{!important};

            &:hover,
            &.k-hover {
            }
          }

          &:hover,
          &.k-hover {
            background: $hover-bg-color !important;
            color: var(--tb-kendo-body-b) !important;
          }

          &:focus {
            @apply t-shadow-none #{!important};
          }

          .k-link {
            @apply t-justify-center #{!important};
          }
        }
      }
    }
    &.v-custom-tabstrip {
      @apply t-h-full t-w-full t-overflow-hidden;
      .k-tabstrip-content {
        @apply t-overflow-hidden #{!important};
      }
    }
    &.v-custom-tabstrip-case {
      &.k-tabstrip {
        .k-tabstrip-items {
          .k-item {
            background-color: #d1e4e2 !important;
          }
        }
      }
      .k-tabstrip-items-wrapper {
        @apply t-pl-3 t-pt-2 t-border-l-[1px] t-border-l-[#ccc] #{!important};
      }
    }
    &.v-custom-tabstrip-password { 
      .k-tabstrip-items-wrapper {
        @apply t-border-l-0 #{!important};
      }
    }
  }
}
