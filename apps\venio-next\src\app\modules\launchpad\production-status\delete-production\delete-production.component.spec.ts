import { TestBed, ComponentFixture } from '@angular/core/testing'
import { DeleteProductionComponent } from './delete-production.component'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { FormsModule } from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { ProductionFacade } from '@venio/data-access/common'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('DeleteProductionComponent', () => {
  let component: DeleteProductionComponent
  let fixture: ComponentFixture<DeleteProductionComponent>

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        DeleteProductionComponent,
        CommonModule,
        InputsModule,
        IconsModule,
        ButtonModule,
        FormsModule,
        LabelModule,
        DialogModule,
      ],
      providers: [
        {
          provide: ProductionFacade,
          useValue: {
            deleteProduction: jest.fn(),
            selectDeleteProductionSuccessResponse$: jest.fn(),
          },
        },
        {
          provide: NotificationService,
          useValue: {
            show: jest.fn(),
          },
        },
      ],
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(DeleteProductionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
