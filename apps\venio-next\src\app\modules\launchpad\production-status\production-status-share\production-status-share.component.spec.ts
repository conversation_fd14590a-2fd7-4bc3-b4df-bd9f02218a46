import { TestBed, ComponentFixture } from '@angular/core/testing'
import { ProductionStatusShareComponent } from './production-status-share.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'
import { provideAnimations } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'
import {
  ProductionFacade,
  ProductionShareInvitationService,
} from '@venio/data-access/common'
import { VenioNotificationService } from '@venio/feature/notification'
import { of } from 'rxjs'
import {
  FormBuilder,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
} from '@angular/forms'

const mockProductionFacade = {
  fetchInternalUser: jest.fn(),
  fetchExternalUser: jest.fn(),
  selectInternalUserSuccessResponse$: of([]),
  selectExternalUserSuccessResponse$: of([]),
  selectUserMessage$: of(null),
  selectInvitationInProgressFlag$: of(false),
  setUserMessage: jest.fn(),
  sendProductionDownloadInvitation: jest.fn(),
  selectSendProductionDownloadInvitationSuccessResponse$: of(null),
  selectSendProductionDownloadInvitationErrorResponse$: of(null),
}

const mockVenioNotificationService = {
  showSuccess: jest.fn(),
  showError: jest.fn(),
}

const mockFormGroup = new FormGroup({
  shareToExternalUsers: new FormControl(false),
  newEmail: new FormControl(''),
  productionDownloadInstruction: new FormControl(''),
  productionDownloadExpirationPeriod: new FormControl(''),
  recipientUserIds: new FormControl([]),
  exportId: new FormControl(),
  invitedExtUserInfo: new FormControl([]),
  invitedIntUserInfo: new FormControl([]),
})

const mockProductionShareInvitationService = {
  productionShareForm: mockFormGroup,
  get: jest.fn().mockImplementation((key: string) => {
    return mockFormGroup.get(key)
  }),
  setExternalUser: jest.fn(),
  setInternalUser: jest.fn(),
  getFormValue: jest.fn().mockReturnValue({}),
}

const mockDialogRef = {
  close: jest.fn(),
}

describe('ProductionStatusShareComponent', () => {
  let component: ProductionStatusShareComponent
  let fixture: ComponentFixture<ProductionStatusShareComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, ProductionStatusShareComponent],
      providers: [
        provideAnimations(), // Enable animations
        { provide: ProductionFacade, useValue: mockProductionFacade },
        {
          provide: VenioNotificationService,
          useValue: mockVenioNotificationService,
        },
        {
          provide: ProductionShareInvitationService,
          useValue: mockProductionShareInvitationService,
        },
        { provide: DialogRef, useValue: mockDialogRef },
        FormBuilder,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(ProductionStatusShareComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
