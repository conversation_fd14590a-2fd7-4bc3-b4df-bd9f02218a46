const fs = require('fs')
const path = require('path')

const rootPackageJsonPath = path.join(__dirname, '../../package.json') // Path to the root package.json
const rootPackageJson = JSON.parse(fs.readFileSync(rootPackageJsonPath, 'utf8'))
const libsPath = path.join(__dirname, '../../libs') // Path to the libs directory

// Function to update the dependencies in a package.json file
const updateDependencies = (libDependencies, rootDependencies) => {
  Object.keys(libDependencies).forEach((dep) => {
    if (rootDependencies.hasOwnProperty(dep)) {
      libDependencies[dep] = rootDependencies[dep]
    }
  })
}

// Function to recursively update package.json files in a directory
const updatePackageJsonFiles = (dir) => {
  fs.readdirSync(dir, { withFileTypes: true }).forEach((entry) => {
    const fullPath = path.join(dir, entry.name)
    if (entry.isDirectory()) {
      updatePackageJsonFiles(fullPath) // Recurse into subdirectories
    } else if (entry.isFile() && entry.name === 'package.json') {
      const libPackageJson = JSON.parse(fs.readFileSync(fullPath, 'utf8'))

      // Update dependencies and peerDependencies
      if (libPackageJson.dependencies) {
        updateDependencies(
          libPackageJson.dependencies,
          rootPackageJson.dependencies
        )
      }
      if (libPackageJson.peerDependencies) {
        updateDependencies(
          libPackageJson.peerDependencies,
          rootPackageJson.dependencies
        )
      }

      // Write the updated package.json back to the file
      fs.writeFileSync(fullPath, JSON.stringify(libPackageJson, null, 2))
      console.log(`Updated dependencies in ${fullPath}`)
    }
  })
}

// Start the update process
updatePackageJsonFiles(libsPath)
