import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  runInInjectionContext,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { TabStripModule } from '@progress/kendo-angular-layout'
import { marked } from 'marked'
import {
  UserFacade,
  WebSocketMessage,
  WebSocketService,
} from '@venio/data-access/common'
import { filter, map, Subject, takeUntil } from 'rxjs'
import { UserModel } from '@venio/shared/models/interfaces'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { AiFacade } from '@venio/data-access/ai'
import { AutoScrollOrFocusDirective } from '@venio/feature/shared/directives'
import { TooltipModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-ai-search-progress',
  standalone: true,
  imports: [
    CommonModule,
    TabStripModule,
    IndicatorsModule,
    AutoScrollOrFocusDirective,
    TooltipModule,
  ],
  templateUrl: './ai-search-progress.component.html',
  styleUrl: './ai-search-progress.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiSearchProgressComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private readonly toDestroy$ = new Subject<void>()

  private webSocketService = inject(WebSocketService)

  private userFacade = inject(UserFacade)

  private aiFacade = inject(AiFacade)

  private injector = inject(Injector)

  private currentUser = signal<UserModel>(undefined)

  public progressMessages = signal<WebSocketMessage[]>([])

  public updateTrigger = 0

  public isSearchLoading = signal<boolean>(false)

  public formatMarkdown(text: string): string {
    const value = (text || '').trim()
    if (!value) {
      return ''
    }

    return marked(value) as string
  }

  private activeUuid = signal<string | undefined>(undefined)

  public ngOnInit(): void {
    this.#selectActiveUuid()
    this.#selectIsAiSearchLoading()
    this.#selectCurrentUser()
    this.#selectProgressMessages()
    this.#toggleDialogPositionCssClass()
    this.#selectResetTrigger()
  }

  public ngAfterViewInit(): void {
    this.#initializeWebSocket()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #initializeWebSocket(): void {
    this.webSocketService.messages$
      .pipe(
        filter((response) => Boolean((response?.message || '').trim())),
        takeUntil(this.toDestroy$)
      )
      .subscribe((data) => {
        this.aiFacade.updateAiProgressMessage(data, this.activeUuid())
      })
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserDetails$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((user) => {
        this.currentUser.set(user)
      })
  }

  #selectIsAiSearchLoading(): void {
    this.aiFacade.selectIsAiSearchLoading$
      .pipe(
        filter((loading) => typeof loading === 'boolean'),
        takeUntil(this.toDestroy$)
      )
      .subscribe((loading) => {
        this.progressMessages.set([])
        this.isSearchLoading.set(loading)
      })
  }

  #selectActiveUuid(): void {
    this.aiFacade.selectActiveUuid$
      .pipe(
        filter((uuid) => typeof uuid === 'string'),
        takeUntil(this.toDestroy$)
      )
      .subscribe((uuid) => {
        this.activeUuid.set(uuid)
      })
  }

  #selectProgressMessages(): void {
    this.aiFacade.selectAiSearchAiSummaryList$
      .pipe(
        filter((summary) => Boolean(summary) && Boolean(this.activeUuid())),
        map((summary) => summary[this.activeUuid()]),
        filter((result) => Boolean(result?.progress_messages)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result) => {
        this.progressMessages.set(result.progress_messages)
      })
  }

  #toggleDialogPositionCssClass(): void {
    runInInjectionContext(this.injector, () =>
      effect(() => {
        const _ = this.progressMessages()
        const randomValue = Math.random() * Math.PI * Math.sqrt(2)
        const truncatedValue = parseFloat(randomValue.toFixed(13))
        this.updateTrigger = truncatedValue
      })
    )
  }

  #selectResetTrigger(): void {
    this.aiFacade.selectIsResetTriggered$
      .pipe(
        filter((isReset) => isReset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.progressMessages.set([])
        this.aiFacade.resetAiState('isResetTriggered')
      })
  }
}
