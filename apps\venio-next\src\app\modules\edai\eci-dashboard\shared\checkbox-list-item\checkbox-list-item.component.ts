import { custodians } from './../../pages/eci/mockData';
import { Component, Input } from '@angular/core';
import { KENDO_INPUTS } from "@progress/kendo-angular-inputs";
import { KENDO_LABELS } from "@progress/kendo-angular-label";
@Component({
  selector: 'venio-eci-checkbox-list-item',
  standalone: true,
  imports: [KENDO_INPUTS, KENDO_LABELS],
  templateUrl: './checkbox-list-item.component.html',
  styleUrl: './checkbox-list-item.component.scss'
})
export class EciCheckboxListItemComponent {
  @Input()
  public custodian!: {
    id: number;
    name: string;
    email: string;
  };
}
