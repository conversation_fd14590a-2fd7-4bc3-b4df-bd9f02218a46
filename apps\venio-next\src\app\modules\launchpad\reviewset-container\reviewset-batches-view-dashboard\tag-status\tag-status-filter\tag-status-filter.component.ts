import {
  Component,
  computed,
  effect,
  inject,
  Optional,
  signal,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { GridModule, SelectableSettings } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  DynamicHeightDirective,
  AfterValueChangedDirective,
} from '@venio/feature/shared/directives'
import { searchIcon, eyeIcon } from '@progress/kendo-svg-icons'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { FilterField } from '@venio/shared/models/interfaces'
import { cloneDeep } from 'lodash'

@Component({
  selector: 'venio-tag-status-filter',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    GridModule,
    InputsModule,
    LabelModule,
    ButtonsModule,
    IconsModule,
    DynamicHeightDirective,
    AfterValueChangedDirective,
  ],
  templateUrl: './tag-status-filter.component.html',
  styleUrl: './tag-status-filter.component.scss',
})
export class TagStatusFilterComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  public icons = { search: searchIcon, eyeIcon: eyeIcon }

  public dialogTitle: string

  public initialGridData = signal<Array<FilterField>>([])

  public gridData = signal<Array<FilterField>>([])

  public selectedKeys = signal<string[]>([])

  public selectableSettings: SelectableSettings = {
    checkboxOnly: true,
    mode: 'multiple',
  }

  /** Signal for the review set progress detail */
  private readonly reviewSetTagStatus = toSignal(
    this.reviewSetFacade.selectReviewSetTagStatusSuccess$
  )

  /** Computed property for the review set progress detail */
  public readonly loadedReviewSetTagStatus = computed(() => {
    return this.reviewSetTagStatus() || []
  })

  /** Signal for the filter fields */
  private readonly filterFields = toSignal(
    this.reviewSetFacade.selectFilterFields$
  )

  /** Computed property for the user selected filter fields */
  public readonly selectedFilterFields = computed(() => {
    return this.filterFields() || []
  })

  constructor(
    @Optional()
    private dialogRef: DialogRef
  ) {
    effect(() => {
      if (this.loadedReviewSetTagStatus()?.length > 0) {
        untracked(() => this.initializeFieldSelections())
      }
    })
  }

  public onFilter(value): void {
    const searchTerm: string = value.trim().toLowerCase()
    const data = cloneDeep(this.initialGridData())
    if (searchTerm) {
      const gridData = data.filter((item) =>
        item.name.toLowerCase().includes(searchTerm)
      )
      this.gridData.set(gridData)
    } else {
      this.gridData.set(data)
    }
  }

  private initializeFieldSelections(): void {
    const initialGridData = this.loadedReviewSetTagStatus().map((item) => {
      return {
        id: item.tagId,
        name: item.tagName,
      }
    })
    this.initialGridData.set(cloneDeep(initialGridData))
    this.gridData.set(initialGridData)
    const userSelectedFields = this.selectedFilterFields()
    const fields = this.loadedReviewSetTagStatus().map((item) => item.tagName)

    const selectedKeys = userSelectedFields?.[0]
      ? fields.filter((field) => userSelectedFields.includes(field))
      : fields
    this.selectedKeys.set(selectedKeys)
  }

  public save(): void {
    this.reviewSetFacade.setSelectedFilterFields(this.selectedKeys())
    this.close()
  }

  public close(): void {
    this.dialogRef.close()
  }
}
