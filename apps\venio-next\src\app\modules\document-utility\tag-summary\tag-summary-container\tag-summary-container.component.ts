import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core'
import { CommonModule, formatDate } from '@angular/common'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { DocumentsFacade } from '@venio/data-access/review'
import { Subject, filter, distinctUntilChanged, takeUntil } from 'rxjs'
import { TagSummaryComponent } from '../tag-summary/tag-summary.component'
import { TagHistoryComponent } from '../tag-history/tag-history.component'

@Component({
  selector: 'venio-tag-summary-container',
  standalone: true,
  imports: [
    CommonModule,
    LayoutModule,
    TagSummaryComponent,
    TagHistoryComponent,
  ],
  templateUrl: './tag-summary-container.component.html',
  styleUrl: './tag-summary-container.component.scss',
})
export class TagSummaryContainerComponent implements OnInit, OnDestroy {
  public tagHistoryTitle: string

  private toDestroy$: Subject<void> = new Subject<void>()

  constructor(
    private documentsFacade: DocumentsFacade,
    private changeDetectorRef: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.#selectFileName()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #selectFileName(): void {
    this.documentsFacade.getCurrentFileName$
      .pipe(
        filter((fileName) => !!fileName),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fileName) => {
        const today = new Date()
        const currentDate = formatDate(today, 'MMM dd yy', 'en-US')
        this.tagHistoryTitle =
          'Document History for ' + currentDate + ' ' + fileName
        this.changeDetectorRef.markForCheck()
      })
  }
}
