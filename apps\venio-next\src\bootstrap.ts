import { enableProdMode } from '@angular/core'
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { environment } from '@venio/shared/environments'
import { AppModule } from './app/app.module'
import '@angular/localize/init'
import {
  AppIdentitiesTypes,
  Message,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { PlotlyModule } from 'angular-plotly.js'
import * as PlotlyJS from 'plotly.js-dist-min'

// Configure PlotlyJS
PlotlyModule.plotlyjs = PlotlyJS

if (environment.production) {
  enableProdMode()
}

/**
 * We need to notify to the parent window that the document is ready.
 * @return {void}
 */
const notifyDocumentReady = (): void => {
  if (typeof window === 'undefined') return

  // The structure of this message is important as it is used by the parent window to identify the micro app
  window.parent.postMessage(
    {
      type: 'MICRO_APP_DATA_CHANGE',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.DOCUMENT_READY,
        content: { notifyOnly: true },
      },
    } as Message,
    environment.allowedOrigin
  )
}

function bootstrap(): void {
  notifyDocumentReady()

  platformBrowserDynamic()
    .bootstrapModule(AppModule)
    // eslint-disable-next-line no-console
    .catch((err) => console.error(err))
}

if (
  document.readyState === 'complete' ||
  document.readyState === 'interactive'
) {
  bootstrap()
} else {
  document.addEventListener('DOMContentLoaded', bootstrap)
}
