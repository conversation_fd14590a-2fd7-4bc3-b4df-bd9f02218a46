import { environment } from '@venio/shared/environments'
// PlotlyJS configuration will be done in bootstrap.ts
const mfManifest = 'assets/module-federation.manifest.json'
/**
 * If the app is hosted on subdirectory, we need to set the base href.
 * Here, we have set /OnDemand/venio-next/ as the base href so if it got changed,
 * we need to update here as well.
 * The complete url becomes like this: https://domain.com/Venioweb/OnDemand/venio-next/
 */
const moduleFedBaseUrl = environment.production
  ? typeof window !== 'undefined' &&
  `${window.location.origin}/${environment.deployUrl}${mfManifest}`
  : environment.applicationUrl + '/' + mfManifest

fetch(moduleFedBaseUrl.replace(/([^:]\/)\/+/g, '$1'))
  .then((res) => res.json())
  // Uncomment this block to enable module federation.
  // we'll use this in near future:
  // .then((mf) => {
  //   return Object.keys(mf).reduce((acc, key) => {
  //     acc[key] = `${environment.applicationUrl}/${mf[key]}`
  //     return acc
  //   }, {})
  // })
  // .then((definitions) => setRemoteDefinitions(definitions))
  // eslint-disable-next-line no-console
  .then(() => import('./bootstrap').catch((err) => console.error(err)))
