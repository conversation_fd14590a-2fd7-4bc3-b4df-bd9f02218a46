<div
  *ngIf="selectedSummary()"
  class="t-flex t-grow t-relative t-w-full t-h-full t-rounded-md t-border-[1px] t-border-[#cccccc] t-overflow-hidden"
  #container>
  <div class="t-flex t-flex-col t-w-full t-p-3">
    <div
      class="t-flex t-relative t-text-xl t-font-medium t-w-full t-mb-2"
      #header>
      Document Synopsis
    </div>
    <div class="t-flex t-flex-col t-gap-3 t-w-full t-pr-[5px]">
      <div class="t-flex t-w-full t-flex-col t-gap-3">
        <div
          #title
          class="t-sticky t-top-0 t-text-[#1EBADC] t-font-semibold t-mt-3 t-bg-white t-pr-2.5">
          <kendo-popover #searchTermPopoverDocument [width]="400">
            <ng-template kendoPopoverBodyTemplate>
              <div class="t-block t-max-h-52 v-hide-scrollbar">
                {{ selectedSummary().original_query }}
              </div>
            </ng-template>
          </kendo-popover>
          <div
            class="t-truncate t-inline-block t-max-w-full"
            kendoPopoverAnchor
            [popover]="searchTermPopoverDocument"
            showOn="hover">
            {{ selectedSummary().original_query }}
          </div>
        </div>

        <div
          class="t-w-full t-relative t-overflow-x-hidden v-hide-scrollbar"
          [ngStyle]="{
            'height.px':
              container.offsetHeight -
              (header.offsetHeight + title.offsetHeight + 50)
          }"
          [innerHTML]="formatMarkdownSummary(selectedSummary().summary)"></div>
      </div>
    </div>
  </div>
</div>
