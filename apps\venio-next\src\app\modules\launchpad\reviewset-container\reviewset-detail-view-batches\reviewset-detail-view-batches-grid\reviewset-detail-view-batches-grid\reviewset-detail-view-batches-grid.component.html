<div #containerElement class="t-flex-1 t-relative">
  <kendo-grid
    venioDynamicHeight
    [extraSpacing]="70"
    class="t-flex t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto v-case-launchpad-grid"
    [data]="gridView()"
    [loading]="isReviewSetBatchLoading()"
    [skip]="skip"
    [pageSize]="pageSize"
    [rowHeight]="34"
    [sortable]="false"
    [groupable]="false"
    [reorderable]="false"
    [resizable]="true"
    [trackBy]="batchTrackByFn"
    [selectable]="{ mode: 'multiple', cell: false, checkboxOnly: true }"
    [selectedKeys]="selectedBatchIds()"
    (pageChange)="handlePagingForVirtualScroll($event)"
    (selectedKeysChange)="selectReviewSetBatch($event)"
    scrollable="virtual"
    kendoGridSelectBy="uuid"
    filterable="menu">
    <ng-template kendoGridNoRecordsTemplate>
      <p *ngIf="!isReviewSetBatchLoading()">No records found</p>
    </ng-template>
    <kendo-grid-column
      field="sn"
      [width]="30"
      title="#"
      headerClass="t-text-primary"
      [filterable]="false" />
    <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40" />
    <kendo-grid-column
      field="name"
      title="Name"
      [width]="170"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.name }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="remainingFiles"
      title="Total Documents"
      [width]="150"
      class="!t-text-right !t-pr-4"
      headerClass="t-text-primary t-justify-end"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.totalFiles }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="remainingFiles"
      title="Remaining"
      [width]="150"
      class="!t-text-right !t-pr-4"
      headerClass="t-text-primary t-justify-end"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.remainingFiles }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="reviewer"
      title="Reviewer"
      [width]="150"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.reviewer }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="batchStatus"
      title="Status"
      [width]="150"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          class="t-font-medium"
          [ngClass]="{
              't-text-success': dataItem.batchStatus === 'Completed',
              't-text-error': dataItem.batchStatus === 'Failed',
              't-text-[#FFBB12]': dataItem.batchStatus === 'In Progress',
            }">
          {{ dataItem.batchStatus }}
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      title="Actions"
      [width]="200"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <ng-template #actionPlaceholder>
          <div class="t-flex t-flex-row t-gap-2">
            <kendo-skeleton
              *ngFor="let n of [1, 2, 3, 4]"
              height="25px"
              width="25px"
              shape="rectangle"
              class="t-rounded-md" />
          </div>
        </ng-template>
        @defer {
        <venio-reviewset-detail-view-batches-grid-actions
          *ngIf="dataItem"
          [rowDataItem]="dataItem"
          (actionInvoked)="forwardActionControlClick($event, dataItem)" />
        } @placeholder {
        <ng-container *ngTemplateOutlet="actionPlaceholder" />
        }
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
