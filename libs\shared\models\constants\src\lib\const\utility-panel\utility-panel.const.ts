import {
  UtilityPanelTitle,
  UtilityPanelType,
} from '../../enums/utility-panel-menu'

export const ParentChildMessages = {
  FILE_NOT_IN_SCOPE: (fileId: number): string =>
    `Document Id ${fileId} is not in the current search scope. Do you still want to see the details?`,
}

export const PanelMap: Record<string, string> = {
  [UtilityPanelType.TAG_CODING]: UtilityPanelTitle.TAG_CODING,
  [UtilityPanelType.METADATA]: UtilityPanelTitle.METADATA,
}

export const SHOWHIDE_FIELDS = {
  TITLE: (panelValue: string): string => {
    return `Show/Hide ${PanelMap[panelValue] || ''} Fields`
  },
}

export const titleMapping: Record<string, UtilityPanelTitle> = {
  Tag: UtilityPanelTitle.TAG_CODING,
  Metadata: UtilityPanelTitle.METADATA,
  ParentChild: UtilityPanelTitle.FAMILY,
  EmailThread: UtilityPanelTitle.EMAIL_THREAD,
  Duplicates: UtilityPanelTitle.DUPLICATE,
  Notes: UtilityPanelTitle.NOTES,
  DocumentHistory: UtilityPanelTitle.DOCUMENT_HISTROY,
  SimilarDocument: UtilityPanelTitle.SIMILAR_DOCUMENTS,
  NearDuplicate: UtilityPanelTitle.NEAR_DUPLICATE,
  EdaiRelevance: UtilityPanelTitle.EDAI_AI_RELEVANCE,
  EdaiPrivilege: UtilityPanelTitle.EDAI_AI_Privilege,
  EdaiPIIDetect: UtilityPanelTitle.EDAI_AI_PII_DETECT,
  EdaiPIIExtract: UtilityPanelTitle.EDAI_AI_PII_EXTRACT,
  Coding: UtilityPanelTitle.CODING,
}

export const componentIdMapping: Record<string, UtilityPanelType> = {
  Tag: UtilityPanelType.TAG_CODING,
  Metadata: UtilityPanelType.METADATA,
  ParentChild: UtilityPanelType.FAMILY,
  EmailThread: UtilityPanelType.EMAIL_THREAD,
  Duplicates: UtilityPanelType.DUPLICATE,
  Notes: UtilityPanelType.NOTES,
  DocumentHistory: UtilityPanelType.DOCUMENT_HISTROY,
  SimilarDocument: UtilityPanelType.SIMILAR_DOCUMENTS,
  NearDuplicate: UtilityPanelType.NEAR_DUPLICATE,
  EdaiRelevance: UtilityPanelType.EDAI_AI_RELEVANCE,
  EdaiPrivilege: UtilityPanelType.EDAI_AI_Privilege,
  EdaiPIIDetect: UtilityPanelType.EDAI_AI_PII_DETECT,
  EdaiPIIExtract: UtilityPanelType.EDAI_AI_PII_EXTRACT,
}

export const ExcludedPanelsFromRight = [
  'TextViewer',
  'NearNativeViewer',
  'ImageViewer',
  'Transcript',
  'Folder',
  'FolderTree',
  'CodingHistory',
  'NativeViewer',
]

export const UtilityPanelItems: {
  title: UtilityPanelTitle
  componentId: UtilityPanelType
  expanded: boolean
  isVisible: boolean
  hasPermission: boolean
  order: number
  hasShowHideFields: boolean
}[] = [
  {
    title: UtilityPanelTitle.TAG_CODING,
    componentId: UtilityPanelType.TAG_CODING,
    expanded: true,
    isVisible: true,
    hasPermission: true,
    order: 1,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.METADATA,
    componentId: UtilityPanelType.METADATA,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 2,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.FAMILY,
    componentId: UtilityPanelType.FAMILY,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 3,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.DOCUMENT_HISTROY,
    componentId: UtilityPanelType.DOCUMENT_HISTROY,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 4,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.DUPLICATE,
    componentId: UtilityPanelType.DUPLICATE,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 5,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.NOTES,
    componentId: UtilityPanelType.NOTES,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 6,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.SIMILAR_DOCUMENTS,
    componentId: UtilityPanelType.SIMILAR_DOCUMENTS,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 7,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.EMAIL_THREAD,
    componentId: UtilityPanelType.EMAIL_THREAD,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 8,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.NEAR_DUPLICATE,
    componentId: UtilityPanelType.NEAR_DUPLICATE,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 9,
    hasShowHideFields: true,
  },
  {
    title: UtilityPanelTitle.EDAI_AI_RELEVANCE,
    componentId: UtilityPanelType.EDAI_AI_RELEVANCE,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 10,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.EDAI_AI_Privilege,
    componentId: UtilityPanelType.EDAI_AI_Privilege,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 11,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.EDAI_AI_PII_DETECT,
    componentId: UtilityPanelType.EDAI_AI_PII_DETECT,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 12,
    hasShowHideFields: false,
  },
  {
    title: UtilityPanelTitle.EDAI_AI_PII_EXTRACT,
    componentId: UtilityPanelType.EDAI_AI_PII_EXTRACT,
    expanded: true,
    isVisible: false,
    hasPermission: true,
    order: 13,
    hasShowHideFields: false,
  },
]
