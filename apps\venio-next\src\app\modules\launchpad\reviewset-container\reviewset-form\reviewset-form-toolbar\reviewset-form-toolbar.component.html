<div class="t-flex t-flex-row">
  <h3 class="t-font-semibold t-text-base">Select a Case</h3>
</div>
<div class="t-flex t-gap-3" [formGroup]="reviewSetForm()">
  <kendo-dropdownlist
    [loading]="isCaseLoading()"
    formControlName="projectId"
    textField="projectName"
    valueField="projectId"
    [valuePrimitive]="true"
    [filterable]="true"
    (filterChange)="dropdownFilter($event, 'CASE')"
    class="t-w-1/5"
    [defaultItem]="
      isCaseLoading() ? { projectName: 'Loading..' } : defaultCaseItem()
    "
    [data]="filteredCases()">
    <ng-template kendoDropDownListValueTemplate let-dataItem>
      @if(dataItem){
      <div class="t-inline t-align-middle">
        {{ dataItem.projectName }}
        @if (reviewSetFormService.selectedProjectId() <= 0 &&
        defaultCasePlaceholder) {
        <span class="t-text-error t-align-super t-ml-1">*</span>
        }
      </div>
      }
    </ng-template>
  </kendo-dropdownlist>

  @if(!isEdit()) {
  <kendo-dropdownlist
    class="t-w-1/5"
    [ngClass]="{
      't-opacity-50': reviewSetFormService.selectedProjectId() <= 0
    }"
    formControlName="reviewSetTemplateId"
    (valueChange)="reviewSetFormService.handleReviewSetTemplateChange()"
    textField="templateName"
    valueField="templateId"
    [valuePrimitive]="true"
    [filterable]="true"
    (filterChange)="dropdownFilter($event, 'TEMPLATE')"
    [defaultItem]="
      isReviewSetTemplateLoading()
        ? { templateName: 'Loading..' }
        : defaultTemplateItem()
    "
    [loading]="isReviewSetTemplateLoading()"
    [data]="filteredReviewSetTemplates()" />
  }
</div>
