import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { IconsModule } from '@progress/kendo-angular-icons'
import { eyeIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-near-duplicate-ui',
  standalone: true,
  imports: [CommonModule, GridModule, TooltipModule, IconsModule],
  templateUrl: './near-duplicate-ui.component.html',
  styleUrl: './near-duplicate-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NearDuplicateUiComponent implements OnInit {
  public gridData: Array<{
    details: string
    internalFileId: number
    originalFileName: string
    centroid: string
    similarity: number
  }> = []

  public icons = { eyeIcon: eyeIcon }

  public ngOnInit(): void {
    this.generateDummyData()
  }

  private generateDummyData(): void {
    for (let i = 0; i < 10; i++) {
      this.gridData.push({
        details: `Details ${i + 1}`,
        internalFileId: i + 1,
        originalFileName: `Original_File_${i + 1}.txt`,
        centroid: Math.random() > 0.5 ? 'Yes' : 'No',
        similarity: Math.random(), // Generates a number between 0 and 1
      })
    }
  }
}
