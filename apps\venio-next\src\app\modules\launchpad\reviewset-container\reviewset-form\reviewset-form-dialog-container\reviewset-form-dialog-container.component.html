<!-- NOTE: The z-index were 10002 which seems push dialog behind so we reduce it to 10000 -->
<!-- So the dialog will be on top of the kendo window -->
<kendo-window
  [left]="0"
  [top]="-200"
  [resizable]="false"
  [draggable]="false"
  class="v-custom-window !t-z-[10000]"
  state="maximized">
  <kendo-window-titlebar>
    <span class="k-window-title t-items-center t-text-primary t-text-lg">
      <button
        class="t-inline-block t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-items-center"
        fillMode="clear"
        kendoButton
        imageUrl="assets/svg/icon-set-one-rating-multiple.svg"></button>
      {{ reviewSetFormTitle() }} @if(dirtyCheckFormValidationMessage()){
      <span class="t-mx-3 t-text-xs t-text-error">
        All fields marked with an asterisk (<span class="t-text-error">*</span>)
        are required. Please check the form below.</span
      >
      }
    </span>
    <button
      kendoWindowCloseAction
      (click)="closeDialog()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
  </kendo-window-titlebar>
  <div
    class="t-bg-[#FBFBFB] t-p-4 t-mb-8 t-rounded-sm t-basis-full t-w-full t-flex t-flex-col t-flex-1 t-relative"
    #container>
    <venio-reviewset-form-toolbar
      class="t-mb-3"
      [reviewSetForm]="reviewSetFormService.reviewSetForm" />
    <kendo-expansionpanel
      *ngFor="let panel of expansionPanelItems"
      [title]="panel.title"
      [disabled]="reviewSetFormService.selectedProjectId() <= 0"
      [expanded]="
        panel.expanded && reviewSetFormService.selectedProjectId() > 0
      "
      (expand)="panelExpansionState(true, panel)"
      (collapse)="panelExpansionState(false, panel)"
      class="t-mb-4 t-w-2/3 t-relative t-overflow-visible t-rounded-md v-custom-expansion-case"
      [ngClass]="{
        't-opacity-50': reviewSetFormService.selectedProjectId() <= 0
      }"
      [svgExpandIcon]="icons.downIcon"
      [svgCollapseIcon]="icons.upIcon">
      <ng-template kendoExpansionPanelTitleDirective>
        <span
          class="t-w-full t-text-[#707070] t-font-medium t-text-[15px]"
          [ngClass]="{
            't-text-info':
              panel.expanded && reviewSetFormService.selectedProjectId() > 0
          }"
          >{{ panel.title }}</span
        >
      </ng-template>
      @defer { @switch (panel.title) { @case (panelTitle.GeneralSetting) {
      <venio-reviewset-form-general-settings
        [reviewSetForm]="reviewSetFormService.reviewSetForm" />
      } @case (panelTitle.Source) {
      <venio-reviewset-form-source
        [reviewSetForm]="reviewSetFormService.reviewSetForm" />
      } @case (panelTitle.Reviewers) {
      <venio-reviewset-form-reviewers
        [reviewSetForm]="reviewSetFormService.reviewSetForm" />
      } @case (panelTitle.DocumentSortOptions) {
      <venio-reviewset-form-document-sort-options
        [reviewSetForm]="reviewSetFormService.reviewSetForm" />
      } @case (panelTitle.AdvancedOptions) {
      <venio-reviewset-form-advanced-options
        [reviewSetForm]="reviewSetFormService.reviewSetForm" />
      } } }
    </kendo-expansionpanel>
  </div>
  <div
    class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4 t-bottom-[0] t-left-[0px] t-bg-[#FFFFFF] t-p-4 t-fixed t-z-10">
    <button
      kendoButton
      class="v-custom-secondary-button t-flex t-items-center"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save"
      [disabled]="isReviewSetSaving()"
      (click)="submitReviewSet()">
      @if(isReviewSetSaving()) {
      <kendo-loader size="medium" themeColor="secondary" />
      } SAV{{ isReviewSetSaving() ? 'ING..' : 'E' }}
    </button>
    <button
      data-qa="cancel"
      kendoButton
      themeColor="dark"
      fillMode="outline"
      [disabled]="isReviewSetSaving()"
      (click)="closeDialog()">
      CANCEL
    </button>
  </div>
</kendo-window>
