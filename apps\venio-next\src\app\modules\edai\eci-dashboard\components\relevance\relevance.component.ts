import { Component } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { NgFor } from '@angular/common';

const mockData = [40951, 28816, 12345];

@Component({
  selector: 'venio-eci-relevance',
  standalone: true,
  imports: [PlotlyModule, NgFor],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss'
})
export class EciRelevanceComponent {
  public sunburstChartColors = [
    'transparent',
    '#6305FF',
    '#0084FF',
    '#FF00FF',
    '#1100FF',
    '#0FE5B7',
    '#00D0FF'
  ];
  
  getColor(i: number): string {
    return this.sunburstChartColors[i + 1];
  }
  
  public labels = [
    'Relevant',
    'Not Relevant',
    'Potentially Relevant',
    'Likely Relevant',
    'No Content',
    'Tech Issue'
  ];
  
  public graph = {
    data: [{ 
      values: mockData, 
      type: 'pie', 
      hole: 0.5, 
      marker: { 
        colors: ['#6305FF', '#0084FF', '#FF00FF']
      } 
    }],
    layout: {
      autosize: true,
      title: 'Document Relevance',
      automargin: true,
      margin: { t: 40, r: 0, b: 20, l: 20 },
      showlegend: false
    }
  };
  
  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d'
    ]
  };
}
