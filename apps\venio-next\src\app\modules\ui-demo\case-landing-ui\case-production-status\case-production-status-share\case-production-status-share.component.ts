import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

import { userOutlineIcon, chevronLeftIcon } from '@progress/kendo-svg-icons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { EditorModule } from '@progress/kendo-angular-editor'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { LabelModule } from '@progress/kendo-angular-label'

@Component({
  selector: 'venio-case-production-status-share',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    ButtonsModule,
    GridModule,
    DropDownsModule,
    InputsModule,
    EditorModule,
    LayoutModule,
    LabelModule,
  ],
  templateUrl: './case-production-status-share.component.html',
  styleUrl: './case-production-status-share.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseProductionStatusShareComponent {
  public icons = {
    UserOutlineIcon: userOutlineIcon,
    chevronLeftIcon: chevronLeftIcon,
  }

  public internalUsers = [
    { id: 1, name: 'John Doe', role: 'Admin' },
    { id: 2, name: 'Melissa', role: 'User' },
    { id: 1, name: 'Rebecca son', role: 'Admin' },
    { id: 2, name: 'Aston Martin', role: 'User' },
  ]

  public defaultItemExternal: { text: string; value: number } = {
    text: 'Add/Select External User',
    value: null,
  }

  public externalUsers: Array<{ text: string; value: number }> = [
    { text: 'John Doe', value: 1 },
    { text: 'Melissa', value: 2 },
    { text: 'Rebecca son', value: 3 },
    { text: 'Aston Martin', value: 4 },
  ]

  public validityOptions = ['1 Day', '7 Days', '30 Days']

  public defaultValidity = '7 Days'
}
