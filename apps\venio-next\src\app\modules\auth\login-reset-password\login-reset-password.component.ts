import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  Signal,
  signal,
  viewChild,
  ViewChild,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import {
  chevronLeftIcon,
  eyeIcon,
  eyeSlashIcon,
} from '@progress/kendo-svg-icons'
import {
  DialogRef,
  DialogTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { LabelComponent } from '@progress/kendo-angular-label'
import {
  FormFieldComponent,
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import {
  AuthFacade,
  AuthService,
  LoginSettingsModel,
} from '@venio/data-access/auth'
import { filter, Subject, take, takeUntil } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  PopoverAnchorDirective,
  PopoverBodyTemplateDirective,
  PopoverComponent,
} from '@progress/kendo-angular-tooltip'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { CopyPreventionDirective } from '@venio/feature/shared/directives'
import { UserFacade } from '@venio/data-access/common'

interface PasswordResetModel {
  userName: FormControl<string>
  oldPassword: FormControl<string>
  newPassword: FormControl<string>
  confirmPassword: FormControl<string>
  isPasswordExpired: FormControl<boolean>
  remarkMessage: FormControl<string>
}

interface PasswordResetResponseModel {
  type: 'error' | 'success' | ''
  message: string
}
@Component({
  selector: 'venio-login-reset-password',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DialogTitleBarComponent,
    SVGIconComponent,
    LabelComponent,
    FormFieldComponent,
    TextBoxComponent,
    ReactiveFormsModule,
    NgOptimizedImage,
    TextBoxSuffixTemplateDirective,
    FormsModule,
    LoaderComponent,
    PopoverBodyTemplateDirective,
    PopoverComponent,
    PopoverAnchorDirective,
    CopyPreventionDirective,
  ],
  templateUrl: './login-reset-password.component.html',
  styleUrl: './login-reset-password.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginResetPasswordComponent
  implements AfterViewInit, OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  public readonly iconLeft = chevronLeftIcon

  public readonly iconEye = eyeIcon

  public readonly iconSlashEye = eyeSlashIcon

  private readonly dialogRef = inject(DialogRef)

  private readonly windowRef = inject(WINDOW)

  private readonly formBuilder = inject(FormBuilder)

  private readonly authService = inject(AuthService)

  private readonly authFacade = inject(AuthFacade)

  private readonly userFacade = inject(UserFacade)

  private readonly notificationService = inject(NotificationService)

  public passwordResetFormGroup: FormGroup<PasswordResetModel>

  @ViewChild('currentUsername')
  public currentUsername: TextBoxComponent

  @ViewChild('newPasswordControl')
  public newPasswordControl: TextBoxComponent

  public currentActiveFocusControl = signal('')

  public isCurrentPasswordVisible = signal(false)

  public isNewPasswordVisible = signal(false)

  public isConfirmPasswordVisible = signal(false)

  public isPasswordResetLoading = signal(false)

  public readonly loginSettings = signal<LoginSettingsModel>(undefined)

  public passwordResetResponse = signal<PasswordResetResponseModel>(undefined)

  public get formControls(): PasswordResetModel {
    return this.passwordResetFormGroup?.controls
  }

  public newPasswordPopoverAnchor: Signal<PopoverAnchorDirective | undefined> =
    viewChild('newPasswordPopoverAnchor')

  public newPasswordStrengthMessage = ''

  public confirmPasswordPopoverAnchor: Signal<
    PopoverAnchorDirective | undefined
  > = viewChild('confirmPasswordPopoverAnchor')

  public confirmPasswordStrengthMessage = ''

  constructor() {
    this.#initForm()
  }

  public ngOnInit(): void {
    this.#selectLoginSettings()
    this.#fetchLoginSettings()
  }

  public ngAfterViewInit(): void {
    this.#handleDialogTitleBarAction()
    this.#focusOnCurrentPassword()
    this.hidePopoverInitially()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public closeDialog(): void {
    this.dialogRef.close()
  }

  public resetClick(): void {
    this.passwordResetResponse.set(undefined)

    const { newPassword, confirmPassword, oldPassword } =
      this.passwordResetFormGroup.getRawValue()

    Object.values(this.formControls).forEach((control) => {
      control.markAsTouched()
      control.markAsDirty()
    })

    if (
      !(newPassword.trim() || confirmPassword.trim()) ||
      (newPassword && confirmPassword && newPassword !== confirmPassword)
    ) {
      return
    }

    if (oldPassword.trim() === newPassword.trim()) {
      this.#showMessage(
        'New password cannot be the same as the current password',
        {
          style: 'error',
          icon: true,
        }
      )
      return
    }

    this.isPasswordResetLoading.set(true)
    this.#selectResetPasswordResponse()
  }

  private hidePopoverInitially(): void {
    if (this.passwordResetResponse()) return
    this.newPasswordPopoverAnchor().showOn = 'none'
    this.confirmPasswordPopoverAnchor().showOn = 'none'
  }

  private passwordMatchValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    if (!control.parent) {
      return null
    }

    const newPasswordControl = control.parent.get('newPassword')
    const confirmPasswordControl = control

    const newPassword = newPasswordControl?.value?.trim() || ''
    const confirmPassword = confirmPasswordControl.value?.trim() || ''

    if (newPasswordControl && newPasswordControl.value !== newPassword) {
      newPasswordControl.setValue(newPassword, { emitEvent: false })
    }
    if (confirmPasswordControl.value !== confirmPassword) {
      confirmPasswordControl.setValue(confirmPassword, { emitEvent: false })
    }

    if (!newPassword || !confirmPassword) {
      return null
    }

    if (newPassword !== confirmPassword) {
      return { mismatch: true }
    }
    return null
  }

  private formatMessages(messages: string[]): string {
    if (messages.length === 1) {
      return messages[0]
    } else if (messages.length === 2) {
      return `${messages[0]} and ${messages[1]}`
    }
    return `${messages.slice(0, -1).join(', ')}, and ${
      messages[messages.length - 1]
    }`
  }

  public setCurrentActiveControl(
    control: 'newPassword' | 'confirmPassword' | ''
  ): void {
    this.currentActiveFocusControl.set(control)

    // Hide or show popovers based on the active control
    if (control === 'newPassword') {
      this.confirmPasswordPopoverAnchor()?.hide()
      if (this.formControls.newPassword.invalid) {
        this.newPasswordPopoverAnchor()?.show()
      }
    } else if (control === 'confirmPassword') {
      this.newPasswordPopoverAnchor()?.hide()
      if (this.formControls.confirmPassword.invalid) {
        this.confirmPasswordPopoverAnchor()?.show()
      }
    } else {
      // If no control is active, hide both popovers
      this.newPasswordPopoverAnchor()?.hide()
      this.confirmPasswordPopoverAnchor()?.hide()
    }
  }

  private passwordStrengthValidator(
    controlType: 'newPassword' | 'confirmPassword',
    control: AbstractControl
  ): ValidationErrors | null {
    if (
      !this.newPasswordPopoverAnchor() ||
      !this.confirmPasswordPopoverAnchor()
    ) {
      return null
    }

    const password = control.value || ''
    const isActive = this.currentActiveFocusControl() === controlType

    if (!password) {
      if (controlType === 'newPassword') {
        this.newPasswordPopoverAnchor().showOn = 'none'
        this.newPasswordPopoverAnchor().hide()
        this.newPasswordStrengthMessage = ''
      } else {
        this.confirmPasswordPopoverAnchor().showOn = 'none'
        this.confirmPasswordPopoverAnchor().hide()
        this.confirmPasswordStrengthMessage = ''
      }

      return null
    }

    const loginSettings = this.loginSettings() || ({} as LoginSettingsModel)
    const errors: ValidationErrors = {}
    const messages: string[] = []

    // Password validation logic
    if (
      loginSettings.checkEditPwdLength &&
      password.trim().length < loginSettings.spinEditPwdLength
    ) {
      errors.minlength = {
        requiredLength: loginSettings.spinEditPwdLength,
        actualLength: password.trim().length,
      }
      messages.push(
        `Password needs to be at least ${loginSettings.spinEditPwdLength} characters`
      )
    }

    if (loginSettings.checkEditUpperCase && !/[A-Z]/.test(password)) {
      errors.uppercase = true
      messages.push('one uppercase')
    }

    if (loginSettings.checkEditLowerCase && !/[a-z]/.test(password)) {
      errors.lowercase = true
      messages.push('one lowercase')
    }

    if (loginSettings.checkEditNumeric && !/\d/.test(password)) {
      errors.numeric = true
      messages.push('one numeric')
    }

    if (
      loginSettings.checkEditSpecialCharacter &&
      !/[`~!@#$%^&*()_\-+={[}\]|\\:;"'<,>.?/]/.test(password)
    ) {
      errors.specialCharacter = true
      messages.push('one special character')
    }

    if (Object.keys(errors).length > 0) {
      const formattedMessage = this.formatMessages(messages) + '.'

      if (isActive) {
        if (controlType === 'newPassword') {
          this.newPasswordPopoverAnchor().showOn = 'click'
          this.newPasswordStrengthMessage = formattedMessage
          this.newPasswordPopoverAnchor().show()
          this.confirmPasswordPopoverAnchor().hide()
        } else {
          this.confirmPasswordPopoverAnchor().showOn = 'click'
          this.confirmPasswordStrengthMessage = formattedMessage
          this.confirmPasswordPopoverAnchor().show()
          this.newPasswordPopoverAnchor().hide()
        }
      } else {
        // Hide the popover if this control is not active
        if (controlType === 'newPassword') {
          this.newPasswordPopoverAnchor().hide()
        } else {
          this.confirmPasswordPopoverAnchor().hide()
        }
      }
      return errors
    }
    // If there are no errors, hide the popover
    if (isActive) {
      if (controlType === 'newPassword') {
        this.newPasswordPopoverAnchor().showOn = 'none'
        this.newPasswordPopoverAnchor().hide()
        this.newPasswordStrengthMessage = ''
      } else {
        this.confirmPasswordPopoverAnchor().showOn = 'none'
        this.confirmPasswordPopoverAnchor().hide()
        this.confirmPasswordStrengthMessage = ''
      }
    }
    return null
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #fetchLoginSettings(): void {
    this.authFacade.fetchLoginSettings()
  }

  #selectLoginSettings(): void {
    this.authFacade.selectLoginSettingSuccess$
      .pipe(
        filter((settings) => Boolean(settings)),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((settings) => this.loginSettings.set(settings))
  }

  #focusOnCurrentPassword(): void {
    // Wait until the control is fully rendered
    setTimeout(() => this.currentUsername.focus(), 300)
  }

  #handleDialogTitleBarAction(): void {
    const button = this.windowRef.document.getElementsByClassName(
      'k-dialog-titlebar-action'
    )
    if (button.length) {
      button[0].addEventListener('click', () => {
        this.dialogRef.close()
      })
    }
  }

  #initForm(): void {
    this.passwordResetFormGroup = this.formBuilder.group<PasswordResetModel>({
      userName: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      oldPassword: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      newPassword: this.formBuilder.control('', {
        validators: [
          Validators.required,
          this.passwordStrengthValidator.bind(this, 'newPassword'),
          this.passwordMatchValidator.bind(this),
        ],
      }),
      confirmPassword: this.formBuilder.control('', {
        validators: [
          Validators.required,
          this.passwordStrengthValidator.bind(this, 'confirmPassword'),
          this.passwordMatchValidator.bind(this),
        ],
      }),
      isPasswordExpired: this.formBuilder.control(false),
      remarkMessage: this.formBuilder.control(''),
    })
  }

  #selectResetPasswordResponse(): void {
    this.authService
      .resetCurrentPassword({
        userName: this.formControls.userName.value,
        oldPassword: this.formControls.oldPassword.value,
        newPassword: this.formControls.newPassword.value,
      })
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (response) => {
          const status = (response.status || '').toLowerCase()
          const statusType =
            status.length > 0 &&
            (status === 'error'
              ? 'error'
              : status === 'success'
              ? 'success'
              : '')
          if (statusType === 'success') {
            this.passwordResetResponse.set({
              type: statusType,
              message: response.message,
            })
            this.userFacade.fetchCurrentUser()
          } else {
            this.#showMessage(response.message, {
              style: 'error',
              icon: true,
            })
          }
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error as ResponseModel
          this.#showMessage(
            error.message ||
              'An error occurred while resetting your password. Please try again or contact support',
            { style: 'error', icon: true }
          )
          this.isPasswordResetLoading.set(false)
        },
      })
  }
}
