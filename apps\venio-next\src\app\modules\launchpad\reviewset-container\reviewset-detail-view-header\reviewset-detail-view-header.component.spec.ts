import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewHeaderComponent } from './reviewset-detail-view-header.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { ResponseModel, UserModel } from '@venio/shared/models/interfaces'
import { RightModel, StartupsFacade } from '@venio/data-access/review'

describe('ReviewsetDetailViewHeaderComponent', () => {
  let component: ReviewsetDetailViewHeaderComponent
  let fixture: ComponentFixture<ReviewsetDetailViewHeaderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewHeaderComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            isFavoriteProjectToggleLoading$: of({} as Record<number, boolean>),
            selectProjectIdsToRights$: of({}),
            selectReviewSetDeleteError$: of({} as ResponseModel),
            selectReviewSetDeleteSuccess$: of({} as ResponseModel),
            selectIsReviewSetDeleteLoading$: of(''),
            deleteReviewSet: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({ Invalid_Global_Right_List: {} } as RightModel),
          } satisfies Partial<StartupsFacade>,
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of({} as UserModel),
          } satisfies Partial<UserFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewHeaderComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
