<div class="eci-dashboard-container">
  <div class="filters-wrapper">
    <button kendoButton [svgIcon]="svgFilter" #anchor (click)="onToggle()">Filters</button>
    @if (show) {
    <kendo-popup [anchor]="anchor.element">
      @if (showCustodianFilters) {
      <kendo-listview [data]="custodians">
        <ng-template kendoListViewItemTemplate let-dataItem="dataItem" let-isFirst="isFirst">
          <div class="custodian-item">
            <input type="checkbox" [id]="'custodian-' + dataItem.id">
            <label [for]="'custodian-' + dataItem.id">{{ dataItem.name }} ({{ dataItem.email }})</label>
          </div>
        </ng-template>
      </kendo-listview>
      } @else {
      <div style="padding: 30px; background-color: #fcf7f8">
        <button kendoButton (click)="onCustodianClick()">Custodians</button>
      </div>
      }
    </kendo-popup>
    }
  </div>

  <venio-eci-summary></venio-eci-summary>

  <div class="dashboard-grid">
    <div class="grid-row">
      <div class="grid-col-1">
        <venio-eci-relevance></venio-eci-relevance>
      </div>
      <div class="grid-col-2">
        <venio-eci-word-cloud></venio-eci-word-cloud>
      </div>
    </div>

    <div class="grid-row">
      <div class="grid-col-1">
        <venio-eci-sunburst [title]="'Document Types'"></venio-eci-sunburst>
      </div>
      <div class="grid-col-1">
        <venio-eci-sunburst [title]="'File Extensions'"></venio-eci-sunburst>
      </div>
      <div class="grid-col-1">
        <venio-eci-sunburst [title]="'Date Ranges'"></venio-eci-sunburst>
      </div>
    </div>

    <div class="grid-row">
      <div class="grid-col-full">
        <venio-eci-inappropriate-content></venio-eci-inappropriate-content>
      </div>
    </div>
  </div>
</div>