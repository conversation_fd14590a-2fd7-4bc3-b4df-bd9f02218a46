<div class="eci-dashboard-container">
  <div class="filters-wrapper">
    <button kendoButton [svgIcon]="svgFilter" #anchor (click)="onToggle()">Filters</button>
    @if (show) {
      <kendo-popup [anchor]="anchor.element">
        @if (showCustodianFilters) {
          <kendo-listview [data]="custodians">
            <ng-template kendoListViewItemTemplate let-dataItem="dataItem" let-isFirst="isFirst">
              <venio-eci-checkbox-list-item [custodian]="dataItem"></venio-eci-checkbox-list-item>
            </ng-template>
          </kendo-listview>
        } @else {
          <div style="padding: 30px; background-color: #fcf7f8">
            <button kendoButton (click)="onCustodianClick()">Custodians</button>
          </div>
        }
      </kendo-popup>
    }
  </div>

  <venio-eci-summary></venio-eci-summary>

  @if (isFocusedSectionOpened) {
    <venio-eci-focused-section></venio-eci-focused-section>
  }

  <div class="flex flex-col gap-6">
    <div class="w-full grid grid-cols-1 lg:grid-cols-3 lg:gap-6">
      <div class="col-span-1"><venio-eci-relevance></venio-eci-relevance></div>
      <div class="col-span-2"><venio-eci-word-cloud></venio-eci-word-cloud></div>
    </div>
    <div class="w-full grid grid-cols-1 lg:grid-cols-3 lg:gap-6 min-h-[400px]">
      <div class="col-span-1"><venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst></div>
      <div class="col-span-1"><venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst></div>
      <div class="col-span-1"><venio-eci-sunburst [showLegend]="true"></venio-eci-sunburst></div>
    </div>
    <div class="w-full grid grid-cols-1 min-h-[400px]">
      <div class="col-span-1"><venio-eci-inappropriate-content></venio-eci-inappropriate-content></div>
    </div>
  </div>
</div>