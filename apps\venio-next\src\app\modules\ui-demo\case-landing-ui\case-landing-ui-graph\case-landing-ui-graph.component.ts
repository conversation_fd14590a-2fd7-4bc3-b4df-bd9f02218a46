import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ChartsModule,
  SeriesLabelsContentArgs,
} from '@progress/kendo-angular-charts'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-case-landing-ui-graph',
  standalone: true,
  imports: [CommonModule, ChartsModule, DynamicHeightDirective],
  templateUrl: './case-landing-ui-graph.component.html',
  styleUrl: './case-landing-ui-graph.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseLandingUiGraphComponent {
  public data = [
    {
      kind: 'Completed',
      share: 66,
      color: '#BEF8BA', // Light green for Completed
    },
    {
      kind: 'In Progress',
      share: 12,
      color: '#FFBB12', // Yellow for In Progress
    },
    {
      kind: 'Not Started',
      share: 100,
      color: '#a59b7e', // Dark gray for Not Started
    },
  ]

  public labelContent(e: SeriesLabelsContentArgs): string {
    return e.category
  }
}
