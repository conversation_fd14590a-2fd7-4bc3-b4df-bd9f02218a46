import {
  Component,
  computed,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownListComponent,
  ValueTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import {
  CaseDetailModel,
  CaseType,
  ReviewSetForm,
  ReviewSetTemplateModel,
} from '@venio/shared/models/interfaces'
import { ReviewsetFormService } from '../reviewset-form.service'

@Component({
  selector: 'venio-reviewset-form-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    DropDownListComponent,
    ValueTemplateDirective,
    ReactiveFormsModule,
  ],
  templateUrl: './reviewset-form-toolbar.component.html',
  styleUrl: './reviewset-form-toolbar.component.scss',
})
export class ReviewsetFormToolbarComponent implements OnInit, OnDestroy {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  private readonly projectFacade = inject(ProjectFacade)

  public readonly reviewSetFormService = inject(ReviewsetFormService)

  public readonly isCaseLoading = toSignal(
    this.projectFacade.selectIsCaseDetailLoading$
  )

  private readonly caseFilterTerm = signal('')

  private readonly reviewSetTemplateFilterTerm = signal('')

  public readonly isEdit = computed(
    () => this.reviewSetForm()?.controls.reviewSetId.value > 0
  )

  public defaultCasePlaceholder = {
    projectId: 0,
    projectName: 'Select a case',
  }

  public defaultReviewSetTemplateItem = {
    templateName: 'Clone from template',
    templateId: 0,
  }

  private readonly allCases = toSignal(
    this.projectFacade.selectCaseDetail$.pipe(
      map((c) => c?.caseDetailEntries || [])
    )
  )

  public readonly isReviewSetTemplateLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isReviewSetTemplateLoading
  )

  public readonly filteredCases = computed<CaseDetailModel[]>(() => {
    const term = this.caseFilterTerm().trim().toLowerCase()
    const cases = this.allCases()
    return term
      ? cases.filter((c) => c.projectName.toLowerCase().includes(term))
      : cases
  })

  public readonly filteredReviewSetTemplates = computed<
    ReviewSetTemplateModel[]
  >(() => {
    const term = this.reviewSetTemplateFilterTerm().trim().toLowerCase()
    const templates = this.reviewSetFormService.allReviewSetTemplate()
    return term
      ? templates.filter((t) => t.templateName.toLowerCase().includes(term))
      : templates
  })

  public readonly defaultCaseItem = computed(() =>
    this.reviewSetFormService.selectedProjectId() <= 0
      ? this.defaultCasePlaceholder
      : undefined
  )

  public readonly defaultTemplateItem = computed(() =>
    this.reviewSetFormService.selectedTemplateId() <= 0
      ? this.defaultReviewSetTemplateItem
      : undefined
  )

  public ngOnInit(): void {
    this.#fetchCases()
  }

  public ngOnDestroy(): void {
    this.projectFacade.resetProjectState([
      'isCaseDetailLoading',
      'caseDetailSuccessResponse',
      'caseDetailRequestInfo',
    ])
  }

  public dropdownFilter(term: string, type: 'CASE' | 'TEMPLATE'): void {
    switch (type) {
      case 'CASE':
        this.caseFilterTerm.set(term)
        break
      case 'TEMPLATE':
        this.reviewSetTemplateFilterTerm.set(term)
        break
    }
  }

  #fetchCases(): void {
    this.projectFacade.updateCaseDetailRequestInfo({
      pageNumber: 1,
      // Probably there won't be more cases but just in case,
      // we fetch 10000 cases to avoid missing any.
      // TODO: In the future, we'll need to implement load next batch on scroll.
      pageSize: 10000,
      caseTypeFilter: CaseType.VOD_SERVICE,
    })
    this.projectFacade.fetchCaseDetail()
  }
}
