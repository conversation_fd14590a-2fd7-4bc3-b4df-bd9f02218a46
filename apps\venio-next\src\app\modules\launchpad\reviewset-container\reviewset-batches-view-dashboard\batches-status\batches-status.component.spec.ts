import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BatchesStatusComponent } from './batches-status.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('BatchesStatusComponent', () => {
  let component: BatchesStatusComponent
  let fixture: ComponentFixture<BatchesStatusComponent>

  const mockReviewSetFacade = {
    selectReviewSetBatchSummaryDetailSuccess$: of(undefined),
    selectIsReviewSetBatchSummaryDetailLoading$: of(false),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BatchesStatusComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(BatchesStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
