import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule, DialogRef } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { userOutlineIcon, checkIcon } from '@progress/kendo-svg-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-case-landing-document-shared-document-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    IconsModule,
    ButtonsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './case-landing-document-shared-document-dialog.component.html',
  styleUrl: './case-landing-document-shared-document-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseLandingDocumentSharedDocumentDialogComponent {
  public icons = { UserOutlineIcon: userOutlineIcon, tickIcon: checkIcon }

  public dialogTitle = 'Shared Document 98738E7'

  constructor(private dialogRef: DialogRef) {}

  public close(status: string): void {
    this.dialogRef.close()
  }
}
