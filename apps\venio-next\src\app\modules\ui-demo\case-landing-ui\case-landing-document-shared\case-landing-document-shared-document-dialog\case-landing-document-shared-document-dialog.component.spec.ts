import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseLandingDocumentSharedDocumentDialogComponent } from './case-landing-document-shared-document-dialog.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('CaseLandingDocumentSharedDocumentDialogComponent', () => {
  let component: CaseLandingDocumentSharedDocumentDialogComponent
  let fixture: ComponentFixture<CaseLandingDocumentSharedDocumentDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CaseLandingDocumentSharedDocumentDialogComponent,
        NoopAnimationsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      CaseLandingDocumentSharedDocumentDialogComponent
    )
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
