@if(isReviewersLoading()){
<div class="t-flex t-flex-col t-gap-2">
  @for (n of [1, 2, 3, 4, 5]; track n) {
  <div class="t-flex t-flex-row t-items-center t-gap-2">
    <kendo-skeleton shape="text" [width]="15" />
    <kendo-skeleton shape="text" [width]="200" />
  </div>
  }
</div>
} @else {
<h3 class="t-font-semibold t-text-base t-mb-2">
  Reviewer(s) <span class="t-text-error">*</span>
</h3>

<kendo-treeview
  [nodes]="reviewSetFormService.allProjectUsers()"
  textField="name"
  kendoTreeViewExpandable
  kendoTreeViewCheckable
  kendoTreeViewFlatDataBinding
  [(checkedKeys)]="selectedKeys"
  idField="keyId"
  checkBy="keyId"
  parentIdField="parentId"
  [loadOnDemand]="false"
  class="v-hide-scrollbar t-max-h-[300px]"
  (checkedChange)="selectionChange()" />

@if(isSelectedUserGroupInvalid()){
<div class="t-text-error t-my-2">Reviewer(s) are required</div>
} }
