import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseProductionStatusGraphComponent } from './case-production-status-graph.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseProductionStatusGraphComponent', () => {
  let component: CaseProductionStatusGraphComponent
  let fixture: ComponentFixture<CaseProductionStatusGraphComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseProductionStatusGraphComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseProductionStatusGraphComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
