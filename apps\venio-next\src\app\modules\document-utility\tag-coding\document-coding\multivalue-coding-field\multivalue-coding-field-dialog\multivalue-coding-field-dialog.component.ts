import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { NotificationModule } from '@progress/kendo-angular-notification'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { Subject, distinctUntilChanged, filter, takeUntil } from 'rxjs'
import { LabelModule } from '@progress/kendo-angular-label'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { SVGIcon, infoCircleIcon } from '@progress/kendo-svg-icons'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import {
  DocumentCodingModel,
  SelectedMultiCodingValueModel,
} from '@venio/shared/models/interfaces'
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'
import { DocumentCodingControlActionType } from '@venio/shared/models/constants'
import { StringUtils } from '@venio/util/utilities'

@Component({
  selector: 'venio-multivalue-coding-field-dialog',
  standalone: true,
  imports: [
    CommonModule,
    PopoverModule,
    ButtonModule,
    FormsModule,
    ReactiveFormsModule,
    DialogModule,
    DropDownListModule,
    NotificationModule,
    SvgLoaderDirective,
    InputsModule,
    LabelModule,
  ],
  templateUrl: './multivalue-coding-field-dialog.component.html',
  styleUrl: './multivalue-coding-field-dialog.component.scss',
})
export class MultivalueCodingFieldDialogComponent implements OnInit, OnDestroy {
  public readonly toDestroy$ = new Subject<void>()

  public EventType: DocumentCodingControlActionType

  public opened = false

  public dialogTitle = ''

  public infoTooltip: SVGIcon = infoCircleIcon

  public selectedMultiCodingValue: SelectedMultiCodingValueModel

  public selectedField: DocumentCodingModel

  public selectedValues: any[]

  public multipleCodingSettings = [
    'Override existing values',
    'Append to end',
    'Insert at beginning',
  ]

  public isBulkDocument = false

  public shouldShowDelimiter = false

  public isAddOrAppendValues = false

  public multiValueForm: FormGroup

  public resultValues: any[] = []

  public submitCaption = 'OK'

  //delimiter selected for the field
  public selectedDelimiter: string

  // new value type use for display and save: text / select / checkbox
  public newValueType: string

  /**
   * show / hide coding infomation
   */
  public codingInfoVisible = false

  public newValuesCaption = 'New Values'

  public updateValues = ''

  constructor(
    private documentCodingFacade: DocumentCodingFacade,
    private changeDetectorRef: ChangeDetectorRef,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.openDialog()
    this.#selectCodingField()
  }

  public close(status: string): void {
    this.opened = false
    this.dialogRef.close()
  }

  public openDialog(): void {
    this.opened = true
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #selectCodingField(): void {
    this.documentCodingFacade.selectedMultiCodingValue$
      .pipe(
        distinctUntilChanged(),
        filter((mulitCodingField) => !!mulitCodingField && this.opened),
        takeUntil(this.toDestroy$)
      )
      .subscribe((mulitCodingField) => {
        this.selectedMultiCodingValue = mulitCodingField
        this.selectedField = mulitCodingField.selectedField
        this.isBulkDocument = mulitCodingField.isBulkDocument
        this.dialogTitle = this.selectedField?.displayName
        this.#prepareData(this.selectedField)
        this.changeDetectorRef.markForCheck()
      })
  }

  #prepareData(field: DocumentCodingModel): void {
    this.selectedValues = field.currentFieldValue.split(
      field.delimiterForCodingValues
    )
    /**
     * incase of selection of the value from the text field the value is set in the updatedFieldValue
     * so updatedfieldvalue is set as selectedValues
     * this logic here might have to be changed though
     */
    if (field.updatedFieldValue && !field.currentFieldValue) {
      this.selectedValues = field.updatedFieldValue.split(
        field.delimiterForCodingValues
      )
    }

    this.#getSelectedDelimiter(field?.delimiterForCodingValues)
    this.#createMultiValueForm()
    this.#updateNewValueField()
    this.#setAddOrAppendValues()
  }

  #updateNewValueField(): void {
    this.newValuesCaption = 'New Values'
    if (
      this.selectedField.detailDataCount > 0 &&
      !this.selectedField.allowPredefinedCodingValuesOnly &&
      this.selectedField.allowMultipleCodingValues
    ) {
      this.newValueType = 'text-checkbox'
      this.newValuesCaption = 'Update Values'
    } else if (
      this.selectedField.detailDataCount > 0 &&
      this.selectedField.allowPredefinedCodingValuesOnly &&
      this.selectedField.allowMultipleCodingValues
    ) {
      this.newValueType = 'checkbox'
    } else if (
      !(this.selectedField.detailDataCount > 0) &&
      !this.selectedField.allowPredefinedCodingValuesOnly
    ) {
      this.newValueType = 'text'
    } else if (
      this.selectedField.detailDataCount > 0 &&
      !this.selectedField.allowMultipleCodingValues
    ) {
      this.newValueType = 'select'
    }
  }

  #getSelectedDelimiter(selectedDelimiter: string): void {
    // create from VOD: 035 (#) , create from Console: [; ]
    if (selectedDelimiter.trim().length >= 7) {
      this.selectedDelimiter = selectedDelimiter.slice(5, 6) // get the value from the delimiter string for e.g 044 (,)
    } else {
      this.selectedDelimiter = selectedDelimiter
    }

    if (
      this.selectedDelimiter !== '' &&
      this.selectedField.allowMultipleCodingValues &&
      !this.selectedField.allowPredefinedCodingValuesOnly
    ) {
      this.shouldShowDelimiter = true
    }
  }

  #createMultiValueForm(): void {
    const group = {}
    this.selectedField.fieldCodingValues.forEach((value) => {
      group[value] = new FormControl()
    })
    let updateType = '0'
    if (this.selectedField.multiValuedCodingOptions) {
      updateType = this.selectedField.multiValuedCodingOptions.toString()
    }
    group['updateType'] = new FormControl(updateType)
    group[this.selectedField.fieldName] = new FormControl(
      this.selectedField.updatedFieldValue ??
        this.selectedField.currentFieldValue
    )
    this.multiValueForm = new FormGroup(group)
  }

  #setAddOrAppendValues(): void {
    this.isAddOrAppendValues =
      this.EventType === DocumentCodingControlActionType.MULTIDOCUMENT
        ? true
        : false
  }

  #getFinalResult(getNewOnly: boolean, getCheckboxOnly: boolean): string {
    const formValues = this.multiValueForm.value
    const selectedDelimiter =
      this.selectedDelimiter !== ' '
        ? this.selectedDelimiter.trim()
        : this.selectedDelimiter
    if (
      !getCheckboxOnly &&
      (this.isAddOrAppendValues || this.newValueType === 'text-checkbox') &&
      (this.newValueType === 'text' ||
        this.newValueType === 'text-checkbox' ||
        this.newValueType === 'select')
    ) {
      if (this.newValueType === 'text-checkbox') {
        let newValues = this.updateValues
        let currentFieldValue = this.selectedField.currentFieldValue

        if (+formValues.updateType === 0 || getNewOnly)
          //override existing values
          currentFieldValue = newValues
        else if (+formValues.updateType === 1)
          //append to end
          currentFieldValue = currentFieldValue + selectedDelimiter + newValues
        //insert at beginning
        else
          currentFieldValue = newValues + selectedDelimiter + currentFieldValue

        newValues = StringUtils.trim(currentFieldValue, selectedDelimiter)

        return newValues
      }
      return formValues[this.selectedField.fieldName]
    }
    let result = ''
    if (this.resultValues.length > 0)
      result = this.resultValues.join(selectedDelimiter)

    let currentFieldValue = this.selectedField.currentFieldValue
    if (+formValues.updateType === 0 || getNewOnly)
      //override existing values
      currentFieldValue = result
    else if (+formValues.updateType === 1)
      //append to end
      currentFieldValue = currentFieldValue + selectedDelimiter + result
    //insert at beginning
    else currentFieldValue = result + selectedDelimiter + currentFieldValue
    return StringUtils.trim(currentFieldValue, selectedDelimiter)
  }

  public onCodingValueChanged(event): void {
    if (!this.resultValues) this.resultValues = []
    const index: number = this.resultValues.findIndex(
      (value) => value === event.target.value
    )
    if (index >= 0) this.resultValues.splice(index, 1)
    if (event.target.checked) this.resultValues.push(event.target.value)

    if (this.newValueType === 'text-checkbox') {
      this.updateValues = this.#getFinalResult(true, true)
    }
  }

  #prepareResultValues(): void {
    const formValues = this.multiValueForm.value

    const selectedValues = this.#getFinalResult(false, false)

    const selectedNewValues = this.#getFinalResult(true, false)

    const selectedMultiValue: SelectedMultiCodingValueModel = {
      selectedField: this.selectedField,
      selectedValues: selectedValues,
      selectedOperationType: +formValues.updateType,
      selectedNewValues: selectedNewValues,
      eventType: this.EventType,
      isBulkDocument: this.isBulkDocument,
    }

    this.selectedMultiCodingValue = selectedMultiValue
  }

  public save(): void {
    this.#prepareResultValues()
    this.documentCodingFacade.setMultiCodingValue(
      this.EventType,
      this.selectedMultiCodingValue
    )
    this.close('save')
  }
}
