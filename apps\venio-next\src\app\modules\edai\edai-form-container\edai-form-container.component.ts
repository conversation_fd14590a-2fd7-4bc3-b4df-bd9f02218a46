import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { EdaiIssueFormComponent } from '../edai-issue-form/edai-issue-form.component'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { EdaiJobInputsComponent } from '../edai-issue-inputs/edai-job-inputs.component'
import { AIJobType, JobForm } from '@venio/data-access/ai'
import { EdaiPrivilegeFormComponent } from '../edai-privilege-form/edai-privilege-form.component'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { startWith, switchMap } from 'rxjs'
import { EdaiPiiFormComponent } from '../edai-pii-form/edai-pii-form.component'

@Component({
  selector: 'venio-edai-form-container',
  standalone: true,
  imports: [
    CommonModule,
    EdaiIssueFormComponent,
    EdaiJobInputsComponent,
    ReactiveFormsModule,
    EdaiPrivilegeFormComponent,
    EdaiPiiFormComponent,
  ],
  templateUrl: './edai-form-container.component.html',
  styleUrl: './edai-form-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiFormContainerComponent {
  public edaiFormGroup = input.required<FormGroup<JobForm>>()

  public readonly selectedJobType = toSignal(
    toObservable(this.edaiFormGroup).pipe(
      switchMap((formGroup) => {
        const control = formGroup.controls.jobType
        return control.valueChanges.pipe(startWith(control.value))
      })
    )
  )

  public readonly aiJobTypes = AIJobType
}
