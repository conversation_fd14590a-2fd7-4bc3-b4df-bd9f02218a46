import { FieldOperator } from '../search/operators.model'
import { SearchField } from '../search/search-field.model'

export enum GroupOperator {
  AND = 'AND',
  OR = 'OR',
  NOT = 'NOT',
}

export type SizeType = 'bytes' | 'kb' | 'mb' | 'gb'

export enum ConditionUiType {
  VIEW_CONDITION = 'VIEW_CONDITION',
  BREADCRUMB_CONDITION = 'BREADCRUMB_CONDITION',
}
export enum ConditionRowChangeTypes {
  ADD_CONDITION = 'ADD_CONDITION',
  REMOVE_CONDITION = 'REMOVE_CONDITION',
  ADD_GROUP = 'ADD_GROUP',
  REMOVE_GROUP = 'REMOVE_GROUP',
  CHANGE_GROUP_OPERATOR = 'CHANGE_GROUP_OPERATOR',
  UPDATE_CONDITION = 'UPDATE_CONDITION',
}

export enum GroupStackType {
  CUSTOM = 'Custom Search',
  ADVANCED_SEARCH = 'Advanced Search',
  FIELDS = 'Field Search',
  TAGS = 'Tag Search',
  FOLDERS = 'Folder Search',
  QUICK_SEARCH = 'Quick Search',
  TALLY = 'Tally',
  AI_SEARCH = 'AI Search',
  ANALYZE_SEARCH = 'Analyze Search',
  FILE_UPLOAD_HISTORY_SEARCH = 'File Upload History Search',
  PRODUCTION_SEARCH = 'Production Search',
  DOCUMENT_SHARE_SEARCH = 'Document Share Search',
  VIEW_SEARCH = 'View Search ',
  AI_PRIVILEGE_SEARCH = 'AI Privilege Search',
  AI_RELEVANCE_SEARCH = 'AI Relevance Search',
  SEARCH_LOG = 'Search Log',
  SAVED_SEARCH = 'Saved Search',
  SYSTEM_FOLDER = 'System Folder Search',
  AUTO_FOLDER = 'Auto Folder Search',
  DYNAMIC_FOLDER = 'Dynamic Folder Search',
  ADMIN = 'Admin Search',
  BULK_REDACT = 'Bulk Redact',
}

export type ConditionElement = {
  id: string
  selectedField?: SearchField
  fieldName: string
  sizeType?: SizeType
  operator: FieldOperator | undefined
  primaryValue: any
  secondaryValue?: any
  conditionSyntax?: string
}

export enum ConditionType {
  Group = 'group',
  Operator = 'operator',
}

// Type for a group of conditions
export type ConditionGroup = {
  id: string
  operator?: GroupOperator
  conditions?: ConditionElement[]
  conditionType?: ConditionType
  groupStackType?: GroupStackType
  checked?: boolean
  children?: ConditionGroup[]
}
