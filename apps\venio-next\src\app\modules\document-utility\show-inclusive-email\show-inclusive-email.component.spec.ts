import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ShowInclusiveEmailComponent } from './show-inclusive-email.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  DocumentsFacade,
  EmailThreadVisibleType,
  ReviewFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { BehaviorSubject } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ShowInclusiveEmailComponent', () => {
  let component: ShowInclusiveEmailComponent
  let fixture: ComponentFixture<ShowInclusiveEmailComponent>
  let mockReviewFacade: any
  let mockDocumentsFacade: any

  const menuEvent$ = new BehaviorSubject<DocumentMenuType>(null)
  const visibleEmailType$ = new BehaviorSubject<EmailThreadVisibleType>(
    EmailThreadVisibleType.All
  )

  beforeEach(async () => {
    mockReviewFacade = {
      setVisibleEmailType: jest.fn(),
      getVisibleEmailType$: visibleEmailType$.asObservable(),
    }

    mockDocumentsFacade = {
      selectDocumentMenuEvent$: menuEvent$.asObservable(),
      resetDocumentState: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [ShowInclusiveEmailComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: ReviewFacade, useValue: mockReviewFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ShowInclusiveEmailComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  beforeEach(() => {
    jest.resetAllMocks()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should switch to inclusive email view when inclusive email menu is selected', () => {
    // Spy on mockReviewFacade.setVisibleEmailTypeSpy
    const setVisibleEmailTypeSpy = jest.spyOn(
      mockReviewFacade,
      'setVisibleEmailType'
    )

    fixture.detectChanges()

    // Trigger the show inclusive email menu click event
    menuEvent$.next(DocumentMenuType.SHOW_INCLUSIVE_EMAIL)

    fixture.detectChanges()

    // Expect setVisibleEmailType method to have been called with the expected arguments
    expect(setVisibleEmailTypeSpy).toHaveBeenCalled()
  })

  it('should not switch to inclusive email view when inclusive email menu is selected', () => {
    // Spy on mockReviewFacade.setVisibleEmailTypeSpy
    const setVisibleEmailTypeSpy = jest.spyOn(
      mockReviewFacade,
      'setVisibleEmailType'
    )

    fixture.detectChanges()

    // Trigger the show inclusive email menu click event
    menuEvent$.next(DocumentMenuType.EXPORT_TO_FILE)

    fixture.detectChanges()

    // Expect setVisibleEmailType method to have been called with the expected arguments
    expect(setVisibleEmailTypeSpy).not.toHaveBeenCalled()
  })

  it('should switch to "inclusive email only" view when current mode is set to "all email threads"', () => {
    // Spy on mockReviewFacade.setVisibleEmailTypeSpy
    const setVisibleEmailTypeSpy = jest.spyOn(
      mockReviewFacade,
      'setVisibleEmailType'
    )

    // set current email thread view to all
    visibleEmailType$.next(EmailThreadVisibleType.All)

    fixture.detectChanges()

    // Trigger the show inclusive email menu click event
    menuEvent$.next(DocumentMenuType.SHOW_INCLUSIVE_EMAIL)

    fixture.detectChanges()

    // Expect setVisibleEmailType method to have been called with the expected arguments
    expect(setVisibleEmailTypeSpy).toHaveBeenCalledWith(
      EmailThreadVisibleType.InclusiveEmailOnly
    )
  })

  it('should switch to "all email threads" view when current mode is set to inclusive email only', () => {
    // Spy on mockReviewFacade.setVisibleEmailTypeSpy
    const setVisibleEmailTypeSpy = jest.spyOn(
      mockReviewFacade,
      'setVisibleEmailType'
    )

    // set current email thread view to inclusive email only
    visibleEmailType$.next(EmailThreadVisibleType.InclusiveEmailOnly)

    fixture.detectChanges()

    // Trigger the show inclusive email menu click event
    menuEvent$.next(DocumentMenuType.SHOW_INCLUSIVE_EMAIL)

    fixture.detectChanges()

    // Expect setVisibleEmailType method to have been called with the expected arguments
    expect(setVisibleEmailTypeSpy).toHaveBeenCalledWith(
      EmailThreadVisibleType.All
    )
  })
})
