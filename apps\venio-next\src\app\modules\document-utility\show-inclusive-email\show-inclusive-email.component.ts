import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentsFacade,
  EmailThreadVisibleType,
  ReviewFacade,
} from '@venio/data-access/review'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { Subject, filter, takeUntil, withLatestFrom } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-review-switch-view',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  template: '',
  styles: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShowInclusiveEmailComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  constructor(
    private documentsFacade: DocumentsFacade,
    private reviewFacade: ReviewFacade
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.SHOW_INCLUSIVE_EMAIL),
        withLatestFrom(this.reviewFacade.getVisibleEmailType$),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([menuType, currentVisibleEmailType]) => {
        this.#handleShowInclusiveEmailMenu(currentVisibleEmailType)
        this.resetEvents()
      })
  }

  /**
   * Sets email thread visibility type to 'inclusive email only' if the current type is 'all' and vice versa
   * @param {EmailThreadVisibleType} currentType currently set email thread visibility type
   * @returns {void}
   */
  #handleShowInclusiveEmailMenu(currentType: EmailThreadVisibleType): void {
    if (currentType === EmailThreadVisibleType.All) {
      this.reviewFacade.setVisibleEmailType(
        EmailThreadVisibleType.InclusiveEmailOnly
      )
    } else {
      this.reviewFacade.setVisibleEmailType(EmailThreadVisibleType.All)
    }
  }

  public resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }
}
