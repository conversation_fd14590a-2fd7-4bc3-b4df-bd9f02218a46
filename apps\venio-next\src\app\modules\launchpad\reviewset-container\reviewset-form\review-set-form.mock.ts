// review-set-form-mock.ts
import { FormBuilder, FormGroup } from '@angular/forms'
import {
  ReviewSetForm,
  ReviewSetSourceTypes,
} from '@venio/shared/models/interfaces'

export function createMockReviewSetForm(
  fb: FormBuilder
): FormGroup<ReviewSetForm> {
  return fb.group<ReviewSetForm>({
    projectId: fb.control(1),
    reviewSetTemplateId: fb.control(0),
    reviewSetId: fb.control(0),
    name: fb.control('Mock Name'),
    batchPrefix: fb.control('Default'),
    batchStartNumber: fb.control(1),
    batchPaddingLength: fb.control(8),
    batchSize: fb.control(100),
    purpose: fb.control(''),
    reviewSource: fb.control(ReviewSetSourceTypes.TAG),
    tagId: fb.control([]),
    tagSelectionOption: fb.control('OR'),
    folderId: fb.control([]),
    folderSelectionOption: fb.control('OR'),
    savedSearchId: fb.control(null),
    orderByField: fb.control('INGESTION_ORDER'),
    sortOrder: fb.control('asc'),
    sortByCustodian: fb.control(true),
    custodianSortOrder: fb.control('asc'),
    displayTag: fb.control([]),
    enableAutoCollect: fb.control(false),
    autoCollectFrequency: fb.control(24),
    autoCollectExpiresOn: fb.control(''),
    autoCollectMinThresholdValue: fb.control(500),
    autoCollectionSelectionCriteria: fb.control(true),
    autoCollectReviewset: fb.control(null),
    selectedUserGroups: fb.control([]),
    layout: fb.control(1),
    highlightGroup: fb.control(1),
    parentChildIncluded: fb.control(false),
    msgThreadIncluded: fb.control(false),
    excludePrevReviewSetDoc: fb.control(false),
    excludeNonInclusiveEmails: fb.control(false),
    propagateTagPCSet: fb.control(false),
    tagPropagationRule: fb.control(false),
    propagateTagEmailThread: fb.control(false),
    propagateReviewPCSet: fb.control(false),
    reviewDuplicatePropagationRule: fb.control(false),
    propagateReviewEmailThread: fb.control(false),
    useCALProfileForReviewSet: fb.control(false),
    trainingRecallThreshold: fb.control(70),
    categoryTrainingThreshold: fb.control(70),
    predictionAccuracyThreshold: fb.control(70),
    batchRichnessThreshold: fb.control(10),
    reviewRelevanceThreshold: fb.control(80),
    allowReviewAfterCALThreshold: fb.control(true),
    controlSetSizeDerivedBy: fb.control(0),
    percentageOfPopulation: fb.control(10),
    numberOfDocuments: fb.control(1000),
    isDynamicControlSet: fb.control(true),
    calControlSetDocCount: fb.control(0),
    confidenceLevel: fb.control(95),
    confidenceInterval: fb.control(5),
    controlSetPercentFromTrainingBatch: fb.control(5),
    calControlSetMinDocCount: fb.control(5),
    calControlSetMaxDocCount: fb.control(0),
    calTrainingSetMinDocCount: fb.control(5),
    calTrainingSetMaxDocCount: fb.control(3000),
    autoQueueForHtmlConversion: fb.control(true),
    autoQueueForTiff: fb.control(false),
    markTaggedDocsAsReviewed: fb.control(false),
  })
}
