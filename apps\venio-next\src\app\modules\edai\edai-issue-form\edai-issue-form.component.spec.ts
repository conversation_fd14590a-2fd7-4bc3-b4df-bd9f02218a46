import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiIssueFormComponent } from './edai-issue-form.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { AiFacade, JobForm } from '@venio/data-access/ai'
import { of } from 'rxjs'

describe('EdaiIssueFormComponent', () => {
  let component: EdaiIssueFormComponent
  let fixture: ComponentFixture<EdaiIssueFormComponent>
  let mockAiFacade: any
  beforeEach(async () => {
    mockAiFacade = {
      selectEdaiRelevanceCompletedJobs: of({
        data: [],
        status: 'Success',
        message: '',
      }),
      fetchEdaiRelevanceCompletedJobs: jest.fn(), // Prevent actual API calls
      loadRelevanceJob$: of({ issueDetail: [] }) as any, // Mock the relevance job loading
    }

    await TestBed.configureTestingModule({
      imports: [EdaiIssueFormComponent, ReactiveFormsModule, FormsModule],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        { provide: AiFacade, useValue: mockAiFacade },
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiIssueFormComponent)
    component = fixture.componentInstance
    const mockFormGroup = new FormGroup<JobForm>({} as any)
    fixture.componentRef.setInput('edaiFormGroup', mockFormGroup)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
