import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentAdditionalActionsComponent } from './document-additional-actions.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { provideMockStore } from '@ngrx/store/testing'
import { StartupsFacade } from '@venio/data-access/review'
import { environment } from '@venio/shared/environments'

describe('DocumentAdditionalActionsComponent', () => {
  let component: DocumentAdditionalActionsComponent
  let fixture: ComponentFixture<DocumentAdditionalActionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentAdditionalActionsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        DialogService,
        DialogContainerService,
        StartupsFacade,
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin, // Adjust the origin accordingly
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentAdditionalActionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
