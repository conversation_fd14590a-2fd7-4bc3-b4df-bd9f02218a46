<div>
  <h1 class="t-text-[#030303] t-text-4xl t-font-black t-mb-3 t-text-left">
    CHANGE PASSWORD
  </h1>

  <p class="t-text-[#FFBB12] t-text-2xl t-font-black t-mb-6 t-text-left">
    Please enter new password
  </p>
</div>

<form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()">
  <div class="t-mb-4 t-flex t-flex-col t-items-start">
    <kendo-textbox
      formControlName="newPassword"
      [tabindex]="0"
      placeholder="New Password"
      [type]="showNewPassword ? 'text' : 'password'"
      [popover]="newPassword"
      kendoPopoverAnchor
      #newPasswordAnchor
      class="t-w-72 t-max-w-full t-rounded v-input-shadow">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          kendoButton
          class="t-pr-2"
          look="clear"
          (click)="toggleNewPasswordVisibility()">
          <kendo-svgicon
            [icon]="
              showNewPassword ? icons.eyeIcon : icons.slashIcon
            "></kendo-svgicon>
        </button>
      </ng-template>
    </kendo-textbox>

    <kendo-popover #newPassword position="right" [width]="280">
      <ng-template kendoPopoverBodyTemplate>
        <div
          [ngClass]="passwordStrengthClass"
          class="v-error-popover t-opacity-90">
          {{ passwordStrengthMessage }}
        </div>
      </ng-template>
    </kendo-popover>
  </div>

  <div class="t-mb-4">
    <kendo-textbox
      [tabIndex]="1"
      formControlName="confirmPassword"
      placeholder="Confirm Password"
      [type]="showConfirmPassword ? 'text' : 'password'"
      class="t-w-72 t-max-w-full t-rounded v-input-shadow">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          kendoButton
          class="t-pr-2"
          look="clear"
          (click)="toggleConfirmPasswordVisibility()">
          <kendo-svgicon
            [icon]="
              showConfirmPassword ? icons.eyeIcon : icons.slashIcon
            "></kendo-svgicon>
        </button>
      </ng-template>
    </kendo-textbox>
  </div>

  <kendo-formerror
    *ngIf="passwordMismatch"
    class="t-mb-3 t-w-72 t-max-w-full t-justify-center">
    Password doesn't match!
  </kendo-formerror>

  <button
    kendoButton
    type="submit"
    class="t-bg-[#9BD2A7] t-text-white t-w-72 t-max-w-full t-py-2 t-rounded-lg t-shadow-md disabled:t-cursor-not-allowed disabled:t-grayscale disabled:t-shadow-none"
    [disabled]="resetPasswordForm.invalid || passwordMismatch">
    Reset Password
  </button>
</form>
