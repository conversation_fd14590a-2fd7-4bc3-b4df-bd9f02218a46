import { Component } from '@angular/core';
import { SunburstComponent } from '../sunburst/sunburst.component';
import { DataTableForFocusedSectionComponent } from '../data-table-for-focused-section/data-table-for-focused-section.component';
import { ListOfFilesComponent } from '../list-of-files/list-of-files.component';
import { NgIf } from '@angular/common';
import { DataService } from '../data.service';
@Component({
  selector: 'venio-eci-focused-section',
  standalone: true,
  imports: [SunburstComponent, DataTableForFocusedSectionComponent, ListOfFilesComponent, NgIf],
  templateUrl: './focused-section.component.html',
  styleUrl: './focused-section.component.scss'
})
export class EciFocusedSectionComponent {
  constructor(private dataService: DataService) { }

  public showDetails: boolean = false;

  ngOnInit() {
    this.dataService.showDetails$.subscribe(state => {
      console.log('showDetails state changed:', state);
      this.showDetails = state;
      // this.cdr.detectChanges();
    });
  }
}
