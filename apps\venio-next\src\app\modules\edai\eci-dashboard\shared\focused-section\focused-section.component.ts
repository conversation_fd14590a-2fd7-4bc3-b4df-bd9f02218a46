import { Component } from '@angular/core';
import { EciSunburstComponent } from '../sunburst/sunburst.component';
import { DataTableForFocusedSectionComponent } from '../data-table-for-focused-section/data-table-for-focused-section.component';
import { ListOfFilesComponent } from '../list-of-files/list-of-files.component';
import { NgIf } from '@angular/common';
import { EciDataService } from '../eci-data.service';
@Component({
  selector: 'venio-eci-focused-section',
  standalone: true,
  imports: [EciSunburstComponent, DataTableForFocusedSectionComponent, ListOfFilesComponent, NgIf],
  templateUrl: './focused-section.component.html',
  styleUrl: './focused-section.component.scss'
})
export class EciFocusedSectionComponent {
  constructor(private dataService: EciDataService) { }

  public showDetails: boolean = false;

  ngOnInit() {
    this.dataService.showDetails$.subscribe(state => {
      console.log('showDetails state changed:', state);
      this.showDetails = state;
      // this.cdr.detectChanges();
    });
  }
}
