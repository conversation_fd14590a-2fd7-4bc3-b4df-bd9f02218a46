<div
  kendoPopoverContainer
  #container="kendoPopoverContainer"
  [popover]="selectOptions">
  <div
    #anchor
    (click)="container.toggle(anchor)"
    class="t-flex t-items-center t-gap-1">
    <input
      type="checkbox"
      [indeterminate]="false"
      #chkSelectAll
      kendoCheckBox
      (change)="onSelectAllChanged($event)" />
    <span
      venioSvgLoader
      color="#979797"
      applyEffectsTo="fill"
      svgUrl="assets/svg/blue-arrow-down.svg"
      width="11.47px"></span>
  </div>
</div>
<kendo-popover #selectOptions position="bottom">
  <ng-template kendoPopoverBodyTemplate>
    <ul class="list-none">
      <li class="t-m-2">
        <button
          (click)="onSelectOptionClicked('SELECT-CURRENT')"
          type="button"
          class="list-group-item list-group-item-action">
          Select all from current page
        </button>
      </li>
      <li class="t-m-2">
        <button
          (click)="onSelectOptionClicked('DESELECT-CURRENT')"
          type="button"
          class="list-group-item list-group-item-action">
          Deselect all from current page
        </button>
      </li>
      <li class="t-m-2">
        <button
          (click)="onSelectOptionClicked('SELECT_ALL')"
          type="button"
          class="list-group-item list-group-item-action">
          Select all from all pages
        </button>
      </li>
      <li class="t-m-2">
        <button
          (click)="onSelectOptionClicked('DESELECT-ALL')"
          type="button"
          class="list-group-item list-group-item-action">
          Deselect all from all pages
        </button>
      </li>
    </ul>
  </ng-template>
</kendo-popover>
