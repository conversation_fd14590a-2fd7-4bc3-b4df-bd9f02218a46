import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewDialogContainerComponent } from './reviewset-detail-view-dialog-container.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'

describe('ReviewsetDetailViewDialogContainerComponent', () => {
  let component: ReviewsetDetailViewDialogContainerComponent
  let fixture: ComponentFixture<ReviewsetDetailViewDialogContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewDialogContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            resetProjectState: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      ReviewsetDetailViewDialogContainerComponent
    )
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
