import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentSearchControlComponent } from './document-search-control.component'
import { FormControl, FormGroup } from '@angular/forms'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import {
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { of } from 'rxjs'

describe('DocumentSearchControlComponent', () => {
  let component: DocumentSearchControlComponent
  let fixture: ComponentFixture<DocumentSearchControlComponent>
  let mockStartupsFacade: any

  beforeEach(async () => {
    mockStartupsFacade = {
      fetchDefaultGroups: jest.fn(),
      getUserRights$: of({}),
      hasGroupRight$: jest.fn().mockReturnValue(of(true)),
    }

    await TestBed.configureTestingModule({
      imports: [DocumentSearchControlComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        SearchFacade,
        FieldFacade,
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentSearchControlComponent)
    component = fixture.componentInstance
    component.searchFormGroup = new FormGroup({
      searchExpression: new FormControl(),
      includePC: new FormControl(),
    })
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
