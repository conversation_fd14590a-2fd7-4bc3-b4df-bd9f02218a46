<div class="t-relative">
  @if (isReviewSetProgressLoading()) {
  <div
    class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
  }

  <div class="t-flex">
    @if (groupedData()) {
    <kendo-chart
      class="t-w-full t-h-[260px] !t-border-none"
      [chartArea]="COMMON_CHART_OPTIONS.chartArea">
      <kendo-chart-category-axis>
        <kendo-chart-category-axis-item
          [categories]="formattedDates()"
          [line]="{ visible: false }"
          [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [minorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [majorTicks]="COMMON_CHART_OPTIONS.majorTicks"
          [minorTicks]="COMMON_CHART_OPTIONS.minorTicks"
          [labels]="CATEGORY_AXIS_LABELS">
        </kendo-chart-category-axis-item>
      </kendo-chart-category-axis>
      <kendo-chart-value-axis>
        <kendo-chart-value-axis-item
          [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [minorGridLines]="COMMON_CHART_OPTIONS.gridLines"
          [majorTicks]="COMMON_CHART_OPTIONS.majorTicks"
          [minorTicks]="COMMON_CHART_OPTIONS.minorTicks"
          [labels]="VALUE_AXIS_LABELS">
        </kendo-chart-value-axis-item>
      </kendo-chart-value-axis>

      <kendo-chart-series>
        <kendo-chart-series-item
          type="column"
          [data]="groupedData()"
          field="value"
          categoryField="date"
          color="color"
          [gap]="12"
          [spacing]="3"
          [border]="COMMON_CHART_OPTIONS.border"
          [labels]="COMMON_CHART_OPTIONS.seriesLabels">
        </kendo-chart-series-item>
      </kendo-chart-series>
    </kendo-chart>
    }
  </div>
</div>

<!-- TODO: This functionality will be implemented at a later stage -->
<!-- Footer - Date Label -->
<!-- <div class="t-text-center t-text-sm t-font-medium">
            <div class="t-relative t-top-[-5px]">
              <span class="t-tracking-[1.5px] t-font-medium t-text-[10px]"
                >DATE</span
              >
            </div>
          </div> -->
