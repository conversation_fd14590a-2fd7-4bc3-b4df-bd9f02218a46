<div class="t-flex t-h-screen">
  <!-- Left Pane: Logo -->
  <div
    class="t-w-3/6 t-relative t-flex t-items-center t-justify-center v-login-bg">
    <div class="t-absolute t-inset-0 t-bg-[#b6c66a] t-opacity-20"></div>

    <div class="t-relative t-z-10">
      <img
        ngSrc="assets/img/venio-logo-full-vertical.png"
        height="148"
        width="123"
        alt="Venio"
        class="t-h-[9rem] t-w-[10.75rem] t-object-contain" />
    </div>
  </div>

  <div class="t-w-3/6 t-flex t-items-center t-p-7 t-bg-white">
    <div class="t-bg-white t-p-9 t-mx-3 t-w-[30rem]">
      <div>
        <h1 class="t-text-[#030303] t-text-4xl t-font-black t-mb-3 t-text-left">
          LOGIN VERIFICATION
        </h1>

        <p class="t-text-[#FFBB12] t-text-2xl t-font-black t-mb-6 t-text-left">
          Please enter security code
        </p>
      </div>
      <form>
        <div class="t-mb-4 t-flex t-flex-col t-items-start">
          <kendo-textbox
            [formControl]="securityCodeControl"
            placeholder="Security Code"
            [maxlength]="6"
            type="text"
            class="t-w-72 t-max-w-full t-rounded v-input-shadow">
          </kendo-textbox>
          @if(isInvalid){
          <span class="t-accent-error t-text-error t-m-1 t-text-[11.23px]">
            Enter a valid security code.
          </span>
          }
        </div>
        <div
          [ngClass]="{
            't-text-error': messageState()?.type === 'error',
            't-text-success': messageState()?.type === 'success'
          }"
          class="t-flex t-items-center t-text-[14px] t-font-medium t-justify-start t-max-w-[18rem]">
          {{ messageState()?.message }}
        </div>

        <button
          kendoButton
          type="button"
          fillMode="clear"
          themeColor="none"
          (click)="regenerateCode()"
          class="t-flex t-text-[#21857E] t-text-sm t-mt-1 t-mb-3 t-justify-self-center t-font-semibold">
          Regenerate the code
        </button>
        <button
          (click)="login()"
          [disabled]="isLoginInProgress()"
          kendoButton
          fillMode="clear"
          themeColor="secondary"
          type="button"
          class="t-bg-[#9BD2A7] t-text-white t-w-72 t-max-w-full t-py-2 t-rounded-lg t-shadow-md disabled:t-cursor-not-allowed disabled:t-grayscale disabled:t-shadow-none">
          <kendo-loader
            *ngIf="isLoginInProgress()"
            type="pulsing"
            themeColor="success" />
          Login
        </button>
      </form>
    </div>
  </div>
</div>
