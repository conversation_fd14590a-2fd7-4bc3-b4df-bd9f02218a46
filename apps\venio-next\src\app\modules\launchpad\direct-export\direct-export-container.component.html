<div
  class="t-flex t-flex-col t-gap-2 t-h-full t-px-[10px] t-py-2"
  [ngClass]="{
    't-bg-[#FAFAFAB5]': currentStep === 1,
    't-border-1 t-border-solid t-border t-rounded t-border-[#E2E2E2]':
      currentStep !== 1
  }">
  <div class="t-flex t-flex-1 t-overflow-auto t-p-0 t-w-80%" #content>
    <venio-direct-export-detail
      #directExprtDetail
      class="t-flex t-flex-1 t-h-full"
      *ngIf="currentStep === 1"
      (selectedServiceTypeName)="setSelectedServiceType($event)"
      (existingCaseValue)="setIsExistingCase($event)"
      (selectedCaseIdValue)="existingCaseId = $event"
      [selectedServiceType]="selectedServiceTypeName"
      (selectedBaseAPIUrl)="selectedBaseAPIUrl = $event"
      [settingsForm]="settingsForm"
      [timeZones$]="timeZone$"
      [serviceTypeList]="serviceTypeList"
      [exportTemplates]="exportTemplatesList"
      [existingCaseId]="existingCaseId"
      [isExistingCase]="existingCase"
      [canCreateCase]="canCreateCase"
      [allowCaseSelection]="allowCaseSelection"
      [isCaseCreationFlow]="isCaseCreationFlow"></venio-direct-export-detail>

    <venio-direct-export-detail-summary
      class="t-flex t-flex-1 t-h-full"
      *ngIf="currentStep === 2 || currentStep === 3"
      [selectedBaseAPIUrl]="selectedBaseAPIUrl"
      [selectedServiceTypeName]="selectedServiceTypeName"
      [settings]="settings"
      [timeZones$]="timeZone$"
      [serviceTypeList]="serviceTypeList"
      [settingsForm]="settingsForm"></venio-direct-export-detail-summary>
  </div>

  <div
    class="t-flex t-flex-row t-justify-end t-gap-4 t-mb-2 t-w-full t-items-end"
    #footer>
    @if(currentStep !== 1 && !isServiceTypeCaseLoading){
    <button
      class="!t-border !t-border-[#1DBADC] t-bg-[#ffffff] !t-text-[#1DBADC] hover:!t-bg-[#1DBADC] hover:!t-text-[#ffffff] !t-rounded-[4px]"
      kendoButton
      [disabled]="isServiceTypeCaseLoading"
      (click)="previousStep()">
      BACK</button
    >} @if(isServiceTypeCaseLoading || currentStep < 3){
    <button
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      (click)="nextStep()"
      [disabled]="isServiceTypeCaseLoading">
      <kendo-loader
        *ngIf="isServiceTypeCaseLoading"
        type="pulsing"
        themeColor="success" />
      {{ currentStep === 1 ? 'Next' : 'Upload' }}</button
    >}
  </div>
</div>
