import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProgressComponent } from './progress.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

jest.mock('@venio/util/utilities', () => {
  return {
    ReviewSetBatchChartWorkerService: jest.fn().mockImplementation(() => {
      return {
        generateProgressChartData: jest.fn().mockResolvedValue([]),
        terminate: jest.fn(),
      }
    }),
  }
})

describe('ProgressComponent', () => {
  let component: ProgressComponent
  let fixture: ComponentFixture<ProgressComponent>

  const mockReviewSetFacade = {
    selectReviewSetProgressSuccess$: of(undefined),
    isReviewSetProgressLoading$: of(false),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProgressComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(ProgressComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
