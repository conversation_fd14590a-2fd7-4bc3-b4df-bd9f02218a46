import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SummaryComponent } from './summary.component'
import { of } from 'rxjs'
import { ReviewSetFacade } from '@venio/data-access/common'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'

describe('SummaryComponent', () => {
  let component: SummaryComponent
  let fixture: ComponentFixture<SummaryComponent>

  const mockReviewSetFacade = {
    selectReviewSetBatchSummary$: of(undefined),
    selectReviewSetBatchSummaryRate$: of(undefined),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SummaryComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: ReviewSetFacade, useValue: mockReviewSetFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
