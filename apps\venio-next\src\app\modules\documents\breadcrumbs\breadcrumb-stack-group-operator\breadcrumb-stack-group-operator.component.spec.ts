import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbStackGroupOperatorComponent } from './breadcrumb-stack-group-operator.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { provideMockStore } from '@ngrx/store/testing'

describe('BreadcrumbStackGroupOperatorComponent', () => {
  let component: BreadcrumbStackGroupOperatorComponent
  let fixture: ComponentFixture<BreadcrumbStackGroupOperatorComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbStackGroupOperatorComponent, NoopAnimationsModule],
      providers: [provideMockStore({}), BreadcrumbFacade],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbStackGroupOperatorComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
