<div class="t-flex t-mt-4 t-flex-col t-w-full">
  <kendo-grid
    [kendoGridBinding]="gridView"
    kendoGridSelectBy="id"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    [skip]="skip">
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total">
      <div class="t-flex t-items-center t-gap-2">
        <div class="t-flex t-items-center">
          <input
            type="checkbox"
            #notification
            kendoCheckBox
            data-qa="notificationCheck" />
          <kendo-label
            class="k-checkbox-label"
            [for]="notification"
            text="Refresh in every "></kendo-label>
        </div>
        <div>
          <kendo-textbox
            class="!t-border-[#ccc] !t-w-14"
            data-qa="refresh-time"
            size="large"
            placeholder="1"
            [clearButton]="true"></kendo-textbox>
        </div>
        <div>second(s)</div>
      </div>
      <kendo-grid-spacer></kendo-grid-spacer>
      <kendo-label class="k-form">
        <kendo-numerictextbox
          class="!t-w-[3rem]"
          format="number"
          [step]="1"
          [value]="currentPage"
          [min]="1"
          [max]="totalPages"
          data-qa="currentPage"
          [spinners]="false"
          [selectOnFocus]="true"></kendo-numerictextbox>
      </kendo-label>
      <span> - {{ pageSize }} Of {{ gridView.length }} </span>
      <kendo-dropdownlist
        data-qa="pageSize"
        class="!t-w-[5rem] !t-border-[#707070]"
        [data]="sizes"
        [value]="pageSize"></kendo-dropdownlist>
      per page
      <div class="t-flex t-gap-2">
        <button
          kendoButton
          data-qa="firstPage"
          #parentEl
          *ngFor="let icon of svgIconForPageControls"
          class="!t-p-[0.3rem]"
          (click)="browseActionClicked(icon.actionType)"
          fillMode="outline"
          size="none">
          <span
            [parentElement]="parentEl.element"
            venioSvgLoader
            hoverColor="#FFFFFF"
            [svgUrl]="icon.iconPath"
            height="0.8rem"
            width="1rem"></span>
        </button>
      </div>
    </ng-template>

    <ng-template kendoGridToolbarTemplate>
      <kendo-multiselect
        data-qa="searchBy"
        adaptiveMode="auto"
        [checkboxes]="true"
        [autoClose]="false"
        [data]="notStartedDropdown"
        class="!t-w-[25rem]"></kendo-multiselect>
      <kendo-grid-spacer></kendo-grid-spacer>
    </ng-template>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="full_name"
      title="Search Name"
      [width]="220">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          class="customer-photo"
          [ngStyle]="{ 'background-image': '' }"></div>
        <div class="customer-name">{{ dataItem.full_name }}</div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="job_title"
      title="Completed Line"
      [width]="220">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="is_online"
      title="Completed %"
      [width]="100"
      [class]="{ 'text-center': true }"
      [resizable]="false"
      filter="boolean">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span *ngIf="dataItem.is_online === true" class="badge badge-success"
          >Online</span
        >
        <span *ngIf="dataItem.is_online === false" class="badge badge-danger"
          >Offline</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="phone"
      title="Status"
      [width]="130">
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="address"
      title="ETA"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="gender"
      title="Searched By"
      [width]="200">
    </kendo-grid-column>
  </kendo-grid>
</div>
