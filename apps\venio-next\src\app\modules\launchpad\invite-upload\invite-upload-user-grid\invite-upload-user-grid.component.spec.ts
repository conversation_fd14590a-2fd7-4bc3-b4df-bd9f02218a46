import { ComponentFixture, TestBed } from '@angular/core/testing'
import { InviteUploadUserGridComponent } from './invite-upload-user-grid.component'
import { of } from 'rxjs'
import { UserFacade } from '@venio/data-access/common'
import { GridTypes, UserGridColumns } from '@venio/shared/models/constants'
import { InviteUploadLocalState } from '../invite-upload-container/invite-upload-local-state'

describe('InviteUploadUserGridComponent', () => {
  let component: InviteUploadUserGridComponent
  let fixture: ComponentFixture<InviteUploadUserGridComponent>

  const mockUserFacade = {
    selectIsInvitationInProgress$: of(false),
  }

  const mockInviteUploadViewState = {
    gridColumnMap: jest.fn().mockReturnValue(UserGridColumns),
    selectedInternalUsers: jest.fn().mockReturnValue(['<EMAIL>']),
    selectedExternalUsers: jest.fn().mockReturnValue(['<EMAIL>']),
    filteredInternalUsers: jest
      .fn()
      .mockReturnValue([
        { email: '<EMAIL>', userName: 'Internal User' },
      ]),
    filteredExternalUsers: jest
      .fn()
      .mockReturnValue([
        { email: '<EMAIL>', userName: 'External User' },
      ]),
    shareToExternalUsers: jest.fn().mockReturnValue(false),
    addSelectedInternalUser: jest.fn(),
    addSelectedExternalUser: jest.fn(),
    addInternalUserList: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InviteUploadUserGridComponent],
      providers: [
        { provide: UserFacade, useValue: mockUserFacade },
        {
          provide: InviteUploadLocalState,
          useValue: mockInviteUploadViewState,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(InviteUploadUserGridComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('gridType', GridTypes.INTERNAL)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should display the correct columns for the internal grid type', () => {
    // GIVEN: Mock gridType as external
    fixture.componentRef.setInput('gridType', GridTypes.INTERNAL)

    // WHEN: Columns are computed
    fixture.detectChanges()

    // THEN: Verify the correct columns
    const internalColumns = mockInviteUploadViewState
      .gridColumnMap()
      .get(GridTypes.INTERNAL)
    expect(component.columns()).toEqual(internalColumns)
  })

  it('should display the correct columns for the external grid type', () => {
    // GIVEN: Mock gridType as external
    fixture.componentRef.setInput('gridType', GridTypes.EXTERNAL)

    // WHEN: Columns are computed
    fixture.detectChanges()

    // THEN: Verify the correct columns
    const externalColumns = mockInviteUploadViewState
      .gridColumnMap()
      .get(GridTypes.EXTERNAL)
    expect(component.columns()).toEqual(externalColumns)
  })

  it('should display the correct users for the internal grid type', () => {
    // GIVEN: Mock gridType as internal
    fixture.componentRef.setInput('gridType', GridTypes.INTERNAL)

    // WHEN: Users are computed
    fixture.detectChanges()

    // THEN: Verify the correct users
    const internalUsers = [
      { email: '<EMAIL>', userName: 'Internal User' },
    ]
    expect(component.users()).toEqual(internalUsers)
  })

  it('should display the correct users for the external grid type', () => {
    // GIVEN: Mock gridType as external
    fixture.componentRef.setInput('gridType', GridTypes.EXTERNAL)

    // WHEN: Columns are computed
    fixture.detectChanges()

    // THEN: Verify the correct columns
    const externalUsers = [
      { email: '<EMAIL>', userName: 'External User' },
    ]
    expect(component.users()).toEqual(externalUsers)
  })

  it('should display the correct selected users for the external grid type', () => {
    // GIVEN: Mock gridType as internal
    fixture.componentRef.setInput('gridType', GridTypes.EXTERNAL)

    // WHEN: Selected users are computed
    const externalSelectedUsers = ['<EMAIL>']
    mockInviteUploadViewState.selectedExternalUsers.mockReturnValue(
      externalSelectedUsers
    )
    fixture.detectChanges()

    // THEN: Verify the correct selected users
    expect(component.selectedUsers()).toEqual(externalSelectedUsers)
  })

  it('should display the correct selected users for the internal grid type', () => {
    // GIVEN: Mock gridType as internal
    fixture.componentRef.setInput('gridType', GridTypes.INTERNAL)

    // WHEN: Selected users are computed
    const internalSelectedUsers = ['<EMAIL>']
    mockInviteUploadViewState.selectedInternalUsers.mockReturnValue(
      internalSelectedUsers
    )
    fixture.detectChanges()

    // THEN: Verify the correct selected users
    expect(component.selectedUsers()).toEqual(internalSelectedUsers)
  })
})
