.eci-dashboard-container {
  padding: 1rem;
  height: 100%;
  overflow-y: auto;

  // CSS Variables for ECI theme
  --primary-01: #af26c6;
  --primary-02: #4b3af5;
  --primary-03: #121542;
  --secondary-01: #efefef;
  --secondary-02: #777777;
  --secondary-03: #231f20;
  --viz-bg: #fcfbff;
  --viz-border: #ccc9da;
  --viz-primary: #b940e5;
  --viz-secondary: #6305ff;
  --viz-accent-pink: #ff00ff;
  --viz-black: #272829;
  --viz-light-grey: #f9f9fa;
  --chart-purple: #6305ff;
  --viz-text: #0f0f0f;
  --viz-text-light: #b3b2b8;
  --viz-active: #fbf0ff;
  --viz-legend-bg: #fcfcfd;
}

.filters-wrapper {
  margin-bottom: 1rem;
  position: relative;
}

.custodian-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  label {
    cursor: pointer;
    font-size: 0.875rem;
  }
}

.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.grid-row {
  display: grid;
  gap: 1.5rem;

  &:first-child {
    grid-template-columns: 1fr 2fr;
  }

  &:nth-child(2) {
    grid-template-columns: repeat(3, 1fr);
  }

  &:last-child {
    grid-template-columns: 1fr;
  }
}

.grid-col-1,
.grid-col-2,
.grid-col-full {
  min-height: 400px;
}

// Responsive design
@media (max-width: 1200px) {
  .grid-row {
    &:first-child {
      grid-template-columns: 1fr;
    }

    &:nth-child(2) {
      grid-template-columns: 1fr 1fr;
    }
  }
}

@media (max-width: 768px) {
  .grid-row {

    &:first-child,
    &:nth-child(2) {
      grid-template-columns: 1fr;
    }
  }

  .eci-dashboard-container {
    padding: 0.5rem;
  }

  .dashboard-grid {
    gap: 1rem;
  }

  .grid-row {
    gap: 1rem;
  }
}