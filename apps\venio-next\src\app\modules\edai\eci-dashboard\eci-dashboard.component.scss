/* ECI Dashboard Styles */
.eci-dashboard-container {
  padding: 1.5rem;
  background-color: #f9fafb;
  min-height: 100vh;
}

.chart-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.filter-popup {
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* Custom styles for charts */
:host ::ng-deep {
  .plotly-chart {
    width: 100% !important;
    height: 100% !important;
  }

  .k-tabstrip {
    border: none;
  }

  .k-tabstrip-items {
    border-bottom: 1px solid #e5e7eb;
  }

  .k-tabstrip-tab {
    border: none;
    background: transparent;
  }

  .k-tabstrip-tab.k-active {
    background: #f3f4f6;
    border-bottom: 2px solid #6366f1;
  }
}