import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SetOneBatchDashboardComponent } from './set-one-batch-dashboard.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SetOneBatchDashboardComponent', () => {
  let component: SetOneBatchDashboardComponent
  let fixture: ComponentFixture<SetOneBatchDashboardComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SetOneBatchDashboardComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SetOneBatchDashboardComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
