import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DropDownListModule,
  DropDownsModule,
} from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  LayoutModule,
  ExpansionPanelModule,
} from '@progress/kendo-angular-layout'
import {
  AutoFolder,
  AutoFolderOption,
  DelimiterInfo,
  Field,
  FieldFacade,
  FolderFacade,
  ProjectGroups,
  SearchFacade,
  StartupsFacade,
  TempTableResponseModel,
} from '@venio/data-access/review'
import { Subject, filter, map, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { validateBeforeSubmit } from '@venio/feature/generic-validator'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { DialogRef } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-auto-folder',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    LabelModule,
    GridModule,
    InputsModule,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    DropDownsModule,
    ExpansionPanelModule,
    ReactiveFormsModule,
  ],
  templateUrl: './auto-folder.component.html',
  styleUrls: ['./auto-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AutoFolderComponent implements OnInit, OnDestroy, AfterViewInit {
  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  constructor(
    private cdr: ChangeDetectorRef,
    private folderFacade: FolderFacade,
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private activatedRoute: ActivatedRoute,
    private startupsFacade: StartupsFacade,
    private fb: FormBuilder,
    private notificationService: NotificationService,
    @Optional()
    public dialogRef: DialogRef
  ) {}

  public autoFolderErrorMessage: string

  public autoFolderFormGroup: FormGroup

  public autoFolderOption = AutoFolderOption

  public showCustomFieldOption = false

  public searchTempTable: TempTableResponseModel

  public customFields: Field[] = []

  public separators: DelimiterInfo[]

  public delimiters: DelimiterInfo[]

  public securityGroups: ProjectGroups[] = []

  public securityPermissionData = [
    { text: 'Read Only', value: 'READ_ONLY' },
    { text: 'None', value: 'NONE' },
  ]

  public toDestroy$ = new Subject<void>()

  public ngOnInit(): void {
    this.fetchCustomFoldersAndGroupDetails()
    this.fieldFacade.fetchAllPermittedFields(this.projectId)
    this.folderFacade.fetchDelimeters()

    this.#initForm()
    this.#selectSearchTempTable()
    this.#selectCustomFields()
    this.#selectSeparators()
    this.#selectSecurityGroupData()
    this.#selectCreateAutoFolderSuccessResponse()
    this.#selectCreateAutoFolderFailureResponse()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngAfterViewInit(): void {
    this.#onFormChanges()
  }

  private fetchCustomFoldersAndGroupDetails(): void {
    this.folderFacade.fetchStaticFolders(this.projectId)
    this.startupsFacade.fetchDefaultGroups(this.projectId)
    this.folderFacade.fetchFolderSecurityGroupAction()
  }

  #initForm(): void {
    this.autoFolderFormGroup = this.fb.group({
      autoFolderOption: [this.autoFolderOption.Relative_Path],
      isCreateFolderForEachCustodian: [true],
      isCreateFolderForEachMedia: [true],
      customFieldId: [null],
      customFieldSeparator: [null],
    })
  }

  #selectSearchTempTable(): void {
    this.searchFacade.getSearchTempTables$
      .pipe(
        filter((searchTempTable) => !!searchTempTable),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchTempTable) => {
        this.searchTempTable = searchTempTable
      })
  }

  #selectCustomFields(): void {
    this.fieldFacade.getPermittedFields$
      .pipe(
        filter((res) => !!res),
        map((res) => res.filter((item) => item.isCustomField === true)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.cdr.markForCheck()
        this.customFields = res
      })
  }

  #selectSeparators(): void {
    this.folderFacade.getDelimitersSuccessResponse$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.cdr.markForCheck()
        this.separators = this.delimiters = res.data
        this.patchFormValue({ customFieldSeparator: ';' })
      })
  }

  #selectSecurityGroupData(): void {
    this.folderFacade.getSecurityGroupData$
      .pipe(
        filter((securityGroupData) => securityGroupData?.length > 0),
        map(
          (securityGroupData) =>
            securityGroupData.map((item) => ({
              ...item,
              permission: 'READ_ONLY',
            })) //to set default as we don't have READ/WRITE for autofolder
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((securityGroupData) => {
        this.cdr.markForCheck()
        this.securityGroups = securityGroupData
      })
  }

  #selectCreateAutoFolderSuccessResponse(): void {
    this.folderFacade.getCreateAutoFolderSuccessResponse$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.cdr.markForCheck()
        this.#showMessage(res.message, {
          style: this.#mapResponseStatusToStyle(res.status),
        })
        this.#resetFolderState()
        this.folderFacade.fetchAutoFolders(this.projectId)
        this.dialogRef.close()
      })
  }

  #selectCreateAutoFolderFailureResponse(): void {
    this.folderFacade.getCreateAutoFolderFailureResponse$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.cdr.markForCheck()
        this.#showMessage(res.message, {
          style: this.#mapResponseStatusToStyle(res.status),
        })
        this.#resetFolderState()
      })
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #resetFolderState(): void {
    this.folderFacade.resetFolderState([
      'createAutoFolderSuccessResponse',
      'createAutoFolderFailureResponse',
      'isCreateFolderLoading',
    ])
  }

  // Function to map response status to a valid style value
  #mapResponseStatusToStyle(status: string): Type['style'] {
    switch (status.toLowerCase()) {
      case 'success':
        return 'success'
      case 'warning':
        return 'warning'
      case 'error':
        return 'error'
      case 'info':
        return 'info'
      default:
        return 'none'
    }
  }

  // listen for changes on specific form control
  #onFormChanges(): void {
    this.autoFolderFormGroup
      .get('autoFolderOption')
      .valueChanges.pipe(takeUntil(this.toDestroy$))
      .subscribe((val: AutoFolderOption) => {
        if (val === AutoFolderOption.CustomField) {
          this.autoFolderFormGroup
            .get('customFieldId')
            .setValidators([Validators.required])
          this.autoFolderFormGroup
            .get('customFieldSeparator')
            .setValidators([Validators.required])
          this.showCustomFieldOption = true
        } else {
          this.autoFolderFormGroup.get('customFieldId').clearValidators()
          this.autoFolderFormGroup.get('customFieldSeparator').clearValidators()
          this.showCustomFieldOption = false
        }

        this.autoFolderFormGroup.get('customFieldId').updateValueAndValidity()
        this.autoFolderFormGroup
          .get('customFieldSeparator')
          .updateValueAndValidity()
        this.cdr.markForCheck()
      })
  }

  public createAutoFolder(): void {
    this.clearFormErrorMessage()
    const errorMessage = validateBeforeSubmit(this.autoFolderFormGroup)
    if (errorMessage) {
      this.setErrorMessage(errorMessage)
      return
    }

    const body: AutoFolder = this.autoFolderFormGroup.value

    body.customField =
      this.customFields?.find((x) => x?.venioFieldId === body?.customFieldId)
        ?.displayFieldName || ''

    if (body.autoFolderOption === AutoFolderOption.Relative_Path) {
      delete body['customFieldId']
      delete body['customField']
      delete body['customFieldSeparator']
    }

    body.groupAccess = this.securityGroups.map((g) => ({
      groupId: g.groupId,
      permission: g.permission,
    }))

    body.fileListTempTableName = this.searchTempTable?.searchResultTempTable

    this.folderFacade.createAutoFolder(this.projectId, body)
  }

  private setErrorMessage(message: string): void {
    this.cdr.markForCheck()
    this.autoFolderErrorMessage = message
  }

  private clearFormErrorMessage(): void {
    this.autoFolderErrorMessage = null
  }

  public handleFilterChange(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.separators = this.delimiters // Reset to the original list if the search term is empty
      return
    }

    const normalizedQuery = searchTerm.toLowerCase()

    this.separators = this.delimiters.filter(
      (delimiter: DelimiterInfo) =>
        delimiter.displayText.toLowerCase().includes(normalizedQuery) ||
        delimiter.text.toLowerCase().includes(normalizedQuery) ||
        delimiter.value.toString().toLowerCase().includes(normalizedQuery)
    )
  }

  public onSelectionChanged(event: DelimiterInfo): void {
    this.patchFormValue({ customFieldSeparator: event.text })
  }

  private patchFormValue(val): void {
    this.autoFolderFormGroup.patchValue(val)
  }
}
