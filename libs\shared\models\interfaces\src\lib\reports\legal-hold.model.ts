export interface ActiveTerminatedLegalHolds {
  holdId: number
  description: string
  holdStatus: string
  createdDate: Date
  createdBy: number
  modifiedDate: Date
  modifiedBy: number
  issuedDate: Date | null
  liftedDate: Date | null
  createdByName: string | null
  clientMatterNumber: string
  clientMatterName: string
  legalHoldOwner: string
  legalHoldNoticeDate: Date
  followUpDate: Date
  legalHoldStatus: string
  transferedToNetworkShare: string
  holdIssued: boolean
  documentMemoRetentionDate: Date
}

export interface LegalHoldsModel {
  holdId: number
  description: string
  holdStatus: string
  createdDate: string
  createdBy: number
  modifiedDate: string
  modifiedBy: number
  issuedDate: string | null
  liftedDate: string | null
  createdByName: string | null
  clientMatterNumber: string
  clientMatterName: string
  legalHoldOwner: string
  legalHoldNoticeDate: string
  followUpDate: string
  legalHoldStatus: string
  transferedToNetworkShare: string
  holdIssued: boolean
  documentMemoRetentionDate: string
}

export interface CustodianListModel {
  holdId: number
  custodianId: number
  custodianName: string
}
