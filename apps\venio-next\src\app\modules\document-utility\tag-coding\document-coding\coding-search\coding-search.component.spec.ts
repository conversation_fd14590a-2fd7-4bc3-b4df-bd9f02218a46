import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CodingSearchComponent } from './coding-search.component'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('CodingSearchComponent', () => {
  let component: CodingSearchComponent
  let fixture: ComponentFixture<CodingSearchComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodingSearchComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentCodingFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CodingSearchComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
