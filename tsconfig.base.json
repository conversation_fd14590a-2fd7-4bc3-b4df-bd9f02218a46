{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "esModuleInterop": true, "target": "ES2022", "module": "esnext", "lib": ["es2022", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "types": ["node", "jest"], "paths": {"@venio/data-access/ai": ["libs/data-access/ai/src/index.ts"], "@venio/data-access/auth": ["libs/data-access/auth/src/index.ts"], "@venio/data-access/breadcrumbs": ["libs/data-access/breadcrumbs/src/index.ts"], "@venio/data-access/common": ["libs/data-access/common/src/index.ts"], "@venio/data-access/control-settings": ["libs/data-access/control-settings/src/index.ts"], "@venio/data-access/document-utility": ["libs/data-access/document-utility/src/index.ts"], "@venio/data-access/feature-flag": ["libs/data-access/feature-flag/src/index.ts"], "@venio/data-access/iframe-messenger": ["libs/data-access/iframe-messenger/src/index.ts"], "@venio/data-access/reports": ["libs/data-access/reports/src/index.ts"], "@venio/data-access/review": ["libs/data-access/review/src/index.ts"], "@venio/feature/convert-document": ["libs/feature/convert-document/src/index.ts"], "@venio/feature/delete-document": ["libs/feature/delete-document/src/index.ts"], "@venio/feature/document-notes": ["libs/feature/document-notes/src/index.ts"], "@venio/feature/document-share": ["libs/feature/document-share/src/index.ts"], "@venio/feature/email-thread": ["libs/feature/email-thread/src/index.ts"], "@venio/feature/field-selector": ["libs/feature/field-selector/src/index.ts"], "@venio/feature/fulltext-viewer": ["libs/feature/fulltext-viewer/src/index.ts"], "@venio/feature/generic-validator": ["libs/feature/generic-validator/src/index.ts"], "@venio/feature/grid-stack": ["libs/feature/grid-stack/src/index.ts"], "@venio/feature/job-status": ["libs/feature/job-status/src/index.ts"], "@venio/feature/move-to-parent": ["libs/feature/move-to-parent/src/index.ts"], "@venio/feature/native-download": ["libs/feature/native-download/src/index.ts"], "@venio/feature/navigation-progress-bar": ["libs/feature/navigation-progress-bar/src/index.ts"], "@venio/feature/near-native": ["libs/feature/near-native/src/index.ts"], "@venio/feature/notification": ["libs/feature/notification/src/index.ts"], "@venio/feature/password-bank": ["libs/feature/password-bank/src/index.ts"], "@venio/feature/pdf-viewer": ["libs/feature/pdf-viewer/src/index.ts"], "@venio/feature/print-document": ["libs/feature/print-document/src/index.ts"], "@venio/feature/project-role": ["libs/feature/project-role/src/index.ts"], "@venio/feature/replace-field-value": ["libs/feature/replace-field-value/src/index.ts"], "@venio/feature/review": ["libs/feature/review/src/index.ts"], "@venio/feature/shared/directives": ["libs/feature/shared/directives/src/index.ts"], "@venio/feature/svg-icon": ["libs/feature/svg-icon/src/index.ts"], "@venio/feature/tag-email-thread": ["libs/feature/tag-email-thread/src/index.ts"], "@venio/feature/tiff-viewer": ["libs/feature/tiff-viewer/src/index.ts"], "@venio/feature/transcript-viewer": ["libs/feature/transcript-viewer/src/index.ts"], "@venio/shared/assets": ["libs/shared/assets/src/index.ts"], "@venio/shared/audio-transcribe": ["libs/feature/audio-transcribe/src/index.ts"], "@venio/shared/convert-to-html": ["libs/feature/convert-to-html/src/index.ts"], "@venio/shared/convert-to-image": ["libs/feature/convert-to-image/src/index.ts"], "@venio/shared/convert-to-rtf": ["libs/feature/convert-to-rtf/src/index.ts"], "@venio/shared/create-slipsheet": ["libs/feature/create-slipsheet/src/index.ts"], "@venio/shared/environments": ["libs/shared/environments/src/index.ts"], "@venio/shared/models/constants": ["libs/shared/models/constants/src/index.ts"], "@venio/shared/models/interfaces": ["libs/shared/models/interfaces/src/index.ts"], "@venio/shared/ocr-generated-image": ["libs/feature/ocr-generated-image/src/index.ts"], "@venio/shared/ocr-image": ["libs/feature/ocr-image/src/index.ts"], "@venio/shared/ocr-redacted-image": ["libs/feature/ocr-redacted-image/src/index.ts"], "@venio/shared/storage": ["libs/shared/storage/src/index.ts"], "@venio/shared/styles": ["libs/shared/styles/src/index.ts"], "@venio/shared/treelist": ["libs/feature/treelist/src/index.ts"], "@venio/ui/date-time-picker": ["libs/ui/date-time-picker/src/index.ts"], "@venio/ui/grid-custom-filter-checklist": ["libs/ui/grid-custom-filter-checklist/src/index.ts"], "@venio/ui/pagination": ["libs/ui/pagination/src/index.ts"], "@venio/ui/review": ["libs/ui/review/src/index.ts"], "@venio/ui/shortcut-key-dictionary": ["libs/ui/shortcut-key-dictionary/src/index.ts"], "@venio/util/utilities": ["libs/util/utilities/src/index.ts"], "@venio/util/uuid": ["libs/util/uuid/src/index.ts"], "document-notes": ["libs/feature/document-notes/src/index.ts"], "feature-tiff-viewer": ["libs/feature/tiff-viewer/src/index.ts"], "feature-transcript-viewer": ["libs/feature/transcript-viewer/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}