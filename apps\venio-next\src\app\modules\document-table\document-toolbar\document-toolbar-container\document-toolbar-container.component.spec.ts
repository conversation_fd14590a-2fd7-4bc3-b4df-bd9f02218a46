import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentToolbarContainerComponent } from './document-toolbar-container.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { ReactiveFormsModule } from '@angular/forms'
import { provideMockStore } from '@ngrx/store/testing'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'

describe('DocumentToolbarContainerComponent', () => {
  let component: DocumentToolbarContainerComponent
  let fixture: ComponentFixture<DocumentToolbarContainerComponent>
  let mockStartupsFacade: any

  beforeEach(async () => {
    mockStartupsFacade = {
      fetchDefaultGroups: jest.fn(),
      getUserRights$: of({}),
      hasGroupRight$: jest.fn().mockReturnValue(of(true)),
      getSearchParams$: of({}),
    }

    await TestBed.configureTestingModule({
      imports: [
        DocumentToolbarContainerComponent,
        ReactiveFormsModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        SearchFacade,
        FieldFacade,
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        DocumentsFacade,
        provideMockStore({}),
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin, // Adjust the origin accordingly
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
            snapshot: {
              queryParams: {
                projectId: '2',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentToolbarContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
