import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SendToProductionComponent } from './send-to-production.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { PLATFORM_ID } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { BehaviorSubject, of } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SendToProductionComponent', () => {
  let component: SendToProductionComponent
  let fixture: ComponentFixture<SendToProductionComponent>
  let mockSearchFacade: any
  let mockDialogService: any
  let mockIframeMessengerService: any
  let mockDocumentsFacade: any

  const menuEvent$ = new BehaviorSubject<DocumentMenuType>(null)

  const saveSearchSuccessResponse$ = new BehaviorSubject<any>(null)
  const isSaveSearchForProduction$ = new BehaviorSubject<boolean>(false)
  const saveSearchFailureResponse$ = new BehaviorSubject<any>(null)

  const isBatchSelected$ = new BehaviorSubject<boolean>(false)
  const selectedDocuments$ = new BehaviorSubject<number[]>([1, 2, 3])
  const unselectedDocuments$ = new BehaviorSubject<number[]>([])

  beforeEach(async () => {
    mockSearchFacade = {
      getSaveSearchSuccessResponse$: saveSearchSuccessResponse$.asObservable(),
      getIsSaveSearchForProduction$: isSaveSearchForProduction$.asObservable(),
      getSaveSearchFailureResponse$: saveSearchFailureResponse$.asObservable(),
      getSearchTempTables$: of({ searchGuid: '1231231' }),
      getTotalHitCount$: of(55),
      saveSearch: jest.fn(),
      resetIsSaveSearchForProductionFlag: jest.fn(),
      IsSearchLoading: jest.fn(),
    }

    mockDialogService = { open: jest.fn() }

    mockIframeMessengerService = {
      sendMessage: jest.fn(),
    }

    mockDocumentsFacade = {
      selectDocumentMenuEvent$: menuEvent$.asObservable(),
      getIsBatchSelected$: isBatchSelected$.asObservable(),
      getSelectedDocuments$: selectedDocuments$.asObservable(),
      getUnselectedDocuments$: unselectedDocuments$.asObservable(),
      resetDocumentState: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [SendToProductionComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DialogContainerService,
        FieldFacade,
        NotificationService,
        provideMockStore({}),
        {
          provide: IframeMessengerService,
          useValue: mockIframeMessengerService,
        },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: DialogService, useValue: mockDialogService },
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SendToProductionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  beforeEach(() => {
    jest.resetAllMocks()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should open production when production menu is clicked', () => {
    // Spy on searchFacade.saveSearch
    const saveSearchSpy = jest.spyOn(mockSearchFacade, 'saveSearch')

    fixture.detectChanges()

    // Trigger the production menu click event
    menuEvent$.next(DocumentMenuType.PRODUCTION)

    fixture.detectChanges()

    // Expect saveSearch method to have been called with the expected arguments
    expect(saveSearchSpy).toHaveBeenCalled()
  })

  it('should not open production when any other menu is clicked', () => {
    // Spy on searchFacade.saveSearch
    const saveSearchSpy = jest.spyOn(mockSearchFacade, 'saveSearch')

    fixture.detectChanges()

    // Trigger the production menu click event
    menuEvent$.next(DocumentMenuType.CONVERT)

    fixture.detectChanges()

    // Expect saveSearch method to have been called with the expected arguments
    expect(saveSearchSpy).not.toHaveBeenCalled()
  })

  it('should launch production if save search is done for production and successful', () => {
    // simulating save search for production by setting isSavedSearchForProduction as true
    isSaveSearchForProduction$.next(true)

    // simulate save search success
    saveSearchSuccessResponse$.next({ data: 5 })

    fixture.detectChanges()

    // should call sendMessage to launch production
    expect(mockIframeMessengerService.sendMessage).toHaveBeenCalled()

    // should reset the flag isSaveSearchForProduction
    expect(
      mockSearchFacade.resetIsSaveSearchForProductionFlag
    ).toHaveBeenCalled()
  })

  it('should not launch production if normal save search is done', () => {
    // simulating normal save search by setting isSavedSearchForProduction as false
    isSaveSearchForProduction$.next(false)

    // simulate save search success
    saveSearchSuccessResponse$.next({ data: 5 })

    fixture.detectChanges()

    // should not call sendMessage to launch production
    expect(mockIframeMessengerService.sendMessage).not.toHaveBeenCalled()
  })

  it('should show error message if no document is selected when launching production', () => {
    // setting selected documents as empty
    selectedDocuments$.next([])

    // setting batch selected as false
    isBatchSelected$.next(false)

    fixture.detectChanges()

    //simulating production menu click
    menuEvent$.next(DocumentMenuType.PRODUCTION)

    fixture.detectChanges()

    // should show error message using dialog service
    expect(mockDialogService.open).toHaveBeenCalled()
  })

  it('should display error message if API throws exception when saving search for production', () => {
    // simulating save search for production by setting isSavedSearchForProduction as true
    isSaveSearchForProduction$.next(true)

    // simulate save search failure
    saveSearchFailureResponse$.next({ ExceptionMessage: 'Error' })

    fixture.detectChanges()

    // should show error message using dialog service
    expect(mockDialogService.open).toHaveBeenCalled()
  })
})
