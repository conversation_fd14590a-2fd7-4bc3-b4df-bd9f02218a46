<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Folder UI</button>
</div>
<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="560"
  [minWidth]="250"
  [width]="'70%'">
  <kendo-dialog-titlebar>
    <div class="t-font-semibold t-text-lg">Folder</div>
  </kendo-dialog-titlebar>

  <div
    class="t-flex t-flex-col t-border t-border-t-[#ebebeb] t-border-x-0 t-border-b-0">
    <div class="t-flex t-gap-5">
      <div class="t-flex t-flex-1 t-flex-col t-w-full">
        <kendo-tabstrip
          class="t-w-full t-h-full"
          (tabSelect)="onTabSelect($event)">
          <kendo-tabstrip-tab title="Custom Folder" [selected]="true">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-w-full t-flex-col t-mt-2">
                  <div
                    class="t-flex t-mt-4 t-flex-wrap t-gap-3 v-custom-grey-bg">
                    <div
                      showHints="always"
                      class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
                      <kendo-label
                        for="DynamicName"
                        class="t-text-xs t-uppercase t-tracking-widest">
                        Name <span class="t-text-error">*</span>
                      </kendo-label>

                      <kendo-textbox
                        placeholder="Folder Name"
                        #DynamicName></kendo-textbox>
                    </div>

                    <div
                      showHints="always"
                      class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
                      <kendo-label
                        for="Reviewer"
                        class="t-text-xs t-uppercase t-tracking-widest">
                        Parent Folder
                      </kendo-label>

                      <kendo-dropdownlist
                        #Reviewer
                        [defaultItem]="defaultItemFolder"
                        [data]="['sample 1', 'sample 2', 'sample 3']"
                        textField="text"
                        valueField="value">
                      </kendo-dropdownlist>
                    </div>
                  </div>
                  <div
                    class="t-flex t-pt-0 t-flex-wrap t-gap-3 v-custom-grey-bg">
                    <div
                      showHints="always"
                      class="t-flex t-basis-[100%] t-flex-col t-gap-1">
                      <kendo-label
                        for="dynamicNote"
                        class="t-text-xs t-uppercase t-tracking-widest">
                        Description
                      </kendo-label>
                      <kendo-textarea
                        #dynamicNote
                        placeholder="Folder Description"
                        [rows]="3"
                        resizable="vertical"></kendo-textarea>
                    </div>
                  </div>

                  <div
                    class="t-flex t-pt-0 t-flex-col t-w-fill v-custom-grey-bg t-gap-3">
                    <div class="t-text-xs t-pl-1 t-uppercase t-tracking-widest">
                      Security
                    </div>
                    <div class="t-flex t-w-full">
                      <kendo-grid
                        class="!t-max-h-60"
                        filterable="menu"
                        [resizable]="true"
                        [pageable]="{ type: 'numeric', position: 'top' }"
                        [data]="sampleSecurityData">
                        <ng-template
                          kendoPagerTemplate
                          let-totalPages="totalPages"
                          let-currentPage="currentPage"
                          let-total="total">
                          <kendo-grid-spacer></kendo-grid-spacer>
                          <kendo-label class="k-form">
                            <kendo-numerictextbox
                              class="!t-w-[3rem]"
                              format="number"
                              [step]="1"
                              [value]="1"
                              [min]="1"
                              [max]="totalPages"
                              [spinners]="false"
                              [selectOnFocus]="true">
                            </kendo-numerictextbox>
                          </kendo-label>
                          <span>
                            - {{ pageSize }} Of {{ sampleSecurityData.length }}
                          </span>
                          <kendo-dropdownlist
                            class="!t-w-[4rem] !t-border-[#707070]"
                            [data]="sizes"
                            [value]="pageSize"></kendo-dropdownlist>
                          per page
                          <div class="t-flex t-gap-2">
                            <button
                              kendoButton
                              #parentEl
                              *ngFor="let icon of svgIconForPageControls"
                              class="!t-p-[0.3rem]"
                              fillMode="outline"
                              size="none">
                              <span
                                [parentElement]="parentEl.element"
                                venioSvgLoader
                                hoverColor="#FFFFFF"
                                [svgUrl]="icon.iconPath"
                                height="0.8rem"
                                width="1rem"></span>
                            </button>
                          </div>
                        </ng-template>

                        <kendo-grid-column
                          field="CompanyName"
                          headerClass="t-text-primary"
                          title="Role"
                          [width]="500">
                        </kendo-grid-column>
                        <kendo-grid-column
                          field="ContactTitle"
                          headerClass="t-text-primary"
                          title="Permission">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <kendo-dropdownlist
                              [defaultItem]="defaultItemCategories"
                              [data]="['sample 1', 'sample 2', 'sample 3']"
                              textField="text"
                              valueField="value">
                            </kendo-dropdownlist>
                          </ng-template>
                        </kendo-grid-column>
                      </kendo-grid>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
          <kendo-tabstrip-tab title="Save a Dynamic Folder">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-mt-2 t-w-full t-flex-col">
                  <div class="t-flex t-flex-col t-w-full">
                    <div
                      class="t-flex t-mt-4 t-flex-wrap t-gap-3 v-custom-grey-bg">
                      <div
                        showHints="always"
                        class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
                        <kendo-label
                          for="DynamicName"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          Name <span class="t-text-error">*</span>
                        </kendo-label>

                        <kendo-textbox
                          placeholder="Input Value"
                          #DynamicName></kendo-textbox>
                      </div>
                    </div>

                    <div
                      class="t-flex t-pt-0 t-flex-wrap t-gap-3 v-custom-grey-bg">
                      <div
                        showHints="always"
                        class="t-flex t-basis-[100%] t-flex-col t-gap-1">
                        <kendo-label
                          for="dynamicNote"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          Note
                        </kendo-label>
                        <kendo-textarea
                          #dynamicNote
                          placeholder="Tell us a little bit about yourself..."
                          [rows]="3"
                          resizable="vertical"></kendo-textarea>
                      </div>
                    </div>

                    <div class="t-flex t-w-full t-mt-3 t-flex-col">
                      <kendo-expansionpanel>
                        <ng-template kendoExpansionPanelTitleDirective>
                          <div class="header-content">
                            <span>Advance Options</span>
                          </div>
                        </ng-template>

                        <div
                          class="t-flex t-mt-4 t-w-full t-flex-wrap t-gap-3 v-custom-grey-bg">
                          <div
                            showHints="always"
                            class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
                            <kendo-label
                              for="advanceContainer"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Container <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-dropdownlist
                              #advanceContainer
                              [defaultItem]="defaultItemCategories"
                              [data]="[]"
                              textField="text"
                              valueField="\Global">
                            </kendo-dropdownlist>
                          </div>
                        </div>

                        <div class="t-flex t-pt-0 t-w-full v-custom-grey-bg">
                          <div
                            showHints="always"
                            class="t-flex t-basis-[100%] t-flex-col t-gap-1">
                            <kendo-label
                              for="DynamicName"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Security <span class="t-text-error">*</span>
                            </kendo-label>

                            <div class="t-flex t-w-full">
                              <kendo-grid
                                filterable="menu"
                                [resizable]="true"
                                [data]="sampleSecurityData">
                                <kendo-grid-column
                                  field="CompanyName"
                                  headerClass="t-text-primary"
                                  title="Role"
                                  [width]="300">
                                </kendo-grid-column>
                                <kendo-grid-column
                                  field="ContactTitle"
                                  headerClass="t-text-primary"
                                  title="Permission">
                                  <ng-template
                                    kendoGridCellTemplate
                                    let-dataItem>
                                    <kendo-dropdownlist
                                      [defaultItem]="defaultItemCategories"
                                      [data]="[]"
                                      textField="text"
                                      valueField="value">
                                    </kendo-dropdownlist>
                                  </ng-template>
                                </kendo-grid-column>
                              </kendo-grid>
                            </div>
                          </div>
                        </div>
                      </kendo-expansionpanel>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>

          <kendo-tabstrip-tab title="Auto Folder">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-mt-2 t-w-full t-flex-col">
                  <div
                    class="t-flex t-flex-col t-mt-3 t-gap-3 v-custom-grey-bg">
                    <div
                      class="t-flex t-flex-1 t-flex-row-reverse t-justify-end t-gap-2">
                      <kendo-label
                        class="k-radio-label"
                        [for]="relativePath"
                        text="Relative File Path"></kendo-label>

                      <input
                        type="radio"
                        name="customField"
                        #relativePath
                        kendoRadioButton />
                    </div>

                    <div
                      class="t-flex t-flex-1 t-flex-row-reverse t-justify-end t-gap-2">
                      <kendo-label
                        class="k-radio-label"
                        [for]="customField"
                        text="Custom Field"></kendo-label>

                      <input
                        type="radio"
                        name="customField"
                        #customField
                        kendoRadioButton />
                    </div>

                    <div
                      class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
                      <label class="k-checkbox-label" for="custodian"
                        >Create Folder for Each Custodian</label
                      >
                      <input
                        type="checkbox"
                        id="custodian"
                        [size]="'small'"
                        kendoCheckBox />
                    </div>

                    <div
                      class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-items-center">
                      <label class="k-checkbox-label" for="media"
                        >Create Folder for Each Media</label
                      >
                      <input
                        type="checkbox"
                        id="media"
                        [size]="'small'"
                        kendoCheckBox />
                    </div>
                  </div>

                  <div
                    class="t-flex t-flex-col t-w-full t-gap-2 t-mt-0 v-custom-grey-bg">
                    <div class="t-text-xs t-pl-1 t-uppercase t-tracking-widest">
                      Security
                    </div>

                    <kendo-grid
                      class="!t-max-w-[100%] !t-w-full"
                      filterable="menu"
                      [data]="sampleSecurityData"
                      [resizable]="true">
                      <kendo-grid-column
                        field="CompanyName"
                        title="Role"
                        [width]="400"
                        headerClass="t-text-primary">
                      </kendo-grid-column>
                      <kendo-grid-column
                        field="ContactTitle"
                        title="Permission"
                        headerClass="t-text-primary">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <kendo-dropdownlist
                            [defaultItem]="defaultItemCategories"
                            [data]="[]"
                            textField="text"
                            valueField="value">
                          </kendo-dropdownlist>
                        </ng-template>
                      </kendo-grid-column>
                    </kendo-grid>
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
        </kendo-tabstrip>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('yes')"
        class="v-custom-secondary-button"
        fillMode="outline"
        themeColor="secondary">
        {{ saveTitle }}
      </button>
      <button
        kendoButton
        (click)="close('no')"
        themeColor="dark"
        fillMode="outline">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
