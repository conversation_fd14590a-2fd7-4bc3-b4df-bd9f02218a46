import { Injectable, OnDestroy, inject } from '@angular/core'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  ReviewPanelViewState,
  ReviewPanelFacade,
  ReviewPanelModel,
  TagGroupInfo,
  UtilityPanel,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import {
  CompositeLayoutState,
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
  ViewFacade,
  CompositeLayoutFacade,
  ReviewSetStateService,
} from '@venio/data-access/review'

import { Subject } from 'rxjs'
import {
  WindowMessage,
  WindowMessageContent,
  WindowMessageType,
} from '../../../services/window.messenger.service'

@Injectable({ providedIn: 'root' })
export class UtilityPanelStoreUpdateService implements OnDestroy {
  private documentsFacade = inject(DocumentsFacade)

  private documentTagFacade = inject(DocumentTagFacade)

  private documentCodingFacade = inject(DocumentCodingFacade)

  private searchFacade = inject(SearchFacade)

  private searchResultFacade = inject(SearchResultFacade)

  private viewFacade = inject(ViewFacade)

  private utilityPanelFacade = inject(UtilityPanelFacade)

  private fieldFacade = inject(FieldFacade)

  private reviewPanelFacade = inject(ReviewPanelFacade)

  private caseInfoFacade = inject(CaseInfoFacade)

  private startupsFacade = inject(StartupsFacade)

  private reviewPanelViewState = inject(ReviewPanelViewState)

  private reviewFacade = inject(ReviewFacade)

  private toDestroy$ = new Subject<void>()

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  private reviewSetState = inject(ReviewSetStateService)

  /**
   * Updates the store data based on the message received from the parent window.
   * @param {WindowMessage} message - The message received from the parent window.
   * @returns {void} This method does not return anything.
   */
  public setMessengerData(message: WindowMessage): void {
    const data = message.payload
    if (data.type === WindowMessageType.FILEID_CHANGED) {
      this.updateStoreDataForFileChanged(data)
    } else if (data.type === WindowMessageType.DATA_TRANSFER) {
      this.updateStoreDataForDataTransferChanged(data)
    } else if (data.type === WindowMessageType.DATA_UPDATE) {
      this.updateStoreDataForDataUpdate(data)
    } else {
      this.updateStoreData(data)
    }
  }

  /**
   * Updates store data on initial load in the new window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreData(data: WindowMessageContent): void {
    const {
      selectedDocuments,
      currentDocument,
      isBulkDocument,
      searchResponse,
      isBatchSelected,
      searchResults,
      documentPaging,
      userDefaultView,
      utilityPanelUIState,
      reviewPanelUIState,
      userSelectedLayout,
      selectedTagGroup,
      permittedFields,
      reviewViewType,
      reviewSetBasicInfo,
      reviewsetBatchInfo,
      batchdId,
    } = data.content
    if (!selectedDocuments) return

    this.layoutState.userSelectedLayout.set(userSelectedLayout)
    // Store update for review panel
    this.#updateReviewPanelUIState(reviewPanelUIState)

    // Store update for tag coding view
    this.documentsFacade.setSelectedDocuments(selectedDocuments)
    this.documentsFacade.setCurrentDocument(currentDocument)
    this.documentsFacade.setIsBatchSelection(isBulkDocument)
    this.documentsFacade.setIsBatchSelection(isBatchSelected)
    this.searchFacade.setSearchResponse(searchResponse)
    this.searchResultFacade.setSearchResults(searchResults)

    // Store update for tag coding filter values
    // No need to update if the values not exists

    this.#updateUtilityPanelUIState(utilityPanelUIState)

    // Store update for parent-child view
    this.documentsFacade.updateCurrentDocumentTablePageNumber(
      documentPaging.pageNumber
    )
    this.searchResultFacade.storeDocumentTablePaging(
      documentPaging.pageNumber,
      documentPaging.pageSize
    )
    this.viewFacade.setUserDefaultView(userDefaultView)

    if (reviewViewType) this.reviewFacade.setReviewViewType(reviewViewType)

    this.fieldFacade.setPermittedFields(permittedFields)

    // State update for tag group in document-tag
    if (selectedTagGroup) {
      this.#updateTagGroupState(selectedTagGroup)
    }

    //Setting Batch review information
    this.reviewSetState.reviewSetBasicInfo.set(reviewSetBasicInfo),
      this.reviewSetState.reviewsetBatchInfo.set(reviewsetBatchInfo),
      this.reviewSetState.batchId.set(batchdId)
  }

  /**
   * Updates store data parent document change in the new window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreDataForFileChanged(data: WindowMessageContent): void {
    const {
      fileId,
      currentPageNumber,
      searchResponse,
      searchResults,
      selectedDocumentPageNo,
      pageSize,
    } = data.content
    if (!fileId) return

    //added for review panel popout window to update the file id in the ui
    this.documentsFacade.setCurrentDocument(fileId)

    if (searchResponse) this.searchFacade.setSearchResponse(searchResponse)

    if (searchResults) {
      const isFileIdExists = searchResults.some(
        (result) => result.fileId === fileId
      )
      if (isFileIdExists)
        this.searchResultFacade.setSearchResults(searchResults)
      else {
        this.searchResultFacade.storeDocumentTablePaging(
          selectedDocumentPageNo,
          pageSize
        )
        this.searchResultFacade.fetchDocumentTablePage()
      }
    }

    this.documentsFacade.setSelectedDocuments([fileId])

    if (currentPageNumber)
      this.documentsFacade.updateCurrentDocumentTablePageNumber(
        currentPageNumber
      )
  }

  /**
   * Updates store data parent document change in the parent window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreDataForDataTransferChanged(
    data: WindowMessageContent
  ): void {
    const {
      searchResponse,
      searchResults,
      projectGroups,
      userRights,
      permittedFields,
      userSelectedLayout,
    } = data.content

    //added for review panel popout window to update the search response and search results in the ui
    if (searchResponse) this.searchFacade.setSearchResponse(searchResponse)

    if (searchResults) this.searchResultFacade.setSearchResults(searchResults)

    if (projectGroups) this.startupsFacade.setProjectGroups(projectGroups)

    if (userRights) this.startupsFacade.setUserRights(userRights)

    if (permittedFields) this.fieldFacade.setPermittedFields(permittedFields)

    if (userSelectedLayout) {
      this.layoutState.userSelectedLayout.set(userSelectedLayout)
      this.layoutFacade.notifyLoadLayout.next()
    }
  }

  /**
   * Updates store data when project changed in the parent window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreDataForProjectChanged(data: WindowMessageContent): void {
    const { fileId, projectId } = data.content
    this.documentTagFacade.fetchProjectTags(projectId, false, false)
    this.documentsFacade.setSelectedDocuments([fileId])
    this.documentsFacade.setCurrentDocument(fileId)
  }

  public resetStoreDataForRouteChanged(): void {
    this.documentsFacade.resetDocumentState([
      'selectedDocuments',
      'currentDocument',
    ])

    this.documentsFacade.setSelectedDocuments([-1])
    this.documentsFacade.setCurrentDocument(-1)
    this.caseInfoFacade.fetchProjectList()
  }

  /**
   * Updates store data to maintain UI state in the parent window after closing the new window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreDataFromParentWindow(data: WindowMessageContent): void {
    const {
      utilityPanel,
      reviewPanel,
      selectedTagGroup,
      currentDocumentSearchScope,
      searchResultEntities,
    } = data.content

    this.#updateUtilityPanelUIState(utilityPanel)
    this.#updateReviewPanelUIState(reviewPanel)

    if (currentDocumentSearchScope)
      this.searchResultFacade.setDocumentExistsInSearchScope(
        currentDocumentSearchScope
      )
    this.searchResultFacade.updateManySearchResult(
      Object.values(searchResultEntities)
    )
    this.#updateTagGroupState(selectedTagGroup)
    this.layoutState.userSelectedLayout.set(data.content.userSelectedLayout)
  }

  /**
   * Updates store data to maintain the utililty panel UI state.
   * @param {UtilityPanel} utilityPanel - The data containing information to update the store.
   * @returns {void}
   */
  #updateUtilityPanelUIState(utilityPanel: UtilityPanel): void {
    const {
      expandedStatus,
      visibilityStatus,
      panelSortOrder,
      visibleTags,
      expandedTagIds,
      filterDocumentTags,
      visibleCodingFields,
      panelFilterEvent,
      visibleMetaDataFields,
    } = utilityPanel
    if (panelSortOrder?.length > 0) {
      this.utilityPanelFacade.setPanelSortOrder(panelSortOrder)
    }

    if (expandedStatus) {
      this.utilityPanelFacade.setExpandedItems(expandedStatus)
    }
    if (visibilityStatus) {
      this.utilityPanelFacade.setVisibilityItems(visibilityStatus)
    }

    if (filterDocumentTags?.length > 0) {
      this.documentTagFacade.filterDocumentTags(filterDocumentTags)
    }

    this.documentCodingFacade.updatVisibleCodingFields(visibleCodingFields)

    this.documentTagFacade.updatVisibleTagFields(visibleTags)

    if (expandedTagIds?.length > 0) {
      this.documentTagFacade.setExpandedTagIds(expandedTagIds)
    }

    if (panelFilterEvent) {
      this.utilityPanelFacade.triggerPanelFilterActionEvent(panelFilterEvent)
    }

    if (visibleMetaDataFields) {
      this.utilityPanelFacade.setVisibleMetaDataFields(visibleMetaDataFields)
    }
  }

  #updateReviewPanelUIState(reviewPanelUIState: ReviewPanelModel): void {
    if (reviewPanelUIState) {
      this.reviewPanelFacade.setSimilarityScore(
        reviewPanelUIState.similarityScore
      )
      this.reviewPanelFacade.setSimilaritySearchScopeType(
        reviewPanelUIState.similaritySearchScopeType
      )
    }
  }

  #updateTagGroupState(selectedTagGroup: TagGroupInfo): void {
    if (selectedTagGroup) {
      this.reviewPanelViewState.setSelectedTagGroup(selectedTagGroup)
    }
  }

  /**
   * Updates store data parent document update in the parent window.
   * @param {WindowMessageContent} data - The data containing information to update the store.
   * @returns {void}
   */
  public updateStoreDataForDataUpdate(data: WindowMessageContent): void {
    const { projectId, isTagupdate, isCodingUpdate } = data.content

    //added for review panel popout window to update the search response and search results in the ui
    if (isTagupdate) this.documentTagFacade.setDocumentTagUpdated(isTagupdate)

    if (isCodingUpdate)
      this.documentCodingFacade.setDocumentCodingUpdated(isCodingUpdate)

    if (isCodingUpdate) this.fieldFacade.fetchAllCustomFields(projectId, true)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
