<div class="relevance-container">
  <div class="relevance-header">
    <h3>Document Relevance</h3>
  </div>
  <div class="relevance-content">
    <div class="chart-container">
      <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"></plotly-plot>
    </div>
    <div class="legend-container">
      <div class="legend-item" *ngFor="let label of labels; let i = index">
        <div class="legend-color" [style.background-color]="getColor(i)"></div>
        <span class="legend-text">{{ label }}</span>
      </div>
    </div>
  </div>
</div>
