import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, distinctUntilChanged, filter, takeUntil } from 'rxjs'
import { DocumentsFacade, TagSummaryModel } from '@venio/data-access/review'

@Component({
  selector: 'venio-tag-summary',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tag-summary.component.html',
  styleUrl: './tag-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagSummaryComponent implements OnInit, OnDestroy {
  public tagSummary: TagSummaryModel

  private toDestroy$: Subject<void> = new Subject<void>()

  constructor(
    private documentsFacade: DocumentsFacade,
    private changeDetectorRef: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.#fetchTagSummary()
    this.#selectTagSummary()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetTagSumState()
  }

  #resetTagSumState(): void {
    this.documentsFacade.resetDocumentState([
      'tagSummary',
      'tagSummaryErrorResponse',
    ])
  }

  #fetchTagSummary(): void {
    this.documentsFacade.fetchTagSummary()
  }

  #selectTagSummary(): void {
    this.documentsFacade.getTagSummary$
      .pipe(
        filter((tagSummary) => !!tagSummary),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((tagSummary) => {
        this.tagSummary = tagSummary
        this.changeDetectorRef.markForCheck()
      })
  }
}
