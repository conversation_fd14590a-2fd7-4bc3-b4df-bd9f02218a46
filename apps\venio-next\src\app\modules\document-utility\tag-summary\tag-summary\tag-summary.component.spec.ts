import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagSummaryComponent } from './tag-summary.component'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagSummaryComponent', () => {
  let component: TagSummaryComponent
  let fixture: ComponentFixture<TagSummaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagSummaryComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagSummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
