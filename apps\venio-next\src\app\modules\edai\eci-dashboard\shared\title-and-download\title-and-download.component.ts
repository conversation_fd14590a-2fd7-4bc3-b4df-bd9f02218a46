import { Component, Input } from '@angular/core';
import { importIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons';

@Component({
  selector: 'venio-eci-title-and-download',
  standalone: true,
  imports: [KENDO_BUTTONS],
  templateUrl: './title-and-download.component.html',
  styleUrl: './title-and-download.component.scss'
})
export class EciTitleAndDownloadComponent {
  @Input() title: string = '';
  @Input() iconButton: string = '';
  public svgDownload: SVGIcon = importIcon;

  downloadCSV() {
    // Implement your CSV download logic here
    // For demo:
    console.log('Download CSV clicked!');
  }
}
