import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ChartsModule } from '@progress/kendo-angular-charts'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { filter } from 'rxjs'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { SUMMARY_CHART_OPTIONS } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-summary-chart',
  standalone: true,
  imports: [CommonModule, ChartsModule, SkeletonComponent],
  templateUrl: './summary-chart.component.html',
  styleUrl: './summary-chart.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SummaryChartComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  public readonly SUMMARY_CHART_OPTIONS = SUMMARY_CHART_OPTIONS

  public isReviewSetBatchDashboardLoading = toSignal(
    this.reviewSetFacade.selectIsReviewSetBatchSummaryLoading$,
    { initialValue: true }
  )

  /** Signal for review set summary */
  private readonly reviewSetSummaryDetail = toSignal(
    this.reviewSetFacade.selectReviewSetBatchSummary$.pipe(
      filter((e) => typeof e !== 'undefined')
    )
  )

  public readonly hasReviewSetBatch = computed(
    () => this.reviewSetSummaryDetail()?.batchCount > 0
  )

  /** The pie chart data for review set batch count information */
  public readonly pieChartInfo = computed(() => [
    {
      status: 'Completed',
      count: this.reviewSetSummaryDetail()?.documentReviewedCount || 0,
      color: '#BEF8BA', // Light green for Completed
    },
    {
      status: 'Not Started',
      count:
        this.reviewSetSummaryDetail()?.documentCount -
          this.reviewSetSummaryDetail()?.documentReviewedCount || 0,
      color: '#718792', // Dark gray for Not Started
    },
  ])
}
