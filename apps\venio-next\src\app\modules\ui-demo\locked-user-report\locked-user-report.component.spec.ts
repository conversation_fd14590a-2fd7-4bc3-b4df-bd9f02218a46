import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LockedUserReportComponent } from './locked-user-report.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LockedUserReportComponent', () => {
  let component: LockedUserReportComponent
  let fixture: ComponentFixture<LockedUserReportComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LockedUserReportComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(LockedUserReportComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
