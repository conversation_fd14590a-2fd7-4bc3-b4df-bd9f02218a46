<div
  class="t-flex t-border-y-[1px] t-border-[#cccccc] t-p-4 t-justify-between t-items-center t-mt-4 t-px-6">
  <span class="t-font-semibold">Status</span>

  <button
    kendoButton
    class="v-custom-secondary-button t-font-semibold"
    themeColor="secondary"
    fillMode="outline"
    data-qa="reprocess-button">
    REPROCESS
  </button>
</div>

<div
  class="t-bg-white t-border t-border-[#E0E0E0] t-rounded t-shadow-xl t-p-4 t-flex t-flex-col t-gap-3 t-w-[450px] t-m-5">
  <div>
    <h2 class="t-text-base t-font-semibold t-text-primary">
      Overall Processing
    </h2>
    <p class="t-text-sm t-mt-3 t-text-[#979797]">
      Uploaded by <span class="t-font-medium t-text-[#000000]">Admin</span>
    </p>
  </div>

  <div class="t-flex t-items-center t-gap-4 t-flex-col">
    <div class="t-block t-w-full">
      <kendo-progressbar
        [value]="progressValue1"
        [progressCssStyle]="getProgressBarStyle(progressValue1)"
        [min]="0"
        [max]="100"
        [label]="progressLabel"
        [animation]="{ duration: 3400 }"
        class="t-flex-1 t-w-full t-h-2 t-rounded-md t-h-3"></kendo-progressbar>
    </div>
    <div class="t-text-sm t-text-[#979797] t-flex t-justify-between t-w-full">
      <span
        >Date
        <span class="t-text-[#000000] t-font-medium">12 10 2024</span></span
      >
      <span
        >Last updated
        <span class="t-text-[#000000] t-font-medium">Today</span></span
      >
    </div>
  </div>
</div>

<div class="t-p-6">
  <div class="t-mb-4 t-flex t-items-center t-gap-4">
    <h1 class="t-text-lg t-font-semibold">Processing Details</h1>
    <div class="t-flex t-items-center t-gap-4 t-text-xs t-font-medium">
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#9BD2A7]"></span> COMPLETED
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#FFBC3E]"></span> INPROGRESS
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#EDEBE9]"></span> NOT
        STARTED
      </div>
    </div>
  </div>

  <div
    class="t-bg-white t-border t-border-[#E0E0E0] t-rounded t-shadow-xl t-p-4 t-flex t-flex-col t-gap-4 t-w-[450px]">
    <div>
      <h2 class="t-text-base t-font-semibold t-text-primary">Source Two</h2>
      <p class="t-text-sm t-mt-3 t-text-[#979797]">
        Custodian Name
        <span class="t-font-medium t-text-[#000000]">William</span>
      </p>
    </div>

    <div class="t-flex t-items-center t-gap-4 t-flex-col">
      <div class="t-block t-w-full">
        <kendo-progressbar
          [value]="progressValue2"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [min]="0"
          [max]="100"
          [label]="{
        visible: false,
      }"
          [animation]="{ duration: 3400 }"
          class="t-flex-1 t-w-full t-h-2 t-rounded-md t-h-3"></kendo-progressbar>
      </div>
      <div class="t-text-sm t-text-[#979797] t-flex t-justify-between t-w-full">
        <span
          >Date
          <span class="t-text-[#000000] t-font-medium">12 10 2024</span></span
        >
        <span
          >Last updated
          <span class="t-text-[#000000] t-font-medium">Today</span></span
        >
      </div>
    </div>

    <div class="t-flex t-justify-end t-w-full">
      <button
        kendoButton
        class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
        rounded="full"
        fillMode="clear"
        title="View Detail"
        [ngClass]="{ 't-rotate-180 t-ease-out': !showCharts }"
        (click)="this.showCharts = !this.showCharts">
        <span
          venioSvgLoader
          class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
          svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
      </button>
    </div>

    <div
      [ngClass]="{
        't-h-0 t-opacity-0': !showCharts,
        't-h-[140px] t-opacity-100': showCharts
      }"
      class="t-overflow-hidden t-transition-all t-duration-500">
      <ng-container *ngIf="showCharts">
        <div class="t-flex t-justify-around">
          <div
            *ngFor="let progress of progressData"
            class="t-flex t-flex-col t-items-center">
            <!-- DO NOT REPLACE THE INLINE STYLE WITH TAILWIND IT WONT WORK -->
            <kendo-circularprogressbar
              style="width: 88px; height: 88px"
              [value]="progress.value"
              [progressColor]="colors"
              [animation]="true">
              <ng-template
                kendoCircularProgressbarCenterTemplate
                let-color="color"
                let-value="value">
                <span class="t-text-sm t-font-medium">{{ value }}%</span>
              </ng-template>
            </kendo-circularprogressbar>
            <span class="t-text-sm t-mt-2 t-font-medium t-w-24 t-text-center">
              {{ progress.label }}
            </span>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>
