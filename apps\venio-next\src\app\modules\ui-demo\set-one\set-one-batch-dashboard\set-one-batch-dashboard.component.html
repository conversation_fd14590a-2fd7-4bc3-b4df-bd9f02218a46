<div class="t-flex t-bg-[#FBFBFB] t-p-4">
  <div class="t-flex t-flex-col t-w-[350px]">
    <div class="t-text-primary t-text-lg t-font-semibold">Summary</div>

    <div class="t-w-full t-flex-col t-gap-8">
      <div class="t-items-center t-w-full">
        <kendo-chart
          style="height: 300px"
          [chartArea]="{ background: 'transparent' }"
          class="t-bg-[#FBFBFB] t-border-0 t-w-full">
          <kendo-chart-series>
            <kendo-chart-series-item
              type="pie"
              [data]="pieData"
              field="value"
              categoryField="category"
              [colorField]="'color'"
              [labels]="{
                visible: true,
                content: labelContent,
                position: 'center',
                background: 'transparent'
              }">
            </kendo-chart-series-item>
          </kendo-chart-series>

          <kendo-chart-legend
            position="bottom"
            orientation="horizontal"
            [labels]="{
              color: '#000',
              font: '14px Arial'
            }"
            [markers]="{
                      type: 'square',
                    }">
          </kendo-chart-legend>
        </kendo-chart>
      </div>

      <div class="t-w-full">
        <div class="t-p-4 t-space-y-4">
          <div
            class="t-bg-white t-shadow-xl t-mb-8 t-border-[1px] t-border-[#E0E0E0] t-rounded-md t-p-4 t-w-full t-max-w-sm t-mx-auto">
            <div class="t-flex t-justify-between t-items-center t-mb-3">
              <div class="t-flex t-items-center">
                <span
                  class="t-inline-block t-w-3 t-h-3 t-bg-[#9BD2A7] t-rounded-full t-mr-2"></span>
                <span class="t-text-[#393939] t-text-sm t-font-medium"
                  >Online Reviewer</span
                >
              </div>
              <div class="t-flex t-items-center">
                <span
                  class="t-inline-block t-w-3 t-h-3 t-bg-[#A09F9F] t-rounded-full t-mr-2"></span>
                <span class="t-text-[#393939] t-text-sm t-font-medium"
                  >Total Reviewer</span
                >
              </div>
            </div>
            <div class="t-flex t-justify-around t-items-center">
              <div class="t-text-4xl t-font-bold t-text-[#9BD2A7]">5</div>
              <div class="t-text-4xl t-font-bold t-text-[#A09F9F]">9</div>
            </div>
          </div>
          <div
            class="t-bg-[#F5F7F0] t-rounded-md t-shadow-md t-p-4 t-w-full t-max-w-sm t-mx-auto">
            <div class="t-grid t-grid-cols-2 t-gap-y-4 t-items-center">
              <div class="t-flex t-items-center t-gap-x-3">
                <div
                  class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
                  <span
                    class="t-w-full"
                    venioSvgLoader
                    svgUrl="assets/svg/icon-summary-set-one-cloud.svg"
                    height="28px"
                    width="28px">
                  </span>
                </div>
                <span class="t-font-medium">Per Day</span>
              </div>
              <div class="t-text-right t-font-medium t-text-lg">12010</div>
              <div class="t-flex t-items-center t-gap-x-3">
                <div
                  class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
                  <span
                    class="t-w-full"
                    venioSvgLoader
                    svgUrl="assets/svg/icon-summary-set-one-watch.svg"
                    height="28px"
                    width="28px">
                  </span>
                </div>
                <span class="t-font-medium">Per Hour</span>
              </div>
              <div class="t-text-right t-font-medium t-text-lg">3210</div>
              <div class="t-flex t-items-center t-gap-x-3">
                <div
                  class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
                  <span
                    class="t-w-full"
                    venioSvgLoader
                    svgUrl="assets/svg/icon-summary-set-one-hiererachy.svg"
                    height="28px"
                    width="28px">
                  </span>
                </div>
                <span class="t-font-medium">Per Reviewer</span>
              </div>
              <div class="t-text-right t-font-medium t-text-lg">3210</div>
            </div>
          </div>

          <div class="t-flex t-justify-between t-items-center">
            <span class="t-text-[#9F9F9F] t-text-sm">Completion Date</span>
            <span class="t-text-[#127628] t-text-sm t-font-semibold"
              >Jan 18th 2024</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="t-flex t-flex-1 t-border-l-[1px] t-border-[#E0E0E0] t-px-4 t-flex-col t-gap-8 t-pl-6 t-ml-6">
    <div class="t-flex t-gap-8">
      <div
        class="t-w-[1/4] t-h-auto t-bg-white t-shadow-[0_3px_10px_rgba(108,164,242,0.16)] t-rounded-lg t-py-2 t-px-4">
        <div class="t-w-full t-h-[260px]">
          <div class="t-flex t-items-center t-mb-3">
            <span
              class="t-inline-block t-w-3 t-h-3 t-bg-[#FFB300] t-rounded-full t-mr-2"></span>
            <span class="t-text-[#393939] t-text-sm t-font-medium"
              >Progress</span
            >
          </div>

          <kendo-chart
            class="t-shadow-none t-border-0 t-w-full v-custom-char-production-bar t-h-[100%]">
            <!-- Category Axis (Dates) -->
            <kendo-chart-category-axis>
              <kendo-chart-category-axis-item
                [categories]="categories"
                [majorGridLines]="{ visible: false }"
                [line]="{ visible: true }"
                [labels]="{
                  visible: true,
                  rotation: 'auto',
                  color: '#000000',
                  font: 'bold  10px Arial',
                  padding: { top: 10 }
                }">
              </kendo-chart-category-axis-item>
            </kendo-chart-category-axis>

            <!-- Value Axis -->
            <kendo-chart-value-axis>
              <kendo-chart-value-axis-item
                [min]="0"
                [max]="5000"
                [majorGridLines]="{ visible: false }"
                [minorGridLines]="{ visible: false }"
                [labels]="{
                  visible: true,
                  color: '#000000',
                  font: 'bold 10px Arial',
                  content: formatLabel
                }">
              </kendo-chart-value-axis-item>
            </kendo-chart-value-axis>

            <!-- Series -->
            <!-- to show titles and labes -->
            <!--
       [title]="item.date"
          [labels]="{
            visible: false,
            position: 'above',
            format: '{0}',
            background: 'transparent',
            font: 'bold 10px Arial, sans-serif',
            color: '#212121'
          }"
    -->
            <kendo-chart-series>
              <kendo-chart-series-item
                *ngFor="let item of chartDataMo"
                type="column"
                [data]="[item.value]"
                [color]="item.color"
                [gap]="0.5"
                [spacing]="0.2"
                [border]="{ width: 0, color: '#fff' }">
              </kendo-chart-series-item>
            </kendo-chart-series>
          </kendo-chart>
        </div>

        <!-- Footer - Date Label -->
        <!-- <div class="t-text-center t-text-sm t-font-medium">
          <div class="t-relative t-top-[-5px]">
            <span class="t-tracking-[1.5px] t-font-medium t-text-[10px]"
              >DATE</span
            >
          </div>
        </div> -->
      </div>

      <div
        class="t-w-[1/4] t-h-auto t-bg-white t-shadow-[0_3px_10px_rgba(108,164,242,0.16)] t-rounded-lg t-py-2 t-px-4">
        <div class="t-flex t-items-center t-mb-3">
          <span
            class="t-inline-block t-w-3 t-h-3 t-bg-[#9BD2A7] t-rounded-full t-mr-2"></span>
          <span class="t-text-[#393939] t-text-sm t-font-medium">Reviewer</span>
        </div>

        <div class="t-w-full t-h-[260px]">
          <kendo-chart
            class="t-w-full t-h-[260px] !t-border-none"
            [chartArea]="{ background: 'transparent', border: { width: 0 } }">
            <kendo-chart-legend
              position="top"
              [visible]="false"
              orientation="horizontal"></kendo-chart-legend>
            <kendo-chart-category-axis>
              <kendo-chart-category-axis-item
                [categories]="categories"
                [line]="{ visible: false }"
                [majorGridLines]="{ visible: false }"
                [minorGridLines]="{ visible: false }"
                [majorTicks]="{ visible: false }"
                [minorTicks]="{ visible: false }"
                [labels]="{
                  visible: true,
                  rotation: 0,
                  color: '#000000',
                  font: 'bold 10px Arial',
                  content: formatLabel
                }">
              </kendo-chart-category-axis-item>
            </kendo-chart-category-axis>
            <kendo-chart-value-axis>
              <kendo-chart-value-axis-item
                [majorGridLines]="{ visible: false }"
                [minorGridLines]="{ visible: false }"
                [majorTicks]="{ visible: false }"
                [minorTicks]="{ visible: false }"
                [labels]="{
                  format: 'n0',
                  visible: true,
                  color: '#000000',
                  font: 'bold 10px Arial',
                  content: formatLabel
                }">
              </kendo-chart-value-axis-item>
            </kendo-chart-value-axis>
            <kendo-chart-series>
              <kendo-chart-series-item
                type="column"
                name="Clothing"
                [stack]="true"
                [data]="clothingData"
                [color]="clothingColor">
              </kendo-chart-series-item>
              <kendo-chart-series-item
                type="column"
                name="Equipment"
                [stack]="true"
                [data]="equipmentData"
                [color]="equipmentColor">
              </kendo-chart-series-item>
              <kendo-chart-series-item
                type="column"
                name="Accessories"
                [stack]="true"
                [data]="accessoriesData"
                [color]="accessoriesColor">
              </kendo-chart-series-item>
            </kendo-chart-series>
          </kendo-chart>
        </div>

        <!-- Footer Details PLEASE NOTE ADJUST THE LOGIC OF THE CHART ACCORDING TO THE EXISTING APP -->
        <!-- <div class="t-text-center t-text-sm t-font-medium">
          <div class="t-relative t-top-[-5px]">
            <span class="t-tracking-[1.5px] t-font-medium t-text-[10px]"
              >Date
            </span>
          </div>
        </div> -->
      </div>
    </div>

    <div class="t-flex t-gap-8">
      <div
        class="t-w-[1/4] t-h-auto t-bg-white t-shadow-[0_3px_10px_rgba(108,164,242,0.16)] t-rounded-lg t-py-2 t-px-4">
        <div class="t-w-full t-h-[260px]">
          <div class="t-flex t-items-center t-justify-between t-mb-3 t-w-full">
            <div class="t-flex t-items-center t-w-full">
              <span
                class="t-inline-block t-w-3 t-h-3 t-bg-[#02A9D3] t-rounded-full t-mr-2"></span>
              <span class="t-text-[#393939] t-text-sm t-font-medium"
                >Tag Status</span
              >
            </div>

            <button
              kendoButton
              fillMode="clear"
              size="none"
              class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-leading-none t-overflow-hidden t-group">
              <span
                class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>

              <kendo-svgicon
                class="t-text-[#6C6C6C] group-hover:t-text-[#1EBADC]"
                [icon]="icons.eyeIcon"></kendo-svgicon>
            </button>
          </div>

          <kendo-chart
            class="t-shadown-none t-border-0 t-w-full v-custom-char-production-bar t-h-[210px]">
            <kendo-chart-category-axis>
              <kendo-chart-category-axis-item
                [categories]="['']"
                [majorGridLines]="{ visible: false }"
                [line]="{ visible: true }"
                [labels]="{ visible: false }">
              </kendo-chart-category-axis-item>
            </kendo-chart-category-axis>
            <kendo-chart-value-axis>
              <kendo-chart-value-axis-item
                [min]="0"
                [max]="7"
                [majorGridLines]="{ visible: false }"
                [minorGridLines]="{ visible: false }"
                [labels]="{
                  visible: false,
                  step: 1,
                  format: '{0}',
                  color: '#6b7280'
                }">
              </kendo-chart-value-axis-item>
            </kendo-chart-value-axis>

            <kendo-chart-series>
              <kendo-chart-series-item
                *ngFor="let item of chartData"
                type="bar"
                [data]="[item.value > 0 ? item.value : 0.01]"
                [color]="item.color"
                [gap]="14"
                [spacing]="3"
                [border]="{
                  width: 0,
                  color: '#fff',
                  dashType: 'solid'
                }"
                [title]="item.value"
                [labels]="{
                            visible: true,
                            position: 'left',
                            format: '{0}',
                            background: 'transparent',
                            font: 'bold 8px Arial, sans-serif',
                            color: '#212121',
                            margin: -15,
                          }">
              </kendo-chart-series-item>
            </kendo-chart-series>
          </kendo-chart>
        </div>
      </div>

      <div class="t-flex t-grow t-w-full t-flex-1">
        <div
          class="t-bg-white t-rounded-md t-shadow-md t-p-4 t-w-full t-max-w-lg t-flex-col t-gap-2">
          <div class="t-flex t-items-center t-w-full">
            <span
              class="t-inline-block t-w-3 t-h-3 t-bg-[#02A9D3] t-rounded-full t-mr-2"></span>
            <span class="t-text-[#393939] t-text-sm t-font-medium"
              >Tag Rate</span
            >
          </div>

          <kendo-grid
            [data]="gridData"
            class="t-border-none t-mt-4 t-border-1 t-border-t-[1px]"
            [resizable]="false"
            [sortable]="false">
            <kendo-grid-column
              field="tag"
              title="Tags"
              [width]="150"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate>
                <span class="t-font-bold t-text-indigo-700">Tags</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span class="t-text-gray-700">{{ dataItem.tag }}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column
              field="johnDoe"
              title="John Doe"
              [width]="100"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate>
                <span class="t-font-bold t-text-indigo-700">John Doe</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span class="t-text-gray-700">{{ dataItem.johnDoe }}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column
              field="melissa"
              title="Melissa"
              [width]="100"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate>
                <span class="t-font-bold t-text-indigo-700">Melissa</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span class="t-text-gray-700">{{ dataItem.melissa }}</span>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
        </div>
      </div>
    </div>
  </div>
</div>
