import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginResetPasswordComponent } from './login-reset-password.component'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { PLATFORM_ID } from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AuthFacade } from '@venio/data-access/auth'
import { of } from 'rxjs'

describe('LoginResetPasswordComponent', () => {
  let component: LoginResetPasswordComponent
  let fixture: ComponentFixture<LoginResetPasswordComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginResetPasswordComponent,

        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: AuthFacade,
          useValue: {
            fetchLoginSettings: jest.fn(),
            selectLoginSettingSuccess$: of(undefined),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginResetPasswordComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
