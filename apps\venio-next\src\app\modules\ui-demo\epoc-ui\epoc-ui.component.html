<div class="t-flex t-flex-col">
  <!-- Right Panel AI Relevance UI -->
  <div class="t-flex t-w-full">
    <div class="t-p-3 t-max-w-xl t-mx-auto">
      <h2 class="t-text-lg t-font-bold t-mb-3">AI Summary</h2>
      <p class="t-text-[#707070] t-mb-5">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua.
      </p>

      <div
        *ngFor="let issue of aiData"
        class="t-mb-5 t-rounded-md t-border-2 t-border-[#F3F2F3] t-border-dashed">
        <div class="t-flex t-items-center t-mb-4 t-gap-3">
          <div
            class="t-flex t-min-w-[62px] t-p-1 t-bg-[#1EBADC] t-rounded-md t-flex t-flex-col t-items-center t-justify-center t-text-white t-font-bold">
            {{ issue.issueNumber | number : '2.0' }}
            <div class="t-mt-[-2px]">Issue</div>
          </div>

          <div class="t-flex">
            <p class="t-text-[#707070]">{{ issue.issueTitle }}</p>
          </div>
        </div>

        <div class="t-w-full t-p-2">
          <div class="t-mb-2">
            <span class="t-font-semibold">Tag</span>
            <p class="t-text-[#707070]">{{ issue.tag }}</p>
          </div>

          <div>
            <p class="t-font-semibold">Explanation</p>
            <p class="t-text-[#707070]">{{ issue.explanation }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- End here -->

  <kendo-dialog
    *ngIf="opened"
    (close)="close('cancel')"
    [height]="'90vh'"
    [minWidth]="250"
    [maxWidth]="1600"
    [width]="'80%'">
    <kendo-dialog-titlebar>
      <div class="t-flex t-w-[65%] t-justify-between">
        <div class="t-block">{{ dialogTitle }}</div>
      </div>
    </kendo-dialog-titlebar>

    <kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
      <kendo-tabstrip-tab title="AI Relevance" [selected]="true">
        <ng-template kendoTabContent>
          <div class="t-flex t-gap-3">
            <div class="t-w-full t-mt-4">
              <div class="t-flex t-gap-3">
                <kendo-textbox
                  placeholder="Job Name"
                  class="t-w-56"></kendo-textbox>
                <kendo-multiselect
                  [data]="users"
                  textField="text"
                  valueField="value"
                  [valuePrimitive]="true"
                  [listHeight]="500"
                  [checkboxes]="true"
                  [autoClose]="false"
                  [tagMapper]="tagMapper"
                  [clearButton]="false"
                  placeholder="Relevance"
                  class="!t-w-56 v-custom-multiselect-auto-w">
                  <ng-template kendoSuffixTemplate>
                    <button
                      kendoButton
                      [svgIcon]="downIcon"
                      fillMode="link"
                      class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
                  </ng-template>
                  <ng-template kendoMultiSelectItemTemplate let-dataItem>
                    <div
                      kendoTooltip
                      title="{{ dataItem.text }}"
                      class="t-overflow-hidden t-text-ellipsis t-whitespace-nowrap t-px-2 t-py-1">
                      {{ dataItem.text }}
                    </div>
                  </ng-template>
                </kendo-multiselect>
              </div>

              <div
                class="t-flex t-font-semibold t-text-[#1EBADC] t-items-center t-mt-3">
                <kendo-svg-icon
                  [icon]="icons.plusIcon"
                  size="medium"
                  class="t-w-[10px] t-w-[10px] t-flex t-items-center t-bg-[#1EBADC] t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>
                Issue
              </div>

              <div
                class="t-flex t-flex-col t-gap-3 t-mt-2 t-overflow-y-auto"
                venioDynamicHeight
                [isKendoDialog]="true"
                [useMaxHeight]="true">
                <div class="t-flex t-items-center">
                  <div
                    class="t-flex t-items-center t-border-1 t-overflow-hidden t-rounded-md t-flex-1">
                    <div
                      class="t-flex t-justify-center t-p-5 t-text-base t-text-white t-flex-col t-min-h-[75px] t-w-[90px] t-bg-[#1EBADC] t-text-center t-gap-0">
                      <div>01</div>
                      <div>Issue</div>
                    </div>

                    <div
                      class="t-flex t-flex-1 t-w-full t-ml-[-1px] t-mr-[1px]">
                      <kendo-textarea
                        placeholder="Type the issue here"
                        class="t-h-[88px] t-pt-1 t-border-[#BEBEBE] t-border-l-0 t-shadow-none t-flex-1 t-rounded-r-[0.3rem]"
                        resizable="none"
                        [rows]="5"></kendo-textarea>
                    </div>
                  </div>

                  <div
                    class="t-flex t-h-full t-p-3 t-text-[#1EBADC] t-items-center t-min-w-[52px]"></div>
                </div>

                <div class="t-flex t-items-center">
                  <div
                    class="t-flex t-items-center t-border-1 t-overflow-hidden t-rounded-md t-flex-1">
                    <div
                      class="t-flex t-justify-center t-p-5 t-text-base t-text-white t-flex-col t-min-h-[75px] t-w-[90px] t-bg-[#1EBADC] t-text-center t-gap-0">
                      <div>02</div>
                      <div>Issue</div>
                    </div>

                    <div
                      class="t-flex t-flex-1 t-w-full t-m-[1px] t-ml-[-1px] t-mr-[1px]">
                      <kendo-textarea
                        placeholder="Type the issue here"
                        class="t-h-[88px] t-pt-1 t-border-[#BEBEBE] t-border-l-0 t-shadow-none t-flex-1 t-rounded-r-[0.3rem]"
                        resizable="none"
                        [rows]="5"></kendo-textarea>
                    </div>
                  </div>

                  <div
                    class="t-flex t-h-full t-p-3 t-text-[#1EBADC] t-items-center t-min-w-[52px] t-gap-2 t-flex-col">
                    <kendo-svg-icon
                      [icon]="icons.plusIcon"
                      kendoTooltip
                      title="Add New Issue"
                      size="medium"
                      class="t-cursor-pointer t-w-[10px] t-flex t-items-center t-bg-[#1EBADC] t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>

                    <kendo-svg-icon
                      kendoTooltip
                      [icon]="icons.minusIcon"
                      title="Remove Issue"
                      size="medium"
                      class="t-w-[10px] t-cursor-pointer t-flex t-items-center t-bg-error t-rounded-full t-p-[0.1rem] t-mr-2 t-text-white"></kendo-svg-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="Status">
        <ng-template kendoTabContent>
          <div class="t-flex t-w-full t-mt-3">
            <div class="t-flex t-flex-col t-gap-4 t-w-full">
              <div
                class="t-flex t-gap-3 t-w-full t-justify-between t-items-center">
                <div class="t-flex t-gap-3">
                  <kendo-multiselect
                    [data]="users"
                    textField="text"
                    valueField="value"
                    [valuePrimitive]="true"
                    [listHeight]="500"
                    [checkboxes]="true"
                    [autoClose]="false"
                    [tagMapper]="tagMapper"
                    [clearButton]="false"
                    placeholder="All users"
                    class="!t-w-56 v-custom-multiselect-auto-w">
                    <ng-template kendoSuffixTemplate>
                      <button
                        kendoButton
                        [svgIcon]="downIcon"
                        fillMode="link"
                        class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
                    </ng-template>
                    <ng-template kendoMultiSelectItemTemplate let-dataItem>
                      <div
                        kendoTooltip
                        title="{{ dataItem.text }}"
                        class="t-overflow-hidden t-text-ellipsis t-whitespace-nowrap t-px-2 t-py-1">
                        {{ dataItem.text }}
                      </div>
                    </ng-template>
                  </kendo-multiselect>
                  <venio-report-date-picker
                    class="t-min-w-[150px] t-w-56 t-self-center" />
                  <kendo-dropdownlist
                    defaultItem="Filter By Status"
                    [data]="listItems"
                    [valuePrimitive]="true"
                    class="t-w-56">
                  </kendo-dropdownlist>
                </div>

                <!-- Note: As requested generate button wont be here for now-->
                <!-- <button
                  kendoButton
                  class="v-custom-secondary-button"
                  themeColor="secondary"
                  data-qa="redact-button">
                  GENERATE
                </button> -->
              </div>

              <div class="t-flex">
                <div class="t-flex t-mt-4 t-flex-col t-w-full">
                  <kendo-grid
                    class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
                    [kendoGridBinding]="gridDataHistory"
                    kendoGridSelectBy="hashNum"
                    venioDynamicHeight
                    [isKendoDialog]="true"
                    [sortable]="true"
                    [groupable]="false"
                    [reorderable]="true"
                    [resizable]="true"
                    [pageable]="{ type: 'numeric', position: 'top' }">
                    <ng-template kendoPagerTemplate>
                      <div class="t-flex t-gap-2"></div>
                      <kendo-grid-spacer></kendo-grid-spacer>

                      <venio-pagination
                        [disabled]="gridDataHistory?.length === 0"
                        [totalRecords]="gridDataHistory?.length"
                        [pageSize]="pageSize"
                        [showPageJumper]="false"
                        [showPageSize]="true"
                        [showRowNumberInputBox]="true"
                        class="t-px-5 t-block t-py-2">
                      </venio-pagination>
                    </ng-template>
                    <kendo-grid-column
                      field="hashNum"
                      [width]="50"
                      title="#"
                      headerClass="t-text-primary">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="#"
                          >#</span
                        >
                      </ng-template>
                    </kendo-grid-column>
                    <kendo-grid-column
                      field="jobName"
                      title="#"
                      headerClass="t-text-primary">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="#"
                          >Job Name</span
                        >
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="redactedBy"
                      title="Redacted By & On"
                      headerClass="t-text-primary">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Created By & On"
                          >Created By & On</span
                        >
                      </ng-template>
                      <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="t-flex t-gap-1 t-flex-col">
                          <div>{{ dataItem.redactedBy }}</div>
                          <div class="t-text-xs">
                            {{
                              dataItem.redactedOn
                                | date : 'MMM dd yyyy HH:mm:ss a'
                            }}
                          </div>
                        </div>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="numOfDoc"
                      title="Total Documents Submitted"
                      headerClass="t-text-primary">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Total Documents Submitted"
                          >Total Documents Submitted
                        </span>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="numOfDoc"
                      title="Total Documents Processed"
                      headerClass="t-text-primary">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Total Documents Processed "
                          >Total Documents Processed
                        </span>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="redactedStatus"
                      title="Status"
                      headerClass="t-text-primary">
                      <ng-template kendoGridCellTemplate let-dataItem>
                        <div
                          class="t-flex t-items-center t-gap-4 t-justify-between">
                          <div
                            class="t-font-medium"
                            [ngClass]="{
                              't-text-success':
                                dataItem.redactedStatus === 'COMPLETED',
                              't-text-error':
                                dataItem.redactedStatus === 'FAILED',
                              't-text-[#FFBB12]':
                                dataItem.redactedStatus === 'IN PROGRESS'
                            }">
                            <span
                              *ngIf="dataItem.redactedStatus === 'FAILED'"
                              >{{ dataItem.statusCount }}</span
                            >
                            {{ dataItem.redactedStatus }}
                          </div>

                          <div
                            class="t-flex t-items-center t-w-16 t-justify-end t-gap-2">
                            <span
                              *ngIf="dataItem.viewAction"
                              kendoPopoverAnchor
                              [popover]="myPopover"
                              class="hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-flex t-w-full">
                              <kendo-svg-icon
                                class="hover:t-text-[#1EBADC]"
                                [icon]="icons.eyeIcon"></kendo-svg-icon>
                            </span>

                            <span
                              *ngIf="dataItem.redactedStatus === 'IN PROGRESS'"
                              class="t-text-[#FFBB12] t-flex t-w-full">
                              40%
                            </span>
                          </div>
                        </div>
                      </ng-template>
                    </kendo-grid-column>
                  </kendo-grid>

                  <kendo-popover #myPopover position="left" [width]="670">
                    <ng-template kendoPopoverBodyTemplate>
                      <div class="t-flex t-mt-2">
                        <kendo-grid
                          [data]="gridData"
                          class="t-w-full"
                          [height]="350">
                          <kendo-grid-column
                            [width]="80"
                            headerClass="t-text-primary"
                            field="Issue"
                            title="Issue"></kendo-grid-column>
                          <kendo-grid-column
                            [width]="100"
                            headerClass="t-text-primary"
                            field="Relevant"
                            title="Relevant"></kendo-grid-column>
                          <kendo-grid-column
                            [width]="140"
                            headerClass="t-text-primary"
                            field="NonRelevant"
                            title="Non-Relevant"></kendo-grid-column>
                          <kendo-grid-column
                            [width]="200"
                            headerClass="t-text-primary"
                            field="NeedFurtherReview"
                            title="Need Further Review"></kendo-grid-column>
                          <kendo-grid-column
                            [width]="120"
                            headerClass="t-text-primary"
                            field="TechIssue"
                            title="Tech Issue"></kendo-grid-column>
                        </kendo-grid>
                      </div>
                    </ng-template>
                  </kendo-popover>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>

    <kendo-dialog-actions>
      <div class="t-flex t-gap-4 t-justify-end">
        <button
          kendoButton
          (click)="close('no')"
          class="v-custom-secondary-button"
          themeColor="secondary"
          data-qa="save"
          fillMode="outline">
          SUBMIT
        </button>
      </div>
    </kendo-dialog-actions>
  </kendo-dialog>
</div>
