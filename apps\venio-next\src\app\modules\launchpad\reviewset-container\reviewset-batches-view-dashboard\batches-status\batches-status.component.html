<kendo-grid
  [kendoGridBinding]="loadedReviewSetBatchSummaryDetail()"
  class="t-flex t-flex-col t-w-full t-border-none t-mt-4 t-border-1 t-border-t-[1px] t-h-[300px] t-relative t-overflow-y-auto"
  [loading]="isReviewSetBatchDashboardLoading()"
  [resizable]="false"
  [sortable]="false">
  <kendo-grid-column
    [resizable]="true"
    field="userFullName"
    title="Reviewer"
    headerClass="t-text-primary"
    [width]="180"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.userFullName }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [resizable]="true"
    field="batchName"
    title="Batch Name"
    headerClass="t-text-primary"
    [width]="150"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.batchName }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [resizable]="true"
    field="batchStatus"
    title="Batch Status"
    [width]="150"
    headerClass="t-text-primary"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.batchStatus }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [resizable]="true"
    field="batchTotalCount"
    title="Batch Total Count"
    headerClass="t-text-primary"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.batchTotalCount }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [resizable]="true"
    field="batchReviewedCount"
    title="Reviewed Document Count"
    headerClass="t-text-primary"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.batchReviewedCount }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    [resizable]="true"
    field="batchNotReviewedCount"
    title="Not Reviewed Document Count"
    headerClass="t-text-primary"
    [minResizableWidth]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-text-gray-700">
        <span>{{ dataItem.batchNotReviewedCount }}</span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoGridNoRecordsTemplate> No record found. </ng-template>
</kendo-grid>
