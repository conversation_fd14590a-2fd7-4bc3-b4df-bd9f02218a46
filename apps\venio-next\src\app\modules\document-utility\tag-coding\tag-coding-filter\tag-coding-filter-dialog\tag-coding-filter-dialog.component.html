<kendo-dialog-titlebar (close)="close()">
  <div>
    {{ dialogTitle }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-mt-3 t-gap-[3%]">
  <div
    class="t-flex t-w-[48.5%] t-flex-col t-gap-3 t-border t-border-t-0 t-border-b-0 t-border-l-0 !t-border-r-[#ebebeb] t-pr-[3%]">
    <div class="t-flex-none t-font-bold t-text-base">Tags</div>
    <ng-container
      *ngComponentOutlet="tagSearchComponent | async"></ng-container>

    <!-- <kendo-textbox
      class="!t-border-[#ccc]"
      placeholder="Search For Tags"
      [clearButton]="true"
      (venioAfterValueChanged)="onFilter($event)">
      <ng-template kendoTextBoxSuffixTemplate>
        <kendo-textbox-separator></kendo-textbox-separator>
        <button kendoButton fillMode="clear">
          <span
            venioSvgLoader
            svgUrl="assets/svg/icon-search.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </ng-template>
    </kendo-textbox> -->
    <div class="t-flex t-mt-1">
      <kendo-treelist
        #tagTreeList
        [ngClass]="'v-custom-tagtree t-h-[295px] t-w-full'"
        scrollable="virtual"
        [rowHeight]="30"
        [kendoTreeListFlatBinding]="tagNodes"
        idField="TreeKeyId"
        parentIdField="parentTreeKeyId"
        kendoTreeListExpandable
        [sortable]="true"
        [initiallyExpanded]="true"
        kendoTreeListSelectable
        [selectable]="settings"
        [(selectedItems)]="selectedTags"
        [loading]="isTagNodesLoading"
        (selectionChange)="onSelectionChange($event)">
        <kendo-treelist-checkbox-column
          headerClass="t-text-primary v-custom-tagheader"
          [expandable]="true"
          field="TagName"
          title="Name"
          [width]="350">
          <ng-template
            kendoTreeListHeaderTemplate
            let-column
            let-columnIndex="columnIndex">
            <div class="t-flex t-gap-4">
              <input
                type="checkbox"
                #tagCheckboxRef
                [indeterminate]="isIndeterminateAllTags"
                [checked]="isAllTagsSelected"
                (click)="onHeaderCheckBoxSelected()"
                kendoCheckBox />
              <kendo-label class="t-text-primary v-custom-tagheader"
                >Select all</kendo-label
              >
            </div>
          </ng-template>
          <ng-template kendoTreeListCellTemplate let-dataItem>
            <div class="t-inline-block t-absolute t-ml-7">
              {{ dataItem.TagName }}
            </div>
          </ng-template>
        </kendo-treelist-checkbox-column>
      </kendo-treelist>
    </div>
  </div>

  <div class="t-flex t-w-[48.5%] t-flex-col t-gap-3">
    <div class="t-flex-none t-font-bold t-text-base">Coding</div>
    <ng-container
      *ngComponentOutlet="codingSearchComponent | async"></ng-container>
    <div class="t-flex t-mt-1">
      <kendo-grid
        [ngClass]="'v-custom-tagtree t-h-[295px] t-w-full'"
        scrollable="virtual"
        [rowHeight]="30"
        [kendoGridBinding]="codingNodes"
        kendoGridSelectBy="customFieldInfoId"
        [(selectedKeys)]="selectedCoding"
        [loading]="isCodingNodesLoading"
        [sortable]="true"
        [selectable]="true">
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="50"></kendo-grid-checkbox-column>
        <kendo-grid-column
          field="displayName"
          title="Select all"
          [sortable]="true"
          headerClass="t-text-primary v-custom-tagheader"></kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="save()"
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      SAVE
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
