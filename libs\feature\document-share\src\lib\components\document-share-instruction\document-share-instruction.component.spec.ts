import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentShareInstructionComponent } from './document-share-instruction.component'
import { EditorModule } from '@progress/kendo-angular-editor'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentShareFacade } from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentShareInstructionComponent', () => {
  let component: DocumentShareInstructionComponent
  let fixture: ComponentFixture<DocumentShareInstructionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentShareInstructionComponent],
      imports: [EditorModule, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentShareFacade,
        DocumentShareFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentShareInstructionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
