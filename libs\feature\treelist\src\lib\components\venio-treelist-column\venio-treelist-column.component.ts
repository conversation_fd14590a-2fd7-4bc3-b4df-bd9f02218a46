import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Input,
  TemplateRef,
} from '@angular/core'
import { ColumnSortSettings } from '@progress/kendo-angular-treelist'

@Component({
  selector: 'venio-treelist-column',
  template: ``,
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VenioTreelistColumnComponent {
  @Input() public field: string

  @Input() public title: string

  @Input() public width: number

  @Input() public resizable: boolean

  @Input() public reorderable: boolean

  @Input() public autoSize: boolean

  @Input() public hidden: boolean

  @Input() public format: any

  @Input() public sortable: boolean | ColumnSortSettings

  @Input() public class:
    | string
    | string[]
    | Set<string>
    | { [key: string]: any }

  @ContentChild(TemplateRef, { static: false })
  public columnTemplate: TemplateRef<any>
}
