{"name": "document-share", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/document-share/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/document-share/ng-package.json", "tailwindConfig": "libs/feature/document-share/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/document-share/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/document-share/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/document-share/jest.config.ts"}}}}