import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'

@Component({
  selector: 'venio-search-history-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    FormsModule,
    InputsModule,
  ],
  templateUrl: './search-history-dialog.component.html',
  styleUrl: './search-history-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchHistoryDialogComponent implements OnInit {
  public dialogTitle = 'Search history'

  public opened = false

  public ngOnInit(): void {
    this.openDialog()
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }
}
