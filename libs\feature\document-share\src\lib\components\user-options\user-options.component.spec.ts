import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UserOptionsComponent } from './user-options.component'
import { DocumentShareFacade } from '@venio/data-access/review'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('UserOptionsComponent', () => {
  let component: UserOptionsComponent
  let fixture: ComponentFixture<UserOptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [UserOptionsComponent],
      imports: [
        GridModule,
        InputsModule,
        LabelModule,
        FormsModule,
        ReactiveFormsModule,
        ButtonsModule,
        SvgLoaderDirective,
        NoopAnimationsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        DocumentShareFacade,
        DocumentShareFormService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UserOptionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
