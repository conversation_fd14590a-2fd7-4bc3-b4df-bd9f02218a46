import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagStatusFilterComponent } from './tag-status-filter.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('TagStatusFilterComponent', () => {
  let component: TagStatusFilterComponent
  let fixture: ComponentFixture<TagStatusFilterComponent>

  const mockReviewSetFacade = {
    selectFilterFields$: of(undefined),
    isReviewSetTagStatusLoading$: of(false),
    selectReviewSetTagStatusSuccess$: of({ message: '', success: true }),
    setSelectedFilterFields: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagStatusFilterComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(TagStatusFilterComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
