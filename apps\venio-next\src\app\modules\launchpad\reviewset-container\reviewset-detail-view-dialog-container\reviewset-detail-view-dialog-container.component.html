@defer {
<venio-reviewset-detail-view-filter-toolbar
  [batchDeletionStatus]="batchDeletionStatus()"
  (searchClicked)="switchToTreeView()"
  [selectedReviewSetEntry]="selectedReviewSetEntry()" />
<div class="t-flex t-flex-col t-gap-4 t-flex-1 t-mt-4">
  <div
    class="t-flex t-gap-2 t-font-semibold t-text-base t-items-center t-border-[#ececec] t-border-b-[1px] t-border-l-0 t-border-r-0 t-pb-2"
    [ngClass]="{
      't-border-[#ececec] t-border-b-[1px] t-border-l-0 t-border-r-0 t-pb-2 ':
        isTreeView()
    }">
    Batch Summary
    <button
      kendoButton
      themeColor="secondary"
      class="v-custom-secondary-button t-p-0 t-w-[37px] hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
      fillMode="outline"
      *ngIf="isTreeView()"
      (click)="switchToBatchView()"
      #reloadView>
      <span
        venioSvgLoader
        [parentElement]="reloadView['element']"
        applyEffectsTo="both"
        hoverColor="#FFFFFF"
        color="#9AD3A6"
        svgUrl="assets/svg/icon-refresh-twoway.svg"
        height="1.1rem"
        width="1.1rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
    </button>
  </div>
  <venio-reviewset-detail-view-container
    (batchDeletionStatus)="batchDeletionStatusChanged($event)"
    [selectedViewType]="selectedViewType()"
    [selectedReviewSetEntry]="selectedReviewSetEntry()" />
</div>
}
