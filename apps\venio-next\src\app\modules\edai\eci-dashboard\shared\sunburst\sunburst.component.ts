import { Component, OnInit, Input } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import { calculateSunburstData } from './helpers';
import { mockDocumentTypes } from './mockData';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';
import { VerticalLegendsComponent } from '../vertical-legends/vertical-legends.component';
import { NgIf } from '@angular/common';
import { DataService } from '../data.service';
import { formatParentData, formatChildData } from '../data-table-for-focused-section/helpers';
import { CenterTextComponent } from '../center-text/center-text.component';

@Component({
  selector: 'venio-sunburst',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    VerticalLegendsComponent,
    NgIf,
    CenterTextComponent
  ],
  templateUrl: './sunburst.component.html',
  styleUrl: './sunburst.component.scss'
})
export class SunburstComponent implements OnInit {
  @Input() showLegend = true;
  @Input() chartTitle = 'Document Types';
  constructor(private dataService: DataService) { }
  public sortedDocuTypes = mockDocumentTypes.sort((a, b) => b.count - a.count);
  public parentLabels: any;
  public childrenLabels: any;
  public chartOneTotal!: number;
  public chartOneSubTotal!: number[];
  public chartOnePercents: number[] | undefined;
  public chartOneChildPercents: number[][] | undefined;
  public allCountsOne!: number[];
  public allValsOne!: number[];
  public formattedCounts!: string[];
  public config: any;
  public graph: any;
  public isParentData: boolean = true;
  public labels: string[] = [];
  public parents: string[] = ['Total'];

  ngOnInit() {
    const {
      parentLabels,
      childrenLabels,
      chartOneTotal,
      chartOneSubTotal,
      chartOnePercents,
      chartOneChildPercents,
      allCountsOne,
      allValsOne,
      formattedCounts
    } = calculateSunburstData(this.sortedDocuTypes);

    this.parentLabels = parentLabels;
    this.childrenLabels = childrenLabels;
    this.chartOneTotal = chartOneTotal;
    this.chartOneSubTotal = chartOneSubTotal;
    this.chartOnePercents = chartOnePercents;
    this.chartOneChildPercents = chartOneChildPercents;
    this.allCountsOne = allCountsOne;
    this.allValsOne = allValsOne;
    this.formattedCounts = formattedCounts;

    this.dataService.isParentData$.subscribe(val => {
      this.isParentData = val;
    });

    this.labels = ['Total'];
    this.parents = [''];

    const parentsLen = this.parentLabels.length;
    this.labels.push(...this.parentLabels);
    this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'));

    for (let i = 0; i < this.childrenLabels.length; i++) {
      this.labels.push(...this.childrenLabels[i]);
      const len = this.childrenLabels[i].length;
      this.parents.push(...Array.from({ length: len }, () => this.parentLabels[i]));
    }

    // structure for the sunburst chart for angular - adjusting from React version
    // const labels = ['Total', 'A', 'B', 'A1', 'A2', 'B1'];
    // const parents = ['', 'Total', 'Total', 'A', 'A', 'B'];
    // const values = [0, 0, 0, 30, 40, 50];
    const updatedVals = [...this.allValsOne];
    this.parents.forEach((parent, idx) => {
      if (parent === 'Total' || idx === 0) {
        updatedVals[idx] = 0;
      }
    });
    this.graph = {
      data: [
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: 0.5,
          textinfo: 'label'
        }
      ],
      layout: {
        autosize: true,
        automargin: true,
        branchvalues: 'remainder', // Required for donut
        margin: { t: 0, r: 0, b: 0, l: 0 }
      }
    };
    this.config = {
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      modeBarButtonsToRemove: [
        'toImage',
        'sendDataToCloud',
        'editInChartStudio',
        'zoom2d',
        'select2d',
        'pan2d',
        'lasso2d',
        'autoScale2d',
        'resetScale2d'
      ]
    };
  }
  onChartClick(event: any) {
    if (this.isParentData && event.points[0].parent === 'Total') {
      // child is clicked
      const childLabel = event.points[0].label;
      const childData = this.sortedDocuTypes.find(docuType => docuType.category === childLabel);
      console.log('Child data:', childData);
      if (!childData) return;
      const formatted = formatChildData(childData);
      this.dataService.setData(formatted);
      this.dataService.setIsParentData(false);

      this.dataService.setShowDetails(true);
    } else if (!this.isParentData && event.points[0].parent === 'Total') {
      console.log('Clicked on parent -Total');
      const formatted = formatParentData(this.sortedDocuTypes);
      this.dataService.setData(formatted);
      this.dataService.setIsParentData(true);
      this.dataService.setShowDetails(false);
    } else {
      console.log('grandchild clicked', event.points[0]);
      // open details data table
      this.dataService.setShowDetails(true);
    }
  }
}
