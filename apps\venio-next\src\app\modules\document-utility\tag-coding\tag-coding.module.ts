import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'

import { TagCodingRoutingModule } from './tag-coding-routing.module'
import { TagCodingContainerComponent } from './tag-coding-container/tag-coding-container.component'
import { DocumentTagModule } from './document-tag/document-tag.module'
import { DocumentCodingModule } from './document-coding/document-coding.module'
import { UserGroupRightCheckDirective } from '@venio/feature/shared/directives'
import { DataAccessDocumentUtilityModule } from '@venio/data-access/document-utility'
import { DialogModule } from '@progress/kendo-angular-dialog'

@NgModule({
  declarations: [TagCodingContainerComponent],
  imports: [
    CommonModule,
    TagCodingRoutingModule,
    DocumentTagModule,
    DocumentCodingModule,
    UserGroupRightCheckDirective,
    DataAccessDocumentUtilityModule,
    DialogModule,
  ],
})
export class TagCodingModule {}
