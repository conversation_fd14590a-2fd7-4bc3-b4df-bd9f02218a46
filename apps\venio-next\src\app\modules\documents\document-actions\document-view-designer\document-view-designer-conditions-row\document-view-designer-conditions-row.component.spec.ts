import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentViewDesignerConditionsRowComponent } from './document-view-designer-conditions-row.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentViewDesignerConditionsRowComponent', () => {
  let component: DocumentViewDesignerConditionsRowComponent
  let fixture: ComponentFixture<DocumentViewDesignerConditionsRowComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DocumentViewDesignerConditionsRowComponent,
        NoopAnimationsModule,
      ],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(
      DocumentViewDesignerConditionsRowComponent
    )
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
