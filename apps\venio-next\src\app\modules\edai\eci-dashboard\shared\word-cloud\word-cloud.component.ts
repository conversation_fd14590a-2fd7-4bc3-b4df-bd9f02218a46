import { Component } from '@angular/core';
import { TagCloudComponent, CloudOptions, CloudData } from 'angular-tag-cloud-module';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'app-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss'
})
export class WordCloudComponent {
  options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  };

  data: CloudData[] = [
    { text: 'adoption', weight: 15, color: '#8B5CF6' },
    { text: 'visitation', weight: 12, color: '#A855F7' },
    { text: 'brian', weight: 10, color: '#9333EA' },
    { text: 'dependency', weight: 14, color: '#7C3AED' },
    { text: 'inappropriate', weight: 8, color: '#6D28D9' },
    { text: 'elian', weight: 11, color: '#5B21B6' },
    { text: 'solicitor', weight: 9, color: '#553C9A' },
    { text: 'abandoned', weight: 7, color: '#4C1D95' },
    { text: 'gal', weight: 8, color: '#7C3AED' },
    { text: 'kearney', weight: 13, color: '#8B5CF6' },
    { text: 'foster', weight: 16, color: '#A855F7' },
    { text: 'registration', weight: 10, color: '#9333EA' },
    { text: 'nanny', weight: 9, color: '#7C3AED' },
    { text: 'paternal', weight: 8, color: '#6D28D9' },
    { text: 'deportation', weight: 11, color: '#5B21B6' },
    { text: 'homestead', weight: 7, color: '#553C9A' },
    { text: 'permanency', weight: 12, color: '#4C1D95' },
    { text: 'adam', weight: 6, color: '#7C3AED' },
    { text: 'felicia', weight: 8, color: '#8B5CF6' },
    { text: 'hu', weight: 5, color: '#A855F7' },
    { text: 'stefani', weight: 7, color: '#9333EA' },
    { text: 'cws', weight: 9, color: '#7C3AED' },
    { text: 'alva', weight: 6, color: '#6D28D9' },
    { text: 'malho', weight: 5, color: '#5B21B6' },
    { text: 'kathleen', weight: 8, color: '#553C9A' },
    { text: 'dcf', weight: 7, color: '#7C3AED' },
    { text: 'state', weight: 11, color: '#8B5CF6' },
    { text: 'fl', weight: 6, color: '#A855F7' },
    { text: 'support', weight: 9, color: '#9333EA' },
  ];
}
