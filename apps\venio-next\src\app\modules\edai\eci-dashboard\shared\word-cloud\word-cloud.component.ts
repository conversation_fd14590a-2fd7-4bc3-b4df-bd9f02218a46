import { Component } from '@angular/core';
import { TagCloudComponent, CloudOptions, CloudData } from 'angular-tag-cloud-module';
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component';

@Component({
  selector: 'app-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss'
})
export class WordCloudComponent {
  options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
    zoomOnHover: {
      scale: 1.3,
      transitionTime: 0.3,
      delay: 0.1
    },
    delay: 0.1
  };

  data: CloudData[] = [
    { text: 'adoption', weight: 15, color: '#8B5CF6', tooltip: 'Legal adoption proceedings' },
    { text: 'visitation', weight: 12, color: '#A855F7', tooltip: 'Visitation rights and schedules' },
    { text: 'brian', weight: 10, color: '#9333EA', tooltip: 'Individual case reference' },
    { text: 'dependency', weight: 14, color: '#7C3AED', tooltip: 'Child dependency cases' },
    { text: 'inappropriate', weight: 8, color: '#6D28D9', tooltip: 'Inappropriate conduct' },
    { text: 'elian', weight: 11, color: '#5B21B6', tooltip: 'Case reference - Elian' },
    { text: 'solicitor', weight: 9, color: '#553C9A', tooltip: 'Legal representation' },
    { text: 'abandoned', weight: 7, color: '#4C1D95', tooltip: 'Child abandonment cases' },
    { text: 'gal', weight: 8, color: '#7C3AED', tooltip: 'Guardian ad Litem' },
    { text: 'kearney', weight: 13, color: '#8B5CF6', tooltip: 'Case reference - Kearney' },
    { text: 'foster', weight: 16, color: '#A855F7', tooltip: 'Foster care placement' },
    { text: 'registration', weight: 10, color: '#9333EA', tooltip: 'Legal registration processes' },
    { text: 'nanny', weight: 9, color: '#7C3AED', tooltip: 'Childcare provider' },
    { text: 'paternal', weight: 8, color: '#6D28D9', tooltip: 'Paternal rights and responsibilities' },
    { text: 'deportation', weight: 11, color: '#5B21B6', tooltip: 'Immigration deportation' },
    { text: 'homestead', weight: 7, color: '#553C9A', tooltip: 'Property and housing' },
    { text: 'permanency', weight: 12, color: '#4C1D95', tooltip: 'Permanency planning' },
    { text: 'adam', weight: 6, color: '#7C3AED', tooltip: 'Individual case reference' },
    { text: 'felicia', weight: 8, color: '#8B5CF6', tooltip: 'Individual case reference' },
    { text: 'hu', weight: 5, color: '#A855F7', tooltip: 'Individual case reference' },
    { text: 'stefani', weight: 7, color: '#9333EA', tooltip: 'Individual case reference' },
    { text: 'cws', weight: 9, color: '#7C3AED', tooltip: 'Child Welfare Services' },
    { text: 'alva', weight: 6, color: '#6D28D9', tooltip: 'Individual case reference' },
    { text: 'malho', weight: 5, color: '#5B21B6', tooltip: 'Individual case reference' },
    { text: 'kathleen', weight: 8, color: '#553C9A', tooltip: 'Individual case reference' },
    { text: 'dcf', weight: 7, color: '#7C3AED', tooltip: 'Department of Children and Families' },
    { text: 'state', weight: 11, color: '#8B5CF6', tooltip: 'State government involvement' },
    { text: 'fl', weight: 6, color: '#A855F7', tooltip: 'Florida jurisdiction' },
    { text: 'support', weight: 9, color: '#9333EA', tooltip: 'Support services and payments' },
  ];
}
