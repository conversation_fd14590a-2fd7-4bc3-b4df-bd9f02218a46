import { Component } from '@angular/core';
import { TagCloudComponent, CloudOptions, CloudData } from 'angular-tag-cloud-module';

@Component({
  selector: 'venio-eci-word-cloud',
  standalone: true,
  imports: [TagCloudComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss'
})
export class EciWordCloudComponent {
  options: CloudOptions = {
    width: 1000,
    height: 400,
    overflow: false,
  };
  
  data: CloudData[] = [
    { text: 'visitation', weight: 8, color: '#6305FF' },
    { text: 'brian', weight: 10, color: '#0084FF' },
    { text: 'adoption', weight: 12, color: '#FF00FF' },
    { text: 'limberlake', weight: 11, color: '#1100FF' },
    { text: 'hibiscus', weight: 10, color: '#0FE5B7' },
    { text: 'dependency', weight: 9, color: '#00D0FF' },
    { text: 'old', weight: 8, color: '#6305FF' },
    { text: 'mental', weight: 10, color: '#0084FF' },
    { text: 'tamira', weight: 9, color: '#FF00FF' },
    { text: 'wenwood', weight: 8, color: '#1100FF' },
    { text: 'regier', weight: 7, color: '#0FE5B7' },
    { text: 'kearney', weight: 8, color: '#00D0FF' },
    { text: 'foster', weight: 10, color: '#6305FF' },
    { text: 'separation', weight: 9, color: '#0084FF' },
    { text: 'paternal', weight: 8, color: '#FF00FF' },
    { text: 'macdonald', weight: 7, color: '#1100FF' },
    { text: 'ashleigh', weight: 6, color: '#0FE5B7' },
    { text: 'commissioner', weight: 8, color: '#00D0FF' },
    { text: 'stefani', weight: 9, color: '#6305FF' },
    { text: 'parenting', weight: 8, color: '#0084FF' },
    { text: 'reunification', weight: 7, color: '#FF00FF' },
    { text: 'permanency', weight: 6, color: '#1100FF' },
    { text: 'custody', weight: 10, color: '#0FE5B7' },
    { text: 'court', weight: 9, color: '#00D0FF' },
    { text: 'child', weight: 7, color: '#6305FF' },
    { text: 'welfare', weight: 6, color: '#0084FF' },
    { text: 'family', weight: 8, color: '#FF00FF' },
    { text: 'services', weight: 9, color: '#1100FF' },
    { text: 'placement', weight: 8, color: '#0FE5B7' },
    { text: 'guardian', weight: 7, color: '#00D0FF' },
    { text: 'legal', weight: 6, color: '#6305FF' },
    { text: 'protection', weight: 10, color: '#0084FF' },
    { text: 'safety', weight: 9, color: '#FF00FF' },
    { text: 'investigation', weight: 7, color: '#1100FF' },
    { text: 'assessment', weight: 6, color: '#0FE5B7' },
  ];
}
