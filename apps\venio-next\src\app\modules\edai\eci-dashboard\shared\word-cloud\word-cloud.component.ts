import { Component } from '@angular/core';
import { TagCloudComponent, CloudOptions, CloudData } from 'angular-tag-cloud-module';
import { ButtonComponent } from '@progress/kendo-angular-buttons';

@Component({
  selector: 'venio-eci-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, ButtonComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss'
})
export class EciWordCloudComponent {
  options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  };

  data: CloudData[] = [
    { text: 'Weight-8-link-color', weight: 8, link: 'https://google.com', color: '#ffaaee' },
    { text: 'Weight-10-link', weight: 10, link: 'https://google.com', tooltip: 'display a tooltip' },
    { text: 'Climate', weight: 12, tooltip: 'Climate change' },
    { text: 'Sustainability', weight: 11, tooltip: 'Sustainability efforts' },
    { text: 'Recycling', weight: 10, tooltip: 'Recycling programs' },
    { text: 'Biodiversity', weight: 9, tooltip: 'Biodiversity protection' },
    { text: 'Ecosystem', weight: 8, tooltip: 'Ecosystem balance' },
    { text: 'Conservation', weight: 10, tooltip: 'Conservation of resources' },
    { text: 'Renewable', weight: 9, tooltip: 'Renewable energy' },
    { text: 'Solar', weight: 8, tooltip: 'Solar power' },
    { text: 'Wind', weight: 7, tooltip: 'Wind energy' },
    { text: 'Water', weight: 8, tooltip: 'Water conservation' },
    { text: 'Pollution', weight: 10, tooltip: 'Pollution reduction' },
    { text: 'Deforestation', weight: 9, tooltip: 'Deforestation issues' },
    { text: 'Wildlife', weight: 8, tooltip: 'Wildlife protection' },
    { text: 'Organic', weight: 7, tooltip: 'Organic farming' },
    { text: 'Compost', weight: 6, tooltip: 'Composting' },
    { text: 'Greenhouse', weight: 8, tooltip: 'Greenhouse gases' },
    { text: 'Carbon', weight: 9, tooltip: 'Carbon footprint' },
    { text: 'Oceans', weight: 8, tooltip: 'Ocean health' },
    { text: 'Plastic', weight: 7, tooltip: 'Plastic waste' },
    { text: 'Emissions', weight: 8, tooltip: 'Emissions control' },
    { text: 'Habitat', weight: 7, tooltip: 'Habitat restoration' },
    { text: 'Trees', weight: 6, tooltip: 'Tree planting' },
    { text: 'Energy', weight: 10, tooltip: 'Energy efficiency' },
    { text: 'Sustainable', weight: 9, tooltip: 'Sustainable living' },
    { text: 'Wildfires', weight: 7, tooltip: 'Wildfire prevention' },
    { text: 'Rainforest', weight: 8, tooltip: 'Rainforest protection' },
    { text: 'Air', weight: 7, tooltip: 'Air quality' },
    { text: 'Soil', weight: 6, tooltip: 'Soil health' },
    { text: 'Fossil', weight: 7, tooltip: 'Fossil fuels' },
    { text: 'Hydro', weight: 6, tooltip: 'Hydropower' },
  ];
}
