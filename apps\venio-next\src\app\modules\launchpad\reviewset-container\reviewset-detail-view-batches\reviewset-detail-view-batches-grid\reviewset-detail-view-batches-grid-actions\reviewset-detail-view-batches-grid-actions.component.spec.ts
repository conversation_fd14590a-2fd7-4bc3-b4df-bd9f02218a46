import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewBatchesGridActionsComponent } from './reviewset-detail-view-batches-grid-actions.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { RightModel, StartupsFacade } from '@venio/data-access/review'
import { of } from 'rxjs'
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { ReviewSetBatchModel } from '@venio/shared/models/interfaces'

describe('ReviewsetDetailViewBatchesGridActionsComponent', () => {
  let component: ReviewsetDetailViewBatchesGridActionsComponent
  let fixture: ComponentFixture<ReviewsetDetailViewBatchesGridActionsComponent>

  const mockRowDataItem: ReviewSetBatchModel = {
    batchId: 1,
    reviewSetId: 1,
    name: 'Batch 1',
    batchStatus: 'Completed',
    reviewer: 'Reviewer 1',
    totalFiles: 10,
    remainingFiles: 10,
    totalGeneratedHtmlFiles: 3,
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewBatchesGridActionsComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selecteSelectedBatchDetail$: of([]),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({ Invalid_Global_Right_List: {} } as RightModel),
            hasGroupRight$: jest.fn().mockReturnValue(of(true)),
          } satisfies Partial<StartupsFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      ReviewsetDetailViewBatchesGridActionsComponent
    )
    component = fixture.componentInstance
    fixture.componentRef.setInput('rowDataItem', mockRowDataItem)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
