<kendo-treelist
  class="v-custom-view-tree t-h-[calc(100vh_-_16rem)]"
  data-qa="selectedTree"
  idField="displayFieldName"
  kendoTreeListSelectable
  kendoTreeListExpandable
  [kendoTreeListFlatBinding]="fields()"
  [trackBy]="trackByFieldName"
  [navigatable]="true"
  [selectedItems]="selected()"
  [selectable]="{ mode: 'row', multiple: true, drag: false }"
  (selectedItemsChange)="selectionItemChanged($event)"
  [initiallyExpanded]="true"
  [filterable]="true"
  [sortable]="true">
  <kendo-treelist-checkbox-column [width]="25" [showSelectAll]="true">
    <ng-template kendoTreeListHeaderTemplate>
      <kendo-checkbox
        [disabled]="!fields()?.length"
        [checkedState]="allItemsSelected()"
        (checkedStateChange)="toggleSelectAll($event)"></kendo-checkbox>
    </ng-template>
  </kendo-treelist-checkbox-column>
  <kendo-treelist-column
    [expandable]="true"
    [sortable]="false"
    field="displayFieldName"
    [title]="title()">
    <ng-template kendoTreeListFilterCellTemplate let-filter let-column="column">
      <kendo-treelist-string-filter-cell
        [column]="column"
        [filter]="filter"
        [showOperators]="false">
      </kendo-treelist-string-filter-cell>
    </ng-template>
  </kendo-treelist-column>

  <!-- Custom No Records Template -->
  <ng-template kendoTreeListNoRecordsTemplate>
    <div
      class="t-w-full t-h-full t-grid t-place-content-center t-absolute t-uppercase t-text-[#979797]">
      No records available.
    </div>
  </ng-template>
</kendo-treelist>
