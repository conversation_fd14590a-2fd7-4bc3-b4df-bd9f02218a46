<div class="t-block t-w-full t-w-80">
  <!-- Near duplicate UI starts here-->
  <kendo-grid
    class="t-w-full"
    [data]="gridData"
    [resizable]="true"
    *ngIf="gridData.length">
    <kendo-grid-column
      field="details"
      title="Details"
      headerClass="t-text-primary"
      [width]="90"
      [minResizableWidth]="70">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip title="Details">Details</span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          kendoTooltip
          title="View"
          class="hover:t-cursor-pointer t-text-[#000000] t-flex t-w-full t-pl-3.5">
          <kendo-svg-icon [icon]="icons.eyeIcon"></kendo-svg-icon
        ></span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="internalFileId"
      title="Internal File ID"
      headerClass="t-text-primary"
      [width]="150"
      [minResizableWidth]="100">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip title="Internal File ID">Internal File ID</span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span kendoTooltip title="{{ dataItem.internalFileId }}">{{
          dataItem.internalFileId
        }}</span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="originalFileName"
      title="Original File Name"
      headerClass="t-text-primary"
      [width]="200"
      [minResizableWidth]="150">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip title="Original File Name">Original File Name</span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span kendoTooltip title="{{ dataItem.originalFileName }}">{{
          dataItem.originalFileName
        }}</span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="centroid"
      title="Centroid"
      headerClass="t-text-primary"
      [width]="100"
      [minResizableWidth]="80">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip title="Centroid">Centroid</span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          [ngClass]="{
            't-text-error': dataItem.centroid === 'No',
            't-text-secondary': dataItem.centroid === 'Yes'
          }"
          kendoTooltip
          title="{{ dataItem.centroid }}">
          {{ dataItem.centroid }}
        </span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="similarity"
      title="Similarity"
      headerClass="t-text-primary"
      format="{0:p0}"
      [width]="150"
      [minResizableWidth]="120">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip title="Similarity">Similarity</span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span kendoTooltip title="{{ dataItem.similarity | percent }}">{{
          dataItem.similarity | percent
        }}</span>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>

  <!-- No Record template -->
  <div
    class="t-grid t-h-28 t-w-full t-place-content-center"
    *ngIf="!gridData.length">
    <div class="t-text-center t-text-[#979797]">No records available.</div>
  </div>
</div>
