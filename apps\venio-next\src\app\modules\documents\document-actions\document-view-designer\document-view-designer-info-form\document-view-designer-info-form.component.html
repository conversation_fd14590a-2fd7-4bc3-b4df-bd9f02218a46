<kendo-tabstrip class="t-w-full t-h-full">
  <kendo-tabstrip-tab title="Information" [selected]="true">
    <ng-template kendoTabContent>
      <form
        class="t-flex t-flex-1 t-flex-wrap t-w-full t-gap-[3%] v-custom-grey-bg t-mt-3"
        [formGroup]="viewInfoFormGroup">
        <!--            <div class="t-flex t-flex-0 t-basis-[48.8%] t-gap-1 t-flex-col">-->
        <!--              <kendo-label-->
        <!--                for="systemFields"-->
        <!--                class="t-text-xs t-uppercase t-tracking-widest">-->
        <!--                System Fields <span class="t-text-error">*</span>-->
        <!--              </kendo-label>-->
        <!--              <kendo-dropdownlist-->
        <!--                #systemFields-->
        <!--                [defaultItem]="['Document']"-->
        <!--                [data]="[]"-->
        <!--                textField="text"-->
        <!--                valueField="value">-->
        <!--              </kendo-dropdownlist>-->
        <!--            </div>-->
        <div class="t-flex t-flex-0 t-flex-col t-gap-1 t-gap-x-1 t-w-[42%]">
          <kendo-label
            for="viewName"
            class="t-text-xs t-uppercase t-tracking-widest">
            NAME <span class="t-text-error">*</span>
          </kendo-label>

          <kendo-textbox
            formControlName="viewName"
            placeholder="View Name"></kendo-textbox>
        </div>
        <div class="t-flex t-flex-0 t-flex-col t-gap-1 t-w-[42%]">
          <kendo-label
            for="tagGroup"
            class="t-text-xs t-uppercase t-tracking-widest">
            OWNER <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-dropdownlist
            formControlName="viewUserIds"
            [filterable]="true"
            [virtual]="{ itemHeight: 28 }"
            [kendoDropDownFilter]="{
              caseSensitive: false,
              operator: 'contains'
            }"
            [loading]="isUserListLoading()"
            [data]="userList()"
            [valuePrimitive]="true"
            textField="userName"
            valueField="userId">
          </kendo-dropdownlist>
        </div>
        <div class="t-flex t-flex-0 t-flex-col t-gap-1 t-w-[10%]">
          <kendo-label class="t-text-xs t-uppercase t-tracking-widest">
          </kendo-label>
          <button
            kendoButton
            [disabled]="isMeSelected"
            class="t-w-full"
            title="Assign yourself as a view owner"
            (click)="selectCurrentUser()">
            ME
          </button>
        </div>
      </form>
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>
