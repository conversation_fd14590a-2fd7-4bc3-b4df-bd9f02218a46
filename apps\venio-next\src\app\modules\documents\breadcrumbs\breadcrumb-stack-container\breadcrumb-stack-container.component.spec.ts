import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbStackContainerComponent } from './breadcrumb-stack-container.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'
import { provideMockStore } from '@ngrx/store/testing'

describe('BreadcrumbStackContainerComponent', () => {
  let component: BreadcrumbStackContainerComponent
  let fixture: ComponentFixture<BreadcrumbStackContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbStackContainerComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbStackContainerComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
