import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetBatchesDashboardHeaderComponent } from './reviewset-batches-dashboard-header.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { ReportsFacade } from '@venio/data-access/reports'

describe('ReviewsetBatchesDashboardHeaderComponent', () => {
  let component: ReviewsetBatchesDashboardHeaderComponent
  let fixture: ComponentFixture<ReviewsetBatchesDashboardHeaderComponent>

  const mockReviewSetFacade = {
    selectReviewSetReviewersSuccess$: of(undefined),
    selectIsReviewSetBatchDashboardLoading$: of(false),
    fetchReviewSetReviewers: jest.fn(),
    updateReviewSetDashboardRequestInfo: jest.fn(),
    fetchReviewerDashboardData: jest.fn(),
  }

  const mockReportsFacade = {
    selectedSelectedReportType$: of(undefined),
    selectSelectedDateRange$: of(undefined),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetBatchesDashboardHeaderComponent],
      providers: [
        provideAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: ReviewSetFacade, useValue: mockReviewSetFacade },
        { provide: ReportsFacade, useValue: mockReportsFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetBatchesDashboardHeaderComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
