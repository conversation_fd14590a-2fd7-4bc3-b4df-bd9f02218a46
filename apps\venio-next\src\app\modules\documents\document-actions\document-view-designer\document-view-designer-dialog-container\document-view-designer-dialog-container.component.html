<div class="t-flex t-flex-wrap t-h-full t-w-full">
  <div class="t-w-[40%]">
    <ng-container *ngComponentOutlet="lazyInfoFormComponent | async" />
  </div>
  <div class="t-flex t-flex-1 t-pl-2 t-flex-grow t-basis-0">
    <kendo-tabstrip
      [keepTabContent]="true"
      class="t-flex t-flex-1 t-h-100 t-relative">
      <kendo-tabstrip-tab
        title="Fields"
        cssClass="t-relative t-h-auto"
        [selected]="true">
        <ng-template kendoTabContent>
          <ng-container *ngComponentOutlet="lazyFieldsComponent | async" />
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab
        title="Conditions"
        cssClass="t-relative t-h-auto"
        *ngIf="!reviewSetState.isBatchReview()">
        <ng-template kendoTabContent>
          <ng-container
            *ngComponentOutlet="lazyConditionsComponent | async; inputs: {
          conditionUiType: conditionUiType,
          }" />
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="Sort" *ngIf="!reviewSetState.isBatchReview()">
        <ng-template kendoTabContent>
          <ng-container *ngComponentOutlet="lazySortComponent | async" />
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>
  </div>
</div>
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      [disabled]="isAddOrUpdateViewLoading$ | async"
      kendoButton
      (click)="
        actionClick(
          isSaveAs ? commonActionTypes.SAVE_AS : commonActionTypes.SAVE
        )
      "
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      <kendo-loader
        *ngIf="isAddOrUpdateViewLoading$ | async"
        themeColor="success"
        size="small"
        type="infinite-spinner"></kendo-loader>
      {{ isSaveAs ? 'SAVE AS' : 'SAVE' }}
    </button>
    <button
      [disabled]="isAddOrUpdateViewLoading$ | async"
      kendoButton
      (click)="actionClick(commonActionTypes.CANCEL)"
      fillMode="outline"
      themeColor="dark">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
