const chalk = require('chalk')
const fs = require('fs').promises

const args = process.argv
  .slice(2)
  .map((arg) => arg.split('='))
  .reduce((args, [value, key]) => {
    value = value.replace(/--/g, '')
    args[value] = key
    return args
  }, {})

const appVersion = args.appVersion || ''
const buildNumber = args.buildNumber || ''

const featureFlagFilePath =
  'libs/shared/assets/src/files/json/app-settings.json'
const message =
  chalk.bgBlueBright(chalk.blue('NOTE: ')) +
  chalk.greenBright(
    `App version property has been updated with value "${appVersion}${
      buildNumber ? '_build-' + buildNumber : ''
    }" on app-settings.json file`
  )

const setJsonPropValue = (fn, value) =>
  fs
    .readFile(fn)
    .then((body) => JSON.parse(body))
    .then((json) => {
      // manipulate your data here
      json.version = value
      return json
    })
    .then((json) => JSON.stringify(json, null, 4))
    .then((body) => fs.writeFile(fn, body))
    .catch((error) => console.error(error))

const versionBranchRegex = /^v[0-9]+\.[0-9]+\.[0-9]+\.[0-9]/gi
const isVersionedBranch = appVersion.match(versionBranchRegex)
const isMaster = appVersion.match(/^master/gi)

if (typeof appVersion === 'string' && !(isMaster || isVersionedBranch)) {
  console.log(
    chalk.blueBright(
      'NOTE: App version "' + appVersion + '" is not a valid one'
    )
  )
  return
}

if (isMaster) {
  console.log(
    chalk.blueBright('Info: App version for branch ' + appVersion + ' is unset')
  )
  return
}

setJsonPropValue(
  featureFlagFilePath,
  `${appVersion.trim()}-build_${buildNumber}`
).then(() => console.log(message))
