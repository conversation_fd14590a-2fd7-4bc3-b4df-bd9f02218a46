import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingActionBarComponent } from './tag-coding-action-bar.component'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  ReviewPanelFacade,
  ReviewPanelViewState,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
  ReviewsetFacade,
  ReviewSetStateService,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentTagUtilityService } from '../../utility-services/document-tag-utility'
import { ActivatedRoute } from '@angular/router'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { BehaviorSubject, Subject } from 'rxjs'
import { signal } from '@angular/core'
import {
  DocumentActionTypeTitle,
  PageControlActionType,
} from '@venio/shared/models/constants'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import { NotificationTriggerService } from '../../../../services/shared-custom.notification.service'
import { NotificationService } from '@progress/kendo-angular-notification'
import { DialogService } from '@progress/kendo-angular-dialog'

describe('TagCodingActionBarComponent', () => {
  let component: TagCodingActionBarComponent
  let fixture: ComponentFixture<TagCodingActionBarComponent>
  let queryParamsSubject: BehaviorSubject<any>
  let mockReviewSetStateService: jest.Mocked<ReviewSetStateService>
  let mockReviewsetFacade: jest.Mocked<ReviewsetFacade>
  let mockDocumentsFacade: jest.Mocked<DocumentsFacade>
  let mockDocumentTagUtilityService: jest.Mocked<DocumentTagUtilityService>
  let mockDocumentTagFacade: jest.Mocked<DocumentTagFacade>
  let mockDocumentCodingFacade: jest.Mocked<DocumentCodingFacade>
  let mockStartupsFacade: jest.Mocked<StartupsFacade>
  let currentDocumentSubject: BehaviorSubject<number>
  let mockIsBatchReviewSignal: any
  let mockReviewSetBasicInfoSignal: any

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })
    currentDocumentSubject = new BehaviorSubject(123)

    // Create mock signals
    mockIsBatchReviewSignal = signal(false)
    mockReviewSetBasicInfoSignal = signal(undefined)

    // Create mock services with signal mocks
    mockReviewSetStateService = {
      isBatchReview: mockIsBatchReviewSignal,
      reviewSetBasicInfo: mockReviewSetBasicInfoSignal,
    } as any

    mockReviewsetFacade = {
      markCurrentDocumentAsReviewedAction: new Subject<number>(),
    } as any

    mockDocumentsFacade = {
      getCurrentDocument$: currentDocumentSubject,
      triggerMenuEvent: jest.fn(),
      triggerPageActionEvent: jest.fn(),
      setDocumentNavigation: jest.fn(),
    } as any

    mockDocumentTagUtilityService = {
      saveTag: jest.fn(),
    } as any

    mockDocumentTagFacade = {
      selectFilterDocumentTags$: new BehaviorSubject(''),
      applyDocumentTagSuccessResponse$: new BehaviorSubject(null),
      applyDocumentTagErrorResponse$: new BehaviorSubject(null),
      selectIsTagDataModified$: new BehaviorSubject(false),
      areTagCommentsMissing$: new BehaviorSubject(false),
      selectIsTagRuleListLoaded$: new BehaviorSubject(false),
      filterDocumentTags: jest.fn(),
      triggerPageActionEvent: jest.fn(),
      resetDocumentTagState: jest.fn(),
    } as any

    mockDocumentCodingFacade = {
      selectIsCodingDataModified$: new BehaviorSubject(false),
      selectIsCodingDataValid$: new BehaviorSubject(true),
      setIsDocumentCodingLoading: jest.fn(),
      resetDocumentCodingState: jest.fn(),
    } as any

    mockStartupsFacade = {
      hasGroupRight$: jest.fn().mockReturnValue(new BehaviorSubject(true)),
    } as any

    TestBed.configureTestingModule({
      imports: [TagCodingActionBarComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useValue: { opener: { postMessage: jest.fn() } } },
        { provide: ReviewSetStateService, useValue: mockReviewSetStateService },
        { provide: ReviewsetFacade, useValue: mockReviewsetFacade },
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        {
          provide: DocumentTagUtilityService,
          useValue: mockDocumentTagUtilityService,
        },
        { provide: DocumentTagFacade, useValue: mockDocumentTagFacade },
        { provide: DocumentCodingFacade, useValue: mockDocumentCodingFacade },
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        {
          provide: ReviewPanelFacade,
          useValue: {
            getShortcutKeysAction: new BehaviorSubject(''),
            setShortcutKeysAction: jest.fn(),
          },
        },
        {
          provide: ReviewPanelViewState,
          useValue: {
            tagGroupName: jest.fn().mockReturnValue('User'),
            addCopyDocumentTags: jest.fn(),
          },
        },
        SearchFacade,
        SearchResultFacade,
        FieldFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: { projectId: '123' } },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        {
          provide: ConfirmationDialogService,
          useValue: { showDialogByType: jest.fn() },
        },
        {
          provide: NotificationTriggerService,
          useValue: { triggerNotification: jest.fn() },
        },
        {
          provide: NotificationService,
          useValue: { show: jest.fn() },
        },
        {
          provide: DialogService,
          useValue: { open: jest.fn() },
        },
      ],
    })

    // Override the component's providers to use our mocks
    TestBed.overrideComponent(TagCodingActionBarComponent, {
      set: {
        providers: [
          {
            provide: DocumentTagUtilityService,
            useValue: mockDocumentTagUtilityService,
          },
        ],
      },
    })

    await TestBed.compileComponents()

    fixture = TestBed.createComponent(TagCodingActionBarComponent)
    component = fixture.componentInstance

    // Ensure validation is disabled for tests
    component.isTagCommentValidationRequired = false

    // Initialize component with default mocks
    fixture.detectChanges()
  })

  // Helper function to reinitialize component with new mock values
  const reinitializeComponent = (): void => {
    fixture = TestBed.createComponent(TagCodingActionBarComponent)
    component = fixture.componentInstance
    // Ensure validation is disabled for tests
    component.isTagCommentValidationRequired = false
    fixture.detectChanges()
  }

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('svgIconForTagControls actionText', () => {
    describe('when isBatchReview is true and markTaggedDocsAsReviewed is true', () => {
      beforeEach(() => {
        mockIsBatchReviewSignal.set(true)
        mockReviewSetBasicInfoSignal.set({
          reviewSetId: 1,
          reviewSetName: 'Test Review Set',
          reviewTagId: 1,
          markTaggedDocsAsReviewed: true,
        })
        reinitializeComponent()
      })

      it('should display review action text for PREV_PAGE', () => {
        const prevPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.PREV_PAGE
        )
        expect(prevPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.REVIEW_MOVE_PREVIOUS_DOCUMENT_TEXT
        )
      })

      it('should display review action text for NEXT_PAGE', () => {
        const nextPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.NEXT_PAGE
        )
        expect(nextPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.REVIEW_MOVE_NEXT_DOCUMENT_TEXT
        )
      })
    })

    describe('when isBatchReview is true and markTaggedDocsAsReviewed is false', () => {
      beforeEach(() => {
        mockIsBatchReviewSignal.set(true)
        mockReviewSetBasicInfoSignal.set({
          reviewSetId: 1,
          reviewSetName: 'Test Review Set',
          reviewTagId: 1,
          markTaggedDocsAsReviewed: false,
        })
        reinitializeComponent()
      })

      it('should display normal action text for PREV_PAGE', () => {
        const prevPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.PREV_PAGE
        )
        expect(prevPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_PREVIOUS_DOCUMENT_TEXT
        )
      })

      it('should display normal action text for NEXT_PAGE', () => {
        const nextPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.NEXT_PAGE
        )
        expect(nextPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_NEXT_DOCUMENT_TEXT
        )
      })
    })

    describe('when isBatchReview is false', () => {
      beforeEach(() => {
        mockIsBatchReviewSignal.set(false)
        mockReviewSetBasicInfoSignal.set({
          reviewSetId: 1,
          reviewSetName: 'Test Review Set',
          reviewTagId: 1,
          markTaggedDocsAsReviewed: true,
        })
        reinitializeComponent()
      })

      it('should display normal action text for PREV_PAGE when not in batch review', () => {
        const prevPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.PREV_PAGE
        )
        expect(prevPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_PREVIOUS_DOCUMENT_TEXT
        )
      })

      it('should display normal action text for NEXT_PAGE when not in batch review', () => {
        const nextPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.NEXT_PAGE
        )
        expect(nextPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_NEXT_DOCUMENT_TEXT
        )
      })
    })

    describe('when reviewSetBasicInfo is undefined', () => {
      beforeEach(() => {
        mockIsBatchReviewSignal.set(true)
        mockReviewSetBasicInfoSignal.set(undefined)
        reinitializeComponent()
      })

      it('should display normal action text for PREV_PAGE when reviewSetBasicInfo is undefined', () => {
        const prevPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.PREV_PAGE
        )
        expect(prevPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_PREVIOUS_DOCUMENT_TEXT
        )
      })

      it('should display normal action text for NEXT_PAGE when reviewSetBasicInfo is undefined', () => {
        const nextPageIcon = component.svgIconForTagControls.find(
          (icon) => icon.actionType === PageControlActionType.NEXT_PAGE
        )
        expect(nextPageIcon?.actionText).toBe(
          DocumentActionTypeTitle.MOVE_NEXT_DOCUMENT_TEXT
        )
      })
    })
  })

  describe('browseActionClicked', () => {
    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks()
    })

    describe('PREV_PAGE action', () => {
      describe('when isBatchReview is true and markTaggedDocsAsReviewed is true', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should mark document as reviewed and call DocumentTagUtilityService.saveTag', () => {
          // Verify the validation property is set correctly
          expect(component.isTagCommentValidationRequired).toBe(false)

          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).toHaveBeenCalledWith(123)
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })
      })

      describe('when currentFileId is 0 or negative', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed when currentFileId is 0', () => {
          // Mock the currentFileId to return 0
          currentDocumentSubject.next(0)

          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })

        it('should not mark document as reviewed when currentFileId is negative', () => {
          // Mock the currentFileId to return -1
          currentDocumentSubject.next(-1)

          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })
      })

      describe('when isBatchReview is true and markTaggedDocsAsReviewed is false', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: false,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })
      })

      describe('when isBatchReview is false', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(false)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })
      })

      describe('when reviewSetBasicInfo is undefined', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set(undefined)
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.PREV_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.PREV_PAGE
          )
        })
      })
    })

    describe('NEXT_PAGE action', () => {
      describe('when isBatchReview is true and markTaggedDocsAsReviewed is true', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should mark document as reviewed and call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).toHaveBeenCalledWith(123)
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })
      })

      describe('when currentFileId is 0 or negative', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed when currentFileId is 0', () => {
          // Mock the currentFileId to return 0
          currentDocumentSubject.next(0)

          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })

        it('should not mark document as reviewed when currentFileId is negative', () => {
          // Mock the currentFileId to return -1
          currentDocumentSubject.next(-1)

          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })
      })

      describe('when isBatchReview is true and markTaggedDocsAsReviewed is false', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: false,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })
      })

      describe('when isBatchReview is false', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(false)
          mockReviewSetBasicInfoSignal.set({
            reviewSetId: 1,
            reviewSetName: 'Test Review Set',
            reviewTagId: 1,
            markTaggedDocsAsReviewed: true,
          })
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })
      })

      describe('when reviewSetBasicInfo is undefined', () => {
        beforeEach(() => {
          mockIsBatchReviewSignal.set(true)
          mockReviewSetBasicInfoSignal.set(undefined)
          reinitializeComponent()
          jest.spyOn(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
            'next'
          )
        })

        it('should not mark document as reviewed but should call DocumentTagUtilityService.saveTag', () => {
          component.browseActionClicked(PageControlActionType.NEXT_PAGE)

          expect(
            mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
          ).not.toHaveBeenCalled()
          expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
            123,
            PageControlActionType.NEXT_PAGE
          )
        })
      })
    })

    describe('other actions', () => {
      beforeEach(() => {
        // Set up default mocks for other actions
        mockIsBatchReviewSignal.set(false)
        mockReviewSetBasicInfoSignal.set(undefined)
        reinitializeComponent()
      })

      it('should handle VIEW action without marking as reviewed', () => {
        jest.spyOn(
          mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
          'next'
        )

        component.browseActionClicked(PageControlActionType.VIEW)

        expect(
          mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
        ).not.toHaveBeenCalled()
      })

      it('should handle SAVE action and call DocumentTagUtilityService.saveTag', () => {
        component.browseActionClicked(PageControlActionType.SAVE)

        expect(mockDocumentTagUtilityService.saveTag).toHaveBeenCalledWith(
          123,
          PageControlActionType.SAVE
        )
      })

      it('should handle ADD action without marking as reviewed', () => {
        jest.spyOn(
          mockReviewsetFacade.markCurrentDocumentAsReviewedAction,
          'next'
        )

        component.browseActionClicked(PageControlActionType.ADD)

        expect(
          mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next
        ).not.toHaveBeenCalled()
      })
    })
  })
})
