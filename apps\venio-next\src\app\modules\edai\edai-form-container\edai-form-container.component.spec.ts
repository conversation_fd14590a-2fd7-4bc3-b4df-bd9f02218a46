import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiFormContainerComponent } from './edai-form-container.component'
import { FormGroup } from '@angular/forms'
import { JobForm } from '@venio/data-access/ai'

describe('EdaiFormContainerComponent', () => {
  let component: EdaiFormContainerComponent
  let fixture: ComponentFixture<EdaiFormContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiFormContainerComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiFormContainerComponent)
    component = fixture.componentInstance
    const mockFormGroup = new FormGroup<JobForm>({} as any)
    fixture.componentRef.setInput('edaiFormGroup', mockFormGroup)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
