<div class="t-flex t-flex-1 t-flex-col t-h-full">
  <kendo-dialog-titlebar (close)="close()">
    <div class="t-flex t-justify-between t-w-full t-items-center mt-ml-[10px]">
      <div
        class="t-block t-tracking-[0.14px] t-text-[#2F3080DE] t-font-medium t-gap-3">
        <span
          class="t-w-10 t-h-10 t-p-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
          <img src="assets/svg/icon-production-status.svg" alt="upload icon" />
        </span>
        {{ dialogTitle }}
      </div>
    </div>
  </kendo-dialog-titlebar>
  @if(isProductionStatusLoading()){
  <venio-production-share-skeleton />
  }@else{
  <div
    class="t-flex t-flex-1 t-flex-col t-mt-1 t-p-2"
    *ngIf="!ifProductionShare">
    <kendo-dropdownlist
      [data]="productionShareFilterData"
      [defaultItem]="defaultproductionShareFilterItem"
      [(ngModel)]="selectedProductionStatus"
      (ngModelChange)="fetchProductionStatus()"
      textField="text"
      valueField="value"
      title="Select Production Status"
      [valuePrimitive]="true"
      class="t-w-52">
    </kendo-dropdownlist>

    @if(allProductionStatus?.length !== 0){
    <ng-container>
      @for(item of allProductionStatus; track item.exportId){ @if (item.status
      !== 'Completed') {
      <div class="t-flex t-my-5">
        <h3
          class="t-text-[16px] t-tracking-[0.75px] t-font-bold t-text-[#000000] t-uppercase">
          {{ item?.exportName }}
        </h3>
        <span
          class="t-grid t-text-[#5F3F0B] t-place-content-center t-p-1 t-text-[10px] t-leading-[16px] t-font-medium t-rounded-[4px] t-ml-2 t-tracking-[1.5px] t-uppercase"
          [ngClass]="badgeClassMap[item?.status?.toUpperCase()]"
          >{{ item?.status }}
        </span>
      </div>

      <div
        class="t-flex t-items-center t-gap-4 t-my-4 t-justify-start t-mt-3 t-font-medium">
        <div class="t-flex t-items-center">
          <span
            class="t-w-[11px] t-h-[9px] t-bg-[#ED7425] t-inline-block"></span>
          <span
            class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
            >FAILED</span
          >
        </div>
        <div class="t-flex t-items-center">
          <span
            class="t-w-[11px] t-h-[9px] t-bg-[#9BD2A7] t-inline-block"></span>
          <span
            class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
            >COMPLETED</span
          >
        </div>
        <div class="t-flex t-items-center">
          <span
            class="t-w-[11px] t-h-[9px] t-bg-[#FFB300] t-inline-block"></span>
          <span
            class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
            >IN PROGRESS</span
          >
        </div>
        <div class="t-flex t-items-center">
          <span
            class="t-w-[11px] t-h-[9px] t-bg-[#718792] t-inline-block"></span>
          <span
            class="t-ml-1 t-font-bold t-text-[10px] t-tracking-[0.5px] t-leading-[16px] t-text-[#000000DE]"
            >NOT STARTED</span
          >
        </div>
      </div>
      <div class="t-flex t-flex-col t-mt-5 t-w-full t-gap-5">
        <div class="t-flex t-flex-row t-gap-5 t-w-full">
          <venio-production-status-graph
            [graphId]="1"
            [graphData]="item?.chartimage"></venio-production-status-graph>
          <venio-production-status-graph
            [graphId]="2"
            [graphData]="item?.chartfulltext"></venio-production-status-graph>
          <venio-production-status-graph
            [graphId]="3"
            [graphData]="item?.chartnative"></venio-production-status-graph>
          <venio-production-status-graph
            [graphId]="4"
            [graphData]="item?.chartloadFile"></venio-production-status-graph>
        </div>
        @if(item?.isRelativityImportEnabled){
        <div class="t-flex t-flex-row t-gap-5 t-w-full">
          <venio-production-status-graph
            [graphId]="5"
            [graphData]="
              item?.chartrelativityImage
            "></venio-production-status-graph>
          <venio-production-status-graph
            [graphId]="6"
            [graphData]="
              item?.chartrelativityFulltext
            "></venio-production-status-graph>
          <venio-production-status-graph
            [graphId]="7"
            [graphData]="
              item?.chartrelativityNative
            "></venio-production-status-graph>
          <div class="!t-w-[30%] t-flex"></div>
        </div>
        }
      </div>
      }@else{
      <div class="t-bg-[#BAE36E0F] t-px-[40px] t-py-5 t-rounded-md t-mt-5">
        <!-- Header -->
        <div class="t-flex t-items-center t-justify-between">
          <div class="t-flex t-items-center t-gap-2">
            <span
              class="t-font-bold t-text-[#000000] t-text-[16px] t-tracking-[0.75px] t-uppercase"
              >{{ item?.exportName }}
            </span>
            <span
              class="t-grid t-place-content-center t-uppercase t-p-1 t-rounded-[4px] t-font-medium t-ml-2 t-tracking-[1.5px] t-text-[#0F4B1B] t-bg-[--tb-kendo-custom-secondary-100] t-text-[10px] t-leading-[16px]">
              {{ item?.status }}
            </span>
            <span
              class="t-text-[#000000] t-text-[12px] t-font-medium t-tracking-[0.4px] t-leading-[16px]"
              >Downloaded
              <span
                class="t-text-[#00000099] t-text-[12px] t-font-normal t-tracking-[0.4px] t-leading-[16px]"
                >{{ item?.downloadCount }}</span
              ></span
            >
          </div>
        </div>

        <!-- Content -->
        <div class="t-flex t-flex-row t-gap-6 t-mt-4">
          <!-- Folder Structure -->
          <div class="t-flex-1">
            <h3
              class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
              Folder Structure
            </h3>
            <ul class="t-mt-2 t-space-y-2">
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Fulltext Count</span
                >
                @if(item.exportFulltext){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.fulltextCompleted }} of
                  {{ item?.totalDocumentCount }}</span
                >
                }@else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Fulltext not exported</span
                >
                }
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Native Count</span
                >
                @if(item.exportNative){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.nativeCompleted }}
                  of
                  {{ item?.totalDocumentCount }}</span
                >
                }@else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Native not exported</span
                >
                }
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Image Count</span
                >

                @if(item.exportTiff){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.imageCompleted }}
                  of
                  {{ item?.totalPageCount }}</span
                >
                }@else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Image not exported</span
                >
                }
              </li>
            </ul>
          </div>

          <!-- Relativity Counts -->
          @if(item?.isRelativityImportEnabled){
          <div class="t-flex-1">
            <ul class="t-mt-[30px] t-space-y-2">
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Relativity Fulltext Count</span
                >
                @if(item.exportFulltext){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.relativityFulltextCompleted }} of
                  {{ item?.totalDocumentCount }}</span
                >} @else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Fulltext not exported</span
                >
                }
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Relativity Native Count</span
                >
                @if(item.exportNative){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.relativityNativeCompleted }} of
                  {{ item?.totalDocumentCount }}</span
                >
                }@else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Native not exported</span
                >
                }
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Relativity Image Count</span
                >
                @if(item.exportTiff){
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.relativityImageCompleted }} of
                  {{ item?.totalPageCount }}</span
                >
                }@else{
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]">
                  Image not exported</span
                >
                }
              </li>
            </ul>
          </div>
          }

          <!-- Other Details -->
          <div class="t-flex-1">
            <h3
              class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
              Other details
            </h3>
            <ul class="t-mt-2 t-space-y-2">
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Started on</span
                >
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.exportStartDate | date : 'medium' }}</span
                >
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Completed on</span
                >
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px]"
                  >{{ item?.exportEndDate | date : 'medium' }}</span
                >
              </li>
              <li class="t-flex t-flex-col">
                <span
                  class="t-font-medium t-text-[12px] t-tracking-[0.4px] t-text-[#000000DE]"
                  >Created by</span
                >
                <span
                  class="t-text-[#00000099] t-font-noraml t-text-[12px] t-tracking-[0.4px] t-capitalize"
                  >{{ item?.createdBy }}</span
                >
              </li>
            </ul>
          </div>

          <!-- Action -->
          <div class="t-flex-1">
            <h3
              class="t-font-bold t-text-[14px] t-tracking-[0.42px] t-text-[var(--v-custom-sky-blue)]">
              Action
            </h3>
            <div class="t-my-2 t-flex t-gap-2">
              <kendo-buttongroup class="v-custom-button-group">
                @for(icon of svgIconForGridControls; track icon.actionType){
                <button
                  kendoButton
                  *ngIf="icon?.isVisible(item)"
                  #actionGridGroup
                  [id]="icon.actionType + '_' + item?.exportId"
                  class="!t-p-[0.3rem] t-w-[1.5rem]"
                  [ngClass]="{
                    'hover:!t-bg-[#1EBADC]': icon.hoverColor === '#1EBADC',
                    'hover:!t-bg-[#FFBB10]': icon.hoverColor === '#FFBB10',
                    'hover:!t-bg-[#2F3080]': icon.hoverColor === '#2F3080',
                    'hover:!t-bg-[#9AD3A6]': icon.hoverColor === '#9AD3A6',
                    'hover:!t-bg-[#ED7425]': icon.hoverColor === '#ED7425'
                  }"
                  (click)="browseActionClicked(icon.actionType, item)"
                  fillMode="outline"
                  kendoTooltip
                  [title]="icon.actionType"
                  size="none">
                  <span
                    [parentElement]="actionGridGroup.element"
                    venioSvgLoader
                    [applyEffectsTo]="icon.applyFill"
                    hoverColor="#FFFFFF"
                    [color]="'#979797'"
                    [svgUrl]="icon.iconPath"
                    height="0.9rem"
                    width="1rem"></span>
                </button>

                }
              </kendo-buttongroup>

              @if(isDeletedProductionVisible && item?.exportId ===
              selectedExportId){
              <kendo-popup
                [margin]="{ horizontal: 2, vertical: 5 }"
                [anchorAlign]="anchorAlign"
                [popupAlign]="popupAlign"
                [anchor]="getAnchorElement(currentButtonId)"
                popupClass="inner-wrapper">
                <venio-delete-production
                  [exportId]="selectedExportId"
                  [projectId]="selectedProjectId"
                  (closeDeletePopup)="
                    isDeletedProductionVisible = !$event;
                    fetchProductionStatus()
                  " />
              </kendo-popup>
              }
            </div>

            @if(isDownloadProgressUiShown && item?.exportId ===
            selectedExportId){
            <venio-download-production
              class="t-mt-2"
              [sourceUrl]="downloadSourceUrl"
              [customFileName]="item?.exportName + '.zip'"
              (cancelClick)="onDownloadCancel()"
              (downloadProgressChange)="
                onDownloadProgressChanged($event); count = 0
              "></venio-download-production>
            }
          </div>

          @if(!item?.isRelativityImportEnabled){
          <div class="!t-w-[5%] t-flex-auto"></div>
          }
          <div class="!t-w-[5%] t-flex-auto"></div>
        </div>
      </div>
      } }
    </ng-container>
    }@else{
    <div class="t-flex t-flex-row t-gap-5 t-align-self-center t-m-auto">
      <img
        src="assets/svg/production-status-no-data-logo.svg"
        alt="upload icon"
        height="100"
        width="100" />

      <div class="t-flex t-flex-col t-align-self-center">
        <p
          class="t-text-[#000000] t-text-[25px] t-font-medium t-tracking-[0.75px]">
          No Data Found
        </p>
        <p
          class="t-text-[#000000] t-text-[14px] t-font-normal t-tracking-[0.42px]"
          (click)="redirectToProduction('PRODUCE_NEW')">
          Click here to
          <span class="t-text-[#9BD2A7] t-cursor-pointer">send</span> files to
          production
        </p>
      </div>
    </div>
    }
  </div>
  }

  <div class="t-flex t-flex-1 t-w-full" *ngIf="ifProductionShare">
    <venio-production-status-share
      (backToProductionShare)="
        ifProductionShare = !$event; fetchProductionStatus()
      "
      [projectId]="selectedProjectId"
      [exportId]="selectedExportId"></venio-production-status-share>
  </div>
</div>
