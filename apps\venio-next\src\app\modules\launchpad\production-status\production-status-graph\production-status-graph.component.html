<div
  class="t-w-[1/4] t-h-auto t-bg-white t-shadow-[0_3px_10px_rgba(108,164,242,0.16)] t-rounded-lg t-py-2 t-px-4">
  <!-- Header -->
  <div class="t-flex t-items-center t-gap-2">
    <span
      class="t-text-lg t-rounded-full t-w-[8px] t-h-[8px]"
      [ngClass]="{
        't-bg-[#E55353]': theme === 'FAILED',
        't-bg-[#588107]': theme === 'COMPLETED',
        't-bg-[#FEB43C]': theme === 'INPROGRESS',
        't-bg-[#718792]': theme === 'NOT STARTED',
        't-bg-gray-300': ![
          'FAILED',
          'COMPLETED',
          'INPROGRESS',
          'NOT STARTED'
        ].includes(theme)
      }"></span>
    <span
      class="t-text-[13px] t-font-normal t-leading-[16px] t-tracking-[0.65px] t-text-[#000000DE]"
      >{{ getGraphTitle(graphId()) }}</span
    >
  </div>

  <!-- Chart -->
  <div class="t-w-full t-h-[100px]">
    <kendo-chart
      class="t-shadown-none t-border-0 t-w-full v-custom-char-production-bar t-h-[100%]"
      [chartArea]="{ margin: { left: 20 } }">
      <kendo-chart-category-axis>
        <kendo-chart-category-axis-item
          [categories]="['']"
          [majorGridLines]="{ visible: false }"
          [line]="{ visible: true }"
          [labels]="{ visible: false }">
        </kendo-chart-category-axis-item>
      </kendo-chart-category-axis>
      <kendo-chart-value-axis>
        <kendo-chart-value-axis-item
          [min]="0"
          [max]="graphData()?.totalDocCount"
          [majorGridLines]="{ visible: false }"
          [minorGridLines]="{ visible: false }"
          [labels]="{
            visible: false,
            step: 1,
            format: '{0}',
            color: '#6b7280'
          }">
        </kendo-chart-value-axis-item>
      </kendo-chart-value-axis>

      <kendo-chart-series>
        <kendo-chart-series-item
          *ngFor="let item of chartData"
          type="bar"
          [data]="[item.value > 0 ? item.value : 0]"
          [color]="item.color"
          [gap]="5"
          [spacing]="2"
          [border]="{
            width: 0,
            color: '#fff',
            dashType: 'solid'
          }"
          [title]="item.value"
          [labels]="{
            visible: true,
            position: 'left',
            format: '{0}',
            background: 'transparent',
            font: 'bold 8px Arial, sans-serif',
            color: '#212121',
            margin: calculateLabelMargin(item.value),
            padding: 5
          }">
        </kendo-chart-series-item>
      </kendo-chart-series>
    </kendo-chart>
  </div>

  <!-- Footer Details PLEASE NOTE ADJUST THE LOGIC OF THE CHART ACCORDING TO THE EXISTING APP -->
  <div class="t-text-center t-text-sm t-font-medium">
    <div class="t-relative t-top-[-5px]">
      <span class="t-tracking-[1.5px] t-font-medium t-text-[10px]"
        >TOTAL FILES
        <span
          class="t-absolute t-text-[8px] t-font-bold t-tracking-[0.4px] t-right-0 t-top-[-2px]"
          >{{ graphData()?.totalDocCount }}</span
        ></span
      >
    </div>
    <div class="t-flex t-flex-col t-w-full t-gap-[2px] t-text-[#757575]">
      @if(graphData()?.eta ){
      <div class="t-w-full t-flex t-gap-2 t-justify-center">
        <span
          class="t-w-1/2 t-text-right t-font-medium t-text-[10px] t-tracking-[0.5px]"
          >ETA</span
        >
        <span
          class="t-w-1/2 t-text-left t-font-medium t-text-[10px] t-tracking-[0.5px]"
          >{{ graphData()?.eta }}</span
        >
      </div>
      }
      <div class="t-w-full t-flex t-gap-2 t-justify-center">
        <span
          class="t-w-1/2 t-text-right t-font-medium t-text-[10px] t-tracking-[0.5px]"
          >STATUS</span
        >
        <span
          class="t-w-1/2 t-text-left t-font-medium t-text-[10px] t-tracking-[0.5px]"
          [ngClass]="{
            't-text-[#ED7425]': theme === 'FAILED',
            't-text-[#9BD2A7]': theme === 'COMPLETED',
            't-text-[#FFB300]': theme === 'INPROGRESS',
            't-text-[#718792]': theme === 'NOT STARTED'
          }"
          >{{ theme }}</span
        >
      </div>
      <div class="t-w-full t-flex t-gap-2 t-justify-center">
        <span
          class="t-w-1/2 t-text-right t-font-medium t-text-[10px] t-tracking-[0.5px]"
          >COMPLETED</span
        >
        <span
          class="t-text-[var(--kendo-custom-secondary-100)] t-w-1/2 t-text-left t-font-medium t-text-[10px] t-tracking-[0.5px]"
          >{{ graphData()?.progress }}%</span
        >
      </div>
    </div>
  </div>
</div>
