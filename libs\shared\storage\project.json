{"name": "shared-storage", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared/storage/src", "prefix": "venio", "tags": ["storage"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/shared/storage"], "options": {"tsConfig": "libs/shared/storage/tsconfig.lib.json", "project": "libs/shared/storage/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared/storage/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared/storage/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/shared/storage"], "options": {"jestConfig": "libs/shared/storage/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}