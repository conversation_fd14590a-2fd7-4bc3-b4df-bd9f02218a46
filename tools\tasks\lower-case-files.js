#!/usr/bin/env node

/**
 * rename-to-lowercase.js
 *
 * A Node.js script to:
 * - Recursively rename all files and folders to lowercase (two-step rename).
 * - Use chalk for colorful logging.
 * - Automatically `git add -A` and commit the changes.
 */

const fs = require('fs')
const path = require('path')
const chalk = require('chalk')
const { execSync } = require('child_process')

// Adjust this to your target folder
const TARGET_PATH =
  'D:\\venio_workspace\\venio-frontend\\apps\\venio-next\\src\\app\\modules\\edai'

/**
 * Recursively gather all files & directories from a given directory.
 */
function getAllPaths(dir) {
  let results = []
  const list = fs.readdirSync(dir)

  list.forEach((file) => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    results.push(filePath) // Add the path itself

    if (stat.isDirectory()) {
      // Recurse into subfolders
      results = results.concat(getAllPaths(filePath))
    }
  })

  return results
}

/**
 * Safely rename a file/folder to its lowercase version via a two-step rename,
 * ensuring Windows recognizes the change.
 */
function renameCase(filePath) {
  const dir = path.dirname(filePath)
  const baseName = path.basename(filePath)
  const lowerName = baseName.toLowerCase()

  // If already lowercase, skip
  if (baseName === lowerName) {
    console.log(chalk.gray(`SKIP: ${filePath} is already lowercase.`))
    return
  }

  // Step 0: Construct intermediate name to force Windows to see the rename
  const tmpName = baseName + '.__tmp__'
  const tmpPath = path.join(dir, tmpName)
  const lowerPath = path.join(dir, lowerName)

  // Step 1: Rename to *.tmp
  console.log(chalk.blue(`INFO: Renaming ${filePath} -> ${tmpPath}...`))
  fs.renameSync(filePath, tmpPath)

  // Step 2: Rename *.tmp -> lowercase
  console.log(chalk.blue(`INFO: Renaming ${tmpPath} -> ${lowerPath}...`))
  fs.renameSync(tmpPath, lowerPath)

  console.log(chalk.green(`SUCCESS: ${filePath} -> ${lowerPath}`))
}

function main() {
  try {
    console.log(chalk.blue(`INFO: Gathering paths under ${TARGET_PATH}...`))
    let allPaths = getAllPaths(TARGET_PATH)

    // Sort by path length descending, so deeper paths get renamed first.
    // This avoids issues with parent directories renaming before children.
    allPaths.sort((a, b) => b.length - a.length)

    console.log(chalk.blue('INFO: Starting two-step rename process...'))
    allPaths.forEach(renameCase)

    // Stage and commit the changes in Git
    console.log(chalk.blue('INFO: Staging changes (git add -A)...'))
    execSync('git add -A', { stdio: 'inherit' })

    console.log(chalk.blue('INFO: Committing changes...'))
    execSync('git commit -m "chore: rename files/folders to lowercase"', {
      stdio: 'inherit',
    })

    console.log(
      chalk.green('SUCCESS: Renaming & commit complete. Review your Git log.')
    )
  } catch (err) {
    console.error(chalk.red(`ERROR: ${err.message}`))
  }
}

main()
