import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiRelevanceJobListComponent } from './edai-relevance-job-list.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('EdaiRelevanceJobListComponent', () => {
  let component: EdaiRelevanceJobListComponent
  let fixture: ComponentFixture<EdaiRelevanceJobListComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiRelevanceJobListComponent],
      providers: [
        provideMockStore(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiRelevanceJobListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
