import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { FormsModule } from '@angular/forms'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import {
  SelectableSettings,
  TreeListModule,
} from '@progress/kendo-angular-treelist'

@Component({
  selector: 'venio-send-folder-dialog',
  standalone: true,
  imports: [
    CommonModule,
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    DropDownListModule,
    SvgLoaderDirective,
    TreeListModule,
  ],
  templateUrl: './send-folder-dialog.component.html',
  styleUrl: './send-folder-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SendFolderDialogComponent implements OnInit {
  public dialogTitle = 'Send / Remove folder'

  public opened = false

  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'From Folder',
    value: null,
  }

  public defaultItemCategoriesTo: { text: string; value: number } = {
    text: 'To Folder',
    value: null,
  }

  public settings: SelectableSettings = {
    mode: 'row',
    multiple: true,
    drag: false,
  }

  public selected: any[] = []

  public data: any[] = [
    {
      id: 1,
      name: 'Daryl Sweeney',
      title: 'Chief Executive Officer',
      phone: '(*************',
      managerId: null,
    },
    {
      id: 2,
      name: 'Guy Wooten',
      title: 'Chief Technical Officer',
      phone: '(*************',
      managerId: 1,
    },
    {
      id: 3,
      name: 'Daryl Sweeney',
      title: 'Chief Executive Officer',
      phone: '(*************',
      managerId: null,
    },
    {
      id: 4,
      name: 'Guy Wooten',
      title: 'Chief Technical Officer',
      phone: '(*************',
      managerId: 3,
    },
    {
      id: 5,
      name: 'Daryl Sweeney',
      title: 'Chief Executive Officer',
      phone: '(*************',
      managerId: null,
    },
    {
      id: 6,
      name: 'Guy Wooten',
      title: 'Chief Technical Officer',
      phone: '(*************',
      managerId: 5,
    },
    {
      id: 32,
      name: 'Buffy Weber',
      title: 'VP, Engineering',
      phone: '(*************',
      managerId: null,
    },
  ]

  public ngOnInit(): void {
    this.openDialog()
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }
}
