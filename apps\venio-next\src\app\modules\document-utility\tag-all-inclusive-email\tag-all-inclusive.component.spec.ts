import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagAllInclusiveComponent } from './tag-all-inclusive.component'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { NotificationService } from '@progress/kendo-angular-notification'
import { VenioNotificationService } from '@venio/feature/notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagAllInclusiveComponent', () => {
  let component: TagAllInclusiveComponent
  let fixture: ComponentFixture<TagAllInclusiveComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagAllInclusiveComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        provideMockStore({}),
        DialogService,
        DialogContainerService,
        SearchFacade,
        FieldFacade,
        VenioNotificationService,
        NotificationService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagAllInclusiveComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
