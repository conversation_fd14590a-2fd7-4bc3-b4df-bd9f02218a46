{"name": "shared-models-interfaces", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared/models/interfaces/src", "prefix": "venio", "tags": ["models"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/shared/models/interfaces"], "options": {"tsConfig": "libs/shared/models/constants/tsconfig.lib.json", "project": "libs/shared/models/interfaces/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared/models/interfaces/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared/models/interfaces/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/shared/models/interfaces"], "options": {"jestConfig": "libs/shared/models/interfaces/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}