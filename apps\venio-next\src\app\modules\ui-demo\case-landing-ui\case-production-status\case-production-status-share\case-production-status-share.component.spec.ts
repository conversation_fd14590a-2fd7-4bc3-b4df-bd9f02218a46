import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseProductionStatusShareComponent } from './case-production-status-share.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseProductionStatusShareComponent', () => {
  let component: CaseProductionStatusShareComponent
  let fixture: ComponentFixture<CaseProductionStatusShareComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseProductionStatusShareComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseProductionStatusShareComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
