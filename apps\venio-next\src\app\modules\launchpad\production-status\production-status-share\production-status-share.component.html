<div class="t-flex t-flex-col t-gap-2">
  <div class="t-flex t-mt-2 t-mb-1 t-w-full t-items-center">
    <button
      kendoButton
      fillMode="outline"
      class="t-w-[74px] t-p-0 t-text-[16px] hover:t-text-[#000000] t-flex t-items-center"
      (click)="backToProductionShare.emit(true)">
      <kendo-svg-icon
        class="t-text-[var(--v-custom-sky-blue)] hover:t-text-[var(--v-custom-sky-blue)]"
        [icon]="icons.chevronLeftIcon"
        size="large"></kendo-svg-icon>
      Back</button
    ><span class="t-ml-2 t-font-semibold t-text-[16px]">Share Production</span>
  </div>
  <form
    class="t-flex t-flex-col t-gap-2 t-pb-3"
    [formGroup]="productionShareForm">
    <div class="t-flex t-flex-row t-gap-3 t-mt-1">
      <!-- Internal Users Section -->
      <div class="t-w-1/2">
        <div class="t-flex t-items-end !t-font-medium t-py-2">
          Share To Internal Users
        </div>

        <div class="t-flex t-flex-col t-mt-1">
          <div
            class="v-search-box-container t-relative t-inline-block t-w-full t-mb-2 t-mt-[2px]">
            <kendo-textbox
              placeholder="Search For Internal Users"
              id="search_internal_user"
              (input)="onFilter($event)"></kendo-textbox>
          </div>
          <div
            (resize)="onContainerResize($event)"
            class="t-h-full"
            #treeListContainer>
            <kendo-grid
              class="t-flex t-flex-col-reverse t-overflow-y-auto"
              style="height: 12rem"
              [kendoGridBinding]="filteredInternalUsers"
              [kendoGridSelectBy]="internalUserSelectionKey"
              [(selectedKeys)]="selectedInternalUsers"
              data-qa="document-share-internal-users-grid"
              (selectionChange)="onInternalUserSelectionChange()"
              [ngClass]="{
                'k-disabled t-opacity-50': invitationInProgress$ | async,
                't-opacity-100': (invitationInProgress$ | async) === false
              }"
              [selectable]="true">
              <kendo-grid-column title="#" [width]="40">
                <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
                  {{ rowIndex + 1 }}
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-checkbox-column
                [showSelectAll]="true"
                [columnMenu]="false"
                [ngClass]="{
                  'k-disabled': !shareToExternalUsers,
                  '': shareToExternalUsers
                }"
                [width]="40">
              </kendo-grid-checkbox-column>

              <kendo-grid-column
                field="userName"
                title="User Name"
                headerClass="t-text-primary">
              </kendo-grid-column>
              <kendo-grid-column
                field="groupName"
                title="Role"
                headerClass="t-text-primary">
              </kendo-grid-column>

              <ng-template kendoGridNoRecordsTemplate>
                No internal users.
              </ng-template>
            </kendo-grid>
          </div>
        </div>
      </div>

      <div
        class="v-dashed-sperator t-mx-[15px] t-opacity-100 t-w-[1px] t-h-auto"></div>

      <!-- External Users Section -->
      <div class="t-flex t-flex-col t-w-1/2">
        <!-- Share to external user checkbox and input textbox -->
        <div class="t-flex t-items-center t-gap-2 t-px-1 t-pb-2">
          <input
            type="checkbox"
            formControlName="shareToExternalUsers"
            data-qa="document-share-share-to-externalusers-checkbox"
            kendoCheckBox
            #shareToExternalUsersLabel />
          <kendo-label
            [for]="shareToExternalUsersLabel"
            text="Share to External Users"
            data-qa="document-share-share-to-externalusers-label"
            [ngClass]="{
              'k-disabled t-opacity-50': invitationInProgress$ | async,
              't-opacity-100': (invitationInProgress$ | async) === false
            }"></kendo-label>
          <kendo-textbox
            formControlName="newEmail"
            class="t-w-1/2"
            placeholder="Add New User Email"
            data-qa="document-share-new-email-input">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                class="t-h-full !t-px-1"
                fillMode="outline"
                size="small"
                [disabled]="!shareToExternalUsers"
                (click)="addExternalUser()"
                [svgIcon]="plusIcon"
                data-qa="document-share-add-new-email-button"
                kendoButton></button>
            </ng-template>
          </kendo-textbox>
        </div>

        <div
          class="v-search-box-container t-relative t-inline-block t-w-full t-mb-2 t-mt-[1px]">
          <kendo-textbox
            placeholder="Search For External Users"
            id="search_external_user"
            (input)="onFilterExternalUser($event)"
            [disabled]="
              !shareToExternalUsers || (invitationInProgress$ | async)
            "></kendo-textbox>
        </div>
        <!-- External user grid -->
        <div
          (resize)="onContainerResize($event)"
          class="t-h-full"
          #treeListContainer>
          <kendo-grid
            [kendoGridBinding]="filteredExternalUsers"
            [kendoGridSelectBy]="externalUserSelectionKey"
            [(selectedKeys)]="selectedExternalUsers"
            class="t-flex t-flex-col-reverse t-overflow-y-auto"
            style="height: 12rem"
            data-qa="document-share-external-users-grid"
            (selectionChange)="onExternalUserSelectionChange()"
            [ngClass]="{
              'k-disabled t-opacity-50':
                !shareToExternalUsers || (invitationInProgress$ | async),
              't-opacity-100':
                shareToExternalUsers ||
                (invitationInProgress$ | async) === false
            }"
            [selectable]="true">
            <kendo-grid-column title="#" [width]="40">
              <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
                {{ rowIndex + 1 }}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-checkbox-column
              [showSelectAll]="true"
              [columnMenu]="false"
              [width]="40">
            </kendo-grid-checkbox-column>

            <kendo-grid-column
              field="email"
              title="User Name"
              headerClass="t-text-primary">
              <ng-template
                kendoGridFilterCellTemplate
                let-filter
                let-column="column">
                <kendo-grid-string-filter-cell
                  class="t-h-4"
                  [column]="column"
                  [filter]="filter">
                </kendo-grid-string-filter-cell>
              </ng-template>
            </kendo-grid-column>
            <ng-template kendoGridNoRecordsTemplate>
              No external users.
            </ng-template>
          </kendo-grid>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-w-full t-justify-start t-gap-2">
      <!-- Instruction Section -->
      <div class="t-flex t-flex-col t-gap-2">
        <kendo-label class="t-block t-text-[#707070]">Instruction</kendo-label>
        <kendo-editor
          formControlName="productionDownloadInstruction"
          class="t-border t-border-[#BEBEBE] t-h-[165px] t-rounded-md t-p-2">
          <kendo-toolbar>
            <kendo-toolbar-dropdownlist
              kendoEditorFontFamily></kendo-toolbar-dropdownlist>
            <kendo-toolbar-dropdownlist
              kendoEditorFontSize></kendo-toolbar-dropdownlist>

            <kendo-toolbar-buttongroup>
              <kendo-toolbar-button
                kendoEditorBoldButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorItalicButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorUnderlineButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorStrikethroughButton></kendo-toolbar-button>
            </kendo-toolbar-buttongroup>
            <kendo-toolbar-buttongroup>
              <kendo-toolbar-button
                kendoEditorAlignLeftButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorAlignCenterButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorAlignRightButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorAlignJustifyButton></kendo-toolbar-button>
            </kendo-toolbar-buttongroup>

            <kendo-toolbar-buttongroup>
              <kendo-toolbar-button
                kendoEditorInsertUnorderedListButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorInsertOrderedListButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorIndentButton></kendo-toolbar-button>
              <kendo-toolbar-button
                kendoEditorOutdentButton></kendo-toolbar-button>
            </kendo-toolbar-buttongroup>
          </kendo-toolbar>
        </kendo-editor>
      </div>

      <div class="t-mt-1 t-flex t-flex-col t-items-start t-gap-2">
        <kendo-label class="t-mr-4 t-w-36 t-text-[#707070]"
          >Valid up-to</kendo-label
        >
        <kendo-dropdownlist
          formControlName="productionDownloadExpirationPeriod"
          [data]="validityOptions"
          class="t-w-36"></kendo-dropdownlist>
      </div>
    </div>
  </form>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="onShare()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      [disabled]="invitationInProgress$ | async"
      data-qa="save-button">
      <kendo-loader
        *ngIf="invitationInProgress$ | async"
        size="small"
        type="pulsing"
        class="t-pl-[0.5rem]"></kendo-loader>
      SHARE
    </button>
    <button
      kendoButton
      (click)="close()"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
