import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ButtonComponent,
  ButtonGroupComponent,
  ButtonItemTemplateDirective,
  DropDownButtonComponent,
} from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { CaseDetailModel } from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { map, switchMap } from 'rxjs/operators'
import { UserRights } from '@venio/data-access/review'
import { isEqual } from 'lodash'

/**
 * Represents a local item action, including its display text and associated action type.
 */
type LocalItemActionType = { text: string; actionType: CommonActionTypes }

/**
 * Enum representing menu titles for dropdown actions.
 * @enum {string}
 */
enum MenuTitles {
  PRODUCE = 'Produce',
  UPLOAD = 'Upload',
}

@Component({
  selector: 'venio-case-grid-actions',
  standalone: true,
  imports: [
    CommonModule,
    DropDownButtonComponent,
    ButtonItemTemplateDirective,
    ButtonGroupComponent,
    ButtonComponent,
    SvgLoaderDirective,
    LoaderComponent,
    TooltipDirective,
  ],
  templateUrl: './case-grid-actions.component.html',
  styleUrl: './case-grid-actions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseGridActionsComponent {
  private readonly projectFacade = inject(ProjectFacade)

  /**
   * Signal representing the current case's row data.
   */
  public rowDataItem = input<CaseDetailModel>()

  public readonly activeAction = signal<CommonActionTypes>(undefined)

  public readonly rowData = computed(() => this.rowDataItem(), {
    equal: isEqual,
  })

  public readonly hoverColor = computed(() =>
    this.rowData()?.isFavoriteProject ? '#ED7425' : '#FFFFFF'
  )

  public readonly color = computed(() =>
    this.rowData()?.isFavoriteProject ? '#ED7425' : '#979797'
  )

  public readonly favoriteButtonClass = computed(() => {
    // console.log(this.rowData())
    return {
      'v-heart-selected-btn t-bg-[#fdf6f3] t-border-[#ED7425]':
        this.rowData()?.isFavoriteProject,
      '!t-px-1.5 !t-py-0 t-border-[#ED7425]':
        this.isFavoriteProjectToggleLoading(),
      '!t-py-[0.38rem] !t-px-[0.5rem]': !this.isFavoriteProjectToggleLoading(),
    }
  })

  /**
   * Indicates if toggling the favorite state is in progress.
   */
  public readonly isFavoriteProjectToggleLoading = toSignal(
    this.projectFacade.isFavoriteProjectToggleLoading$.pipe(
      map(
        (flags: Record<number, boolean>) => flags?.[this.rowData()?.projectId]
      )
    )
  )

  public readonly isMediaStatusLoading = toSignal(
    this.projectFacade.selectIsMediaStatusLoading$.pipe(
      map(
        (flags: Record<number, boolean>) => flags?.[this.rowData()?.projectId]
      )
    )
  )

  /**
   * User rights for the current project.
   */
  private readonly projectRights = toSignal(
    toObservable<CaseDetailModel>(this.rowData).pipe(
      switchMap((rowData) =>
        this.projectFacade.selectProjectIdsToRights$.pipe(
          map((rights) => rights?.[rowData?.projectId] || [])
        )
      )
    ),
    {
      initialValue: [],
    }
  )

  /**
   * Whether a user can view the "Analyze" page.
   */
  public readonly canViewAnalyzePage = computed(() =>
    this.projectRights().includes(UserRights.ALLOW_TO_VIEW_DASHBOARD)
  )

  /**
   * Whether a user can view the "Review" page.
   */
  public readonly canViewReviewPage = computed(() => {
    return this.projectRights().includes(UserRights.ALLOW_SEARCH)
  })

  /**
   * Whether a user can view the "Upload" page.
   */
  public readonly canViewUploadPage = computed(() =>
    this.projectRights().includes(UserRights.ADD_CUSTODIAN_MEDIA)
  )

  /**
   * Whether a user can view the "Reprocessing" page.
   */
  public readonly canViewReprocessingPage = computed(() =>
    this.projectRights().includes(UserRights.ALLOW_TO_REPROCESS)
  )

  /**
   * Whether a user can send "Invite to Upload" links.
   */
  public readonly canViewInviteUploadLink = computed(() =>
    this.projectRights().includes(UserRights.ALLOW_TO_INVITE_DATA_UPLOAD)
  )

  /**
   * Whether a user can access Produce-related actions.
   */
  public readonly canViewProducePage = computed(() =>
    this.projectRights().some((name) =>
      [
        UserRights.ALLOW_EXPORT,
        UserRights.ALLOW_TO_VIEW_EXPORT_STATUS,
        UserRights.ALLOW_TO_DOWNLOAD_EXPORT_ARCHIVES,
      ].includes(name)
    )
  )

  /**
   * Whether a user can create new production
   */
  public readonly canCreateNewProduction = computed(
    () =>
      this.projectRights().includes(UserRights.ALLOW_EXPORT) &&
      // disabling for export service cases
      !this.rowData()?.isExportServiceCase
  )

  /**
   * Emits whenever a user triggers an action.
   */
  public readonly actionInvoked = output<CommonActionTypes>()

  /**
   * Common action types reference.
   */
  public readonly commonActionTypes = CommonActionTypes

  /**
   * Buttons configuration including default and hover icons
   * for dropdown buttons.
   **/
  public readonly buttonsConfig = computed(() => {
    return [
      {
        defaultIcon: 'assets/svg/icon-measurement-setting.svg',
        hoverIcon: 'assets/svg/icon-measurement-setting-white.svg',
        title: MenuTitles.PRODUCE,
      },
      {
        defaultIcon: 'assets/svg/icon-cloud-upload.svg',
        hoverIcon: 'assets/svg/icon-cloud-upload-white.svg',
        title: MenuTitles.UPLOAD,
      },
    ].filter(
      (btn) =>
        (btn.title === MenuTitles.PRODUCE && this.canViewProducePage()) ||
        btn.title === MenuTitles.UPLOAD ||
        this.canViewUploadPage()
    )
  })

  /** Hover states for buttons. Key is projectId and value is a boolean array
   **/
  public hoverStates: { [key: number]: boolean[] } = {}

  constructor() {
    effect(
      () => {
        // When the loading becomes false, we need to reset the active action to undefined
        const isMediaStatusLoading = this.isMediaStatusLoading()
        if (!isMediaStatusLoading) {
          this.activeAction.set(undefined)
        }
      },
      { allowSignalWrites: true }
    )
  }

  /**
   * Retrieves dropdown data for given button index.
   * @param {number} buttonIndex - The index of the button.
   * @returns {Array<{ text: string }>} - Dropdown data for the button.
   */
  public getActionDropdownData(buttonIndex: number): Array<{ text: string }> {
    return buttonIndex === 1 ? this.uploadActions() : this.produceActions()
  }

  /** Local item actions for produce dropdown buttons */
  public produceActions = computed<LocalItemActionType[]>(() => {
    return [
      { text: 'New', actionType: CommonActionTypes.PRODUCE_NEW },
      { text: 'Status', actionType: CommonActionTypes.PRODUCE_STATUS },
    ].filter(
      (action) =>
        (action.actionType === CommonActionTypes.PRODUCE_NEW &&
          this.canViewProducePage() &&
          this.canCreateNewProduction()) ||
        (action.actionType === CommonActionTypes.PRODUCE_STATUS &&
          this.canViewProducePage())
    )
  })

  /** Local item actions for upload dropdown buttons */
  public uploadActions = computed<LocalItemActionType[]>(() => {
    return [
      { text: 'Invite To Upload', actionType: CommonActionTypes.UPLOAD_INVITE },
      { text: 'New Upload', actionType: CommonActionTypes.UPLOAD_NEW },
      { text: 'Reprocess', actionType: CommonActionTypes.UPLOAD_REPROCESS },
    ].filter(
      (action) =>
        (action.actionType === CommonActionTypes.UPLOAD_INVITE &&
          this.canViewInviteUploadLink()) ||
        (action.actionType === CommonActionTypes.UPLOAD_REPROCESS &&
          this.canViewReprocessingPage()) ||
        (action.actionType === CommonActionTypes.UPLOAD_NEW &&
          this.canViewUploadPage())
    )
  })

  /** Get the dynamic class for the button based on the index.
   * @param {number} buttonIndex - The index of the button.
   * @returns {string} - The dynamic class for the button.
   **/
  public getDynamicClass(buttonIndex: number): string {
    if (buttonIndex === 0) {
      return "before:t-content-['Produce']"
    } else if (buttonIndex === 1) {
      return "before:t-content-['Upload']"
    }
    return ''
  }

  /** Set the hover state for the button.
   * @param {number} buttonIndex - The index of the button.
   * @param {boolean} isHovered - The hover state of the button.
   * @returns {void}
   */
  public setHoverState(buttonIndex: number, isHovered: boolean): void {
    const { projectId } = this.rowDataItem()
    if (!this.hoverStates[projectId]) {
      this.hoverStates[projectId] = []
    }
    this.hoverStates[projectId][buttonIndex] = isHovered
  }

  /** Get the icon path for the button based on the projectId and buttonIndex.
   * @param {number} projectId - The projectId of the case.
   * @param {number} buttonIndex - The index of the button.
   * @returns {string} - The icon path for the button.
   */
  public getIconPath(projectId: number, buttonIndex: number): string {
    const buttons = this.buttonsConfig()
    return this.hoverStates[projectId] &&
      this.hoverStates[projectId][buttonIndex]
      ? buttons[buttonIndex].hoverIcon
      : buttons[buttonIndex].defaultIcon
  }

  /**
   * Emits an action when a direct action button is clicked.
   * @param {CommonActionTypes} actionType - The action type.
   * @returns {void}
   */
  public actionButtonClick(actionType: CommonActionTypes): void {
    this.activeAction.set(actionType)
    this.actionInvoked.emit(actionType)
  }

  /**
   * Emits an action when a dropdown action item is clicked.
   * @param {LocalItemActionType} item - The dropdown action item.
   * @returns {void}
   */
  public dropdownActionItemClick(item: LocalItemActionType): void {
    this.actionInvoked.emit(item.actionType)
  }
}
