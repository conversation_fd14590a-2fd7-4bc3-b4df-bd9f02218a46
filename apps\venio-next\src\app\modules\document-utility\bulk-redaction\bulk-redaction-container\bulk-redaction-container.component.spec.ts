import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkRedactionContainerComponent } from './bulk-redaction-container.component'
import { DocumentsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'

describe('BulkRedactionContainerComponent', () => {
  let component: BulkRedactionContainerComponent
  let fixture: ComponentFixture<BulkRedactionContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkRedactionContainerComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(withInterceptorsFromDi()),
        DocumentsFacade,
        DialogService,
        DialogContainerService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkRedactionContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
