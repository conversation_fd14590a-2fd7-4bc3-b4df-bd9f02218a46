import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class EciDataService {
  private dataSubject = new BehaviorSubject<any>(null);
  data$ = this.dataSubject.asObservable();

  private isFocusedSectionOpenedSubject = new BehaviorSubject<boolean>(false);
  isFocusedSectionOpened$ = this.isFocusedSectionOpenedSubject.asObservable();

  private isParentDataSubject = new BehaviorSubject<boolean>(true);
  isParentData$ = this.isParentDataSubject.asObservable();

  private showDetailsSubject = new BehaviorSubject<boolean>(false);
  showDetails$ = this.showDetailsSubject.asObservable();

  setIsFocusedSectionOpened(isOpened: boolean) {
    this.isFocusedSectionOpenedSubject.next(isOpened);
  }
  setData(data: any) {
    this.dataSubject.next(data);
  }

  setIsParentData(value: boolean) {
    this.isParentDataSubject.next(value);
  }

  setShowDetails(state: boolean) {
    this.showDetailsSubject.next(state);
  }
}
