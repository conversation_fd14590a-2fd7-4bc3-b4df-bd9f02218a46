<div [ngClass]="{ 't-h-screen': !isParentAppLinkRequest() }" class="t-flex">
  <!-- Left Pane: Logo -->
  <div
    *ngIf="!isParentAppLinkRequest()"
    class="t-w-3/6 t-relative t-flex t-items-center t-justify-center v-login-bg">
    <div class="t-absolute t-inset-0 t-bg-[#b6c66a] t-opacity-20"></div>

    <div class="t-relative t-z-10">
      <img
        ngSrc="assets/img/venio-logo-full-vertical.png"
        height="148"
        width="123"
        alt="Venio"
        class="t-h-[9rem] t-w-[10.75rem] t-object-contain" />
    </div>
  </div>

  <!-- Right Pane: Login Form -->
  <div
    [ngClass]="{
      't-w-3/6 t-p-7': !isParentAppLinkRequest(),
      't-w-full t-justify-center': isParentAppLinkRequest()
    }"
    class="t-flex t-items-center">
    <div
      [ngClass]="{ 't-mx-3': !isParentAppLinkRequest() }"
      class="t-p-9 t-w-[28rem]">
      <div>
        <h1
          class="t-text-[#030303] t-text-4xl t-whitespace-nowrap t-font-black t-mb-3 t-text-left">
          CHANGE PASSWORD
        </h1>

        <p
          class="t-text-[#FFBB12] t-whitespace-nowrap t-text-2xl t-font-black t-mb-6 t-text-left">
          Please enter new password
        </p>
      </div>
      <form
        autocomplete="off"
        [formGroup]="passwordResetFormGroup"
        class="t-flex t-flex-col t-gap-4">
        <kendo-formfield
          *ngIf="isParentAppLinkRequest()"
          class="t-w-full t-align-left t-mb-2">
          <kendo-textbox
            [type]="isCurrentPasswordVisible() ? 'text' : 'password'"
            formControlName="oldPassword"
            venioCopyPrevention
            class="t-w-full t-align-left v-input-shadow"
            placeholder="Current Password"
            id="currentPassword">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                [tabIndex]="-1"
                themeColor="none"
                fillMode="clear"
                look="clear"
                class="t-pr-2"
                (click)="
                  isCurrentPasswordVisible.set(!isCurrentPasswordVisible())
                ">
                <kendo-svgicon
                  [icon]="isCurrentPasswordVisible() ? iconEye : iconSlashEye">
                </kendo-svgicon>
              </button>
            </ng-template>
          </kendo-textbox>
          <div
            *ngIf="
              (formControls?.oldPassword?.invalid &&
                formControls?.oldPassword?.dirty &&
                !formControls?.oldPassword?.untouched) ||
              (formControls?.oldPassword?.dirty &&
                !formControls?.oldPassword?.untouched &&
                formControls?.oldPassword?.value.trim() === '')
            "
            class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
            {{
              formControls.oldPassword.hasError('required')
                ? 'Current password is required'
                : ''
            }}
          </div>
        </kendo-formfield>
        <kendo-formfield class="t-w-full t-align-left t-mb-2">
          <kendo-textbox
            venioCopyPrevention
            (focus)="setCurrentActiveControl('newPassword')"
            (valueChange)="setCurrentActiveControl('newPassword')"
            (blur)="setCurrentActiveControl('')"
            [popover]="newPassword"
            #newPasswordPopoverAnchor="kendoPopoverAnchor"
            kendoPopoverAnchor
            [type]="isNewPasswordVisible() ? 'text' : 'password'"
            formControlName="newPassword"
            class="t-w-full t-align-left v-input-shadow"
            placeholder="New Password"
            id="newPassword">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                [tabIndex]="-1"
                themeColor="none"
                fillMode="clear"
                look="clear"
                class="t-pr-2"
                (click)="isNewPasswordVisible.set(!isNewPasswordVisible())">
                <kendo-svgicon
                  [icon]="isNewPasswordVisible() ? iconEye : iconSlashEye">
                </kendo-svgicon>
              </button>
            </ng-template>
          </kendo-textbox>
          <div
            *ngIf="
              (formControls?.newPassword?.invalid &&
                formControls?.newPassword?.dirty &&
                !formControls?.newPassword?.untouched) ||
              (formControls?.newPassword?.dirty &&
                !formControls?.newPassword?.untouched &&
                formControls?.newPassword?.value.trim() === '')
            "
            class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
            {{
              formControls.newPassword.hasError('required')
                ? 'New password is required'
                : ''
            }}
          </div>
        </kendo-formfield>
        <kendo-popover #newPassword position="right" [width]="280">
          <ng-template kendoPopoverBodyTemplate>
            <div class="v-error-popover t-opacity-90 v-error-popover">
              {{ newPasswordStrengthMessage }}
            </div>
          </ng-template>
        </kendo-popover>
        <kendo-formfield class="t-w-full t-align-left t-mb-2">
          <kendo-textbox
            venioCopyPrevention
            (focus)="setCurrentActiveControl('confirmPassword')"
            (valueChange)="setCurrentActiveControl('confirmPassword')"
            (blur)="setCurrentActiveControl('')"
            [popover]="confirmNewPassword"
            #confirmPasswordPopoverAnchor="kendoPopoverAnchor"
            kendoPopoverAnchor
            [type]="isConfirmPasswordVisible() ? 'text' : 'password'"
            formControlName="confirmPassword"
            class="t-w-full t-align-left v-input-shadow"
            placeholder="Confirm New Password"
            id="confirmPassword">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                [tabIndex]="-1"
                kendoButton
                themeColor="none"
                fillMode="clear"
                look="clear"
                class="t-pr-2"
                (click)="
                  isConfirmPasswordVisible.set(!isConfirmPasswordVisible())
                ">
                <kendo-svgicon
                  [icon]="isConfirmPasswordVisible() ? iconEye : iconSlashEye">
                </kendo-svgicon>
              </button>
            </ng-template>
          </kendo-textbox>
          @if((formControls?.confirmPassword?.dirty &&
          !formControls?.confirmPassword?.untouched &&
          formControls?.confirmPassword?.invalid) ||
          (formControls?.confirmPassword?.dirty &&
          !formControls?.confirmPassword?.untouched &&
          formControls?.confirmPassword?.value.trim() === '')){
          @if(formControls.confirmPassword?.hasError('mismatch')){
          <div class="t-text-error t-mb-1">
            Passwords do not match. Please try again.
          </div>
          } @else{
          <div class="t-accent-error t-text-error t-m-1 t-text-[11.23px]">
            {{
              formControls.confirmPassword.hasError('required')
                ? 'Confirm new password is required'
                : ''
            }}
          </div>
          } }
        </kendo-formfield>
        <kendo-popover #confirmNewPassword position="right" [width]="280">
          <ng-template kendoPopoverBodyTemplate>
            <div class="v-error-popover t-opacity-90 v-error-popover">
              {{ confirmPasswordStrengthMessage }}
            </div>
          </ng-template>
        </kendo-popover>
        <p
          *ngIf="errorMessage()"
          class="t-text-error t-text-base t-text-center">
          {{ errorMessage() }}
        </p>
        <button
          (click)="resetClick()"
          kendoButton
          type="button"
          [disabled]="isPasswordResetLoading()"
          class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-mt-2 t-h-[2.5rem] t-rounded-xl t-drop-shadow-md t-font-sans t-text-sm t-border-none">
          <kendo-loader
            *ngIf="isPasswordResetLoading()"
            type="pulsing"
            themeColor="success" />
          Reset Password
        </button>
      </form>
    </div>
  </div>
</div>
