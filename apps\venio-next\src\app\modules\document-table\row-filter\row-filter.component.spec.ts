import { ComponentFixture, TestBed } from '@angular/core/testing'
import { RowFilterComponent } from './row-filter.component'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('RowFilterComponent', () => {
  let component: RowFilterComponent
  let fixture: ComponentFixture<RowFilterComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RowFilterComponent, BrowserAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(RowFilterComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
