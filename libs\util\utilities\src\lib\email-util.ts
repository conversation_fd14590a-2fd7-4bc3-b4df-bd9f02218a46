export class EmailUtil {
  /**
   * Validates whether the given email string is in a valid format and adheres to length constraints.
   *
   * @param {string} email - The email address string to validate.
   * @returns {boolean} `true` if the email is valid, otherwise `false`.
   *
   * The function performs the following checks:
   * 1. **Format Validation:** Checks if the email matches a standard email format using a regular expression.
   * 2. **Local Part Length:** Ensures the local part (before the `@` symbol) does not exceed 64 characters.
   * 3. **Domain Part Length:** Ensures the domain part (after the `@` symbol) does not exceed 255 characters.
   * 4. **Total Length:** Ensures the total email length does not exceed 320 characters.
   */
  public static validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

    if (!email) {
      return false
    }

    // Regular expression to validate the format of an email address
    const isEmailFormatOk = emailRegex.test(email)
    if (!isEmailFormatOk) {
      return false
    }

    // Split the email into local and domain parts
    const emailParts = email.split('@')
    if (
      emailParts[0]?.length > 64 ||
      (emailParts?.length > 1 && emailParts[1].length > 255) ||
      email?.length > 320
    ) {
      return false
    }
    return true
  }
}
