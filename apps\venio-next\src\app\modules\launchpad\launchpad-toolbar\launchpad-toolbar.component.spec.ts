import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LaunchpadToolbarComponent } from './launchpad-toolbar.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { StartupsFacade } from '@venio/data-access/review'
import { of } from 'rxjs'
import { provideNoopAnimations } from '@angular/platform-browser/animations'

describe('CaseLaunchpadToolbarComponent', () => {
  let component: LaunchpadToolbarComponent
  let fixture: ComponentFixture<LaunchpadToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LaunchpadToolbarComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({ Invalid_Global_Right_List: {} }),
            fetchUserRights: jest.fn(),
            hasGlobalRight$: jest.fn().mockReturnValue(of(false)),
            fetchFullFeatureLicenseAvailability: jest.fn(),
            selectFullFeatureAvailability$: of(true),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LaunchpadToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
